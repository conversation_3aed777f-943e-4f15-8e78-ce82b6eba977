// Core
import { AiAvatarModels } from 'UI/src';

// Components
import TalkingHeadComponent from '../../components/3d-model/the-model';
import Camera from '../../components/camera';
import Chat from '../../components/chat';

import { motion } from 'framer-motion';
import { RootState, useAppSelector } from 'UI/src';

export const StepperAiQuestion = ({
  isLoaded,
  currentLayout = 'spotlight',
  isFinished,
  setIsFinished,
  result,
  setResult,
  setLoadingProgression,
  modelRef,
  isRecording,
  isChatVisible,
  start,
  textAnswer,
  setTextAnswer,
  recordingMode,
  setRecordingMode,
  sendManualReply,
  transcript,
}: {
  isLoaded: React.MutableRefObject<boolean>;
  currentLayout?: 'spotlight' | 'sidebar' | 'grid';
  isFinished: boolean;
  setIsFinished: (value: boolean) => void;
  result: any;
  setResult: (value: any) => void;
  setLoadingProgression: (value: any) => void;
  modelRef: React.RefObject<any>;
  isRecording: boolean;
  isChatVisible: boolean;
  start: any;
  textAnswer: string;
  setTextAnswer: (value: string) => void;
  recordingMode: string;
  setRecordingMode: (value: string) => void;
  sendManualReply: () => void;
  transcript: string;
}) => {
  // Themes
  const layoutsStyles = {
    sidebar: {
      container: 'grid grid-cols-12 gap-3 w-full h-full',
      model: 'w-full h-full col-span-8 md:col-span-9 lg:col-span-10  relative',
      camera: 'aspect-video w-full col-span-4 md:col-span-3 lg:col-span-2',
    },
    spotlight: {
      container: 'grid w-full h-full grid-cols-1  relative',
      model: 'relative',
      camera: 'w-[160px] md:w-[300px] absolute bottom-3 right-3 aspect-video',
    },
    grid: {
      container: 'grid grid-cols-1 sm:grid-cols-2 gap-3 relative w-full',
      model: ' h-[30dvh] md:h-full aspect-video w-full',
      camera: 'h-[30dvh] md:h-full aspect-video w-full relative',
    },
  };

  const { loading, submissionAi, isSpeaking } = useAppSelector((state: RootState) => state.submissionAi);

  // const selectedAiModel = Object.entries(AiAvatarModels).find(([_, value]) => value === submissionAi?.interview?.avatarName)?.[0];

  // State
  const chat = result?.chat || start?.chat || [];

  return (
    <div className={`w-full lg:w-[60%] mx-auto overflow-hidden ${!isLoaded?.current && 'hidden'}`}>
      {/* Main view */}
      <motion.div layout transition={{ duration: 0.2 }} className={`relative h-full flex items-center ${isChatVisible && 'gap-4'}`}>
        <motion.div layout transition={{ duration: 0.2 }} className={layoutsStyles[currentLayout].container}>
          <motion.div layout transition={{ duration: 0.2 }} className={layoutsStyles[currentLayout].model}>
            <TalkingHeadComponent
              setIsFinished={setIsFinished}
              isSpeaking={isSpeaking}
              transcript={result?.transcript}
              setLoadingProgression={setLoadingProgression}
              processStatus={result?.processStatus}
              ref={modelRef}
            />
            {submissionAi?.interview?.recordInterview && (
              <div className="flex justify-between items-center gap-2 px-3 bg-white border-2 border-[#FF000080] rounded-xl absolute top-4 left-4">
                <p className="size-4 bg-[#FF000080] rounded-full animate-pulse" />
                <span className="text-lg text-[#17171F]">REC</span>
              </div>
            )}
            {/* <div className="absolute bottom-1 left-1 rounded-md bg-black/50 text-[#f9f9f9] py-1 px-3 shadow-lg">
              <p className="font-semibold text-sm md:text-base capitalize">{selectedAiModel?.value}</p>
              <p className="text-xs text-gray-300 md:text-sm">Technical Expert</p>
            </div> */}
          </motion.div>

          <motion.div layout transition={{ duration: 0.2 }} className={layoutsStyles[currentLayout].camera}>
            <Camera isSpeaking={isRecording} />
          </motion.div>
        </motion.div>

        <div
          className={`size-full bg-white border border-gray-300 rounded-2xl transition-all duration-300 absolute lg:relative top-0 right-0 ${
            isChatVisible ? `max-w-[358px] p-4 translate-x-0` : `w-0 translate-x-[150%] md:translate-x-[120%]`
          }`}
        >
          <Chat
            chat={chat}
            loading={loading}
            transcript={transcript || textAnswer}
            isSpeaking={isSpeaking}
            recordingMode={recordingMode}
            sendManualReply={sendManualReply}
            currentQuestion={result?.currentQuestion || start?.currentQuestion}
          />
        </div>
      </motion.div>
    </div>
  );
};
