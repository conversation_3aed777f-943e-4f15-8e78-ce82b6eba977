import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { PlaceholderMessage } from '../../components/placeholder-message';

interface AssessmentStatusDonut {
  chartTitle: string;
  data: any[];
  submissionData: {
    submittedCount: number;
    inProgressCount?: number;
    notStartedCount?: number;
    missedDeadlineCount: number;
  };
  interview?: {
    submittedInterviewCount: number;
    inProgressInterviewCount: number;
    notStartedInterviewCount: number;
  };
}

export const AssessmentStatusDonut = ({ chartTitle, data, submissionData, interview }: AssessmentStatusDonut) => {
  // Check if data exists and has values greater than 0
  const dataAvailable = Array.isArray(data) && data.length > 0 && data.some((item: { value: number }) => item?.value > 0);

  const CustomTooltip = ({ active, payload }: { active: boolean; payload: any[] }) => {
    if (active && payload && payload.length) {
      const { name } = payload[0].payload;

      const filteredData = [
        { label: 'Submitted', count: submissionData?.submittedCount || interview?.submittedInterviewCount },
        { label: 'In Progress', count: submissionData?.inProgressCount || interview?.inProgressInterviewCount },
        { label: 'Not Started', count: submissionData?.notStartedCount || interview?.notStartedInterviewCount },
        { label: 'Missed deadline', count: submissionData?.missedDeadlineCount || 0 },
      ];

      const dataItem = filteredData.find((item) => item.label === name);
      const count = dataItem ? dataItem.count : 0;

      return (
        <div className="bg-white dark:bg-[#181720] p-2 border border-gray-300 rounded shadow dark:border-none">
          <p className="font-semibold text-gray-700 dark:text-gray-200">{`${name}: ${count} ${
            interview ? 'interviews' : chartTitle === 'Assessment Status' ? 'assessments' : 'tests'
          }`}</p>
        </div>
      );
    }
    return null;
  };

  const renderLegend = (props: any) => {
    const { payload } = props;
    if (!payload) return null;

    // Filter out 'In Progress' status from the legend
    const filteredPayload = payload.filter((entry: any) => entry.payload.name !== 'In Progress');

    return (
      <ul className="flex flex-wrap justify-center mt-4">
        {filteredPayload.map((entry: { payload: { percent: number; name: string }; color: string; name: string }, index: number) => {
          // Calculate percentage safely to avoid NaN
          const percent = entry.payload?.percent || 0;
          const percentDisplay = isNaN(percent) ? '0%' : `${(percent * 100).toFixed(0)}%`;

          return (
            <li key={`item-${index}`} className="flex items-center mx-2 text-sm text-gray-700 dark:text-gray-300">
              <div style={{ backgroundColor: entry.color || '#8884d8' }} className="w-3 h-3 mr-2 rounded-full"></div>
              <span className="mr-2 dark:text-gray-300" style={{ color: entry.color || '#8884d8' }}>
                {entry.payload?.name || 'Unknown'}
              </span>
              <span className="text-gray-900 dark:text-gray-400 font-bold">{percentDisplay}</span>
            </li>
          );
        })}
      </ul>
    );
  };

  return (
    <div className="flex flex-col bg-white rounded-lg shadow-sm border border-gray-200 h-full dark:bg-[#3E3D4B] dark:border-none text-white">
      <h2 className="text-lg font-semibold pt-4 px-4 text-black dark:text-white">{chartTitle}</h2>
      <div className="flex-grow flex items-center justify-center">
        {dataAvailable ? (
          <ResponsiveContainer width="100%" height={250}>
            <PieChart>
              <Pie data={data} cx="50%" cy="50%" labelLine={false} outerRadius="90%" innerRadius="60%" fill="#8884d8" dataKey="value">
                {data.map((entry: { color: string }, index: number) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              {/* Tooltip temporarily commented out */}
              {/* <Tooltip content={<CustomTooltip submissionData={submissionData} interview={interview} />} /> */}
              <Legend
                content={renderLegend}
                layout="vertical"
                verticalAlign="bottom"
                align="center"
                wrapperStyle={{ paddingTop: 20 }}
                iconSize={12}
              />
            </PieChart>
          </ResponsiveContainer>
        ) : (
          <PlaceholderMessage message="No data available" />
        )}
      </div>
    </div>
  );
};
