import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { type RootState } from '../../store';

// Define the Applicant type based on the component usage
interface Applicant {
  _id: string;
  name: string;
  email: string;
  mobileNumber: string;
  gender: number;
  seniorityLevel: string;
  trackName: string;
  createdAt: string;
  track: string;
  // Add other properties as needed
}

interface ApplicantsState {
  // Dialog states
  isCreateDialogVisible: boolean;
  needSubscription: boolean;
  isAssignTestVisible: boolean;
  isScreeningVisible: boolean;
  isAssignInterviewTestVisible: boolean;

  // Data states
  applicantDetails: Applicant | null;
  selectedIds: string[];
  backupList: any[];

  // UI states
  handleGet: boolean;
  showMoreMap: { [key: string]: boolean };
  isShowDrawerFilter: boolean;
}

const initialState: ApplicantsState = {
  // Dialog states
  isCreateDialogVisible: false,
  needSubscription: false,
  isAssignTestVisible: false,
  isScreeningVisible: false,
  isAssignInterviewTestVisible: false,

  // Data states
  applicantDetails: null,
  selectedIds: [],
  backupList: [],

  // UI states
  handleGet: false,
  showMoreMap: {},
  isShowDrawerFilter: false,
};

const applicantsSlice = createSlice({
  name: 'applicants',
  initialState,
  reducers: {
    // Dialog actions
    setCreateDialogVisible: (state, action: PayloadAction<boolean>) => {
      state.isCreateDialogVisible = action.payload;
    },
    setNeedSubscription: (state, action: PayloadAction<boolean>) => {
      state.needSubscription = action.payload;
    },
    setAssignTestVisible: (state, action: PayloadAction<boolean>) => {
      state.isAssignTestVisible = action.payload;
    },
    setScreeningVisible: (state, action: PayloadAction<boolean>) => {
      state.isScreeningVisible = action.payload;
    },
    setAssignInterviewTestVisible: (state, action: PayloadAction<boolean>) => {
      state.isAssignInterviewTestVisible = action.payload;
    },

    // Data actions
    setApplicantDetails: (state, action: PayloadAction<Applicant | null>) => {
      state.applicantDetails = action.payload;
    },
    setSelectedIds: (state, action: PayloadAction<string[]>) => {
      state.selectedIds = action.payload;
    },
    setBackupList: (state, action: PayloadAction<any[]>) => {
      state.backupList = action.payload;
    },

    // UI actions
    setHandleGet: (state, action: PayloadAction<boolean>) => {
      state.handleGet = action.payload;
    },
    setShowMoreMap: (state, action: PayloadAction<{ [key: string]: boolean }>) => {
      state.showMoreMap = action.payload;
    },
    setShowDrawerFilter: (state, action: PayloadAction<boolean>) => {
      state.isShowDrawerFilter = action.payload;
    },

    // Reset action
    resetApplicantsState: (state) => {
      return initialState;
    },
  },
});

export const {
  setCreateDialogVisible,
  setNeedSubscription,
  setAssignTestVisible,
  setScreeningVisible,
  setAssignInterviewTestVisible,
  setApplicantDetails,
  setSelectedIds,
  setBackupList,
  setHandleGet,
  setShowMoreMap,
  setShowDrawerFilter,
  resetApplicantsState,
} = applicantsSlice.actions;

export const applicantsState = (state: RootState) => state.applicants;
export default applicantsSlice.reducer;
