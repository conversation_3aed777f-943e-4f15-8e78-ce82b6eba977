// React
import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

import { LookupsCategory, Api, useAppDispatch, useAppSelector, UsersListItem, useAuthUtils } from 'UI';

// Core
import { Icon, Logo } from 'src';

// Flowbite
import { Dropdown } from 'flowbite-react';

// Components
import { ProfileEditPage } from '../../app/modules/profile/pages/edit-dialog';
import { setErrorNotify } from 'UI';

export const PaymentHeader = () => {
  // State
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [trackName, setTrackName] = useState('');

  // Redux
  const userData: UsersListItem = useAppSelector((state) => state.auth.user);

  // Hooks
  const navigate = useNavigate();
  const location = useLocation();
  const currentUrl = location.pathname;
  const dispatch = useAppDispatch();
  const { logout } = useAuthUtils();

  const fetchTrackName = async () => {
    if (userData?.trackId) {
      try {
        const response = await Api.get<LookupsCategory>(`lookups/category/single/${userData?.trackId}`, {});
        console.log('lookups/category/single/', response.data);
        setTrackName(response?.data?.name);
      } catch (error: any) {
        dispatch(setErrorNotify(error?.response?.data?.message));
      }
    }
  };

  const renderRole = () => {
    if (Array.isArray(userData?.role)) {
      if (userData?.role.includes('super-admin')) {
        return (
          <div className="bg-[#F3E8FFC2] text-[#7E22CE] px-3 py-1 text-xs w-fit rounded-full">
            <p>Super Admin</p>
          </div>
        );
      } else if (userData?.role.includes('admin')) {
        return (
          <div className="bg-[#F3E8FFC2] text-[#7E22CE] px-3 py-1 text-xs w-fit rounded-full">
            <p>Admin</p>
          </div>
        );
      } else if (userData?.role.includes('content-creator')) {
        return (
          <div className="bg-[#FCE7F3C2] text-[#BE185D] px-3 py-1 text-xs w-fit rounded-full">
            <p>Content Creator</p>
          </div>
        );
      } else if (userData?.role.includes('hr')) {
        return (
          <div className="bg-[#E3F2FDC2] text-[#3B82F6] px-3 py-1 text-xs w-fit rounded-full">
            <p>HR</p>
          </div>
        );
      }
    }
  };

  useEffect(() => {
    fetchTrackName();
  }, [userData]);

  return (
    <div className={`${currentUrl.includes('pdf') ? 'hidden' : 'block'} print:hidden`}>
      <nav className="flex flex-wrap justify-between items-center bg-white border-b border-[#f4f4f4] px-4 py-2.5 dark:bg-darkBackgroundCard dark:border-[#374151] fixed left-0 right-0 top-0 z-[60]">
        <div className="flex items-center justify-between cursor-pointer" onClick={() => navigate('/')}>
          <Logo className="h-6 sm:h-7" />
        </div>

        <div className="flex items-center gap-4">
          <Dropdown
            label
            className="rounded-xl dark:bg-darkBackgroundCard"
            renderTrigger={() => (
              <div className="w-8 h-8 flex items-center justify-center rounded-full cursor-pointer bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400">
                <Icon width="20" icon="mdi:user" />
              </div>
            )}
          >
            <div className="min-w-56">
              <Dropdown.Header>
                <div className="flex gap-3 items-start">
                  <div className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400">
                    <Icon width="20" icon="mdi:user" />
                  </div>
                  <div className="flex flex-wrap gap-2 items-center pl-3 max-w-[230px]">
                    <span className="block text-base max-w-[200px] break-words">{userData?.name ?? ''}</span>
                    {renderRole()}
                    <p className="text-[#667085] dark:text-gray-300 text-xs">{trackName}</p>
                  </div>
                </div>
              </Dropdown.Header>

              <Dropdown.Item onClick={() => setCreateDialogVisibility(true)}>
                <div className="flex ml-1 gap-5">
                  <Icon width="20" icon="lucide:user" />
                  <span>Edit profile </span>
                </div>
              </Dropdown.Item>

              <Dropdown.Item onClick={logout}>
                <div className="flex ml-1 gap-5">
                  <Icon width="20" icon="mynaui:logout" />
                  <span>Logout</span>
                </div>
              </Dropdown.Item>
            </div>
          </Dropdown>
        </div>
      </nav>
      {isCreateDialogVisible && <ProfileEditPage onClose={() => setCreateDialogVisibility(false) as any} />}
    </div>
  );
};
