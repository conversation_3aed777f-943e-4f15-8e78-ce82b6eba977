// React
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';

// Core
import { CustomIcon } from 'src';

// Component
import { TemplateBasedInterview } from './template-based-interview';
import { InteractiveInterview } from './interactive-interview';

import { Radio, resetForm, useAppDispatch } from 'UI/src';

interface ChooseTypeAssignAssessmentProps {
  formData: any;
  disableButtons: {
    disableNextButton: boolean;
    setDisableNextButton: (value: boolean) => void;
  };
  lastAssessmentTypeData: { lastAssessmentType: number; setLastAssessmentType: React.Dispatch<React.SetStateAction<number>> };
  assessmentTemplateData: {
    assessmentTemplate: string | string[];
    setSelectedAssessmentTemplate: React.Dispatch<React.SetStateAction<string | string[]>>;
  };
}

export const ChooseTypeAssignAssessment = ({
  formData,
  disableButtons,
  lastAssessmentTypeData,
  assessmentTemplateData,
}: ChooseTypeAssignAssessmentProps) => {
  // Hooks
  const { type } = useParams();

  const dispatch = useAppDispatch();

  // Interview Type
  const interviewType = [
    {
      id: 1,
      label: 'Template',
      description: 'Use a pre-defined template with carefully selected questions for consistent evaluation.',
      icon: 'interactiveInterviewAssign',
      component: (
        <TemplateBasedInterview disableButtons={disableButtons} assessmentTemplateData={assessmentTemplateData as any} formData={{} as any} />
      ),
    },
    {
      id: 2,
      label: 'Interactive',
      description: 'Create a custom interview on-the-fly with AI-generated questions based on applicant’s responses.',
      icon: 'imoChatInterviewAssign',
      component: <InteractiveInterview disableButtons={disableButtons} formData={{}} />,
    },
  ];

  // Selected Interview Type
  const { lastAssessmentType, setLastAssessmentType } = lastAssessmentTypeData;

  useEffect(() => {
    dispatch(resetForm());
  }, [lastAssessmentType]);

  return (
    <div className="space-y-4">
      {type === 'interview' && (
        <div className="w-full flex gap-6">
          {interviewType.map((type, index) => (
            <div
              key={type?.id}
              onClick={() => setLastAssessmentType(index)}
              className={`w-1/2 text-center px-3 py-4 space-y-2 border rounded-xl cursor-pointer shadow-sm ${
                lastAssessmentType === index ? 'border-[#8D5BF8]' : 'border-[#DEE2E4]'
              }`}
            >
              <Radio checked={lastAssessmentType === index} />

              <div className="m-auto w-fit">
                <CustomIcon definedIcon={type?.icon} />
              </div>
              <p className={`thepassHtwo ${lastAssessmentType !== index && 'dark:text-white'}`}>{type?.label}</p>
              <p className="thepassHthree text-[#757488]">{type?.description}</p>
            </div>
          ))}
        </div>
      )}

      {interviewType[lastAssessmentType]?.component}
    </div>
  );
};
