// React Phone Number
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

// React
import { useEffect, useState } from 'react';
import { CircularProgressbar, buildStyles } from 'react-circular-progressbar';
import { useParams } from 'react-router-dom';
import { isValid, format } from 'date-fns';

// Core
import { Button, TextInput, Icon, RadioGroup, Dialog } from 'src';
import { Regex, useValidate, initializeForm, RootState, useAppDispatch, useAppSelector, Api, OrganizationType, ProfileComCard } from 'UI/src';
import { setErrorNotify, setNotifyMessage } from 'UI';
import avatarMale from 'images/avatar-male.svg';
import { useFormik } from 'formik';
import { toast } from 'react-toastify';

export const ProfileDetails = () => {
  const { id } = useParams();
  // State
  const [isSignUpDialogVisible, setSignUpDialogVisibilty] = useState(false);
  const [showPassword, setShowPassword] = useState({ password: false, confirmPassword: false });

  // Hooks
  const dispatch = useAppDispatch();

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      mobileNumber: '',
      location: '',
      joinDate: new Date(),
      id,
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  const formatDate = (customDate: Date) => {
    const date = new Date(customDate || Date.now());
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, 'dd MMMM , yyyy');
  };

  const handleGet = async () => {
    try {
      const response = await Api.get<OrganizationType>(`organizations/single/${id}`, {});
      console.log(`organizations/single/${id}`, response.data);
      dispatch(initializeForm(response.data));
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };
  const onSubmit = () => setSignUpDialogVisibilty(false);

  useEffect(() => {
    handleGet();
  }, []);

  const renderPhoneAndEmail = () => {
    const organizationData = [
      {
        startIcon: 'mynaui:envelope',
        endIcon: 'si:copy-line',
        text: form.email,
        notify: 'Email',
      },
      ...(form.mobileNumber
        ? [
            {
              endIcon: 'si:copy-line',
              text: form.mobileNumber,
              startIcon: 'ph:phone',
              displayComponent: (
                <PhoneInput
                  value={form.mobileNumber}
                  disabled
                  inputStyle={{ border: 'none', width: '200px' }}
                  buttonStyle={{ border: 'none', background: 'transparent' }}
                />
              ),
              notify: 'Phone',
            },
          ]
        : []),
    ];
    return (
      <div className="flex pt-1 px-0 gap-4 text-linaHalfGray dark:text-linaDarkHalfGray justify-center sm:justify-start font-semibold flex-wrap sm:flex-nowrap">
        {organizationData?.map((singleData) => (
          <div
            key={singleData?.startIcon}
            className="flex items-center gap-2 sm:border border-[#EAECF0] rounded-lg px-1 py-1 max-w-full overflow-hidden"
          >
            <Icon className="text-[#798296] dark:text-purple-400" width="22" icon={singleData?.startIcon} />
            <p className={`text-[#333] dark:text-gray-300 font-normal truncate ${singleData?.notify === 'Phone' && 'text-[17px] font-semibold'}`}>
              {singleData?.displayComponent || singleData?.text}
            </p>
            <Icon
              className={`dark:text-purple-400 text-[#4f5561] cursor-pointer ${singleData?.notify === 'Phone' && 'text-[#798296] '} `}
              width={singleData?.notify === 'Phone' ? '21' : '20'}
              icon={singleData?.endIcon}
              onClick={() => {
                navigator.clipboard.writeText(singleData?.text);
                dispatch(setNotifyMessage(`${singleData?.notify} copied`));
                toast.success('Email copied');
              }}
            />
          </div>
        ))}
      </div>
    );
  };

  const handleJoinDate = (date: Date) => {
    const [mon, day, year] = new Date(date).toDateString().slice(4).split(' ');
    return `${day} ${mon}, ${year}`;
  };

  return (
    <>
      <ProfileComCard applicantName={form.name} email={form.email} joinedDate={form.joinDate} phoneNumber="" gender={1} />
    </>
  );
};
