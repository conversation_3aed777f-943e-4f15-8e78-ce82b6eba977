// React
import { memo, useEffect } from 'react';
import { Visualizer } from 'react-sound-visualizer';

// Core
import { useScreenSize } from 'UI/src';

const SoundVisualizer = ({ stream }: any) => {
  // Hook
  const screen = useScreenSize();

  return (
    <Visualizer audio={stream} mode="continuous">
      {({ canvasRef, start }) => {
        useEffect(() => {
          if (stream) {
            if (start) {
              start();
            }
          }
        }, [stream, start]);

        return <canvas ref={canvasRef} width={screen.gt.sm() ? 400 : 100} height={40} />;
      }}
    </Visualizer>
  );
};

export default memo(SoundVisualizer);
