import { useNavigate } from 'react-router-dom';
import { AUTH_ROUTE_PATH } from '../configs';
import { Api, updateUserAction } from '../';
import { useAppDispatch } from '../';
import { setErrorNotify, setNotifyMessage } from '../slices/notify/notify.slice';
import Cookies from 'js-cookie';

export function useAuthUtils() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const logout = async () => {
    try {
      await Api.get('/auth/logout', {});
    } catch (err) {
      dispatch(setErrorNotify('Logout request failed'));
    }

    Cookies.remove('userData');
    Cookies.remove('userRoleData');

    dispatch(
      updateUserAction({
        access_token: '',
        email: '',
        features: {
          aiInterviews: false,
          aiQuestions: false,
          aiTests: false,
          applicantAssessmentReport: false,
          applicants: false,
          assignCustomInterviews: false,
          assignInteractiveInterviews: false,
          assignTests: false,
          avatarCloning: false,
          avatars: false,
          createCustomInterviews: false,
          createCustomQuestions: false,
          createCustomTests: false,
          exportReports: false,
          users: false,
        },
        gender: 0,
        name: '',
        organizationId: '',
        planId: '',
        roles: {} as any,
      } as any)
    );

    navigate(`${AUTH_ROUTE_PATH}/login`);
    dispatch(setNotifyMessage('Logout succeeded!'));
  };

  return { logout };
}
