// React
import { Link, useNavigate } from 'react-router-dom';
import { useState, ReactNode, MouseEvent } from 'react';
import React from 'react';

// Flowbite
import { Button as FlowbiteButton, Tooltip } from 'flowbite-react';

// prop-types validation

// Core
import { Icon } from './icon';
import { CustomIcon } from './custom-icons';
import type { CustomIconType } from '../../UI/src/types/CustomIcon.type';
import { SubscribeCard } from './subscribe-card';

//composables
import CheckFeatureManagement from '../composables/feature-management';
/*
  Reference for Button:
    <Button
      _empty_  : Button will be primary
      outline  : add a primary colored border around the button
      tertiary : add a gray colored border around the button

      size : 
        default : will have an md sized Button
        'xs'    : Button will have a smallest height (36px)
        'sm'    : Button will have a smaller height (40px)
        'md'    : Button will have a medium height (44px)
        'lg'    : But<PERSON> will have a larger height (48px)
    />
*/

type PermissionType = string | Record<string, any> | null;

export interface ButtonProps {
  children?: ReactNode;
  label?: string;
  labelSize?: string;
  icon?: React.ReactNode;
  iconRight?: React.ReactNode;
  iconWidth?: string;
  iconHeight?: string;
  customIcon?: CustomIconType | string;
  rightCustomIcon?: CustomIconType;
  loading?: boolean;
  to?: string;
  danger?: boolean;
  outline?: boolean;
  tertiary?: boolean;
  size?: 'xs' | 'sm' | 'md' | 'lg' | string;
  disabled?: boolean;
  disabledMessage?: string | ReactNode;
  disabledMessageClassName?: string;
  className?: string;
  tooltipPlacement?: string;
  permission?: PermissionType;
  onClick?: (event?: MouseEvent<HTMLElement>) => void;
  [key: string]: any;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  label = '',
  labelSize = 'text-sm',
  icon = '',
  iconRight,
  loading = false,
  to = '',
  iconWidth,
  iconHeight,
  className = '',
  outline = false,
  tertiary = false,
  size = 'md',
  customIcon,
  rightCustomIcon,
  tooltipPlacement,
  permission = null,
  danger = false,
  disabled = false,
  disabledMessage,
  disabledMessageClassName,
  onClick,
  ...props
}) => {
  const [needSubscription, setNeedSubscription] = useState<boolean>(false);
  // Call the hook at component level, not in event handler
  const { checkFeature } = CheckFeatureManagement();
  const navigate = useNavigate();

  const customTheme = {
    outline: {
      off: '',
      on: 'flex w-full justify-center bg-white text-primaryPurple transition-all duration-75 ease-in group-enabled:group-hover:bg-opacity-0 group-enabled:group-hover:text-inherit dark:bg-gray-900 dark:text-primaryPurple',
    },
  };

  const handleHeightSize = () => {
    if (size === 'xs') return { width: size, height: 'h-9' }; // 36px
    else if (size === 'sm') return { width: size, height: 'h-9 sm:h-10' }; // 40px
    else if (size === 'md') return { width: size, height: 'h-9 sm:h-10 md:h-11' }; // 44px
    // else if (size === 'lg') return { width: size, height: 'h-9 sm:h-10 md:h-11 lg:h-12' }; // 48px
    else return { width: 'md', height: 'h-9 sm:h-10 md:h-11 lg:h-12' };
  };

  const element = (
    <FlowbiteButton
      theme={customTheme}
      isProcessing={loading}
      className={`${
        outline
          ? 'border-[2px] border-[#8D5BF8] dark:border-opacity-60 !bg-white dark:!bg-transparent text-[#7E3AF2] dark:text-white hover:!bg-[#F3EDFF] dark:hover:!bg-[#a991dc35]'
          : tertiary
          ? `border-[2px] border-[#E4E5E9] dark:border-gray-600 !bg-white dark:!bg-[#1F2837] text-[#2F2F2F] dark:text-white hover:!bg-[#F9FAFB] dark:hover:!bg-[#374151]`
          : '!bg-[#8D5BF8] dark:opacity-80 hover:!bg-[#6F3ED8]'
      } font-semibold items-center focus:ring-0 ${handleHeightSize().height} ${className}`}
      {...props}
      to={to}
      {...(to ? { as: Link } : { disabled })}
      size={handleHeightSize().width}
      onClick={(e: MouseEvent<HTMLElement>) => {
        if (permission) {
          const hasFeature = checkFeature(permission as any);
          if (!hasFeature) {
            // setNeedSubscription(true);
            return;
          }
        }
        onClick?.(e);
      }}
      disabled={permission && !checkFeature(permission as any)}
    >
      {icon && (
        <div className={label ? 'mr-2 self-center' : ''}>
          <Icon width={iconWidth ? iconWidth : '18'} icon={icon} className="flex items-center justify-center" />
        </div>
      )}
      {customIcon && typeof customIcon !== 'string' ? (
        <div className={label ? 'mr-2 self-center' : ''}>
          <CustomIcon
            definedIcon={customIcon.definedIcon}
            width={customIcon.width || iconWidth || '18'}
            height={customIcon.height || iconHeight || '18'}
            className={customIcon.className || 'flex items-center justify-center'}
            stroke={customIcon.stroke!}
            onClick={customIcon.onClick}
          />
        </div>
      ) : null}
      {label && labelSize && <p className={`flex self-center ${labelSize} items-center justify-center`}>{label}</p>}
      {children && children}
      {iconRight && (
        <div className={label ? 'ml-2' : ''}>
          <Icon width={iconWidth ? iconWidth : '22'} icon={iconRight} className="flex items-center justify-center" />
        </div>
      )}
      {rightCustomIcon && (
        <div className={!!label ? 'ml-2 self-center' : ''}>
          <CustomIcon
            definedIcon={rightCustomIcon.definedIcon}
            width={rightCustomIcon.width || iconWidth || '18'}
            height={rightCustomIcon.height || iconHeight || '18'}
            className={rightCustomIcon.className || 'flex items-center justify-center'}
            stroke={rightCustomIcon.stroke!}
            onClick={rightCustomIcon.onClick}
          />
        </div>
      )}
    </FlowbiteButton>
  );
  return (
    <>
      {disabled && disabledMessage ? (
        <Tooltip placement={(tooltipPlacement as any) || 'top'} className={disabledMessageClassName} content={disabledMessage}>
          {element}
        </Tooltip>
      ) : (
        element
      )}

      {needSubscription && (
        <SubscribeCard key={permission as string} onClose={() => setNeedSubscription(false)} onSubscribe={() => setNeedSubscription(true)} />
      )}
    </>
  );
};
