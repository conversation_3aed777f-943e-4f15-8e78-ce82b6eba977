export interface TemplateType {
  _id: string;
  title: string;
  duration: 0;
  createdAt: string;
  updatedAt: string;
  techpassQuiz: true;
  difficulty: 0;
  numOfQuestions: 0;
  seniorityLevel: 0;
  description: string;
  categoryName: string[];
  category: string[];
  subCategory: string[];
  subCategoryName: string[];
  authorId: string;
  authorName: string;
  topicName: string[];
  topic: string[];
}

export type TemplateListItem = TemplateType & { questionIds: string[] };

export interface TemplateList {
  items: TemplateListItem[];
  count: number;
}

export type TemplateCategories = {
  subCategories: string[];
  categoryId: string;
  categoryName: string;
}[];

export type TemplateSubCategories = {
  subCategories: {
    categoryId: 'string';
    _id: 'string';
    name: 'string';
    createdAt: 'string';
    updatedAt: 'string';
  }[];
}[];
