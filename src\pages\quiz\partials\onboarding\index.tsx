import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { TextInput, Button, Checkbox, Icon, CustomIcon, PhoneNumberInput, EnumText } from 'src';
import { AssessmentCompCard, RootState, setErrorNotify, setNotifyMessage, useAppSelector } from 'UI';
import { fetchSubmission, initializeForm, setFieldValue, setLoading } from 'UI/src';
import { Form } from 'UI/src/components/form';
import { Api, Regex, useValidate } from 'UI/src';
import { FaMousePointer, FaSync, FaLock, FaEye, FaWindowRestore } from 'react-icons/fa'; // Icons related to test content
import { useAppDispatch } from 'UI/src';
import { Clock5, HelpCircle, ChartColumnIncreasing, User, Link } from 'lucide-react';

import { Header } from '../../../components/header';
import { AnswerValue } from '../stepper/answer';

// types
type QuizCategory = {
  categoryId?: string;
  categoryName?: string;
};

type Quiz = {
  _id?: string;
  title?: string;
  duration?: number;
  difficulty?: number;
  phoneScreening?: boolean;
  // FIXME: to avoid returning null
  // questionIds: string[] | null;
  questionIds: string[];
  category?: QuizCategory;
};

export type Applicant = {
  _id?: string;
  name?: string;
  email?: string;
  mobileNumber?: string;
  seniorityLevel?: number;
};

export type Submission = {
  _id?: string;
  applicant?: Applicant;
  applicantId?: string;
  randomId?: string;
  quiz: Quiz;
  startedAt?: string;
  submittedAt?: string;
  locked?: boolean;
  expired?: boolean;
  exceededTime?: number;
  stage?: {
    _id: string;
    index: number;
    answer: AnswerValue;
    question: {
      type: number;
    };
  };
};

export type SubmissionContextType = {
  applicantId?: string;
  randomId?: string;
  submission: Submission;

  loading: boolean;
  setLoading: (loading: boolean) => void;
  handleGetSubmission: (id?: string) => Promise<void>;
};

export const SubmissionOnboarding = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { id } = useParams();
  const { isRequired, validateRegex, minLength, maxLength, countryCodeNumberValid } = useValidate();

  const { submission, loading } = useAppSelector((state: RootState) => state.submission);
  const applicantId = submission?.applicant._id;
  const randomId = submission?.randomId;

  const form = useAppSelector((state) => state.form.data);
  const [isAgreedOnTerms, setIsAgreedOnTerms] = useState(false);

  // Methods
  const handleStartSubmission = async () => {
    dispatch(setLoading(true));
    try {
      let newApplicantId = applicantId;
      let submissionId = id;
      if (newApplicantId === undefined) {
        const response = await Api.post('applicants/single/custom', {
          ...form,
          randomId,
        });
        newApplicantId = response.data._id;
        // Create new submission with this applicant
        const submissionResponse = await Api.post('submissions/single', {
          applicantId: newApplicantId,
          quizId: submission.quiz._id,
          randomId,
          type: 'public',
        });
        submissionId = submissionResponse.data.submissionId;
      } else {
        submissionId = submission._id;
      }
      const payload = {
        submissionId: submissionId,
        data: form,
      };
      await Api.post('submissions/progress/start', payload);

      submissionId && (await dispatch(fetchSubmission(submissionId)).unwrap());

      navigate(`/test/${submissionId}`, { replace: true });
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handlePopulateData = () => {
    dispatch(
      initializeForm({
        name: submission.applicant?.name,
        email: submission.applicant?.email,
        mobileNumber: submission.applicant?.mobileNumber,
        seniorityLevel: submission.applicant?.seniorityLevel || submission.quiz?.difficulty,
        track: submission.quiz?.category?.categoryId,
      })
    );
  };

  const instructionsData = [
    {
      number: 1,
      icon: <FaMousePointer className="text-[#743AF5]" />,
      title: 'Mouse Usage',
      content: "Only use the mouse or laptop touchpad, don't press on the keyboard.",
    },
    {
      number: 2,
      icon: <FaSync className="text-[#743AF5]" />,
      title: 'Page Reload',
      content: `Don't refresh or reload the page.`,
    },
    {
      number: 3,
      icon: <FaLock className="text-[#743AF5]" />,
      title: 'IP Address',
      content: "Don't change your IP address during the test.",
    },
    {
      number: 4,
      icon: <FaWindowRestore className="text-[#743AF5]" />,
      title: 'Window Focus',
      content: 'Stay focused on this window; avoid switching to other tabs or applications.',
    },
  ];

  // On Mount
  useEffect(() => {
    handlePopulateData();
  }, []);

  console.log(submission);

  // Render
  return (
    <div className="flex min-h-full flex-1 justify-center bg-gray-50 px-6 py-6 dark:bg-gray-900">
      <Header showThemeIcon={true} setShowBookMark={() => {}} startQuiz={false} duration={submission?.quiz?.duration || 0} />
      <div className="lg:w-[1100px] my-8 p-8 space-y-4 rounded-xl shadow-[0px_0px_14px_0px_rgba(195,195,195,0.22)] dark:shadow-[0px_0px_14px_0px_rgba(195,195,195,0.08)] bg-white dark:bg-gray-800">
        <div className="flex flex-col gap-4 w-full">
          <div>
            {/* If there is categoryName then we are in "Technical test", Otherwise we are in "Screening test" */}
            <p className="thepassHone text-[#1B1F3B]">
              {/* Welcome to {submission?.quiz?.category?.categoryName ? `${submission?.quiz?.category?.categoryName} Test` : submission?.quiz?.title}. */}
              Welcome to Your Assessment
            </p>
            <p className="thepassSubHone text-[#4E5E82]">Review the test details and click “Start” when you’re ready</p>
          </div>

          {/* Test details */}
          <AssessmentCompCard
            // createdByName={submission?.quiz?.authorName || ''}
            // createdByDate={submission?.quiz?.createdAt || ''}
            // updatedDate={submission?.quiz?.updatedAt || ''}
            name={submission?.quiz?.title || ''}
            seniority={submission?.quiz?.seniorityLevel || ''}
            difficulty={submission?.quiz?.difficulty || ''}
            questionsNumber={submission?.quiz?.questionIds?.length || 0}
            duration={submission?.quiz?.duration || 0}
            categoryName={(Array.isArray(submission?.quiz?.category) && submission?.quiz?.category[0]?.categoryName) || ''}
            subCategories={submission?.quiz?.subCategory || []}
            // onReviewQuestionsClick={() => setIsShowReviewDrawer(true)}
          />
        </div>

        <Form onSubmit={handleStartSubmission} className="space-y-4">
          <div className="border border-gray-200 rounded-xl p-4">
            {/* Form Header */}
            <div className="flex flex-col mb-4 gap-3">
              <h2 className="text-xl font-medium text-gray-700 dark:text-gray-200">Your Details</h2>
            </div>

            {/* Form Inputs */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Name Input */}
              <TextInput
                name="name"
                label="Name"
                placeholder="Enter"
                value={form.name}
                disabled={!!submission?.applicant?.name || loading}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'name', value }))}
                className="mb-4"
                // validators={[isRequired(), validateRegex(Regex.name), minLength(2), maxLength(50)]}
              />

              {/* Email Input */}
              <TextInput
                name="email"
                label="Email"
                placeholder="Enter"
                type="email"
                value={form.email}
                disabled={!!submission?.applicant?.email || loading}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'email', value }))}
                className="mb-4"
                validators={[isRequired(), validateRegex(Regex.email)]}
              />

              {/* Mobile Number Input */}
              <PhoneNumberInput
                name="mobileNumber"
                label="Mobile Number"
                value={form.mobileNumber}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'mobileNumber', value }))}
                requiredLabel
                validators={[countryCodeNumberValid()]}
                disabled={!!submission?.applicant?.mobileNumber || loading}
              />
            </div>
          </div>

          {/* Instructions */}
          <div className=" w-full overflow-hidden border border-gray-200 rounded-xl p-4">
            <p className="text-[22px] font-medium mb-5 dark:text-white">Instructions</p>
            <div className="flex flex-col gap-5">
              {instructionsData.map((instruction) => (
                <div key={instruction.number} className="flex gap-2 items-center capitalize">
                  <div className="bg-[#F1E9FE] rounded-full size-8 md:size-9 flex justify-center items-center mr-2">{instruction.icon}</div>
                  <p className="whitespace-nowrap thepassBtwo text-[#4E5E82]">{instruction.title}:</p>
                  <p className="thepassBtwo text-[#1B1F3B]">{instruction.content}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Agree on terms? */}
          <div className="px-2">
            <Checkbox
              name="terms"
              label="I agree and understand that violating these instructions may result in test termination or cancellation."
              value={isAgreedOnTerms}
              onChange={setIsAgreedOnTerms}
              fullWidth={false}
              preventSendingMail={false}
              isCustomLabel={false}
              labelClass="capitalize"
            />
          </div>

          <div className="flex justify-end">
            <Button
              type="submit"
              label={submission?.quiz?.phoneScreening ? 'Start Screening ' : 'Start Test'}
              disabled={loading || !isAgreedOnTerms}
              loading={loading}
            />
          </div>
        </Form>
      </div>
    </div>
  );
};
