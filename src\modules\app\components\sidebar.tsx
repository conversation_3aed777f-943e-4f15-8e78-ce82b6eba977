// React
import { useState } from 'react';
import { useLocation, Link } from 'react-router-dom';
import * as LucideIcons from 'lucide-react';

// Core
import { Icon } from 'src';
import { RootState, useAppSelector, useScreenSize, useAuthUtils, UserData } from 'UI/src';

// Components
import { ProfileEditPage } from '../modules/profile/pages/edit-dialog';
import { menuItems, MenuItem } from 'UI';

interface AppSidebarProps {
  isDrawerVisible: boolean;
  setIsDrawerVisible: (value: boolean) => void;
}

export const AppSidebar = ({ isDrawerVisible, setIsDrawerVisible }: AppSidebarProps) => {
  // Hooks
  const location = useLocation();
  const screen = useScreenSize();

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const currentUrl = location.pathname;
  const isReport = currentUrl.includes('pdf');

  // Permissions
  const isPermittedSuperAdmin = Array.isArray(userData?.role) && userData?.role?.includes('super-admin');
  const isPermittedAdmin = Array.isArray(userData?.role) && userData?.role?.includes('admin');
  const isPermittedContentCreator = Array.isArray(userData?.role) && userData?.role?.includes('content-creator');
  const isPermittedHr = Array.isArray(userData?.role) && userData?.role?.includes('hr');

  console.log(userData, Array.isArray(userData?.role), isPermittedAdmin, isPermittedContentCreator, isPermittedHr, isPermittedSuperAdmin);
  interface expandedItemsType {
    Administration: boolean;
    Assessments: boolean;
    Reports: boolean;
    'Question Bank': boolean;
    [key: string]: boolean;
  }
  // State
  const [expandedItems, setExpandedItems] = useState<expandedItemsType>({
    Administration: true,
    Assessments: true,
    Reports: true,
    'Question Bank': true,
  });
  const [trackName, setTrackName] = useState('');
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState<boolean>(false);
  const [search, setSearch] = useState('');

  // Computed
  const getActiveClasses = (itemPath: string) => {
    if (location.pathname.includes(itemPath)) {
      return 'bg-gray-100 dark:bg-gray-700 dark:text-white';
    }
    return '';
  };

  const toggleExpand = (itemLabel: string) => {
    const key = itemLabel as keyof expandedItemsType;
    setExpandedItems({
      ...expandedItems,
      [key]: !expandedItems[key],
    });
  };
  console.log(
    menuItems({
      isPermittedSuperAdmin,
      isPermittedAdmin,
      isPermittedContentCreator,
      isPermittedHr,
    })
  );

  return (
    <>
      <aside
        id="drawer-navigation"
        className={`w-[220px] fixed top-0 left-0 z-[45] h-screen transition-transform bg-white border-r border-gray-200 dark:bg-darkBackgroundCard dark:border-[#374151] ${
          isReport && 'hidden'
        } ${isDrawerVisible ? '-translate-x-0' : '-translate-x-full'}`}
        aria-label="Sidenav"
      >
        <div className="flex flex-col justify-between gap-3 bg-white overflow-y-auto pt-[70px] pb-5 px-2 h-full  dark:bg-darkBackgroundCard">
          <ul className="">
            {/* <div className="flex justify-between items-center grow py-2 space-y-0 rounded-lg relative">
              <Icon icon="carbon:search" width="20" className="size-5 text-gray-500 dark:text-gray-400 absolute left-3 pointer-events-none" />
              <input
                type="text"
                placeholder="Search..."
                className="w-full p-2 pl-10 dark:bg-gray-700 bg-gray-white text-[13.5px] text-gray-800 border border-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white truncate rounded-lg focus:ring-0 focus:border-gray-300 shadow-sm"
                value={search}
                onInput={(e) => setSearch(e.target.value)}
              />
            </div> */}

            {menuItems({
              isPermittedSuperAdmin,
              isPermittedAdmin,
              isPermittedContentCreator,
              isPermittedHr,
            })
              .filter((menu: MenuItem) => menu?.label.toLowerCase().includes(search.toLowerCase()))
              .map((item, index) => (
                <li key={index} className={item.type === 'header' ? 'text-xs font-semibold text-gray-500 py-2' : ''}>
                  {item.type === 'header' ? (
                    item.label
                  ) : item.children ? (
                    <div>
                      <button
                        onClick={() => {
                          toggleExpand(item.label);
                          // clearSidebarFilter();
                        }}
                        className="flex justify-between items-center w-full p-2 text-base font-medium text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group"
                      >
                        {/* <Icon
                          icon={item.icon}
                          className="text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                          width="20"
                        /> */}
                        {isDrawerVisible && <span className="text-[#83899F] text-base flex-1 text-left whitespace-nowrap">{item.label}</span>}
                        <Icon icon={expandedItems[item.label] ? 'mdi:chevron-up' : 'mdi:chevron-down'} className="text-gray-500" width="18" />
                      </button>

                      {expandedItems[item.label] && (
                        <ul className="py-1 space-y-1  pl-1 ml-3 border-l">
                          {item.children?.map((child, childIndex) => {
                            const Lucide: any = child?.lucide && LucideIcons[child.lucide as keyof typeof LucideIcons];
                            return (
                              <li key={childIndex}>
                                <Link
                                  to={child.path}
                                  className={`flex items-center p-1 text-sm font-normal  text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 ${getActiveClasses(
                                    child.path
                                  )}`}
                                  onClick={() => {
                                    // clearSidebarFilter();
                                    screen.lt.xl() && setIsDrawerVisible(false);
                                  }}
                                >
                                  {/* TODO: Markos Bahgat */}
                                  {child?.lucide && <Lucide width={20} />} {/* TODO: Fix this */}
                                  {child.icon && (
                                    <Icon
                                      icon={child.icon}
                                      className="text-gray-500 transition duration-75  dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                      width="18"
                                    />
                                  )}
                                  {isDrawerVisible && (
                                    <span
                                      className={`ml-3 text-sm font-medium text-[#0A1B39] dark:text-white ${
                                        location.pathname.includes(child.path) && 'font-semibold'
                                      }`}
                                    >
                                      {child.label}
                                    </span>
                                  )}
                                </Link>
                              </li>
                            );
                          })}
                        </ul>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.path!}
                      className={`flex items-center p-2 text-base font-normal text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 ${getActiveClasses(
                        item.path!
                      )}`}
                      // onClick={() => {
                      //   clearSidebarFilter();
                      // }}
                    >
                      <Icon
                        icon={item.icon}
                        className="text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                        width="18"
                      />
                      {isDrawerVisible && <span className="ml-3">{item.label}</span>}
                    </Link>
                  )}
                </li>
              ))}
          </ul>
        </div>

        {/* {(sidebarSearch || sidebarFilter) && <SidebarFilterPage />} */}
      </aside>

      {/* TODO: Markos */}
      {isCreateDialogVisible && <ProfileEditPage onClose={() => setCreateDialogVisibility(false)} />}
    </>
  );
};
