import { createSlice } from '@reduxjs/toolkit';
import { handleGet, type RootState } from '../../..';
import type { QuestionListItem } from '../../../../src/components/drawer/drawer.type';

interface TestState {
  questionList: QuestionListItem[];
  filterBookmark: boolean;
  filterUnanswered: boolean;
  count: number;
  buttonsAvailability: {
    previous: boolean;
    next: boolean;
  };
  pagination: {
    page: number;
    size: number;
  };
}

const initialState: TestState = {
  questionList: [],
  buttonsAvailability: { previous: false, next: false },
  filterBookmark: false,
  filterUnanswered: false,
  count: 0,
  pagination: { page: 1, size: 25 },
};

const testSlice = createSlice({
  name: 'test',
  initialState,
  reducers: {
    setFilterBookmark: (state, { payload }) => {
      state.filterBookmark = payload;
    },
    setFilterUnanswered: (state, { payload }) => {
      state.filterUnanswered = payload;
    },
    setCount: (state, { payload }) => {
      state.count = payload;
    },
    setPagination: (state, { payload }) => {
      state.pagination = payload;
    },
    setQuestionList: (state, { payload }) => {
      state.questionList = payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(handleGet.fulfilled, (state, { payload }) => {
      state.count = payload.count;
      state.questionList = payload.items as any;
    });
  },
});

export const testState = (state: RootState) => state.test;
export const { setFilterBookmark, setFilterUnanswered, setCount, setPagination, setQuestionList } = testSlice.actions;
export default testSlice.reducer;
