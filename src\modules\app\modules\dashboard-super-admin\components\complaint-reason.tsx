// Core
import { PieCharts } from 'src';

export const ComplaintReason = () => {
  const chartsData = [
    { name: 'Technical Issues', value: 45 },
    { name: 'Poor Quality', value: 45 },
    { name: 'Complexity', value: 55 },
  ];

  const COLORS = ['#D0D6FA', '#A5B4FC', '#3D5BF1'];

  const renderCustomLegend = ({ payload }: { payload: [] }) =>
    payload?.map((entry: { value: string | number; color: string; payload: { value: number } }, index: number) => (
      <div key={index} className="flex justify-center items-center gap-2 dark:text-white my-2">
        <span className="size-3 mr-2 rounded-sm" style={{ backgroundColor: entry.color }}></span>
        <span className="w-36">{entry.value}</span>
        <span className="font-medium">{entry.payload.value}%</span>
      </div>
    ));

  return <PieCharts data={chartsData} COLORS={COLORS} customLegend={renderCustomLegend} />;
};
