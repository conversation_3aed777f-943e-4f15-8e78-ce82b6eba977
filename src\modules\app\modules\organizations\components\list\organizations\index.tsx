// React
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { RootState, useAppSelector, UserData, StaticData, useAppDispatch, useFetchList, useScreenSize } from 'UI/src';

// Core
import {
  Icon,
  CustomIcon,
  Button,
  TestDifficulty,
  TestSeniorityLevel,
  AvarageScore,
  // Table,
  FormatDateFieldColumn,
  NameFieldColumn,
  EmailFieldColumn,
} from 'src';

// Flowbite
import { Tooltip } from 'flowbite-react';

// Components
import { InlineFilter } from '../../inline-filter';
import { Table } from 'UI/src';

export const OrganizationList = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Permissions
  const isPermitted = Array.isArray(userData?.role) && userData?.role.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [activeInlineFilter, setActiveInlineFilter] = useState(0);

  // Hooks
  const navigate = useNavigate();
  const screen = useScreenSize();
  const dispatch = useAppDispatch();

  const initialFilters = {
    type: {
      label: 'Plan',
      enum: 'PlanType',
    },
  };

  const { ready, loading, count, list, refresh, filters, setFilters, search, pagination, handleDates } = useFetchList(`/organizations/list`, {
    search: '',
    pagination: {
      pagesCount: 10,
      currentPage: 1,
      limit: 20,
      total: 10,
    },
    filters: initialFilters,
  });
  // TODO: Markos (i need this as it is)
  // @ts-ignore
  const filterFeedData = Object.keys(initialFilters).map((key) => (key === 'difficulty' ? initialFilters.difficulty.enum : key));

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  return (
    <div className="space-y-4">
      {/* we will use it later */}
      {/* <InlineFilter
          // data={data}
          selectedInlineFilter={{
            activeInlineFilter: activeInlineFilter,
            setActiveInlineFilter: setActiveInlineFilter,
          }}
        /> */}

      {/* <div className="flex flex-row items-center justify-end gap-2 text-gray-700 rounded-lg calendar dark:text-gray-300 h-fit">
          <Datepicker
            className="inline-block w-full sm:w-44"
            // onSelectedDateChanged={() => {}}
            showTodayButton={false}
            showClearButton={false}
            // value=""
          />
        </div> */}

      <Table
        tableHeaders={['Organization Name', 'Organization Email', 'Plan', 'Actions']}
        tableRowData={{
          name: {
            meta: {
              variant: 'text',
              props: (row) => ({
                classname: `cursor-pointer text-gray-800 dark:text-grayTextOnDarkMood sm:text-base lg:text-sm sm:font-semibold lg:font-medium capitalize
            native-break-all-words sm:line-clamp-2 lg:line-clamp-none lg:truncate`,
                onClick: () => navigate(`/app/organizations/profile/${row?._id}`),
              }),
            },
          },
          email: {
            meta: {
              variant: 'copy',
              props: (row) => {
                return {
                  text: row.email,
                  display: row.email,
                };
              },
            },
          },
          planName: {
            meta: {
              variant: 'text',
              props: (row) => ({ value: row?.endDate ? row?.planName : 'Not Subscribed Yet' }),
            },
          },
          _id: {
            meta: {
              variant: 'actions',
              props: (row) => {
                return {
                  row,
                  buttons: () => [
                    {
                      label: 'View',
                      customIcon: 'eye',
                      iconWidth: '22',
                      iconHeight: '22',
                      color: 'text-black dark:text-white',
                      path: `/app/organizations/profile/${row?._id}/overview`,
                    },
                  ],
                };
              },
            },
          },
        }}
        sectionBadgeTitle={count}
        sectionTitle="Organizations List"
        data={list}
        loading={loading}
        searchPlaceholder="Search by org name or email..."
        search={search}
        filters={filters}
        pagination={{
          pages: pagination.pagesCount,
          currentPage: pagination.currentPage,
          onPageChange: (page) => pagination.update({ currentPage: page }),
        }}
        setFilters={setFilters}
        placeholder={{
          title: 'No organizations yet',
          subTitle: 'Waiting for the first customers to join.',
          image: '/UI/src/assets/placeholder/NoOrganization.svg',
        }}
      />
    </div>
  );
};
