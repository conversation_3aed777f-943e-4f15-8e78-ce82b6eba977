import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../../store';
import { fetchSubmission } from '../../middlewares/Submission.middleware';

// Types for creation dialog
type Applicant = {
  _id: string;
  email: string;
};

type QuizSearchResult = {
  _id: string;
  title: string;
};

type SubcategoryDetail = {
  subCategoryName: string;
  count: number;
  topics: string[];
};

type SingleQuiz = {
  difficulty: number;
  title: string;
  duration: number;
  numOfQuestions: number;
  subcategoryDetails: SubcategoryDetail[];
};

interface submissionState {
  submission: {
    progressPercentage: any;
    exceededTime: number;
    applicantId: string;
    applicant: {
      _id: string;
      name: string;
      email: string;
      mobileNumber: string;
      seniorityLevel: any;
    };
    _id: string;
    stage: {
      _id: string;
      index: number;
      question: {
        type: number;
        options: {
          id: string;
          label: string;
        }[];
      };
      answer: any;
    };
    expired: any;
    submittedAt: any;
    quiz: {
      _id: string;
      title: string;
      questionIds: [];
      phoneScreening: boolean;
      duration: number;
      subCategory: {
        subCategoryName: string;
      }[];
      category: {
        categoryId: string;
        categoryName: string;
      };
      difficulty: any;
    };
    locked: any;
    startedAt: any;
    randomId: any;
  };
  loading: boolean;
  isMobile: boolean;

  // Creation dialog states
  creationDialog: {
    loading: boolean;
    quizUrl: string;
    submissionId: string;
    applicants: Applicant[];
    quizzes: QuizSearchResult[];
    singleQuiz: SingleQuiz | null;
    searchResult: string | null;
    emailRegex: boolean;
    expandedSections: Record<number, boolean>;
    expandedAll: boolean;
    startDate: Date;
    dueDate: Date;
    extraTime: number;
    isTimeSettingsVisible: boolean;
  };
}

const initialState: submissionState = {
  submission: {
    _id: '',
    applicant: {} as any,
    applicantId: '',
    exceededTime: 0,
    stage: {} as any,
    expired: false,
    submittedAt: '',
    quiz: {} as any,
    progressPercentage: 0,
    locked: false,
    startedAt: '',
    randomId: '',
  },
  loading: false,
  isMobile: false,

  // Creation dialog initial state
  creationDialog: {
    loading: false,
    quizUrl: '',
    submissionId: '',
    applicants: [],
    quizzes: [],
    singleQuiz: null,
    searchResult: null,
    emailRegex: false,
    expandedSections: {},
    expandedAll: false,
    startDate: new Date(),
    dueDate: (() => {
      const result = new Date();
      result.setDate(result.getDate() + 1);
      return result;
    })(),
    extraTime: 0,
    isTimeSettingsVisible: false,
  },
};

const submissionSlice = createSlice({
  name: 'submission',
  initialState,
  reducers: {
    setLoading(state, action: PayloadAction<boolean>) {
      state.loading = action.payload;
    },
    setIsMobile(state, action: PayloadAction<boolean>) {
      state.isMobile = action.payload;
    },
    setSubmission: (state, action: PayloadAction<any>) => {
      state.submission = action.payload;
    },

    // Creation dialog actions
    setCreationDialogLoading: (state, action: PayloadAction<boolean>) => {
      state.creationDialog.loading = action.payload;
    },
    setQuizUrl: (state, action: PayloadAction<string>) => {
      state.creationDialog.quizUrl = action.payload;
    },
    setSubmissionId: (state, action: PayloadAction<string>) => {
      state.creationDialog.submissionId = action.payload;
    },
    setApplicants: (state, action: PayloadAction<Applicant[]>) => {
      state.creationDialog.applicants = action.payload;
    },
    setQuizzes: (state, action: PayloadAction<QuizSearchResult[]>) => {
      state.creationDialog.quizzes = action.payload;
    },
    setSingleQuiz: (state, action: PayloadAction<SingleQuiz | null>) => {
      state.creationDialog.singleQuiz = action.payload;
    },
    setSearchResult: (state, action: PayloadAction<string | null>) => {
      state.creationDialog.searchResult = action.payload;
    },
    setEmailRegex: (state, action: PayloadAction<boolean>) => {
      state.creationDialog.emailRegex = action.payload;
    },
    setExpandedSections: (state, action: PayloadAction<Record<number, boolean>>) => {
      state.creationDialog.expandedSections = action.payload;
    },
    setExpandedAll: (state, action: PayloadAction<boolean>) => {
      state.creationDialog.expandedAll = action.payload;
    },
    setStartDate: (state, action: PayloadAction<Date>) => {
      state.creationDialog.startDate = action.payload;
    },
    setDueDate: (state, action: PayloadAction<Date>) => {
      state.creationDialog.dueDate = action.payload;
    },
    setExtraTime: (state, action: PayloadAction<number>) => {
      state.creationDialog.extraTime = action.payload;
    },
    setTimeSettingsVisible: (state, action: PayloadAction<boolean>) => {
      state.creationDialog.isTimeSettingsVisible = action.payload;
    },

    // Reset creation dialog
    resetCreationDialog: (state) => {
      state.creationDialog = initialState.creationDialog;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSubmission.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchSubmission.fulfilled, (state, action: PayloadAction<any>) => {
        state.submission = action.payload;
        state.loading = false;
      })
      .addCase(fetchSubmission.rejected, (state) => {
        state.loading = false;
      });
  },
});

export const submissionState = (state: RootState) => state.submission;
export const {
  setLoading,
  setSubmission,
  setIsMobile,
  // Creation dialog actions
  setCreationDialogLoading,
  setQuizUrl,
  setSubmissionId,
  setApplicants,
  setQuizzes,
  setSingleQuiz,
  setSearchResult,
  setEmailRegex,
  setExpandedSections,
  setExpandedAll,
  setStartDate,
  setDueDate,
  setExtraTime,
  setTimeSettingsVisible,
  resetCreationDialog,
} = submissionSlice.actions;
export default submissionSlice.reducer;
