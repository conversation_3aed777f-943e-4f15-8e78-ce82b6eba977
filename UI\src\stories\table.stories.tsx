import React from 'react';
import Table from '../components/table/table';

export default {
  title: 'Components/Table',
  component: Table,
};

const tableHeaders = ['Name', 'Email', 'Phone', 'Age', 'Balance', 'Created At', 'Status'];
const tableRowData = ['name', 'email', 'phone_number', 'age', 'balance', 'createdAt', 'status'];
const data = [
  {
    _id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone_number: '1234567890',
    age: 30,
    balance: '$100.00',
    createdAt: new Date().toISOString(),
    status: 'Active',
  },
  {
    _id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone_number: '0987654321',
    age: 25,
    balance: '$200.00',
    createdAt: new Date().toISOString(),
    status: 'Inactive',
  },
];

export const Default = () => <Table tableHeaders={tableHeaders} data={data} tableRowData={tableRowData} />;
