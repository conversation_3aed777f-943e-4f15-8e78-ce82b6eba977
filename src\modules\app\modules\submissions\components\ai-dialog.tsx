import React, { useState, useRef, useEffect, FC, FormEvent } from 'react';
import doneMark from 'images/Vector.svg';

import { Radio, Textarea, Checkbox, MultiSelect, Icon, Select, TextInput } from 'src';
import { Dialog, Button } from 'UI';
import { DatePicker, DateRangePicker } from 'rsuite';
import { Api, Regex, useValidate, Form, initializeForm, RootState, setFieldValue, useAppDispatch, useAppSelector, InterviewType } from 'UI/src';
import { setErrorNotify, setNotifyMessage } from 'UI';
import { useFormik } from 'formik';

type ApplicantDetails = {
  _id: string;
  email: string;
  track: string | null;
};

type AiInterviewDialogProps = {
  onClose: () => void;
  onCreate: () => void;
  applicantDetails?: ApplicantDetails;
  refreshMainTable?: () => void;
};

type InterviewForm = {
  technology: string;
  numberOfQuestions: string;
  yearsOfExperience: string;
  estimationTime: string;
  type: number;
  skips: string;
  applicantId: string;
  notes: string;
  dueDate: Date | string;
  startDate: Date | string;
  category: string | null;
  subCategory: string[] | null;
};

export const AiIntreviewDialog: FC<AiInterviewDialogProps> = ({ onClose, onCreate, applicantDetails, refreshMainTable = () => {} }) => {
  // Hooks
  const dispatch = useAppDispatch();
  const [applicants, setApplicants] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [emailRegex, setEmailRegex] = useState<boolean>(false);
  const [addIconApplicant, setAddIconApplicant] = useState<boolean>(false);
  const [interviewQuiz, setInterviewQuiz] = useState<string | null>(null);
  const [searchResult, setSearchResult] = useState<string | null>(null);
  const [startDate, setStartDate] = useState<Date | undefined>();
  const [dueDate, setDueDate] = useState<Date | undefined>();

  const { isRequired, isNumber, isValidateMaxAndMinNumber } = useValidate();

  // State
  const subCategoryRef = useRef<any>(null);
  const { beforeToday } = DateRangePicker;

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      technology: '',
      numberOfQuestions: '',
      yearsOfExperience: '',
      estimationTime: '',
      type: 1, // this type for interview
      skips: '',
      applicantId: '',
      notes: '',
      // willSendEmail: false,
      dueDate: '',
      startDate: '',
      category: applicantDetails?.track ? applicantDetails.track : null,
      subCategory: [],
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  const handleInsert = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (Number(form.numberOfQuestions) > 30) {
      dispatch(setErrorNotify("Max questions can't exceed 30"));
    } else if (Number(form.skips) >= Number(form.numberOfQuestions)) {
      dispatch(setErrorNotify("Max skips can't exceed or equal to number of questions"));
    } else if (!form?.startDate) {
      dispatch(setErrorNotify('Please select start date'));
    } else if (!form?.dueDate) {
      dispatch(setErrorNotify('Please select due date'));
    } else {
      try {
        setLoading(true);
        const { applicantId, ...rest } = form;

        const payload: Partial<Omit<InterviewForm, 'applicantId'>> & { applicantId?: string[] } = rest;

        if (payload.notes === '') delete payload.notes;
        // @TODO:Handle multi applicant assignment
        if (form.applicantId) payload.applicantId = [form.applicantId];
        const result = await Api.post('ai-interview/single', payload);
        setInterviewQuiz(result.data.quizUrl);
        onCreate();
      } catch (error: any) {
        dispatch(setErrorNotify(error.response.data.message));
      } finally {
        setLoading(false);
      }
    }
  };

  // RANDOM CREATION:- No need for applicant select input
  // const handleSearch =
  //   (endpoint, action, isCustomAdd = false) =>
  //   async (keyword) => {
  //     try {
  //       const result = await Api.get(endpoint, { keyword });
  //       action(result?.data);
  //       if (isCustomAdd) {
  //         if (!result?.data?.length && !!keyword) {
  //           setAddIconApplicant(true);
  //           setSearchResult(keyword);
  //         } else {
  //           setAddIconApplicant(false);
  //         }
  //       }
  //     } catch (error) {
  //       dispatch(setErrorNotify(error.response.data.message));
  //     }
  //   };

  const handleAddNewApplicant = async () => {
    const pattern = Regex.email;
    const value = (document.getElementById('applicantId') as HTMLInputElement)?.value;
    if (!pattern.test(value)) {
      setEmailRegex(true);
      setAddIconApplicant(false);
    } else {
      const requestData = {
        email: searchResult,
      };
      const response = await Api.post('applicants/single/custom', requestData);
      dispatch(setFieldValue({ path: 'applicantId', value: response.data._id }));
      setAddIconApplicant(false);
    }
  };

  const handleCopyLink = () => {
    if (interviewQuiz) {
      navigator.clipboard.writeText(interviewQuiz);
      dispatch(setNotifyMessage('Link copied'));
    }
  };

  const subModalHeader = () => {
    return applicantDetails?.email && <p className="text-base font-semibold text-[#5C5C5C] dark:text-white">{applicantDetails?.email}</p>;
  };

  useEffect(() => {
    if (applicantDetails) {
      dispatch(setFieldValue({ path: 'applicantId', value: applicantDetails?._id }));
      dispatch(setFieldValue({ path: 'category', value: applicantDetails?.track }));
    }
  }, []);

  // useEffect(() => {
  //   dispatch(setFieldValue({ paht: 'startDate', value: startDate }));
  //   dispatch(setFieldValue({ paht: 'dueDate', value: dueDate }));
  // }, [startDate, dueDate]);

  return (
    <Dialog
      size="lg"
      isOpen
      title={applicantDetails?.email ? 'Assign Interview to: ' : 'Generate Interview Link'}
      subtitle={subModalHeader()}
      onClose={() => {
        interviewQuiz && applicantDetails?.email && refreshMainTable();
        onClose();
      }}
    >
      {/* Creation Form */}
      {!interviewQuiz && (
        <Form className="space-y-4" onSubmit={(event: any) => handleInsert(event)}>
          <div className="space-y-4">
            <div className="space-y-3">
              <h3 className="font-medium text-sm text-inputLabel dark:text-inputDarkLabel">
                Interview Type <span className="text-red-600 dark:text-red-800">*</span>
              </h3>
              <div className="flex flex-wrap xsmd:gap-[70px]  gap-4 items-center container mx-auto  ">
                {Object.entries(InterviewType).map(([key, value]) => (
                  <div key={value} className="flex  items-center gap-2">
                    <Radio
                      name="type"
                      selectionValue={value}
                      value={form.type}
                      onChange={() => dispatch(setFieldValue({ path: 'type', value: value }))}
                      required
                      className="cursor-pointer"
                      labelTooltip={
                        key === 'Interactive'
                          ? 'Live interview experience with real-time questions.'
                          : 'Static set of questions for self-paced completion.'
                      }
                      // NB: These styles because tooltip is inside dialog
                      labelTooltipStyles="min-w-60 sm:min-w-72"
                      label={key}
                      fullWidth={false}
                      pointer={false}
                      isCustomLabel={false}
                      applicantTestView={false}
                    />
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium text-sm text-inputLabel dark:text-inputDarkLabel">
                Due Date
                <span className="text-red-600 dark:text-red-800">*</span>
              </h3>

              <div className="flex flex-col sm:flex-row justify-between items-center gap-2 sm:gap-4">
                <DatePicker
                  format="dd/MM/yyyy hh:mm aa"
                  placeholder="Enter a start date"
                  className="w-full bg-gray-50 rounded-full"
                  value={startDate}
                  onChange={(value: any) => setStartDate(value ?? undefined)}
                  showMeridiem
                  placement="autoVerticalStart"
                  disabled={loading}
                  shouldDisableDate={beforeToday()}
                />

                <DatePicker
                  format="dd/MM/yyyy hh:mm aa"
                  placeholder="Enter an end date"
                  className="w-full bg-gray-50 rounded-full"
                  value={dueDate}
                  onChange={(value: any) => setDueDate(value ?? undefined)}
                  showMeridiem
                  placement="autoVerticalStart"
                  disabled={loading}
                  shouldDisableDate={beforeToday()}
                />
              </div>
            </div>

            {!applicantDetails?.track && (
              <Select
                label="Category"
                name="category"
                value={form.category}
                disabled={loading}
                onChange={(newCategory: string) => {
                  subCategoryRef.current?.blur();
                  dispatch(setFieldValue({ path: 'category', value: newCategory }));
                  dispatch(setFieldValue({ path: 'subCategory', value: null }));
                }}
                lookup="category"
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon
                requiredLabel
                creationOptions={{
                  url: 'lookups/category/single',
                  fieldName: 'name',
                  validation: Regex.categorySubcategoryTopic,
                }}
                validators={[]}
              />
            )}

            <MultiSelect
              key={form.category}
              label="Subcategory"
              requiredLabel
              name="subCategory"
              placeholder="Search for subcategory"
              value={Array.isArray(form.subCategory) ? form.subCategory : []}
              onChange={(newSubCategory: string[]) => dispatch(setFieldValue({ path: 'subCategory', value: newSubCategory }))}
              disabled={!form.category || loading}
              disabledMessage="Please select category first"
              lookup="subcategory"
              params={{ categoryId: form.category }}
              creationOptions={{
                url: 'lookups/subCategory/single',
                fieldName: 'name',
                validation: Regex.categorySubcategoryTopic,
              }}
              optionValueKey="_id"
              optionLabelKey="name"
              dropIcon
              validators={[]}
            />

            <div className="grid sm:grid-cols-2 gap-4 space-y-4 sm:space-y-0">
              <Select
                disabled={loading}
                name="difficulty"
                label="Difficulty"
                lookup="$QuizDifficulty"
                value={form.difficulty}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'difficulty', value }))}
                dropIcon
                requiredLabel
                validators={[]}
              />

              <TextInput
                disabled={loading}
                name="numberOfQuestions"
                label="Number of Questions"
                placeholder="Number of questions"
                value={form.numberOfQuestions}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'numberOfQuestions', value }))}
                validators={[isNumber(), isRequired()]}
                requiredLabel
              />
            </div>

            <div className="grid sm:grid-cols-2 gap-4 space-y-4 sm:space-y-0">
              <TextInput
                disabled={loading}
                name="estimationTime"
                label="Estimation Time"
                placeholder="Estimation time"
                labelTooltip="Expected time for the interview in minutes."
                value={form.estimationTime}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'estimationTime', value }))}
                validators={[isNumber(), isRequired(), isValidateMaxAndMinNumber('min', 10), isValidateMaxAndMinNumber('max', 240)]}
                requiredLabel
                type="number"
                min={10}
              />
              <TextInput
                disabled={loading}
                name="skips"
                label="Max Skips"
                labelTooltip="Maximum skips allowed without affecting the score."
                placeholder="Max Skips"
                value={form.skips}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'skips', value }))}
                validators={[isNumber(), isRequired()]}
                requiredLabel
              />
            </div>

            <TextInput
              label="Notes"
              name="notes"
              placeholder="e.g., Focus on advanced JavaScript topics"
              labelTooltip="Provide specific details or instructions for the interview."
              value={form.notes}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'notes', value }))}
              disabled={loading}
              validators={[]}
            />
          </div>

          <Button
            loading={loading}
            disabled={loading}
            colorType="primary"
            className="w-full mt-5 "
            type="submit"
            label={applicantDetails?.email ? 'Assign Interview' : 'Generate Link'}
          />
        </Form>
      )}

      {/* ai interview dialog */}
      {interviewQuiz && (
        <div className="py-5  pt-0">
          <div>
            <div className="flex justify-center items-center mx-auto mb-5 !mt-0 text-gray-300 dark:text-gray-200">
              <img src={doneMark} alt="done mark" />
            </div>

            <div className="text-center">
              <h2 className="text-center dark:text-white font-medium text-xl ">
                AI interview {applicantDetails?.email ? 'Assigned' : 'Generated'} Successfully!
              </h2>
              <div className=" w-72 text-center mx-auto mt-2">
                <p className="text-center font-normal text-base   dark:text-white text-[#626262]">
                  Send the link below for quick and easy access to the applicant
                </p>
              </div>
            </div>
            <div className="mt-5 py-1">
              <div className="grid w-full max-w-80 mx-auto text-center">
                <div className="relative flex ">
                  <input
                    value={interviewQuiz}
                    type="text"
                    className="col-span-6 block w-full rounded-lg border border-gray-300 bg-gray-50 px-2.5 py-4 text-base text-[#313437] pr-12 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-400 dark:placeholder:text-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                  />
                  <Icon
                    icon="ooui:copy-ltr"
                    onClick={() => handleCopyLink()}
                    className="dark:text-white cursor-pointer text-gray-400  text-2xl absolute right-3 top-4"
                  />
                </div>
              </div>
              {/* 
            <Button
              className="w-full"
              outline
              label="Copy Link"
              icon="material-symbols:content-copy-outline-rounded"
              onClick={() => handleCopyLink()}
            /> */}
            </div>
          </div>
        </div>
      )}
    </Dialog>
  );
};
