// React
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';

// Core
import { Card, TextInput, Textarea, Select } from 'src';
import { useValidate, RootState, setFieldValue, useAppDispatch, useAppSelector, Regex } from 'UI/src';

interface TemplateInfoProps {
  formData: {
    title: string;
    description: string;
    seniorityLevel: number;
    difficulty: number;
    duration: number;
    skips: number;
    form: any;
    setFieldValue: React.Dispatch<React.SetStateAction<string | number | object | null>>;
    setFormValue: React.Dispatch<React.SetStateAction<string | number | object | null>>;
    resetForm: () => void;
  };
  disableButtons: any;
}

export const TemplateInfo = ({ formData, disableButtons }: TemplateInfoProps) => {
  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const dispatch = useAppDispatch();

  // Hooks
  const { type, id } = useParams();
  const { isRequired, isNotSpaces, validateRegex, minLength, maxLength, isValidateMaxAndMinNumber, isNumber } = useValidate();

  useEffect(() => {
    disableButtons.setDisableNextButton(!form.title || !form.seniorityLevel || !form.difficulty || !form.duration);
    disableButtons.setDisabledMessage('');
  }, [form]);

  return (
    <div className="border border-[#E6E7EC] rounded-xl space-y-4">
      <h1 className="p-4 thepassHthree bg-[#F9F8FA] border-b border-[#E6E7EC]">Template Setup</h1>
      {/* <p className="text-[#A2A9BA] font-normal text-sm">Basic information about your {type} template</p> */}

      {/* <Button label="Generate with AI" customIcon="stars" tertiary /> */}

      <div className="px-4 pb-4 space-y-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4">
        <TextInput
          label="Template Name"
          className="dark:text-white"
          name="title"
          placeholder={`E.g., Frontend developer ${type}`}
          value={form.title}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'title', value }))}
          validators={[isRequired(), validateRegex(Regex.name), minLength(2), maxLength(50)]}
          requiredLabel
        />

        {/* <Textarea
          label="Description"
          className="dark:text-white resize-none"
          name="description"
          placeholder="Describe the purpose and scope of this template..."
          value={form.description}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'description', value }))}
          validators={[isRequired()]}
        /> */}

        <Select
          label="Seniority Level"
          name="seniorityLevel"
          placeholder="Select seniority level"
          value={form.seniorityLevel}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'seniorityLevel', value }))}
          validators={[isRequired()]}
          lookup="$QuizDifficulty"
          dropIcon={true}
          className="w-full"
          requiredLabel
          optionValueKey="value"
          optionLabelKey="label"
        />

        <Select
          label="Difficulty"
          name="difficulty"
          placeholder="Select difficulty level"
          value={form.difficulty}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'difficulty', value }))}
          validators={[isRequired()]}
          dropIcon={true}
          lookup="$QuestionDifficulty"
          requiredLabel
          optionValueKey="value"
          optionLabelKey="label"
        />

        <TextInput
          label={type == 'interview' ? 'Estimation Time' : 'Duration'}
          subLabel="(in mins)"
          name="duration"
          placeholder={`${type == 'interview' ? 'Estimation time' : 'Duration'}`}
          value={form.duration}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'duration', value }))}
          validators={[isRequired(), isNumber(), isValidateMaxAndMinNumber('min', 10), isValidateMaxAndMinNumber('max', 60)]}
          dropIcon={true}
          type="number"
          requiredLabel
        />

        {type == 'interview' && (
          <TextInput
            label="Max Skips"
            name="skips"
            placeholder="Add max question the applicants allow to skip without affecting the score "
            value={form.skips}
            onChange={(value: any) => dispatch(setFieldValue({ path: 'skips', value }))}
            validators={[isRequired()]}
            dropIcon={true}
            className="w-full"
            type="number"
            requiredLabel
          />
        )}
      </div>
    </div>
  );
};
