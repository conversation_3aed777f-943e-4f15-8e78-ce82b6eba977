// React
import { useRef, useState, useEffect, useCallback } from 'react';

// Core
import { Api } from '../../src';

// Enums
import * as Enums from '../../../UI/src/constants/enums';

interface LookupItem {
  _id: string;
  name: string;
}

// Helpers
const DEFAULT_PROPS = {
  cached: false, // Set default to false to avoid caching
  params: null,
};

const useLocalLookups = () => {
  const getLookup = useCallback(async (name: string, type = 'remote', cached = false, params: any = {}) => {
    const action = type === 'remote' ? 'get' : 'getCustom';

    try {
      const response = await Api.lookups[action]({ name, params });
      const responseLookup = response.data;
      return responseLookup;
    } catch (error) {
      throw error;
    }
  }, []);

  return { getLookup };
};

const getType = (lookup: any) => {
  if (Array.isArray(lookup)) {
    return 'inline';
  }

  if (lookup && lookup[0] === '$') {
    return 'static';
  }

  if (lookup && lookup[0] === '#') {
    return 'custom';
  }

  return 'remote';
};

const useDebounce = (value: any, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

export const useLookups = (lookup: any, { cached, params }: any = DEFAULT_PROPS) => {
  const lookupValue = useRef(lookup);
  const [loading, setLoading] = useState(false);
  const [lookups, setLookups] = useState<LookupItem[] | any>([]);

  const debouncedLookup = useDebounce(lookup, 300); // Adjust the delay as needed
  const { getLookup } = useLocalLookups();

  const handleGetLookup = useCallback(async () => {
    const type = getType(lookupValue.current);
    if (type === 'remote' || type === 'custom') {
      try {
        setLoading(true);
        const response = await getLookup(lookupValue.current, type, cached, params || {});
        setLookups(response);
      } finally {
        setLoading(false);
      }
    } else if (type === 'inline') {
      setLookups(lookup);
    } else if (type === 'static') {
      setLookups(
        Object.keys((Enums as any)[lookupValue.current.substring(1)])
          ?.filter((key: any) => isNaN(+key))
          ?.map((key: any) => ({ value: (Enums as any)[lookupValue.current.substring(1)][key], label: key }))
      );
    }
  }, [lookup, cached, JSON.stringify(params), getLookup]);

  useEffect(() => {
    if (debouncedLookup) {
      handleGetLookup();
    }
  }, [debouncedLookup, handleGetLookup]);

  const refresh = () => {
    handleGetLookup(); // Refresh the lookups
  };

  return {
    lookups,
    loading,
    refresh,
  };
};
