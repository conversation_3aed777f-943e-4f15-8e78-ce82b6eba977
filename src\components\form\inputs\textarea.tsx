import { useRef, useEffect } from 'react';

import { Label, Textarea as Input } from 'flowbite-react';
import { asField } from '../hocs/field';

// UI
import { Radio, Checkbox } from 'src';

type TextareaType = {
  name: string;
  label?: string;
  errorMessage?: string;
  subLabel?: string;
  labelClassName?: string;
  onChange?: any;
  requiredLabel?: boolean;
  value?: string;
  maxHeight?: number;
  inputTypeProps?: any;
};

export const Textarea = asField(
  ({
    name,
    label,
    errorMessage,
    subLabel = '',
    labelClassName,
    onChange,
    requiredLabel,
    value,
    maxHeight,
    inputTypeProps,
    ...props
  }: TextareaType | any) => {
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    const adjustHeight = () => {
      if (textareaRef.current) {
        // TODO: Markos
        textareaRef.current.style.height = 'auto';
        textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
      }
    };

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      onChange(e.target.value);
      adjustHeight();
    };

    useEffect(() => {
      adjustHeight();
    }, [value]);
    const customTheme = {
      colors: {
        gray: `${
          props.readOnly ? 'cursor-not-allowed resize-none dark:bg-gray-800 text-gray-500' : 'dark:bg-gray-700 text-gray-800'
        } block w-full border bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 border-gray-300  dark:border-gray-600 dark:text-white dark:placeholder-gray-400 p-2.5 text-sm rounded-lg focus:ring-0 focus:border-gray-300`,
        failure:
          'border-red-500 bg-white-500 text-gray-900 dark:text-white placeholder-gray-400 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-[#374151] dark:focus:border-red-500 dark:focus:ring-red-500',
      },
    };

    return (
      <div>
        {label && (
          <div className="flex items-center  mb-2">
            <Label htmlFor={name}>
              <div className="flex gap-3">
                {inputTypeProps?.formType && (
                  <div>
                    {inputTypeProps?.formType === 1 ? (
                      <Radio className="text-green-500" {...inputTypeProps} />
                    ) : inputTypeProps?.formType === 2 ? (
                      <Checkbox className="text-green-500" {...inputTypeProps} />
                    ) : inputTypeProps?.formType === 4 ? null : null}
                  </div>
                )}
                <div>
                  <span className={`thepassBtwo dark:text-inputDarkLabel ${labelClassName}`}>{label}</span>{' '}
                  <span className="text-inputSubLabel dark:text-inputLabel">{subLabel}</span>
                  {requiredLabel && <span className="text-red-600 dark:text-red-800"> *</span>}
                </div>
              </div>
            </Label>
          </div>
        )}

        <div className="flex gap-3">
          {!label &&
            (inputTypeProps?.formType === 1 ? (
              <Radio className="text-green-500" {...inputTypeProps} disabled={props.readOnly} />
            ) : inputTypeProps?.formType === 2 ? (
              <Checkbox className="text-green-500" {...inputTypeProps} disabled={props.readOnly} />
            ) : null)}
          <Input
            theme={customTheme}
            color={errorMessage ? 'failure' : 'gray'}
            helperText={errorMessage ? <>{errorMessage}</> : undefined}
            id={name}
            onChange={handleChange}
            value={value}
            ref={textareaRef}
            style={{ maxHeight: `${maxHeight}px`, overflowY: 'scroll' }}
            {...props}
          />
        </div>
      </div>
    );
  }
);
