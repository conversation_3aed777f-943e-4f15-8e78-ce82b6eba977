// React
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
// Core
import { TestSeniorityLevel, AvarageScore, Table, NameFieldColumn, EmailFieldColumn, Icon, ResultStatusSubmission } from 'src';
import { ComponentOverlayInterview } from './component-overlay-interview';
import { ComponentInterviewRecord } from './interview-recored';
import { setNotifyMessage, setErrorNotify, PlanFeatures, api, Tags } from 'UI';
import { useFetchList, useAppDispatch } from 'UI/src';

// Types
interface ApplicantRow {
  _id: string;
  applicantId: string;
  applicantName: string;
  applicantEmail: string;
  seniorityLevel: string;
  status: string;
  score: number;
  assessmentId: string;
  recordInterview?: boolean;
  [key: string]: any;
}

interface LoadingRecord {
  id: string;
  loading: boolean;
}

type ShowMoreMap = Record<string, boolean>;

export const AssignedApplicants = () => {
  const { quizId, type } = useParams<{ quizId: string; type: string }>();
  const dispatch = useAppDispatch();

  // State
  const [showMoreMap, setShowMoreMap] = useState<ShowMoreMap>({});
  const [backupList, setBackupList] = useState<ApplicantRow[]>([]);
  const [showOverlay, setShowOverlay] = useState(false);
  const [showInterviewRecord, setShowInterviewRecord] = useState(false);
  const [applicantDetails, setApplicantDetails] = useState<ApplicantRow | null>(null);
  const [loadingInterviewRecord, setLoadingInterviewRecord] = useState<LoadingRecord[]>([]);

  // Hooks
  const navigate = useNavigate();
  const initialFilters = {
    seniorityLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },
    status: {
      label: 'Status',
      enum: 'SubmissionStatus',
    },
    score: {
      label: 'Average Score',
      enum: 'AverageScore',
    },
  };

  const { ready, loading, count, list, search, pagination, filters, setFilters } = useFetchList(
    `${type === 'interview' ? 'ai-interview' : 'submissions'}/assigned-applicants`,
    {
      search: '',
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
      id: quizId,
    }
  );

  // const handleDeleteApplicant = async (id) => {
  //   showConfirm(ConfirmText('delete'), {
  //     confirmText: 'Yes',
  //     cancelText: 'No',
  //     onConfirm: async () => {
  //       try {
  //         await Api.delete(`applicants/single/${id}`);
  //         refresh();
  //         hideConfirm();
  //         notify('Deleted successfully!');
  //       } catch (error) {
  //         notify.error(error.response?.data?.message);
  //       }
  //     },
  //   });
  // };
  const ConfirmText = (action = 'delete') => {
    let icon = 'hugeicons:archive-02';
    let iconBg = 'bg-[#ddd1f8]';
    let iconInnerBg = 'bg-[#cab6f5]';
    let iconColor = 'text-[#9061F9]';
    let message = 'Are you sure you want to delete this applicant?';
    if (action === 'duplicate') {
      icon = 'lucide:copy-plus';
      iconBg = 'bg-[#ddd1f8]';
      iconInnerBg = 'bg-[#cab6f5]';
      iconColor = 'text-[#9061F9]';
      message = 'Are you sure you want to duplicate this applicant?';
    }
    return (
      <div className="text-center">
        <div className={`flex mx-auto p-4 mb-7 ${iconBg} w-24 h-24 rounded-full`}>
          <div className={`flex mx-auto mb-7 ${iconInnerBg} w-16 h-16 justify-center rounded-full`}>
            <Icon icon={icon} className={iconColor} width="40" />
          </div>
        </div>
        <p>{message}</p>
      </div>
    );
  };

  const handleInterviewRecord = async (row: ApplicantRow) => {
    setShowInterviewRecord(true);
    setApplicantDetails(row);
    setLoadingInterviewRecord((prev) => prev.map((item) => (item.id === row.assessmentId ? { ...item, loading: true } : item)));
  };

  const handleMimicLoading = () => {
    if (showInterviewRecord && applicantDetails) {
      const recordToUpdate = loadingInterviewRecord.find((item) => item.id === applicantDetails.assessmentId);
      if (recordToUpdate && recordToUpdate.loading) {
        const timer = setTimeout(() => {
          setLoadingInterviewRecord((prev) => prev.map((item) => (item.id === applicantDetails.assessmentId ? { ...item, loading: false } : item)));
        }, 500);
        return () => clearTimeout(timer);
      }
    }
  };

  const downloadDocument = async (row: any) => {
    try {
      const response = await api.get(
        `${type === 'test' ? 'submissions' : type === 'interview' ? 'ai-interview' : type === 'screening' ? 'submissions' : '—'}/stages/report/${
          row.assessmentId
        }`,
        {
          responseType: 'blob',
        }
      );
      const url = window.URL.createObjectURL(
        new Blob([response.data], {
          type: response.headers['content-type'],
        })
      );
      const a = document.createElement('a');
      a.href = url;
      a.download = `${type === 'test' ? 'test' : type === 'interviews' ? 'interview' : type === 'screening' ? 'screening' : '—'}-report.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  useEffect(() => {
    if (backupList.length === 0 && Array.isArray(list) && list.length > 0) {
      setBackupList(list as ApplicantRow[]);
      setLoadingInterviewRecord(
        (list as ApplicantRow[]).map((item) => ({
          id: item.assessmentId,
          loading: false,
        }))
      );
    }
  }, [list, backupList.length]);

  useEffect(() => {
    handleMimicLoading();
  }, [showInterviewRecord, applicantDetails, loadingInterviewRecord]);

  return (
    <>
      <Table
        addButtonPermission={false}
        ready={ready}
        loading={loading}
        title="Assigned Applicants"
        searchPlaceholder="Search for applicants..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          applicantName: (_: unknown, row: ApplicantRow) => (
            <NameFieldColumn
              id={row?._id}
              name={row?.applicantName}
              showMoreMap={showMoreMap}
              onClick={() => navigate(`/app/applicants/progress/${row?.applicantId}`)}
            />
          ),
          applicantEmail: (_: unknown, row: ApplicantRow) => <EmailFieldColumn email={row?.applicantEmail} onClick={() => {}} />,
          seniorityLevel: (_: unknown, row: ApplicantRow) => {
            const getSeniorityLevelText = (level: number): string => {
              switch (level) {
                case 1:
                  return 'intern';
                case 2:
                  return 'fresh';
                case 3:
                  return 'junior';
                case 4:
                  return 'mid-level';
                case 5:
                  return 'senior';
                default:
                  return '-';
              }
            };

            return (
              <div className="w-fit">
                <Tags type={getSeniorityLevelText(Number(row?.seniorityLevel))} color="bg-transparent" />
              </div>
            );
          },
          activeStatus: (_: unknown, row: ApplicantRow) => <ResultStatusSubmission statusSubmission={row.status} />,
          averageScore: (_: unknown, row: ApplicantRow) => {
            if (row?.assignedCount === 0) {
              return (
                <div className="w-fit">
                  <span className="text-[#667085]">—</span>
                </div>
              );
            }

            const getScoreColor = (score: number | undefined) => {
              if (score === undefined || score === null) {
                return 'bg-gray-100 text-gray-800';
              }
              if (score >= 0 && score < 50) {
                return 'bg-[#FFECE9] text-[#A80000]';
              } else if (score >= 50 && score < 75) {
                return 'bg-[#FFEDD8] text-[#E9760F]';
              } else if (score >= 75 && score < 100) {
                return 'bg-[#FFFCDF] text-[#BA8500]';
              } else if (score >= 100) {
                return 'bg-[#EEFFF1] text-[#056816]';
              }
              return 'bg-gray-100 text-gray-800';
            };

            const getScoreText = (score: number | undefined) => {
              if (score === null || score === undefined) return '—';
              return `${score}%`;
            };

            return (
              <div className="w-fit">
                <Tags type="score" color={getScoreColor(row?.score)}>
                  {getScoreText(row?.score)}
                </Tags>
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'applicantName',
            label: 'Name',
            primary: true,
            width: '22%',
          },
          {
            key: 'applicantEmail',
            label: 'Email',
            primary: true,
            width: '23%',
            className: 'w-full',
          },
          {
            key: 'seniorityLevel',
            label: 'Seniority Level',
            primary: true,
            width: '18%',
            inline: true,
          },
          {
            key: 'activeStatus',
            label: 'Status',
            width: '19%',
            inline: true,
          },
          {
            key: 'averageScore',
            label: 'Average Score',
            width: '17%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_: unknown, row: ApplicantRow) {
              const actions = [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  onClick: () => navigate(`/app/applicants/progress/${row.applicantId}/${type}`),
                },
                {
                  label: 'Export Report',
                  icon: 'mdi:file-document-arrow-right-outline',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  dropDown: [
                    {
                      label: `${type?.charAt(0).toUpperCase() + (type ?? '').slice(1)} Score`,
                      color: '',
                      permission: PlanFeatures.EXPORT_REPORTS,
                      icon: 'fa6-regular:file-pdf',
                      onClick: () =>
                        window.open(
                          `/app/tests/pdf/${row?.assessmentId}?type=${type === 'interview' ? 'interviews' : 'submissions'}`,
                          '_blank',
                          'noopener,noreferrer'
                        ),
                    },
                    {
                      label: `${type?.charAt(0).toUpperCase() + (type ?? '').slice(1)} Details`,
                      icon: 'hugeicons:xls-02',
                      color: '',
                      permission: '',
                      onClick: () => downloadDocument(row),
                    },
                  ],
                },
                // {
                //   label: 'Archive',
                //   customIcon: 'archive',
                //   iconWidth: '22',
                //   iconHeight: '22',
                //   color: 'text-gray-700 font-normal text-[14px] dark:text-white',
                //   onClick: () => handleDeleteApplicant(row?.applicantId),
                // },
              ];
              if (type === 'interview' && row.recordInterview === true) {
                actions.push({
                  label: 'Interview Record',
                  customIcon: 'interview',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  loading: loadingInterviewRecord.find((item) => item.id === row.assessmentId)?.loading || false,
                  onClick: () => handleInterviewRecord(row),
                } as any); // Table actions may not support loading, so cast as any if needed
              }

              return actions;
            },
          },
        ]}
        // noDataFound={{
        //   customIcon: 'assignedApllicantsNotFound',
        //   message: 'No Assigned Applicants',
        // }}
        placeholder={{
          title: 'No applicants created yet',
          subTitle: 'Add applicants to start building your candidate pipeline.',
          image: '/UI/src/assets/placeholder/NoUsers.svg',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        // showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        hideJumbotron
        isScrollableTabsExists
      />

      {showOverlay && <ComponentOverlayInterview onClose={() => setShowOverlay(false)} />}

      {showInterviewRecord && (
        <ComponentInterviewRecord
          onClose={() => setShowInterviewRecord(false)}
          assessmentId={applicantDetails?.assessmentId}
          applicantId={applicantDetails?.applicantId}
        />
      )}
    </>
  );
};
