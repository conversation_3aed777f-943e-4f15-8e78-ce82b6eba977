import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';
import type { QuizType } from '../types/Quiz.type';
import type { QuestionType } from '../types/Question.type';

// Fetch single quiz
export const fetchQuiz = createAsyncThunk('quizzes/fetchQuiz', async (id: string, { rejectWithValue }) => {
  try {
    const response = await Api.get(`templates/single/${id}`, {});
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to fetch quiz');
  }
});

// Fetch custom quiz
export const fetchCustomQuiz = createAsyncThunk('quizzes/fetchCustomQuiz', async (id: string, { rejectWithValue }) => {
  try {
    const response = await Api.get<QuizType>(`templates/single/custom/${id}`, {});
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to fetch custom quiz');
  }
});

// Create quiz
export const createQuiz = createAsyncThunk('quizzes/createQuiz', async (payload: any, { rejectWithValue }) => {
  try {
    const response = await Api.post('templates/single', payload);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to create quiz');
  }
});

// Update quiz
export const updateQuiz = createAsyncThunk('quizzes/updateQuiz', async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
  try {
    const response = await Api.put(`templates/single/${id}`, data);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to update quiz');
  }
});

// Delete quiz
export const deleteQuiz = createAsyncThunk('quizzes/deleteQuiz', async (id: string, { rejectWithValue }) => {
  try {
    await Api.delete(`/templates/single/${id}`);
    return id;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to delete quiz');
  }
});

// Delete multiple quizzes
export const deleteMultipleQuizzes = createAsyncThunk('quizzes/deleteMultipleQuizzes', async (ids: string[], { rejectWithValue }) => {
  try {
    await Api.delete('templates/multi', { ids });
    return ids;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to delete quizzes');
  }
});

// Search quizzes
export const searchQuizzes = createAsyncThunk('quizzes/searchQuizzes', async (keyword: string, { rejectWithValue }) => {
  try {
    const result = await Api.get('quizzes/search', { keyword });
    return result.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to search quizzes');
  }
});

// Fetch question
export const fetchQuizQuestion = createAsyncThunk('quizzes/fetchQuizQuestion', async (id: string, { rejectWithValue }) => {
  try {
    const response = await Api.get<QuestionType>(`questions/single/${id}`, {});
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to fetch question');
  }
});

// Generate questions
export const generateQuizQuestions = createAsyncThunk('quizzes/generateQuizQuestions', async (payload: any, { rejectWithValue }) => {
  try {
    const response = await Api.post('questions/generate', payload);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to generate questions');
  }
});

// Get questions total
export const getQuizQuestionsTotal = createAsyncThunk('quizzes/getQuizQuestionsTotal', async (params: any, { rejectWithValue }) => {
  try {
    const response = await Api.get<number>('questions/total', params);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to get questions total');
  }
});
