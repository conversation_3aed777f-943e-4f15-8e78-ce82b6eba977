// React
import { useEffect } from 'react';

import { VITE_API_BASE_URL } from '../configs/api';
import { type RootState, updateUserAction, useAppDispatch, useAppSelector, type UsersListItem } from '../';

export const useUserSSE = () => {
  const dispatch = useAppDispatch();
  const userData = useAppSelector((state: RootState) => state.auth.user) as any;

  useEffect(() => {
    if (!userData?.organizationId) return;

    const eventSource = new EventSource(`${VITE_API_BASE_URL}organizations/events/${userData.organizationId}`);

    eventSource.onmessage = (event) => {
      try {
        const { data: features } = JSON.parse(event.data);

        if (features) {
          // Merge new features into existing user data
          const updatedUser = {
            ...userData,
            features: features,
          };

          dispatch(updateUserAction(updatedUser));
        }
      } catch (err) {
        console.error('Failed to parse SSE data:', err);
      }
    };

    eventSource.onerror = (error) => {
      console.error('SSE error:', error);
      eventSource.close();
    };

    return () => {
      eventSource.close();
    };
  }, [userData?.organizationId]); // rerun if org changes
};
