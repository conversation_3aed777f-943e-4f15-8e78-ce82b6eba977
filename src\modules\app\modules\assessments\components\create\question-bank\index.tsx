// React
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// Components
import { QuestionItem } from '../../question-item';

// Core
import { Card, Button, Icon, Select, TextInput, Textarea, ToggleFilter, SidebarFilterPage, NoDataMatches, NoDataFound } from 'src';

import {
  Regex,
  StaticData,
  RootState,
  useAppSelector,
  UserData,
  useFetchList,
  useValidate,
  useScreenSize,
  CustomIconType,
  questionsListRes,
  Placeholder,
} from 'UI/src';

// Flowbite
import { Pagination, Spinner } from 'flowbite-react';

interface QuestionBankProps {
  formData?: any;
  disableButtons: {
    setDisableNextButton: (value: boolean) => void;
    setDisabledMessage: (value: string) => void;
  };
  selectedQuestionsID: Record<string, boolean>;
  setSelectedQuestionsID: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  anyQuestionHasEditMode: Record<string, boolean>;
  setAnyQuestionHasEditMode: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  isRandomizeAskAiVisible: boolean;
  setRandomizeAskAiVisibilty: (value: boolean) => void;
}

export const QuestionBank = ({
  disableButtons,
  selectedQuestionsID,
  setSelectedQuestionsID,
  anyQuestionHasEditMode,
  setAnyQuestionHasEditMode,
  isRandomizeAskAiVisible,
  setRandomizeAskAiVisibilty,
}: QuestionBankProps) => {
  // State
  const [backupList, setBackupList] = useState<questionsListRes[]>([]);

  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Hooks
  const screen = useScreenSize();
  const { isRequired, minLength, maxLength, validateRegex, isNumber } = useValidate();
  const { type } = useParams();
  const initialFilters = {
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
    // ...(userData.trackId
    //   ? {}
    //   : {
    category: {
      label: 'Category',
      lookup: 'category',
    },
    // }),
    subCategory: {
      label: 'Sub Category',
      lookup: 'subcategory',
      parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: null },
    },
    // topic: {
    //   label: 'Topic',
    //   lookup: 'topic',
    //   parentLookup: { key: 'subCategory', fieldName: 'subcategoryId' },
    // },
    // scope: {
    //   label: 'Scope',
    //   enum: 'Scope',
    // },
  };
  // Remove generic from useFetchList
  const {
    ready,
    loading,
    setLoading,
    list: rawList,
    count,
    search,
    pagination,
    filters,
    setFilters,
    refresh,
  } = useFetchList('questions/list', {
    search: '',
    pagination: {
      page: 1,
      size: 15,
    },
    filters: initialFilters,
  });
  const list = (rawList ?? []) as questionsListRes[];

  // Pagination
  const { page, size } = pagination;
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
  const isPaginationActive = !!pagination.update;

  const filterFeedData = Object.keys(initialFilters).map((key) => (key === 'difficulty' ? initialFilters.difficulty.enum : key));

  const handleHeight = () => {
    const hideJumbotron = true;
    const isScrollableTabsExists = false;

    if (hideJumbotron) {
      if (isScrollableTabsExists) {
        return {
          table: '2xl:max-h-[calc(100vh-255px)]',
          sidebarFilter: '2xl:max-h-[calc(100vh-255px)]',
        };
      } else
        return {
          table: '',
          sidebarFilter: '',
        };
    } else {
      if (isScrollableTabsExists) {
        return {
          table: '2xl:max-h-[calc(100vh-400px)]',
          sidebarFilter: '2xl:max-h-[calc(100vh-400px)]',
        };
      } else
        return {
          table: '2xl:max-h-[calc(100vh-170px)]',
          sidebarFilter: '2xl:max-h-[calc(100vh-170px)]',
        };
    }
  };

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  useEffect(() => {
    disableButtons.setDisableNextButton(
      Object.entries(selectedQuestionsID)
        .filter(([_, value]) => value)
        .map(([key, _]) => key)?.length < 3
    );
    Object.entries(selectedQuestionsID)
      .filter(([_, value]) => value)
      .map(([key, _]) => key)?.length < 3 && disableButtons.setDisabledMessage('Select at least 3 questions');
  }, [selectedQuestionsID]);

  return (
    <>
      <div className="flex justify-between gap-4">
        <div className={`hidden 2xl:block w-full max-w-[270px] ${handleHeight()?.sidebarFilter} `}>
          <SidebarFilterPage
            filterData={{
              filterFeedData,
              setFilters,
            }}
            searchInputField={search}
          />
        </div>

        {/* Question list */}
        <div className={`w-full h-full border border-[#DEE2E4] rounded-xl  ${handleHeight()?.table}`}>
          <div className="overflow-y-auto space-y-2 pb-2">
            <h2 className="thepassHthree bg-[#F9F8FA] text-[#111827] p-4">Questions</h2>
            <div className="flex flex-col sm:flex-row justify-between sm:items-center flex-wrap gap-4 rounded-xl px-4">
              <p className="text-[#4E5E82] thepassHfour">Select at least 3 questions</p>

              {/* Search input */}
              <div className="flex items-center gap-2">
                <div className="relative w-full sm:w-[300px]">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search Questions..."
                    className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white"
                    value={search.value}
                    onChange={(e) => {
                      search.update(e.target.value);
                    }}
                  />
                </div>

                {/* toggle filter */}
                <div className="block 2xl:hidden">
                  <ToggleFilter
                    filters={filters}
                    // FIXME: here i don't want to drawerFilter so i make it undefined instead of false best is undefined
                    // drawerFilter={undefined || false }
                    handleDates={{ startDate: { value: null, update: () => {} }, endDate: { value: null, update: () => {} } }}
                    // drawerClearAll={undefined}
                    resultsFound={count}
                    drawerInsideDrawer={false}
                    tempException={false}
                  />
                </div>
              </div>
            </div>

            {list?.map((question) => (
              <div className="px-4" key={question?._id}>
                <QuestionItem
                  key={question?._id}
                  questionId={question?._id}
                  selectedQuestionsID={selectedQuestionsID}
                  setSelectedQuestionsID={setSelectedQuestionsID}
                  anyQuestionHasEditMode={anyQuestionHasEditMode}
                  setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
                  canEditQuestion={true}
                  canRemoveQuestion={true}
                />
              </div>
            ))}
          </div>

          {!list.length && (
            <div className="flex justify-center items-center h-full my-8">
              {backupList.length > 0 ? (
                <Placeholder title="No results found" image="/UI/src/assets/placeholder/NoResults.svg" />
              ) : (
                <Placeholder
                  image="/UI/src/assets/placeholder/NoQuestions.svg"
                  title="No questions created yet"
                  subTitle="Add questions to build your question bank."
                />
              )}
            </div>
          )}
        </div>
      </div>

      {/* Loading */}
      {loading && (
        <div className="absolute z-50 left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
          <Spinner size="lg" color="purple" />
        </div>
      )}

      {isPaginationActive && count > size && (
        <nav className="flex justify-center items-center px-4 my-1">
          {count > size && (
            <Pagination
              theme={StaticData.paginationTheme}
              currentPage={page}
              onPageChange={(page: number) => pagination.update({ page })}
              showIcons
              totalPages={pagesCount}
              layout={screen.gt.md() ? 'pagination' : 'navigation'}
              previousLabel={screen.gt.md() ? 'Previous' : ''}
              nextLabel={screen.gt.md() ? 'Next' : ''}
            />
          )}
        </nav>
      )}
    </>
  );
};
