import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { type RootState } from '../../store';

interface DrawerState {
  // AnswerView component states (used in multiple places)
  answerView1: {
    isExpanded: { [key: string]: boolean };
    isTruncated: boolean;
  };
  answerView2: {
    isExpanded: { [key: string]: boolean };
    isTruncated: boolean;
  };
  
  // DatePicker component states (used in multiple places)
  datePicker1: {
    showExceededTime: boolean;
  };
  datePicker2: {
    showExceededTime: boolean;
  };
  
  // FilterSection component states
  filterSection: {
    isAtStart: boolean;
    isAtEnd: boolean;
  };
  
  // QuestionOfTest component states
  questionOfTest: {
    isExpanded: boolean;
    isOverflow: boolean;
    isEditMode: boolean;
    isShowFullAnswers: boolean;
    isChecked: boolean;
  };
}

const initialState: DrawerState = {
  // AnswerView component states
  answerView1: {
    isExpanded: {},
    isTruncated: false,
  },
  answerView2: {
    isExpanded: {},
    isTruncated: false,
  },
  
  // DatePicker component states
  datePicker1: {
    showExceededTime: false,
  },
  datePicker2: {
    showExceededTime: false,
  },
  
  // FilterSection component states
  filterSection: {
    isAtStart: true,
    isAtEnd: false,
  },
  
  // QuestionOfTest component states
  questionOfTest: {
    isExpanded: false,
    isOverflow: false,
    isEditMode: false,
    isShowFullAnswers: false,
    isChecked: true,
  },
};

const drawerSlice = createSlice({
  name: 'drawer',
  initialState,
  reducers: {
    // AnswerView1 actions
    setAnswerView1Expanded: (state, action: PayloadAction<{ [key: string]: boolean }>) => {
      state.answerView1.isExpanded = action.payload;
    },
    setAnswerView1Truncated: (state, action: PayloadAction<boolean>) => {
      state.answerView1.isTruncated = action.payload;
    },
    
    // AnswerView2 actions
    setAnswerView2Expanded: (state, action: PayloadAction<{ [key: string]: boolean }>) => {
      state.answerView2.isExpanded = action.payload;
    },
    setAnswerView2Truncated: (state, action: PayloadAction<boolean>) => {
      state.answerView2.isTruncated = action.payload;
    },
    
    // DatePicker1 actions
    setDatePicker1ShowExceededTime: (state, action: PayloadAction<boolean>) => {
      state.datePicker1.showExceededTime = action.payload;
    },
    
    // DatePicker2 actions
    setDatePicker2ShowExceededTime: (state, action: PayloadAction<boolean>) => {
      state.datePicker2.showExceededTime = action.payload;
    },
    
    // FilterSection actions
    setFilterSectionAtStart: (state, action: PayloadAction<boolean>) => {
      state.filterSection.isAtStart = action.payload;
    },
    setFilterSectionAtEnd: (state, action: PayloadAction<boolean>) => {
      state.filterSection.isAtEnd = action.payload;
    },
    
    // QuestionOfTest actions
    setQuestionOfTestExpanded: (state, action: PayloadAction<boolean>) => {
      state.questionOfTest.isExpanded = action.payload;
    },
    setQuestionOfTestOverflow: (state, action: PayloadAction<boolean>) => {
      state.questionOfTest.isOverflow = action.payload;
    },
    setQuestionOfTestEditMode: (state, action: PayloadAction<boolean>) => {
      state.questionOfTest.isEditMode = action.payload;
    },
    setQuestionOfTestShowFullAnswers: (state, action: PayloadAction<boolean>) => {
      state.questionOfTest.isShowFullAnswers = action.payload;
    },
    setQuestionOfTestChecked: (state, action: PayloadAction<boolean>) => {
      state.questionOfTest.isChecked = action.payload;
    },
    
    // Reset actions
    resetDrawerState: (state) => {
      return initialState;
    },
    resetAnswerView1: (state) => {
      state.answerView1 = initialState.answerView1;
    },
    resetAnswerView2: (state) => {
      state.answerView2 = initialState.answerView2;
    },
  },
});

export const {
  setAnswerView1Expanded,
  setAnswerView1Truncated,
  setAnswerView2Expanded,
  setAnswerView2Truncated,
  setDatePicker1ShowExceededTime,
  setDatePicker2ShowExceededTime,
  setFilterSectionAtStart,
  setFilterSectionAtEnd,
  setQuestionOfTestExpanded,
  setQuestionOfTestOverflow,
  setQuestionOfTestEditMode,
  setQuestionOfTestShowFullAnswers,
  setQuestionOfTestChecked,
  resetDrawerState,
  resetAnswerView1,
  resetAnswerView2,
} = drawerSlice.actions;

export const drawerState = (state: RootState) => state.drawer;
export default drawerSlice.reducer;
