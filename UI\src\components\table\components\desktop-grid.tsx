import { classNames } from '../../../utils/tailwind-classes';
import {
  //TableState, 
  type ColumnDef, type DataTableProps
} from '../config';
import {
  resolveHeader, renderCell,
  cellAlign, headerAlign
} from '../helpers'

// Design-system shell that renders a table using column defs
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function DesktopGrid<T extends Record<string, any>>({
  data,
  columns,
  state,
  onStateChange,
  getRowId,
  tableClassName,
}: DataTableProps<T>) {

  const onHeaderClick = (col: ColumnDef<T>) => {
    if (!onStateChange) return;
    const id = col.id || (typeof col.header === 'string' ? (col.header as string) : undefined);
    if (!id) return;
    const current = state?.sort?.id === id ? state?.sort : null;
    const next = !current ? { id, desc: false } : current.desc ? null : { id, desc: true };
    onStateChange({ sort: next });
  };

  return (
    <table className={classNames('min-w-full divide-y divide-tertiaryBorder', tableClassName || '')}>
      <thead className="bg-[#F9F8FA]">
        <tr>
          {columns.map((col) => (
            <th
              key={col.id || String(col.header)}
              scope="col"
              className={classNames(
                'px-6 py-4 whitespace-nowrap text-sm font-medium text-text-500 select-none',
                headerAlign(col.meta?.align)
              )}
              onClick={() => onHeaderClick(col)}
            >
              {resolveHeader(col)}
            </th>
          ))}
        </tr>
      </thead>
      <tbody className="divide-y divide-tertiaryBorder">
        {data.map((row, rowIndex) => (
          <tr key={String(getRowId ? getRowId(row, rowIndex) : rowIndex)}>
            {columns.map((col) => (
              <td
                key={(col.id || String(col.header)) + '-' + rowIndex}
                className={classNames(
                  'px-6 py-4 whitespace-nowrap text-base text-textStroke-primary-900 max-w-40 truncate',
                  cellAlign(col.meta?.align),
                  col.className || ''
                )}
              >
                {renderCell(row, col, rowIndex)}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  );
}

export default DesktopGrid;
