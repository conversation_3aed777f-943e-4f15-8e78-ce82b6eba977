import React, { useEffect, useState } from 'react';

import { useNavigate } from 'react-router-dom';
import { Button, Dialog } from 'UI';

import { TextInput, Select, Icon, RadioGroup } from 'src';
import { Regex, useValidate, initializeForm, RootState, setFieldValue, updateUser, useAppDispatch, useAppSelector, UsersListItem, Api } from 'UI/src';
import { fetchUser } from 'UI/src/middlewares/Users.middleware';
import { Form } from 'UI/src/components/form';
import { CreateUserReq, CookieStorage, setErrorNotify, setNotifyMessage, useUserPermissions } from 'UI';
import { useFormik } from 'formik';

export interface UsersSingleDialogProps {
  onClose: () => void;
  onCreate: () => void;
  id: string;
}

export const UsersSIngleDialog = ({ onClose, onCreate, id }: UsersSingleDialogProps) => {
  // Hooks
  const { isRequired, validateRegex, minLength, maxLength, validatePasswordRegex, isNotSpaces } = useValidate();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { handleGetUserRole } = useUserPermissions();

  // State
  const [roles, setRoles] = useState<Array<{ _id: string; name: string; viewName: string }>>([]);
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showError, setShowError] = useState(false);
  const formik = useFormik({
    initialValues: {
      name: '',
      email: '',
      password: '',
      roleId: '',
      roleName: '',
      roleViewName: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  const form = useAppSelector((state: RootState) => state.form.data);

  const handleGet = async () => {
    if (!id) return;
    try {
      const result = await dispatch(fetchUser(id)).unwrap();
      dispatch(initializeForm({ ...result, track: result.track?.trackId }));
    } catch (error: any) {
      dispatch(setErrorNotify(error?.message || 'Failed to fetch user'));
    }
  };

  // const handleInsert = async (e) => {
  //   try {
  //     let roleId = '';
  //     let roleName = '';
  //     let roleViewName = '';

  //     if (Array.isArray(data.roles)) {
  //       if (data.roles.length > 0) {
  //         if (typeof data.roles[0] === 'string') {
  //           roleId = data.roles[0];
  //         } else if (typeof data.roles[0] === 'object' && data.roles[0] !== null) {
  //           const roleObj = data.roles[0] as { roleId: string; name: string; viewName: string };
  //           roleId = roleObj.roleId;
  //           roleName = roleObj.name;
  //           roleViewName = roleObj.viewName;
  //         }
  //       }
  //     } else if (data.roles && typeof data.roles === 'object') {
  //       const roleObj = data.roles as { roleId: string; name: string; viewName: string };
  //       roleId = roleObj.roleId;
  //       roleName = roleObj.name;
  //       roleViewName = roleObj.viewName;
  //     }

  //     setFormValue({
  //       ...data,
  //       track: data.track?.trackId,
  //       roles: roleId,
  //       roleName: roleName,
  //       roleViewName: roleViewName,
  //     });
  //   } catch (error: any) {
  //     dispatch(setErrorNotify(error?.response?.data?.message));
  //   }
  // };

  const handleInsert = async (e: React.FormEvent<HTMLFormElement>) => {
    // e.preventDefault();
    const userData = form;
    // For create mode, password and confirm password are required
    if (!userData.password) {
      dispatch(setErrorNotify('Password is required'));
      return;
    }

    if (!confirmPassword) {
      dispatch(setErrorNotify('Confirm password is required'));
      return;
    }

    if (confirmPassword !== userData.password) {
      setShowError(true);
      dispatch(setErrorNotify("Confirm password doesn't match the password"));
      return;
    }

    try {
      await Api.post('users/single', userData);
      onCreate();
      dispatch(setNotifyMessage('User created successfully!'));
      onClose();
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message || 'Error creating user'));
    }
  };

  const handleUpdate = async (e: React.FormEvent<HTMLFormElement>) => {
    // e.preventDefault();

    // For update mode, if password is provided, confirm password must match
    if (form.password && confirmPassword !== form.password) {
      setShowError(true);
      dispatch(setErrorNotify("Confirm password doesn't match the password"));
      return;
    }

    try {
      const response = await Api.put(`users/single/${id}`, form);
      if (response?.data.access_token) {
        // @FIXME: Fix local storage
        CookieStorage.setItem('userData', JSON.stringify(response.data));
        dispatch(updateUser(response.data));
        dispatch(handleGetUserRole);
        onCreate();
        dispatch(setNotifyMessage('User updated successfully!'));
        onClose();
      } else {
        onCreate();
        dispatch(setNotifyMessage('User updated successfully!'));
        onClose();
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error.response?.data?.message));
    }
  };

  const handleSearch = (endpoint: string, action: (data: any) => void) => async (keyword: string) => {
    try {
      const result = await Api.get(endpoint, { keyword });
      console.log(result.data);
      action(result?.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    }
  };

  const handleShow = (type: 'pass' | 'confirm') => {
    if (type === 'pass') setShowPassword(!showPassword);
    else setShowConfirmPassword(!showConfirmPassword);
  };

  // Getters
  const isEditMode = () => !!form._id;

  // On mount
  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, [id]);

  useEffect(() => {
    if (confirmPassword === form.password) setShowError(false);
  }, [confirmPassword, form.password]);

  return (
    <Dialog size="lg" isOpen title={id ? 'Edit User' : 'Create User'} onClose={onClose}>
      {/* Creation Form */}

      <Form onSubmit={isEditMode() ? (event: any) => handleUpdate(event) : (event: any) => handleInsert(event)}>
        <div className="grid gap-4">
          <div className="flex flex-col gap-2">
            <TextInput
              label="Name"
              name="Name"
              placeholder="Name"
              value={form.name}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'name', value }))}
              validators={[isRequired(), validateRegex(Regex.name), minLength(2), maxLength(50), isNotSpaces()]}
              requiredLabel
            />

            <div className="my-1">
              <RadioGroup
                requiredLabel
                name="gender"
                label="Gender"
                value={form.gender}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'gender', value, type: Number }))}
                className="text-inputLabel dark:text-inputDarkLabel"
                direction="row"
                lookup="$Gender"
                params={{}}
                showSingleClear={false}
                handleSingleClear={() => {}}
              />
            </div>
            <TextInput
              name="email"
              label="Email"
              placeholder="Email"
              value={form.email}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'email', value }))}
              validators={[isRequired(), validateRegex(Regex.email)]}
              requiredLabel
            />
            <div className="flex w-full relative">
              <div className="w-full">
                <TextInput
                  label="Password"
                  name="password"
                  placeholder={isEditMode() ? 'Password (optional)' : 'Password'}
                  autoComplete="new-password"
                  type={showPassword ? 'text' : 'password'}
                  value={form.password}
                  rightIcon={() => {}}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'password', value }))}
                  validators={
                    !isEditMode()
                      ? [isRequired(), isNotSpaces(), minLength(8), maxLength(50), validatePasswordRegex(Regex.password)]
                      : form.password
                      ? [isNotSpaces(), minLength(8), maxLength(50), validatePasswordRegex(Regex.password)]
                      : []
                  }
                  requiredLabel={!isEditMode()}
                />
              </div>

              <div className="mt-[29px] absolute right-1" onClick={() => handleShow('pass')}>
                <Icon
                  className="ml-3 p-5 w-8 h-8 rounded-md cursor-pointer text-[#4E5E82]"
                  width="20"
                  icon={!showPassword ? 'lucide:eye-off' : 'lucide:eye'}
                />
              </div>
            </div>
            <div className="flex w-full relative">
              <div className="w-full">
                <TextInput
                  label="Confirm Password"
                  name="confirm password"
                  placeholder={isEditMode() ? 'Confirm password (optional)' : 'Confirm password'}
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={confirmPassword}
                  onChange={(value: string) => setConfirmPassword(value)}
                  disabled={!form.password}
                  requiredLabel={!isEditMode()}
                  rightIcon={() => {}}
                  validators={
                    !isEditMode()
                      ? [isRequired(), isNotSpaces(), minLength(8), maxLength(50), validatePasswordRegex(Regex.password)]
                      : form.password
                      ? [isNotSpaces(), minLength(8), maxLength(50), validatePasswordRegex(Regex.password)]
                      : []
                  }
                />
              </div>

              <div className="mt-[29px] absolute right-1" onClick={() => handleShow('confirm')}>
                <Icon
                  className="ml-3 p-5 w-8 h-8 rounded-md cursor-pointer text-[#4E5E82]"
                  width="20"
                  icon={!showConfirmPassword ? 'lucide:eye-off' : 'lucide:eye'}
                />
              </div>
            </div>
            {(confirmPassword && confirmPassword !== form.password) || showError ? (
              <label className="text-red-500 text-sm">Confirm password doesn't match the password</label>
            ) : null}
            <div className="space-y-3">
              <Select
                label="Role"
                name="role"
                value={form.roles}
                lookup={roles}
                optionValueKey="_id"
                optionLabelKey="viewName"
                placeholder="Find roles by name..."
                onSearch={handleSearch('roles/single/search', setRoles)}
                dropIcon={true}
                onChange={(event: string) => {
                  const [selectedRole] = roles.filter((singleRole) => singleRole?._id === event);
                  dispatch(setFieldValue({ path: 'roleId', value: selectedRole?._id }));
                  dispatch(setFieldValue({ path: 'roleName', value: selectedRole?.name }));
                  dispatch(setFieldValue({ path: 'roleViewName', value: selectedRole?.viewName })); // NB: This has no use here, it is used only with the optionLabelKey
                }}
                requiredLabel
                validators={[isRequired()]}
              />
            </div>
          </div>
          {form.roleName === 'content-creator' && (
            <div>
              <Select
                label="Track"
                name="track"
                value={form.track}
                lookup="category"
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon={true}
                onChange={(selectedTrack: string) => {
                  dispatch(setFieldValue({ path: 'track', value: selectedTrack }));
                }}
                validators={[isRequired()]}
                creationOptions={{
                  url: 'lookups/category/single',
                  fieldName: 'name',
                  validation: Regex.categorySubcategoryTopic,
                }}
                requiredLabel
              />
            </div>
          )}
        </div>

        <Button className="w-full  mt-2" type="submit" colorType="primary" label={`${!!id ? 'Update' : 'Create'}`} />
      </Form>
    </Dialog>
  );
};
