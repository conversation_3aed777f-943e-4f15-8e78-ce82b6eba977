export interface ApplicantType {
  _id: string;
  name: string;
  email: string;
  phone: string;
  seniorityLevel: number;
  createdAt: string;
}

export type ApplicantItemList = ApplicantType & {
  difficulty: number;
  averageScore: number;
  status: number;
  authorId: string;
  authorName: string;
  category: string[];
  subCategory: string[];
  lastAssessment: {};
};

export interface ApplicantList {
  items: ApplicantItemList[];
  count: number;
}

export interface ApplicantAssessment {
  applicant: ApplicantType;
  submissionCount: number;
  phoneScreeningTests: number;
  interviewsCount: number;
  totalMissed: number;
  totalWeirdBeauvoir: number;
  averageAttitudeScore: number;
  averageEnglishScore: number;
}