// Core
import { Icon } from 'src';
import { Button, CurrentUserRole, setFeatureEnded } from 'UI';
import { RootState, setThemeColor, useAppDispatch, useAppSelector, UserData } from 'UI/src';

export const StudioPage = () => {
  // Hooks
  const userRole: CurrentUserRole = useAppSelector((state: RootState) => state.auth.userRole);

  const themeColor = useAppSelector((state: RootState) => state.app.themeColor);
  const dispatch = useAppDispatch();
  const isFeatureEnded = useAppSelector((state: RootState) => state.featureEnded.isFeatureEnded);

  const newMode = (): 'light' | 'dark' => {
    if (themeColor === 'light') {
      return 'dark';
    }
    return 'light';
  };

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  return (
    <div className="space-y-4 text-red-700 dark:text-cyan-400">
      <p>Studio for testing purpose only</p>

      <Icon icon="emojione:turtle" width={'40'} className="!justify-start" />

      <Button variant="sm" label="userData" onClick={() => console.log('userData:- ', userData)} />

      <Button variant="sm" label="userRole" onClick={() => console.log('userRole:- ', userRole)} />

      <div onClick={() => console.log(isFeatureEnded)}>isFeatureEnded</div>
      <div onClick={() => dispatch(setFeatureEnded(!isFeatureEnded))}>setFeatureEnded</div>

      {/*  <Button
        label="Dark theme"
        icon={themeColor ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'}
        onClick={() => dispatch(setThemeColor(newMode()))}
      /> */}
    </div>
  );
};
