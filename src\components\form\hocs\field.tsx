import { useEffect, useState } from 'react';
import { setFieldValue, useAppDispatch, useAppSelector } from 'UI/src';
import { useValidate } from 'UI/src/hooks';

type Props = {
  name: string;
  value: string | number | boolean | null;
  validators?: any[];
};

export const asField =
  (Field: React.ComponentType<any>) =>
  ({ name, validators, ...props }: { name: string; validators?: any[]; [key: string]: any }) => {
    // Redux
    const dispatch = useAppDispatch();
    const value = useAppSelector((state) => state.form.data[name]);

    // State
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    // Hooks
    const { validate } = useValidate();

    // Handle change
    const handleChange = (val: any): void => {
      dispatch(setFieldValue({ path: name, value: val }));
      const error = validate(val, (validators as any) || []);
      setErrorMessage(error || null);
    };

    useEffect(() => {
      if (value !== undefined && value !== null) setErrorMessage(null);
    }, [value]);

    return <Field name={name} value={value} errorMessage={errorMessage} onChange={handleChange} {...props} />;
  };
