// React
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// React Suite
import 'rsuite/dist/rsuite-no-reset.min.css';

// Components
import { Table, Icon, SubscribeDialog, SidebarFilterDrawer, NameFieldColumn, EmailFieldColumn, TestSeniorityLevel, AvarageScore } from 'src';

// Components
import { ApplicantsSingleDialog } from '../components/single-dialog';
import { AssignScreening } from '../components/assign-screening';
import { AssignTest } from '../components/assign-test';
import { AssignIntreview } from '../components/assign-interview';
import { ResultStatusApplicant } from 'src/components/shared-functions/result-status';
import { Api, hideConfirm, PlanFeatures, showConfirm, useFetchList, useScreenSize, Tags } from 'UI/src';
import { initializeForm } from 'UI/src';
import { setNotifyMessage, setErrorNotify, UserPermissions } from 'UI';

/*
  drawerFilter={{
    element,             : UI Component
    count,               : All results
    filterCountNumber,   : Number of selected filters
    drawerClearAll,      : Form clear all function
    isShowDrawerFilter,  : State of toggle
    setShowDrawerFilter, : Fuction of toggling
  }}
*/

import { RootState, useAppSelector, useAppDispatch, UserData } from 'UI/src';

import {
  setCreateDialogVisible,
  setNeedSubscription,
  setAssignTestVisible,
  setScreeningVisible,
  setAssignInterviewTestVisible,
  setApplicantDetails,
  setSelectedIds,
  setBackupList,
  setHandleGet,
  setShowMoreMap,
  setShowDrawerFilter,
} from 'UI/src/slices/applicants/applicants.slice';

interface initialFiltersTypes {
  difficulty?: {
    label: string;
    enum: string;
  };
  seniorityLevel: {
    label: string;
    enum: string;
  };
  averageScore: {
    label: string;
    enum: string;
  };
}

export const ApplicantsListPage = () => {
  // Hook
  const screen = useScreenSize();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // Redux state
  const {
    isCreateDialogVisible,
    needSubscription,
    isAssignTestVisible,
    isScreeningVisible,
    isAssignInterviewTestVisible,
    applicantDetails,
    // handleGet,
    selectedIds,
    showMoreMap,
    backupList,
    isShowDrawerFilter,
  } = useAppSelector((state: RootState) => state.applicants);
  const ORIGIN = window.location.origin;

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const isPermitted = Array.isArray(userData?.role) && userData?.role.some((role: string) => ['super-admin', 'admin', 'hr'].includes(role));
  // @FIXME: Needs to improve this logic
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.some((role: string) => ['super-admin'].includes(role));

  // Hooks
  const initialFilters: initialFiltersTypes = {
    ...(userData.trackId
      ? {}
      : {
        category: {
          label: 'Interest',
          lookup: 'category',
        },
      }),
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
    seniorityLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },
    averageScore: {
      label: 'Average Score',
      enum: 'AverageScore',
    },
  };
  const filterFeedData = Object.keys(initialFilters).map((key: string) => (key === 'difficulty' ? initialFilters.difficulty?.enum || key : key));

  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh } = useFetchList('applicants/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  });

  const ConfirmText = (value: number | null) => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="hugeicons:archive-02" className="text-[#9061F9]" width="40" />
          </div>
        </div>
        {value ? (
          <p>
            Once confirmed, {value} applicant{value > 1 && 's'} will be archived permanently!
          </p>
        ) : (
          <p>Once confirmed, This applicant will be archived permanently!</p>
        )}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '—';

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      day: 'numeric',
      month: '2-digit',
      year: 'numeric',
    }).format(date);
  };

  const formatTime = (dateString: string) => {
    if (!dateString) return '';

    const date = new Date(dateString);
    const hours = date.getHours();
    const minutes = date.getMinutes();

    const period = hours >= 12 ? 'PM' : 'AM';
    const formattedHours = hours % 12 || 12;
    const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;

    return `${formattedHours}:${formattedMinutes} ${period}`;
  };

  // Delete Applicant
  const handleDelete = async (row: { _id: string }) => {
    dispatch(
      showConfirm({
        message: ConfirmText(null),
        options: {
          onConfirm: async () => {
            try {
              await Api.delete(`applicants/single/${row._id}`);
              dispatch(hideConfirm());
              refresh();
              dispatch(setNotifyMessage('Applicant deleted successfully!'));
            } catch (error: any) {
              dispatch(hideConfirm());
              dispatch(setErrorNotify(error.response.data.message));
            }
          },
        },
      })
    );
  };

  // Delete all selected ids
  const handleArchiveSelectedIds = async () => {
    if (selectedIds.length) {
      dispatch(
        showConfirm({
          message: ConfirmText(selectedIds.length),
          options: {
            onConfirm: async () => {
              try {
                setLoading(true);
                await Api.delete('applicants/multi', { ids: selectedIds });
                dispatch(setSelectedIds([]));
                refresh();
                dispatch(setNotifyMessage('Applicants deleted successfully!'));
              } catch (error: any) {
                dispatch(setErrorNotify(error.response.data.message));
              } finally {
                dispatch(hideConfirm());
                setLoading(false);
              }
            },
          },
        })
      );
    }
  };

  const downloadDocument = async (id: string, type: string) => {
    try {
      const response = await Api.get(`${type === 'interview' ? 'ai-interview' : 'submissions'}/stages/report/${id}`, {
        responseType: 'blob',
      });
      console.log('ai-interview', response.data);
      const url = window.URL.createObjectURL(
        new Blob([response.data], {
          type: response.headers['content-type'],
        })
      );
      const a = document.createElement('a');
      a.href = url;
      a.download = `${type === 'submission' ? 'test' : type}-report.pdf`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      dispatch(setErrorNotify(error.response?.data.message));
    }
  };

  /* === Drawer filter === */

  const clearFilter = () => {
    // resetForm();
    setFilters({});
  };

  const onCloseDialog = () => {
    dispatch(setCreateDialogVisible(false));
    dispatch(initializeForm({}));
  };

  return (
    <>
      {/* <div className={`${ready && '2xl:pl-72'}`}> */}
      <Table
        ready={ready}
        loading={loading}
        title="Applicants"
        addButtonLabel={isPermitted ? 'Create Applicant' : ''}
        searchPlaceholder={screen.customScreen ? 'Search by name or email...' : 'Name, email or mobile'}
        count={count}
        search={search}
        filters={filters}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        addButtonPermission={PlanFeatures.APPLICANTS}
        onClickAdd={() => {
          dispatch(setCreateDialogVisible(true));
          dispatch(setHandleGet(false));
        }}
        addButtonUserPermission={UserPermissions.CREATE_APPLICANT}
        slots={{
          applicantName: (_: any, row: any) => (
            <NameFieldColumn
              id={row?._id}
              name={row?.name}
              showMoreMap={showMoreMap}
              onClick={() => navigate(`/app/applicants/progress/${row?._id}`)}
            />
          ),
          applicantEmail: (_: any, row: any) => <EmailFieldColumn email={row?.email} onClick={() => { }} />,
          seniorityLevel: (_: any, row: any) => {
            const getSeniorityLevelText = (level: number): string => {
              switch (level) {
                case 1:
                  return 'intern';
                case 2:
                  return 'fresh';
                case 3:
                  return 'junior';
                case 4:
                  return 'mid-level';
                case 5:
                  return 'senior';
                default:
                  return '-';
              }
            };

            return (
              <div className="w-fit">
                <Tags type={getSeniorityLevelText(row?.seniorityLevel)} color="bg-transparent" />
              </div>
            );
          },
          status: (_: any, row: any) => {
            const getStatusText = (status: any): string => {
              if (status === 1) {
                return 'available';
              } else if (status === 2) {
                return 'busy';
              } else {
                return 'busy'; // default case
              }
            };

            const getStatusColor = (status: any): string => {
              if (status === 1) {
                return 'text-[#009217]';
              } else {
                return 'text-red-800';
              }
            };

            const getStatusIcon = (status: any) => {
              if (status === 1) {
                return <div className="w-1.5 h-1.5 bg-[#009217] rounded-full"></div>;
              } else {
                return <div className="w-1.5 h-1.5 bg-red-500 rounded-full"></div>;
              }
            };

            return (
              <div className="w-fit">
                <Tags type={getStatusText(row?.status)} color={getStatusColor(row?.status)} icon={getStatusIcon(row?.status)} />
              </div>
            );
          },
          averageScore: (_: any, row: any) => {
            const getScoreColor = (score: number) => {
              if (score >= 0 && score < 50) {
                return 'bg-[#FFECE9] text-[#A80000]';
              } else if (score >= 50 && score < 75) {
                return 'bg-[#FFEDD8] text-[#E9760F]';
              } else if (score >= 75 && score < 100) {
                return 'bg-[#FFFCDF] text-[#BA8500]';
              } else if (score >= 100) {
                return 'bg-[#EEFFF1] text-[#056816]';
              }
              return 'bg-gray-100 text-gray-800';
            };

            const getScoreText = (score: number) => {
              if (score === null || score === undefined) return '—';
              return `${score}%`;
            };

            return (
              <div className="w-fit">
                <Tags type="score" color={getScoreColor(row?.averageScore)}>
                  {getScoreText(row?.averageScore)}
                </Tags>
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'applicantName',
            label: 'Name',
            primary: true,
            width: '22%',
          },
          {
            key: 'applicantEmail',
            label: 'Email',
            primary: true,
            width: '23%',
            className: 'w-full',
          },
          {
            key: 'seniorityLevel',
            label: 'Seniority Level',
            primary: true,
            width: '18%',
            inline: true,
          },
          {
            key: 'status',
            label: 'Status',
            // primary: true,
            width: '19%',
            inline: true,
          },
          {
            key: 'averageScore',
            label: 'Average Score',
            // primary: true,
            width: '17%',
            cardFooterProp: true,
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_: any, row: { _id: string }) {
              //@TODO: Markos will fix this
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/applicants/progress/${row._id}/tests`,
                },
              ];
            },
          },
        ]}
        groups={[
          {
            name: 'group1',
            keys: [['applicantName'], ['status', 'seniorityLevel']], // from table
          },
        ]}
        placeholder={{
          title: 'No applicants created yet',
          subTitle: 'Add applicants to start building your candidate pipeline.',
          image: '/UI/src/assets/placeholder/NoUsers.svg',
        }}
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
      />

      {/* Create new applicant */}
      {isCreateDialogVisible && (
        <ApplicantsSingleDialog
          onClose={onCloseDialog}
          onCreate={refresh}
          id=""
        /* FIXME: Fix handleGet (Edit removed for now as there is no edit button exist) */
        // id={handleGet.toString()}
        />
      )}

      {/* Need Subscription */}
      {needSubscription && <SubscribeDialog onClose={() => dispatch(setNeedSubscription(false))} />}

      {/* Assign test */}
      {isAssignTestVisible && (
        <AssignTest
          // isAssignTestVisible={isAssignTestVisible} // not defined in props
          setAssignTestVisibility={(value: boolean) => dispatch(setAssignTestVisible(value))}
          applicantDetails={applicantDetails}
          setApplicantDetails={(value: any) => dispatch(setApplicantDetails(value))}
          refreshMainTable={refresh}
        />
      )}

      {/* Assign screening */}
      {isScreeningVisible && (
        <AssignScreening
          isAssignTestVisible={isScreeningVisible}
          setAssignTestVisibility={(value: boolean) => dispatch(setScreeningVisible(value))}
          applicantDetails={applicantDetails}
          setApplicantDetails={(value: any) => dispatch(setApplicantDetails(value))}
          refreshMainTable={refresh}
        />
      )}

      {/* Assign ai-interview */}
      {isAssignInterviewTestVisible && (
        <AssignIntreview
          applicantDetails={applicantDetails}
          // setApplicantDetails={setApplicantDetails} // not defined in props
          onClose={() => {
            dispatch(setAssignInterviewTestVisible(false));
            dispatch(setApplicantDetails(null));
          }}
          onCreate={() => { }}
          refreshMainTable={refresh}
        />
      )}

      {/* Filter Drawer */}
      {isShowDrawerFilter && (
        <SidebarFilterDrawer
          drawerFilter={{
            // element: drawerFilter,
            // count: count,
            drawerClearAll: clearFilter,
            // isShowDrawerFilter: isShowDrawerFilter,
            setShowDrawerFilter: (value: boolean | ((prevState: boolean) => boolean)) => {
              const newValue = typeof value === 'function' ? value(isShowDrawerFilter) : value;
              dispatch(setShowDrawerFilter(newValue));
            },
            // isAnyFilterApplied: isAnyFilterApplied,
          }}
          filterData={{
            filterFeedData,
            setFilters,
          }}
        />
      )}
    </>
  );
};
