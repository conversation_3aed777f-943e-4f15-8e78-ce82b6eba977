import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Toolt<PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { PlaceholderMessage } from '../../components/placeholder-message';

interface TrackType {
  _id: string;
  count: number;
}

interface CustomTooltip {
  user?: {
    contentCreatorUsers: number;
    contentCreatorTracks: TrackType[];
  };
  active?: boolean;
  payload?: any;
}

// Custom Tooltip to show additional details
const CustomTooltip = ({ user, active, payload }: CustomTooltip) => {
  if (active && payload && payload.length) {
    const { _id, count } = payload[0].payload;

    return (
      <div className="bg-white dark:bg-[#181720] p-2 border border-gray-300 rounded shadow dark:border-none">
        {_id === 'Content Creator' ? (
          <div>
            <p className="font-semibold text-gray-700 dark:text-gray-300 mb-2">{`Content Creator: ${user?.contentCreatorUsers} ${
              user?.contentCreatorUsers === 1 ? 'user' : 'users'
            }`}</p>
            {user?.contentCreatorTracks?.map((track: TrackType) => (
              <p key={track._id} className="text-gray-600 text-base dark:text-gray-400">
                {`${track._id}: ${track.count} ${track.count === 1 ? 'user' : 'users'}`}
              </p>
            ))}
          </div>
        ) : (
          <p className="font-semibold text-gray-700 dark:text-gray-300">{`${_id}: ${count} users`}</p>
        )}
      </div>
    );
  }
  return null;
};

// Custom render for Legend to include percentages, icons, and styling
const renderLegend = (props: any) => {
  const { payload } = props;
  return (
    <ul className="flex flex-wrap justify-center mt-4">
      {payload?.map((entry: { color: string; payload: { _id: string; value: number } }, index: number) => {
        return (
          <li key={`item-${index}`} className={`flex items-center mx-2 text-sm  text-gray-700 dark:text-gray-300`}>
            <div style={{ backgroundColor: entry.color }} className="w-4 h-4 mr-2 rounded-full"></div>
            <span className={`mr-2  dark:text-gray-300}`} style={{ color: entry.color }}>
              {entry.payload._id}
            </span>
            <span className="text-gray-900 dark:text-gray-400 font-bold">{`${entry.payload.value}%`}</span>
          </li>
        );
      })}
    </ul>
  );
};

export const RoleDistribution = ({ chartTitle, user }: { chartTitle: string; user: any }) => {
  const roleColorMap = {
    'Content Creator': '#fbcfe8', // Yellow
    'Super Admin': '#e9d5ff', // Blue
    Admin: '#e9d5ff', // Blue
    HR: '#bfdbfe', // Green
  };

  const userRolesWithColors = user?.userStatistics?.map((role: { _id: string }) => ({
    ...role,
    color: roleColorMap[role._id as keyof typeof roleColorMap] || 'black',
  }));

  return (
    <div className="flex flex-col bg-white rounded-lg shadow-sm border border-gray-200 h-full dark:bg-[#3E3D4B] dark:border-none text-white">
      <h2 className="text-lg font-semibold pt-4 px-4 dark:text-white text-black">{chartTitle}</h2>
      <div className="flex-grow">
        <ResponsiveContainer width="100%" height={250}>
          {userRolesWithColors && userRolesWithColors.length !== 0 ? (
            <PieChart>
              <Pie data={userRolesWithColors} cx="50%" cy="50%" labelLine={false} outerRadius="90%" fill="#bfdbfe" dataKey="value">
                {userRolesWithColors?.map((statistic: { _id: string; color: string }) => (
                  <Cell key={statistic._id} fill={statistic?.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip />} />
              <Legend content={renderLegend} layout="horizontal" verticalAlign="bottom" align="center" />
            </PieChart>
          ) : (
            <PlaceholderMessage message="No data available" />
          )}
        </ResponsiveContainer>
      </div>
    </div>
  );
};
