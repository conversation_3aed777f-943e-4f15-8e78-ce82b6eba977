import React from 'react';

import { Checkbox as FlowbiteCheckbox, Label } from 'flowbite-react';

type CheckboxType = {
  name?: string;
  value: boolean;
  label?: string | React.ReactNode;
  onChange?: (value: any) => void;
  fullWidth?: boolean;
  preventSendingMail?: boolean;
  isCustomLabel?: boolean;
  className?: string;
  labelClass?: string;
  theme?: any;
  disabled?: boolean;
  outlineDesign?: boolean;
};

export const Checkbox = ({
  name,
  value,
  label,
  labelClass,
  className,
  onChange,
  fullWidth,
  preventSendingMail,
  isCustomLabel,
  outlineDesign = false,
  ...props
}: CheckboxType) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange && e.target) {
      onChange(e.target?.checked as boolean);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <FlowbiteCheckbox id={name} checked={value} onChange={handleChange} {...props} />
      <Label htmlFor={name} className={`${fullWidth && 'w-full'} ${preventSendingMail ? 'cursor-not-allowed opacity-50' : ''} ${labelClass}`}>
        {isCustomLabel ? (
          <div className="border border-[#DEDEDE] py-5 pl-4 pr-1 rounded-lg dark:border-gray-500">
            <span className="inputsLabel"> {label} </span>
          </div>
        ) : (
          <span className={`inputsLabel ${labelClass}`}> {label} </span>
        )}
      </Label>
    </div>
  );
};
