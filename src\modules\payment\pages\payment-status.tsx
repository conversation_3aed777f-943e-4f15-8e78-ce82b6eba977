// React
import { useEffect } from 'react';
import { Api } from 'UI/src';
import { RootState, SubscriptionType, updateUser, useAppSelector, UsersListItem } from 'UI/src';

// Core
import { Button } from 'src';
import { setErrorNotify, CookieStorage, useUserPermissions } from 'UI';

// Components
import { useAppDispatch } from 'UI/src';
import { useNavigate, useParams } from 'react-router-dom';

export const PaymentStatusPage = () => {
  // Hooks
  const navigate = useNavigate();
  const userData: UsersListItem = useAppSelector((state: RootState) => state.auth.user);
  const { paymentStatus, subscriptionId } = useParams();

  const dispatch = useAppDispatch();
  const { handleGetUserRole } = useUserPermissions();

  useEffect(() => {
    const verifyPayment = async () => {
      try {
        if (paymentStatus === '2' && subscriptionId) {
          const { data } = await Api.get<SubscriptionType>(`subscription/single/${subscriptionId}`, {});

          console.log('subscription/single', data);
          const updatedUser = {
            ...userData,
            features: (data as any)?.features,
          };

          dispatch(updateUser(updatedUser));
          // @FIXME: Fix local storage
          CookieStorage.setItem('userData', JSON.stringify(updatedUser));
          dispatch(handleGetUserRole);
        }
      } catch (err: any) {
        console.error(err);
        dispatch(setErrorNotify('Failed to verify subscription.'));
      }
    };
    verifyPayment();
  }, [paymentStatus, subscriptionId]);

  const svg = {
    check: (
      <svg width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="5" y="5" width="60" height="60" rx="30" fill="#F0E7FF" />
        <rect x="5" y="5" width="60" height="60" rx="30" stroke="#F8F4FF" strokeWidth="10" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M35.0143 35.7618L43.5566 27.2195C43.8257 26.9504 44.2001 26.8126 44.5797 26.8438C44.9593 26.8737 45.3064 27.0687 45.53 27.3781C45.53 27.3781 45.5625 27.4249 45.5651 27.4275C47.0965 29.5608 48 32.1751 48 35C48 42.1747 42.1747 48 35 48C27.8253 48 22 42.1747 22 35C22 27.8253 27.8253 22 35 22C37.0748 22 39.0378 22.4875 40.7785 23.3546C41.422 23.6731 41.6833 24.4544 41.3635 25.0966C41.0437 25.7388 40.2637 26.0014 39.6215 25.6816C38.2292 24.9887 36.6588 24.6 35 24.6C29.2605 24.6 24.6 29.2605 24.6 35C24.6 40.7395 29.2605 45.4 35 45.4C40.7395 45.4 45.4 40.7395 45.4 35C45.4 33.2762 44.9801 31.6486 44.2352 30.2173L35.9347 38.5191C35.4264 39.0274 34.6035 39.0274 34.0952 38.5191L30.1952 34.6191C29.6882 34.1121 29.6882 33.2879 30.1952 32.7809C30.7035 32.2739 31.5264 32.2739 32.0347 32.7809L35.0143 35.7618Z"
          fill="#9E6BF5"
        />
      </svg>
    ),
    close: (
      <svg width="66" height="66" viewBox="0 0 66 66" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="5" y="5" width="56" height="56" rx="28" fill="#FAD8D8" />
        <rect x="5" y="5" width="56" height="56" rx="28" stroke="#FEF5F5" strokeWidth="10" />
        <path
          d="M35.9 23H30.1C29.42 23 28.46 23.4 27.98 23.88L23.88 27.98C23.4 28.46 23 29.42 23 30.1V35.9C23 36.58 23.4 37.54 23.88 38.02L27.98 42.12C28.46 42.6 29.42 43 30.1 43H35.9C36.58 43 37.54 42.6 38.02 42.12L42.12 38.02C42.6 37.54 43 36.58 43 35.9V30.1C43 29.42 42.6 28.46 42.12 27.98L38.02 23.88C37.54 23.4 36.58 23 35.9 23Z"
          stroke="#F13E3E"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M29.5 36.5L36.5 29.5" stroke="#F13E3E" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        <path d="M36.5 36.5L29.5 29.5" stroke="#F13E3E" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ),
  };

  const data = {
    /*
      1: Free
      2: Paid
      3: Failed
      4: Wrong
    */

    // 1: {
    //   header: 'Welcome to The Pass!',
    //   description: 'Your free plan is now active. Enjoy access to basic features of our recruitment platform.',
    //   svg: svg.check,
    //   buttonLabel: 'Explore your Dashboard',
    //   buttonStyle: 'fullWidth',
    //   path: '/app',
    // },
    2: {
      header: 'Subscription Activated',
      description: 'Activation completed — your tools are ready to use.',
      svg: svg.check,
      buttonLabel: 'Done',
      buttonStyle: undefined,
      path: '/app',
      tertiary: undefined,
    },
    3: {
      header: 'Payment Failed',
      description: 'We couldn’t process your payment. Please check your billing information or try a different payment method.',
      svg: svg.close,
      buttonLabel: 'Try Again',
      buttonStyle: 'fullWidth',
      path: '/pricing',
      tertiary: true,
    },
    // 4: {
    //   header: 'Oops! Something Went Wrong',
    //   description: 'We couldn’t process your payment. Don’t worry — you can try again in a moment.',
    //   svg: svg.close,
    //   buttonLabel: 'Try Again',
    //   path: '/',
    // },
  };

  const selectedFeature = data[(paymentStatus || 3) as 2 | 3];

  return (
    <div className="h-[calc(100vh-64px)] flex justify-center items-center">
      <div className="text-center space-y-8">
        <div className="space-y-2">
          <div className="w-fit mx-auto">{selectedFeature?.svg}</div>
          <h2 className="text-[#07181F] text-xl font-semibold">{selectedFeature?.header}</h2>
          <p className="text-[#626262] text-sm">{selectedFeature?.description}</p>
        </div>
        <Button
          className={selectedFeature?.buttonStyle === 'fullWidth' ? 'w-full' : 'mx-auto'}
          label={selectedFeature?.buttonLabel}
          onClick={() => navigate(selectedFeature?.path)}
          tertiary={selectedFeature?.tertiary}
        />
      </div>
    </div>
  );
};
