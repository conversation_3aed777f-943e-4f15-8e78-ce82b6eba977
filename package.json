{"name": "techpass", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "prepare": "husky"}, "dependencies": {"@babel/runtime": "^7.15.4", "@emran-alhaddad/saudi-riyal-font": "^1.0.3", "@microlink/react-json-view": "^1.24.0", "@radix-ui/react-dropdown-menu": "^2.1.1", "@react-oauth/google": "^0.12.1", "@react-pdf/renderer": "^3.4.4", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.12", "@tsparticles/engine": "^3.5.0", "@uiw/react-md-editor": "^3.23.6", "@wavesurfer/react": "^1.0.6", "apexcharts": "^4.7.0", "axios": "^1.6.8", "chart.js": "^4.4.1", "clear": "^0.1.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dom-to-image": "^2.6.0", "emoji-picker-react": "^4.12.3", "flowbite-react": "^0.12.7", "formik": "^2.4.6", "framer-motion": "^11.3.19", "history": "^5.3.0", "iconify-icon": "^1.0.8", "immer": "^10.0.3", "libphonenumber-js": "^1.11.19", "lucide-react": "^0.509.0", "moment": "^2.29.4", "object-path": "^0.11.8", "postcss-load-config": "^3.1.4", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-audio-player": "^0.17.0", "react-audio-voice-recorder": "^2.2.0", "react-chartjs-2": "^5.2.0", "react-circular-progressbar": "^2.2.0", "react-click-away-listener": "^2.2.3", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-icons": "^5.5.0", "react-json-view-lite": "^2.3.0", "react-media-recorder": "^1.6.6", "react-particles": "^2.12.2", "react-phone-input-2": "^2.15.1", "react-player": "^2.16.0", "react-redux": "^9.2.0", "react-router-dom": "^6.26.2-pre.0", "react-sound-visualizer": "^1.1.1", "react-toastify": "^11.0.5", "react-unity-webgl": "^9.5.2", "recharts": "^2.12.0", "redux-persist": "^6.0.0", "redux-persist-transform-encrypt": "^5.1.1", "rsuite": "^5.72.0", "sweetalert2": "^11.10.5", "tailwindcss": "^4.0.0-alpha.25", "three": "^0.166.1", "tsparticles": "^3.5.0", "use-immer": "^0.9.0", "wavesurfer": "^1.3.4", "wavesurfer.js": "^7.8.0", "yup": "^1.6.1", "zod": "^3.24.1"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@types/node": "^22.10.7", "@types/object-path": "^0.11.4", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@types/three": "^0.177.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "husky": "^9.1.7", "lint-staged": "^16.1.2", "stylelint": "^16.23.0", "stylelint-config-standard": "^39.0.0", "stylelint-config-standard-scss": "^15.0.1", "typescript": "^5.8.3", "vite": "^5.2.0", "vite-bundle-analyzer": "^1.1.0"}, "overrides": {"rollup": "4.15.0"}}