import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Flowbite
import { Tooltip } from 'flowbite-react';

// UI
import { Icon, EnumText } from 'src';
import { ListPlaceholder } from './list-placeholder';

// Types

type QuestionsListItemProps = {
  onRemove: (id: string) => void;
  data: {
    _id: string;
    title: string;
    subCategoryName?: string;
    topicName?: string;
    authorName?: string;
    difficulty: number;
    [key: string]: any;
  } | null;
  showEditIcon?: boolean;
};

export const QuestionsListItem = ({ onRemove, data, showEditIcon = true }: QuestionsListItemProps) => {
  const [showMore, setShowMore] = useState<boolean>(false);
  const navigate = useNavigate();

  // TODO:FIXME: Markos
  // const difficulty = (value: number) => {
  // return <EnumText name={'QuestionDifficulty'} value={value} />;

  const difficulty = (value: number): string => {
    return typeof EnumText === 'function' ? EnumText({ name: 'QuestionDifficulty', value }) : String(value);
  };

  if (!data) {
    return (
      <div>
        <ListPlaceholder />
      </div>
    );
  }

  return (
    <>
      {/* Large screen */}
      <div className="text-gray-500 dark:text-gray-400 gap-2 hidden lg:grid lg:grid-cols-12 p-4 items-start hover:dark:bg-gray-700">
        <p className="break-words col-span-4 self-center pr-3">{data.title}</p>

        <div className="relative col-span-2">
          <div className="truncate break-all">{data.subCategoryName}</div>
          <Tooltip
            content={data.subCategoryName}
            placement="bottom"
            arrow={false}
            className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit text-sm"
          >
            <div className="absolute top-0 left-0 w-full h-full"></div>
          </Tooltip>
        </div>

        <div className="relative col-span-3">
          <div className="truncate break-all">{data.topicName}</div>
          <Tooltip
            content={data.topicName}
            placement="bottom"
            arrow={false}
            className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit text-sm"
          >
            <div className="absolute top-0 left-0 w-full h-full"></div>
          </Tooltip>
        </div>
        <p className="self-start col-span-2">
          {difficulty(data.difficulty).length > 10 ? difficulty(data.difficulty).substring(0, 10) + ' .....' : difficulty(data.difficulty)}
        </p>
        <div className="flex gap-3 col-span-1">
          {showEditIcon && (
            <Icon width="22" icon="edit" className="text-[#9061F9] cursor-pointer" onClick={() => navigate(`/app/questions/edit/${data._id}`)} />
          )}
          {/* <Icon icon="octicon:trash-16" width={'22'} className="text-[#F05252] cursor-pointer" onClick={() => onRemove(data._id)} /> */}
        </div>
      </div>
      {/* Tablet screen */}
      <section className="text-gray-900 dark:text-white my-3 space-y-4 hidden sm:block lg:hidden">
        <div className="border border-gray-200 dark:border-gray-600 p-3 rounded-lg grid grid-cols-1 gap-3">
          <div className="grid grid-cols-5 gap-2 p-2">
            <p className="col-span-1 text-sm font-medium text-gray-500">Questions Name:</p>
            <p className="col-span-4 break-words">{data.title}</p>
          </div>
          <div className="grid grid-cols-5 p-2">
            <p className="col-span-1 text-sm font-medium text-gray-500">Sub Category:</p>
            {data.subCategoryName}
          </div>
          <div className="grid grid-cols-5 p-2">
            <p className="col-span-1 text-sm font-medium text-gray-500">Topic:</p>
            {data.topicName}
          </div>
          <div className="grid grid-cols-5 p-2">
            <p className="col-span-1 text-sm font-medium text-gray-500">Author:</p>
            <p className="col-span-4 break-words"> {data.authorName}</p>
          </div>
          <div className="grid grid-cols-5 p-2">
            <p className="col-span-1 text-sm font-medium text-gray-500">Actions:</p>
            <div className="flex gap-3">
              {showEditIcon && (
                <Icon icon="edit" width="22" className="text-[#9061F9] cursor-pointer" onClick={() => navigate(`/app/questions/edit/${data._id}`)} />
              )}
              <Icon icon="octicon:trash-16" width="22" className="text-[#F05252] cursor-pointer" onClick={() => onRemove(data._id)} />
            </div>
          </div>
        </div>
      </section>

      {/* Mobile screen */}
      <section
        onClick={!showMore ? () => setShowMore(!showMore) : undefined}
        className={`${
          !showMore && 'cursor-pointer'
        } h-full flex rounded-lg text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600 my-3 space-y-4  sm:hidden `}
      >
        <div onClick={!showMore ? () => setShowMore(!showMore) : undefined} className="w-8 h-8 m-5 flex justify-center items-center cursor-pointer">
          <Icon
            onClick={() => setShowMore(!showMore)}
            className="text-[#8484E1]"
            width="30"
            icon={showMore ? 'ic:round-keyboard-arrow-up' : 'ic:round-keyboard-arrow-down'}
          ></Icon>
        </div>
        <div className={`${showMore ? 'max-h-none' : 'max-h-[45px]'} overflow-hidden grid grid-cols-1 gap-3 pr-2`}>
          <div className="grid grid-cols-1 gap-1 ">
            <p className="text-sm font-medium text-gray-500">Questions Name:</p>
            <p className={showMore ? `break-words` : `truncate`}>{data.title}</p>
          </div>
          <div className="grid grid-cols-1 gap-1 ">
            <p className="text-sm font-medium text-gray-500">Author:</p>
            <p className={showMore ? `break-words` : `truncate`}>{data.authorName}</p>
          </div>
          <div className="grid grid-cols-1 gap-1">
            <p className="text-sm font-medium text-gray-500">Sub Category:</p>
            {data.subCategoryName}
          </div>
          <div className="grid grid-cols-1 gap-1">
            <p className="text-sm font-medium text-gray-500">Topic:</p>
            {data.topicName}
          </div>
          <div className="grid grid-cols-1 gap-1 mb-2">
            <p className="col-span-1 text-sm font-medium text-gray-500">Actions:</p>
            <div className="flex gap-3">
              {showEditIcon && (
                <Icon icon="edit" width="22" className="text-[#9061F9] cursor-pointer" onClick={() => navigate(`/app/questions/edit/${data._id}`)} />
              )}
              <Icon icon="octicon:trash-16" width="22" className="text-[#F05252] cursor-pointer" onClick={() => onRemove(data._id)} />
            </div>
          </div>
        </div>
      </section>
    </>
  );
};
