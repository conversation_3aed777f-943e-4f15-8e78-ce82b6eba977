export const InlineFilter = ({
  data,
  selectedInlineFilter,
}: {
  data: [{ title: string; count: number }];
  selectedInlineFilter?: { activeInlineFilter: number; setActiveInlineFilter: (index: number) => void };
}) => {
  return (
    <div className="w-fit flex rounded-lg border dark:border-gray-500 divide-x dark:divide-gray-500 overflow-hidden">
      {data.map((singleData: { title: string; count: number }, index: number) => (
        <div
          key={singleData?.title}
          className={`flex items-center gap-2.5 px-4 py-3 cursor-pointer ${
            selectedInlineFilter?.activeInlineFilter === index && 'bg-[#f7f3ff] dark:bg-gray-500'
          }`}
          onClick={() => selectedInlineFilter?.setActiveInlineFilter(index)}
        >
          <h1 className="text-[#344054] dark:text-white font-medium">{singleData?.title}</h1>
          <div
            className={`px-2.5 py-0.5 text-[#6941C6] text-sm font-medium border dark:border-gray-500 rounded-full ${
              selectedInlineFilter?.activeInlineFilter === index ? 'bg-[#e4d5ff]' : 'bg-[#f9f5ff] dark:bg-gray-800'
            }`}
          >
            {singleData?.count}
          </div>
        </div>
      ))}
    </div>
  );
};
