import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ModalHeader } from 'flowbite-react';

interface DialogProps {
  children?: React.ReactNode;
  title?: string;
  subtitle?: string;
  modalHeader?: React.ReactNode;
  subModalHeader?: React.ReactNode;
  overflowVisible?: boolean;
  size: string;
  show: boolean;
  popup: boolean;
  onClose: () => void;
  className?: string;
}

export const Dialog = ({ children, title, subtitle, modalHeader, subModalHeader, overflowVisible, className, ...props }: DialogProps) => {
  return (
    <>
      <div className="fixed top-0 left-0 w-screen h-screen bg-black/50 z-50" />
      <Modal {...props} dismissible className={`z-[70] ${className}`}>
        <div className={`pt-2 px-2 xssm:px-4 max-h-screen ${overflowVisible ? 'overflow-y-auto' : 'overflow-x-hidden overflow-y-auto'}`}>
          <ModalHeader>
            <div className="flex flex-wrap items-center gap-1">
              <p className="text-[#374151] dark:text-white text-lg font-semibold">{modalHeader}</p>
              {subModalHeader && subModalHeader}
            </div>
          </ModalHeader>
          {modalHeader && <hr className="mx-2" />}
          <ModalBody className="w-full p-2 pt-4 pb-6 overflow-visible">
            <div className="relative">
              <div>
                {!!title && <h3 className="text-2xl font-semibold text-[#000000] dark:text-white">{title}</h3>}

                {!!subtitle && <p className="text-gray-400">{subtitle}</p>}
              </div>

              {children}
            </div>
          </ModalBody>
        </div>
      </Modal>
    </>
  );
};
