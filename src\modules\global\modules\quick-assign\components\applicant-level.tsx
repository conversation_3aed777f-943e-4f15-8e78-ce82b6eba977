// React
import { useEffect, useState } from 'react';
import { FaUserGraduate, FaUser, FaStar, FaMedal, FaTrophy } from 'react-icons/fa';

// Core
import { Icon } from 'src';
import { Api, useAppDispatch, setFieldValue, QuizDifficulty } from 'UI/src';
import { setNotifyMessage, setErrorNotify } from 'UI';

export const ApplicantLevelAssignPage = ({ formData, disableButtons }: any) => {
  // Destructuring
  const { form } = formData || {};

  // State
  const [loading, setLoading] = useState(false);
  const [seniorityLevelData, setSeniorityLevelData] = useState<any>([]);

  const data = {
    1: {
      icon: <FaUserGraduate className="size-10" />,
      label: 'intern',
      experience: '0-1 year',
      description: 'For recent graduates or career switchers with basic knowledge',
    },
    2: {
      icon: <FaUser className="size-10" />,
      label: 'fresh',
      experience: '1-2 years',
      description: 'Great for professionals building foundational experience ',
    },
    3: {
      icon: <FaStar className="size-10" />,
      label: 'junior',
      experience: '2-3 years',
      description: 'Ideal for professionals who can work independently on complex task',
    },
    4: {
      icon: <FaTrophy className="size-10" />,
      label: 'mid-level',
      experience: '3-5 years',
      description: 'Ideal for professionals who can take ownership of tasks and contribute independently.',
    },
    5: {
      icon: <FaMedal className="size-10" />,
      label: 'senior',
      experience: '5+ years',
      description: 'Perfect for experts who lead teams, manage strategy, and drive impactful decisions.',
    },
  };

  // Redux
  const dispatch = useAppDispatch();

  const handleGetApplicantLevel = async () => {
    try {
      setLoading(true);
      const payload: any = { categoryId: form?.categoryId, subCategoryId: form?.subCategoryId };
      if (form.type === 'interview') payload.type = 'interview';
      const { data } = await Api.get('quizzes/list/seniorityLevel', payload);
      setSeniorityLevelData(data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleGetApplicantLevel();
  }, []);

  useEffect(() => {
    disableButtons.setDisableNextButton(form.seniorityLevel?.length <= 0);
  }, [form.seniorityLevel]);

  return (
    <div className="flex flex-wrap justify-center gap-6">
      {!loading ? (
        seniorityLevelData?.map((singleData: any) => {
          const iconBlock: any = data[singleData?.seniorityLevel as keyof typeof data];
          return (
            <div
              key={singleData?.seniorityLevel}
              className={`w-[370px] text-center p-4 space-y-4 border rounded-lg cursor-pointer ${
                form.seniorityLevel?.includes(singleData?.seniorityLevel) && 'bg-[#F9F6FF] border-[#8D5BF8]'
              }`}
              onClick={() =>
                dispatch(
                  setFieldValue({
                    path: 'seniorityLevel',
                    value: form.seniorityLevel?.includes(singleData?.seniorityLevel)
                      ? form.seniorityLevel?.filter((singleSubCategoryId: any) => singleSubCategoryId !== singleData?.seniorityLevel)
                      : [...form.seniorityLevel, singleData?.seniorityLevel],
                  })
                )
              }
            >
              <div className="w-fit mx-auto p-5 bg-[#F3F4F6] text-[#797B7E] rounded-full">{iconBlock?.icon}</div>
              <p className="text-[22px] font-medium capitalize">{iconBlock?.label} level</p>
              <p className="w-fit px-3 py-0.5 mx-auto text-sm font-medium text-[#333B52] border rounded-full">{iconBlock?.experience} experience</p>
              <p className="w-fit mx-auto text-[#656C86]">{iconBlock?.description} experience</p>
              <div className="w-fit space-y-2 mx-auto">
                {iconBlock?.features?.map((feature: any) => (
                  <div key={feature} className="flex gap-2">
                    <Icon icon="mdi:check" className="mt-0.5 text-[#1DA64F]" />
                    <span>{feature}</span>
                  </div>
                ))}
              </div>

              {QuizDifficulty[form.seniorityLevel]}

              <p className="text-lg font-medium capitalize">{iconBlock?.seniorityLevel}</p>
            </div>
          );
        })
      ) : (
        <div className="size-12 border-y-2 border-purple-500 animate-spin rounded-full" />
      )}
    </div>
  );
};
