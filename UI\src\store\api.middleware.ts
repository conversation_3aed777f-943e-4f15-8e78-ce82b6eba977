import type { Dispatch, MiddlewareAPI } from '@reduxjs/toolkit';
import { type Action, isAction } from 'redux';
import { setIsLoading } from '../slices/app/app.slice';

interface IAction extends Action {
  payload: { status: number; message: string };
}
const apiMiddleware: any = (store: MiddlewareAPI<Dispatch<Action>>) => (next: Dispatch<Action>) => (action: IAction) => {
  if (isAction(action)) {
    // Ensures action has a valid type

    if (action.type.endsWith('/pending')) {
      // store.dispatch(setActionName(action.type.split('/')[0]));
      console.log('🚀 API Request Started:', action.type);
      store.dispatch(setIsLoading(true));
    }

    if (action.type.endsWith('/fulfilled')) {
      console.log('✅ API Request Successful:', action.type);
      store.dispatch(setIsLoading(false));
    }

    if (action.type.endsWith('/rejected')) {
      console.error('❌ API Request Failed:', action.payload);
      store.dispatch(setIsLoading(false));

      if (action.payload.status === 403 && (action.payload as any).response.data.message === 'Forbidden: Invalid token') {
        // @FIXME: Fix local storage
        localStorage.clear();
        location.reload();
      }
    }
  }

  return next(action);
};

export default apiMiddleware;
