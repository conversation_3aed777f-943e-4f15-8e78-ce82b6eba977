import { useState, useEffect } from 'react';

import { setAiIsTyping, useAppDispatch } from '../../';

const useTypingEffect = (text: string, interval = 75) => {
  const [displayedText, setDisplayedText] = useState('');
  const [charIndex, setCharIndex] = useState(0);

  const dispatch = useAppDispatch();

  useEffect(() => {
    if (charIndex < text.length) {
      dispatch(setAiIsTyping(true));
      const timer = setInterval(() => {
        setDisplayedText((prev) => prev + text[charIndex]);
        setCharIndex((prev) => prev + 1);
      }, interval);
      return () => clearInterval(timer);
    } else {
      dispatch(setAiIsTyping(false));
    }
  }, [charIndex, text, interval]);

  return displayedText;
};

export { useTypingEffect };
