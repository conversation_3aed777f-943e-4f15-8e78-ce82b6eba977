import { RootState, useAppSelector, UserData, useScreenSize } from 'UI/src';
import { useLocation, Link, useNavigate } from 'react-router-dom';

import { Icon } from 'src';

interface sidebarProps {
  isDrawerVisible: boolean;
  setIsDrawerVisible: (value: boolean) => void;
}

export const Sidebar = ({ isDrawerVisible, setIsDrawerVisible }: sidebarProps) => {
  // Hooks
  const location = useLocation();
  const screen = useScreenSize();
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);
  const navigate = useNavigate();

  // Computed
  const getActiveClasess = (itemPath: string) => {
    if (location.pathname.includes(itemPath)) {
      return 'bg-gray-100 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-700';
    }
    return '';
  };

  const handleNavigate = (event: React.MouseEvent<HTMLAnchorElement>, path: string) => {
    screen.lt.xl() ? setIsDrawerVisible(false) : {};
    path == '/Contact-us' ? event.preventDefault() : '';
  };

  const handleHashNavigation = (e: React.MouseEvent<HTMLAnchorElement>, path: string) => {
    if (path.includes('#')) {
      e.preventDefault();

      const hash = path.split('#')[1];

      if (location.pathname === '/' || path.startsWith('/#')) {
        const element = document.getElementById(hash);
        if (element) {
          const headerHeight = 80;
          const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
          const offsetPosition = elementPosition - headerHeight;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth',
          });
        }
      } else {
        // Navigate to home page first, then scroll to the section
        navigate('/');

        // Use multiple attempts to find the element with increasing delays
        const attemptScroll = (attempts = 0) => {
          setTimeout(() => {
            const element = document.getElementById(hash);
            console.log(`Sidebar Attempt ${attempts + 1}: Looking for element with id "${hash}"`, element);
            if (element) {
              const headerHeight = 80;
              const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
              const offsetPosition = elementPosition - headerHeight;

              window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth',
              });
            } else if (attempts < 5) {
              // Try again with more delay
              attemptScroll(attempts + 1);
            }
          }, 200 + attempts * 100); // Start with 200ms, increase by 100ms each attempt
        };

        attemptScroll();
      }
    }
  };

  // Items
  const menuItems = [
    {
      label: 'Why Choose Us',
      path: '/#why-choose-us',
      parentPath: '/#why-choose-us',
      icon: 'mdi:lightbulb-outline',
      hide: location.pathname.includes('quick-assign'),
    },
    {
      label: 'Library',
      path: '/programming-test',
      icon: 'mdi:code-tags',
      hide: location.pathname.includes('quick-assign'),
    },
    {
      label: 'Pricing',
      path: '/pricing',
      icon: 'mdi:currency-usd',
      hide: location.pathname.includes('quick-assign'),
    },
    {
      label: 'Contact Us',
      path: '/contact-us',
      icon: 'mdi:phone-outline',
      hide: location.pathname.includes('quick-assign'),
    },
    {
      label: 'Terms of Service',
      path: '/terms',
      icon: 'mdi:file-document-outline',
      hide: location.pathname.includes('quick-assign'),
    },
    {
      label: 'Login',
      path: '/auth/login',
      icon: 'mdi:login',
      showAtMd: false,
      hide: userData?.access_token || location.pathname.includes('quick-assign'),
    },
    {
      label: 'Signup',
      path: '/auth/register',
      icon: 'mdi:register-outline',
      showAtMd: false,
      hide: userData?.access_token || location.pathname.includes('quick-assign'),
    },
    {
      label: 'View Account',
      path: '/auth/login',
      icon: 'mdi:account-circle-outline', // Represents user account or profile
      showAtMd: false,
      hide: !userData?.access_token,
    },
  ];

  return (
    <aside
      id="drawer-navigation"
      className={`fixed top-0 left-0 z-[55] w-64 h-screen pt-14 transition-transform bg-white border-r border-gray-200 dark:bg-darkBackgroundCard dark:border-[#374151] ${
        isDrawerVisible ? '-translate-x-0' : '-translate-x-full'
      } xl:hidden mt-3 bg-lightgraybg`}
      aria-label="Sidenav"
    >
      <div className={`overflow-y-auto py-5 px-3 h-full  bg-lightgraybg dark:bg-darkBackgroundCard`}>
        <ul className="space-y-2">
          {menuItems.map((item) =>
            item.hide || (item.showAtMd && !screen.lt.sm()) ? null : (
              <li key={item.path} className="border-b border-gray-200 dark:border-gray-700">
                <Link
                  to={item.path}
                  onClick={(event) => handleNavigate(event, item.path)}
                  className={`flex items-center p-2 text-base font-medium text-gray-900 rounded-lg dark:text-white group dark:hover:bg-gray-700 hover:bg-gray-100
                    ${getActiveClasess(item.path)}`}
                >
                  <Icon
                    icon={item.icon}
                    className={`${'text-gray-500'} transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white`}
                    width="24"
                  />
                  <span className="ml-2 ">{item.label}</span>
                </Link>
              </li>
            )
          )}
        </ul>
      </div>
    </aside>
  );
};
