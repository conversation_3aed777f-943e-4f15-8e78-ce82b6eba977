import React from 'react';

import MDEditor from '@uiw/react-md-editor';

import { asField } from '../hocs/field';

type RichTextType = {
  name: string;
  value: string;
  label: string;
  subLabel?: string;
  onChange: (value: string) => void;
  height?: string;
  validatorsScroll?: boolean;
  errorMessage?: string;
  editorClassName?: string;
  requiredLabel?: boolean;
  readOnly?: boolean;
};

export const RichText = asField(
  ({
    name,
    value,
    label,
    subLabel = '',
    onChange,
    height = '100%',
    validatorsScroll,
    errorMessage,
    editorClassName = '',
    requiredLabel,
    readOnly,
    ...props
  }: RichTextType | any) => {
    return (
      <div className="space-y-2">
        <p className="text-sm font-normal py-2">
          <span className="text-[#8C939F]  font-medium">{label}</span> <span>{subLabel}</span>
          {requiredLabel && <span className="text-red-600 dark:text-red-800"> *</span>}
        </p>

        <div className={`container min-h-[130px] grid`}>
          <MDEditor
            id={name}
            value={value}
            onChange={onChange}
            {...props}
            height={height}
            scrolltoerror={validatorsScroll && errorMessage && document.getElementById(name)?.scrollIntoView({ behavior: 'smooth', block: 'end' })}
            className={` ${editorClassName} ${errorMessage ? 'border border-red-500 ' : ''} ${
              readOnly ? 'custom-read-only-input cursor-not-allowed' : 'text-gray-600'
            }`}
            preview={readOnly ? 'preview' : 'live'}
            hideToolbar={readOnly}
          />
          {errorMessage && <p className="mt-2 text-sm text-red-600 dark:text-red-500">{errorMessage}</p>}
        </div>
      </div>
    );
  }
);
