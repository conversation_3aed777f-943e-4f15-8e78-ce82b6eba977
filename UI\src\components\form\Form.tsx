import { useSelector } from "react-redux";
import type { RootState } from "../../store";

interface FormProps {
  children: React.ReactNode;
  onSubmit: (formData: Record<string, any>) => void;
  [key: string]: any;
}

export const Form: React.FC<FormProps> = ({ children, onSubmit, ...props }) => {
  const formData = useSelector((state: RootState) => state.form.data);

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    onSubmit(formData);
  };

  return (
    <form onSubmit={handleSubmit} {...props}>
      {children}
    </form>
  );
};

export default Form;