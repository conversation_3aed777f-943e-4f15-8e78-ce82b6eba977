import { type AxiosResponse } from 'axios';
import { api } from './axios';

export const Api = {
  get: <T = any>(endpoint: string, params?: any): Promise<AxiosResponse<T>> => api.get<T>(endpoint, { params }),
  post: (endpoint: string, data: any) => api.post(endpoint, data),
  put: (endpoint: string, data: any) => api.put(endpoint, data),
  delete: (endpoint: string, params?: any) => api.delete(endpoint, { params }),
  call: (method: string, endpoint: string, data: any, params?: any) =>
    api({
      method,
      url: endpoint,
      data,
      params,
    }),
  lookups: {
    get: ({ name, params }: { name: string; params?: any }) => api.get(`/lookups/${name}`, { params }),
    getCustom: ({ name, params }: { name: string; params?: any }) => api.get(name.substring(1), { params }),
  },
};
