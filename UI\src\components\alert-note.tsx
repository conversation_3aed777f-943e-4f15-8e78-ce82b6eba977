// React
import { useNavigate } from 'react-router-dom';

// Core
import { Icon } from '../../../src';

// Format Date
import { format, isValid } from 'date-fns';

export const AlertNote = ({
  message,
  dateMessage,
  icon,
  nav,
}: {
  message?: string;
  dateMessage?: string;
  icon?: string;
  nav?: { label?: string; path?: string };
}) => {
  // Hooks
  const navigate = useNavigate();

  console.log('message', message);

  const formatDate = (customDate?: string | Date) => {
    if (!customDate) return 'Invalid date';
    const date = new Date(customDate);
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, "dd MMMM , yyyy 'At' hh:mm a");
  };

  return (
    <div className="flex justify-between items-center gap-2 bg-[#EAEEF4] px-4 py-2 rounded-xl">
      <div className="flex gap-2">
        <Icon icon={icon || 'solar:info-square-bold'} width="22" className="text-[#7CCCEF]" />

        <div className="flex gap-2">
          <p className="thepassBone text-[#27303A]">Note</p>

          {message && <p className="thepassBthree text-[#2F3F53]">{message}</p>}

          {dateMessage && (
            <div className="flex justify-between items-center gap-1.5">
              <p className="thepassBtwo">
                {dateMessage && new Date(dateMessage).getTime() - new Date().getTime() > 0 ? 'Test link is available till' : 'Expired from'}
              </p>
              <p className="thepassBone">{formatDate(dateMessage)}</p>
            </div>
          )}
        </div>
      </div>

      {!!nav && (
        <p onClick={() => navigate(nav?.path || '/app/add-credits')} className="thepassBone text-[#743AF5] underline cursor-pointer">
          {nav?.label || 'Go to Add Credit'}
        </p>
      )}
    </div>
  );
};
