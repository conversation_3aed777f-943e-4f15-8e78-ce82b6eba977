import { useImmer as useState } from 'use-immer';
import { useCallback, useEffect } from 'react';

import {
  Api,
  AssignmentType,
  AverageScore,
  BillingCycle,
  CurrentStatus,
  Gender,
  Grade,
  InternPhase,
  InterviewType,
  Logs,
  PlanType,
  PricingPeriod,
  QuestionDifficulty,
  QuestionTypeEnum,
  QuizDifficulty,
  Role,
  RoleWithoutSuperAdmin,
  Scope,
  SubmissionDueDate,
  SubmissionStatus,
  SubmissionWarning,
  SubscriptionStatus,
  YesNo,
} from '../';
import { useDebounce, useLookups } from '../';
import { useLocation } from 'react-router-dom';
import { useAppDispatch } from '../';
import { setErrorNotify } from '../slices/notify/notify.slice';

export type filterType =
  | {
      label: string;
      key: string;
      originalKey: any;
      options: {
        value: boolean;
        label: string;
        onChange: (value: boolean) => void;
        reset: () => void;
      }[];
    }
  | undefined;

interface FetchListOptions {
  search?: string;
  exclude?: any[];
  pagination?: { currentPage: number; pagesCount: number; limit: number; total: number };
  id?: string;
  difficulty?: string | number;
  filters?: any;
  props?: any;
}

interface PayloadType {
  search: string;
  pagination: {
    currentPage: number;
    pagesCount: number;
    limit: number;
    total: number;
  };
  startDate: any;
  endDate: any;
  filters?: any;
  exclude?: any[];
  id?: string;
  difficulty?: string | number;
}

export const useFetchList = (endpoint: string, options: FetchListOptions = {}) => {
  // Helpers
  // const generateFiltersState = () => {
  //   if (options.filters) {
  //     return Object.entries(options.filters).reduce((output, [key, filter]) => {
  //       output[key] = Enums[filter.enum].map((item) => item.value);

  //       return output;
  //     }, {});
  //   }

  //   return {};
  // };

  // Hooks
  const location = useLocation();

  // State
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState(null);
  const [count, setCount] = useState(0);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);

  // Search
  const [search, setSearch] = useState(options?.search || '');
  const debouncedValue = useDebounce(search, 500);
  // Questions exclude
  const [exclude, setExclude] = useState(options?.exclude || []);
  // Filters
  const [filters, setFilters] = useState({});

  // Pagination
  const [pagination, setPagination] = useState(options?.pagination || { currentPage: 1, pagesCount: 20, limit: 10, total: 10 });

  const dispatch = useAppDispatch();

  // Methods
  const handleGet = async () => {
    try {
      // Set Loading
      setLoading(true);

      // Payload
      const payload: PayloadType = {
        search,
        pagination,
        startDate,
        endDate,
      };
      if (Object.keys(filters)?.length > 0) {
        payload.filters = filters;
      }
      if (exclude.length > 0) {
        payload.exclude = exclude;
      }
      if (options?.id) {
        payload.id = options.id;
      }
      if (options?.difficulty) {
        payload.difficulty = options.difficulty;
      }
      if (options.props) {
        Object.assign(payload, options.props);
      }

      const response = await Api.get(endpoint, payload);
      console.log(endpoint, ' === ', response.data);
      const { items, count } = response.data;
      setList(items);
      setCount(count);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message || 'An error occurred'));
    } finally {
      setLoading(false);
    }
  };
  const generateSearch = () => ({
    value: search,
    update: setSearch,
  });
  const generateExclude = () => ({
    value: exclude,
    update: setExclude,
  });

  const handleDates = () => ({
    startDate: {
      value: startDate,
      update: setStartDate,
    },
    endDate: {
      value: endDate,
      update: setEndDate,
    },
  });

  const generatePagination = () => ({
    ...pagination,
    update(updates: any) {
      setPagination({
        ...pagination,
        ...updates,
      });
    },
  });
  // const handleFilterChange = (filterName, filterValue) => () =>
  //   setFilters((draft) => {
  //     draft[filterName] = filterValue;
  //   });

  const handleFilterChange = (filterName: string, filterValue: any) => () =>
    /* NB: Immer Draft Object:
     * This is callback Proxy object created by Immer.
     * You can't log the draft directly here
     * use this method: console.log({ ...draft })
     * or use this: JSON.stringify(draft), though it doesn't work well with circular references.
     */

    setFilters((draft: any) => {
      const filterArray = draft[filterName] || []; // Ensure the property is an array

      if (filterArray.includes(filterValue)) {
        // If the value is already in the array, remove it
        draft[filterName] = filterArray.filter((value: any) => value !== filterValue);
      } else {
        // If the value is not in the array, add it
        draft[filterName] = [...filterArray, filterValue];
      }
    });

  const resetFilters = () => () => {
    setFilters((draft: any) => {
      Object.keys(draft).forEach((key: string) => {
        delete draft[key];
      });
    });
    // // Cashing filter
    // localStorage.removeItem('cashed-filters');
    // localStorage.removeItem('cashed-previous-page');
  };

  const resetSingleFilter = (propertyKeyObject: string) => {
    setFilters((draft: any) => {
      delete draft[propertyKeyObject];
    });
  };

  const generateFilters = useCallback(() => {
    const returned: { [key: string]: any } = {
      PlanType: PlanType,
      InterviewType: InterviewType,
      Role: Role,
      QuizDifficulty: QuizDifficulty,
      QuestionDifficulty: QuestionDifficulty,
      AverageScore: AverageScore,
      SubmissionStatus: SubmissionStatus,
      SubmissionDueDate: SubmissionDueDate,
      grade: Grade,
      SubmissionWarning: SubmissionWarning,
      QuestionType: QuestionTypeEnum,
      Gender: Gender,
      YesNo: YesNo,
      Logs: Logs,
      RoleWithoutSuperAdmin: RoleWithoutSuperAdmin,
      Scope: Scope,
      InternPhase: InternPhase,
      AssignmentType: AssignmentType,
      CurrentStatus: CurrentStatus,
      SubscriptionStatus: SubscriptionStatus,
      BillingCycle: BillingCycle,
      PricingPeriod: PricingPeriod,
    };
    const createOptions = (filter: any, key: string) => {
      if (filter.lookup) {
        let params = null;

        if (filter?.parentLookup && (filters as any)?.[filter?.parentLookup?.key]) {
          const { fieldName, key } = filter.parentLookup;
          params = { [fieldName]: (filters as any)[key] };
        }
        if (filter?.parentLookup && filter?.parentLookup?.fieldValue) {
          const { fieldName, fieldValue } = filter.parentLookup;
          params = { [fieldName]: [fieldValue] };
        }

        /* Add specific params in case of no Authentication  */
        if (filter.techPassProgrammingTest) {
          params = {
            ...params,
            techPassProgrammingTest: true,
          };
        }

        const { lookups } = useLookups(filter.lookup, { params });

        return {
          label: filter.label,
          key: filter.lookup,
          originalKey: key,
          options: lookups.map((option) => ({
            name: `${filter.label}-${option._id}`,
            label: option?.name,
            value: (filters as any)?.[key]?.includes(option._id),
            onChange: handleFilterChange(key, option._id),
            reset: resetFilters(),
            resetSingle: resetSingleFilter,
          })),
        };
      } else if (filter.enum) {
        return {
          label: filter.label,
          key: filter.enum,
          originalKey: key,
          options: Object.keys(returned[filter.enum]).map((key) => {
            return {
              name: `${filter.label}-${returned[filter.enum][key]}`,
              label: key,
              value: (filters as any)[key]?.includes(returned[filter.enum][key]),
              onChange: handleFilterChange(key, returned[filter.enum][key]),
              reset: resetFilters(),
              resetSingle: resetSingleFilter,
            };
          }),
        };
      }
    };

    const generateNestedFilters = (nestedFilters: any) => {
      return Object.entries(nestedFilters).reduce((acc: any[], [nestedKey, nestedFilter]) => {
        acc.push(createOptions(nestedFilter, nestedKey));
        return acc;
      }, []);
    };

    if (options.filters) {
      return Object.entries(options.filters).map(([key, filter]) => {
        return createOptions(filter, key);
      });
    }
    return [];
  }, [options.filters, filters]); // Ensure dependencies are correct to prevent unnecessary re-renders

  // Lifecycle
  useEffect(() => {
    if (pagination.currentPage !== 1) {
      setPagination({ ...pagination, currentPage: 1 });
    }
  }, [debouncedValue, filters]);
  useEffect(() => {
    handleGet();
  }, [debouncedValue, pagination, filters, startDate, endDate]);

  // Subtract one from pagination when delete the last element in page
  useEffect(() => {
    if (pagination?.currentPage > 0 && count > 0 && count <= (pagination?.currentPage - 1) * pagination?.pagesCount) {
      setPagination((prev: any) => ({ ...pagination, currentPage: prev.currentPage - 1 }));
    }
  }, [list]);

  return {
    // Loading
    loading,
    ready: !!list,
    // List
    list: list || [],
    count,
    refresh: handleGet,
    // Search
    search: generateSearch(),
    exclude: generateExclude(),
    handleDates: handleDates(),
    pagination: generatePagination(),
    filters: generateFilters(),
    setFilters: setFilters,

    setPagination,
    setLoading,
    setSearch,
  };
};
