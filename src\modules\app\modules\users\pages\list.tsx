// React
import { useEffect, useState } from 'react';

import type { GetUsersOrganizationRes } from 'UI/src/types/User.type';
import { RootState, useAppSelector, UserData, PlanFeatures } from 'UI/src';

// Core
import { Table, Icon, SidebarFilterDrawer } from 'src';

// Components
import { UsersSIngleDialog } from '../components/single-dialog';

// Flowbite
import { Tooltip } from 'flowbite-react';
import { useFetchList, useScreenSize, useAppDispatch, initializeForm } from 'UI/src';
import { setNotifyMessage, useUserPermissions, UserPermissions } from 'UI';
import { Tags } from 'UI/src/components/tags';

export const UsersListPage = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // State
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState<boolean>(false);
  const [handleGet, setHandleGet] = useState<string | false>(false);
  const [showMoreMap, setShowMoreMap] = useState<Record<string, boolean>>({});
  const [backupList, setBackupList] = useState<GetUsersOrganizationRes[]>([]);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState<boolean>(false);

  // hook
  const screen = useScreenSize();
  const dispatch = useAppDispatch();
  const { hasPermission } = useUserPermissions();

  const initialFilters = {
    role: {
      label: 'Role',
      enum:
        Array.isArray(userData?.role) && userData?.role.some((role: any) => ['admin'].includes(typeof role === 'string' ? role : role.name))
          ? 'RoleWithoutSuperAdmin'
          : 'Role',
    },

    // gender: {
    //   label: 'Gender',
    //   enum: 'Gender',
    // },

    // status: {
    //   label: 'Status',
    //   enum: 'CurrentStatus',
    // },
    // scope: {
    //   label: 'Scope',
    //   enum: 'Scope',
    // },
  };

  const { ready, loading, list, count, filters, setFilters, search, pagination, refresh } = useFetchList('users/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  });

  //   const filterFeedData = Object.keys(initialFilters).map((key) => (key === 'difficulty' ? initialFilters.difficulty.enum : key));
  const filterFeedData = Object.keys(initialFilters)?.map((key) =>
    key === 'difficulty' && (initialFilters as any).difficulty ? (initialFilters as any).difficulty.enum : key
  );

  const clearFilter = () => {
    // resetForm();
    setFilters({});
  };

  const onCloseDialog = () => {
    setCreateDialogVisibility(false);
    dispatch(initializeForm({}));
  };

  useEffect(() => {
    if (backupList.length === 0 && list.length > 0) {
      setBackupList(list);
    }
    // setSidebarSearch(search);
    // setSidebarFilter({ filterFeedData, setFilters });
  }, [list, backupList.length]);

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Users List"
        addButtonLabel="Create User"
        searchPlaceholder={screen.customScreen ? 'Search by user name or email' : 'Name or email'}
        count={count}
        search={search}
        filters={filters}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        onClickAdd={() => {
          setCreateDialogVisibility(true);
          setHandleGet(false);
        }}
        addButtonPermission={PlanFeatures.USERS}
        addButtonUserPermission={UserPermissions.CREATE_USER}
        slots={{
          name: (_: any, row: GetUsersOrganizationRes) => {
            return (
              <div
                className="relative flex gap-2 cursor-pointer"
                onClick={() => {
                  setCreateDialogVisibility(true);
                  setHandleGet(row._id);
                }}
              >
                <div className="w-full">
                  <div className="overflow-auto font-medium text-gray-800 capitalize break-words whitespace-normal text-clip dark:text-grayTextOnDarkMood">
                    <p className={`lg:truncate ${!showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'}`}>{row?.name}</p>
                  </div>
                  {screen.gt.md() && (
                    <Tooltip content={row?.name} placement="bottom" arrow={false} className="text-xs bg-gray-700 dark:bg-gray-200 dark:text-gray-900">
                      <div className="w-[92%] h-full absolute left-0 top-0"></div>
                    </Tooltip>
                  )}
                </div>
              </div>
            );
          },
          email: (_: any, row: GetUsersOrganizationRes) => {
            return (
              <div className="relative flex gap-2">
                <div className="w-full">
                  <div className="relative flex items-center gap-2">
                    <div className="truncate max-w-[85%]">
                      {row?.email ? (
                        <Tooltip
                          content={row.email}
                          placement="bottom"
                          arrow={false}
                          className="text-xs bg-gray-700 dark:bg-gray-200 dark:text-gray-900"
                        >
                          <span className="text-[#626874] dark:text-gray-400 truncate">{row.email}</span>
                        </Tooltip>
                      ) : (
                        <span className="text-[#626874]  dark:text-gray-400">—</span>
                      )}
                    </div>
                    {row?.email && (
                      <span
                        onClick={() => {
                          navigator.clipboard.writeText(row.email);
                          dispatch(setNotifyMessage('Email copied'));
                        }}
                        className="inline-block text-gray-500 cursor-pointer dark:text-gray-400"
                      >
                        <Tooltip
                          content="Copy Email"
                          placement="bottom"
                          arrow={false}
                          className="text-xs bg-gray-700 dark:bg-gray-200 dark:text-gray-900"
                        >
                          <Icon icon="ooui:copy-ltr" className="relative text-[#798296] text-base" />
                        </Tooltip>
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          },
          customRolesName: (_: any, row: GetUsersOrganizationRes) => {
            const roleName = row.roleName;
            let displayName = '';
            let tagColor = '';

            switch (roleName) {
              case 'admin':
                displayName = 'Admin';
                break;
              case 'hr':
                displayName = 'HR';
                break;
              case 'content-creator':
                displayName = 'Content Creator';
                break;
              case 'super-admin':
                displayName = 'Super Admin';
                break;
              default:
                displayName = roleName.charAt(0).toUpperCase() + roleName.slice(1);
            }

            return <Tags type={roleName}>{displayName}</Tags>;
          },
        }}
        columns={[
          {
            key: 'name',
            label: 'Name',
            primary: true,
            width: '20%',
          },
          {
            key: 'email',
            label: 'Email',
            primary: true,
            width: '20%',
          },
          {
            key: 'customRolesName', // 'roles.name'
            label: 'Role',
            primary: true,
            width: '15%',
            inline: true,
          },
          ...(hasPermission(UserPermissions.UPDATE_USER) ? [
            {
              key: 'actions',
              label: 'Actions',
              width: '10%',
              buttons(_: unknown, row: GetUsersOrganizationRes) {
                return [
                  {
                    label: 'Edit',
                    customIcon: 'tableEdit',
                    color: 'text-black dark:text-white',
                    onClick: () => {
                      setCreateDialogVisibility(true);
                      setHandleGet(row._id);
                    },
                  },
                ];
              },
            },
          ] : [])
        ]}
        groups={[
          {
            name: 'group1',
            keys: ['name', 'customRolesName'],
          },
        ]}
        // noDataFound={{
        //   customIcon: 'users',
        //   message: 'No users created yet',
        // }}

        placeholder={{
          title: 'No users created yet',
          subTitle: 'Start by creating users to assign roles and manage your organization.',
          image: '/UI/src/assets/placeholder/NoUsers.svg',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        //  showMoreMap={showMoreMap}
        showMoreMap={undefined}
        setShowMoreMap={setShowMoreMap}
      />

      {isCreateDialogVisible && <UsersSIngleDialog onClose={onCloseDialog} onCreate={refresh} id={handleGet as string} />}

      {/* Filter Drawer */}
      {isShowDrawerFilter && (
        <SidebarFilterDrawer
          drawerFilter={{
            // element: drawerFilter,
            // count: count,
            drawerClearAll: clearFilter,

            // isShowDrawerFilter: isShowDrawerFilter,
            //  setShowDrawerFilter: setShowDrawerFilter,
            // isAnyFilterApplied: isAnyFilterApplied,
            setShowDrawerFilter,
            count,
            isShowDrawerFilter: isShowDrawerFilter,
            isAnyFilterApplied: () => false,
            filterCountNumber: 0,
            element: '',
          }}
          filterData={{
            filterFeedData,
            setFilters,
          }}
        />
      )}
    </>
  );
};
