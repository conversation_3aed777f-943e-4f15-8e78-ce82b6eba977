import http from 'http';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const host = 'localhost';
const port = 3000;

const MIME_TYPES = {
  default: 'application/octet-stream',
  html: 'text/html; charset=UTF-8',
  js: 'application/javascript',
  css: 'text/css',
  png: 'image/png',
  jpg: 'image/jpg',
  gif: 'image/gif',
  ico: 'image/x-icon',
  svg: 'image/svg+xml',
};

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const requestListener = async function (req, res) {
  //   Determine if is it file or route
  const isRoute = !req.url.includes('.');

  // Handle if route
  if (isRoute || req.url === '/') {
    // Set Content Type
    res.writeHead(200, { 'Content-Type': 'text/html' });

    // Get the file
    const file = await fs.readFile(__dirname + `/dist/index.html`);

    // End
    res.end(file);
  } else {
    try {
      const extension = req.url.split('.')[1];
      const contentType = MIME_TYPES[extension || 'default'];

      // Set Content Type
      res.writeHead(200, { 'Content-Type': contentType });

      // Get the file
      const file = await fs.readFile(__dirname + `/dist/${req.url}`);

      // End
      res.end(file);
    } catch (error) {
      res.statusCode = 404;
      res.end('404 - Not Found!');
    }
  }
};

const server = http.createServer(requestListener);

server.listen(port, () => {
  console.log(`App is running on http://${host}:${port}`);
});
