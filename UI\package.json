{"name": "ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "storybook:upgrade": "storybook upgrade", "storybook:info": "storybook info", "prepare": "husky install", "lint": "eslint . --ext .js,.jsx,.ts,.tsx --max-warnings=0"}, "dependencies": {"@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.12", "js-cookie": "^3.0.5", "object-path": "^0.11.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.2.0", "redux-persist-transform-encrypt": "^5.1.1", "tailwindcss": "^4.1.12"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.25.0", "@storybook/addon-a11y": "^9.0.18", "@storybook/addon-docs": "^9.0.18", "@storybook/addon-onboarding": "^9.0.18", "@storybook/addon-vitest": "^9.0.18", "@storybook/react-vite": "^9.0.18", "@types/js-cookie": "^3.0.6", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@vitejs/plugin-react": "^4.4.1", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "^9.0.18", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "playwright": "^1.54.1", "storybook": "^9.0.18", "stylelint": "^16.23.0", "stylelint-config-standard": "^39.0.0", "stylelint-config-standard-scss": "^15.0.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}