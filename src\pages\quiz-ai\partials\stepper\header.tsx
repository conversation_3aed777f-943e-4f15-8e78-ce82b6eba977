import { Icon, Logo } from 'src';

// Helper function to get the current question index
const getCurrentQuestionIndex = (result: any, start: any) => {
  if (!result) return start?.currentQuestionIndex;
  return result.currentQuestionIndex;
};

export const StepperAiHeader = ({ result, start, getAvailableSkips, showThemeIcon }: any) => {
  const currentQuestionIndex = getCurrentQuestionIndex(result, start);

  return (
    <nav className="flex items-center gap-8 px-8 py-4">
      <Logo className="h-7 md:h-8" />

      {/* <div className="space-y-1">
        <div className="flex gap-2 text-lg md:text-xl font-medium dark:text-white">
          {!start && !result ? (
            <span className="md:w-40 bg-[#212223] rounded-full h-6 animate-pulse"></span>
          ) : (
            <>
              <p>Question</p>
              <p>
                {currentQuestionIndex}/{start?.totalQuestions}
              </p>
            </>
          )}
        </div>

        {getAvailableSkips()?.showAvailableSkips && (
          <div className="flex gap-2 text-lg md:text-xl font-medium dark:text-white">
            {!start && !result ? (
              <span className="w-52 bg-[#212223] rounded-full h-4 animate-pulse block mt-2"></span>
            ) : (
              <p className="text-sm text-zinc-400">{getAvailableSkips()?.availableSkips} Questions can be skipped</p>
            )}
          </div>
        )}
      </div> */}

      {/* <!-- Dark --> */}
      {/* {showThemeIcon && (
        <button
          type="button"
          className="text-gray-500 mx-8 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 flex items-center justify-center"
          onClick={switchDarkMode}
        >
          <Icon icon={isDark ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'} width="20px" />
        </button>
      )} */}
    </nav>
  );
};
