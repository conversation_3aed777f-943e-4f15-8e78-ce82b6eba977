import { useState } from 'react';
import { ScrollableTabs, Icon } from 'src';
import { AiAvatarModels, Dialog as UIDialog, <PERSON><PERSON> as UIButton } from 'UI/src';
import adamBody from 'src/images/models/adam-body.png';

type AvatarCardProps = {
  name: string;
  role: string;
  avatar: string;
  submittedAt?: string;
  by?: string;
  variant?: 'default' | 'pending' | 'approved' | 'rejected';
  onReviewClick?: () => void;
  rejectionReason?: string;
  expanded?: boolean;
  onToggle?: () => void;
};

const AvatarCard = ({
  name,
  role,
  avatar,
  submittedAt,
  by,
  variant = 'default',
  onReviewClick,
  rejectionReason,
  expanded,
  onToggle,
}: AvatarCardProps) => (
  <div className="p-4 rounded-2xl shadow-[0px_7px_10px_0px_#743AF51A] ">
    <div className="flex items-center justify-between gap-3">
      <div className="flex items-center gap-3">
        <img src={avatar} alt={name} className="w-14 h-14 rounded-full object-cover" />
        <div className="space-y-0.5">
          <p className="text-[15px] font-semibold text-[#1B1F3B] dark:text-white">{name}</p>
          <p className="text-sm text-[#4E5E82]">{role}</p>
        </div>
      </div>

      {variant === 'pending' && (
        <button className="text-primaryPurple" onClick={onReviewClick} aria-label="review-request">
          <svg width="19" height="15" viewBox="0 0 19 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1.66406 7.5C1.66406 7.5 4.57315 1.5 9.66406 1.5C14.755 1.5 17.6641 7.5 17.6641 7.5C17.6641 7.5 14.755 13.5 9.66406 13.5C4.57315 13.5 1.66406 7.5 1.66406 7.5Z"
              stroke="#743AF5"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M9.66406 9.75C10.869 9.75 11.8459 8.74264 11.8459 7.5C11.8459 6.25736 10.869 5.25 9.66406 5.25C8.45908 5.25 7.48224 6.25736 7.48224 7.5C7.48224 8.74264 8.45908 9.75 9.66406 9.75Z"
              stroke="#743AF5"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      )}

      {variant === 'rejected' && (
        <button className="text-primaryPurple" onClick={onToggle} aria-label="toggle-reason">
          <Icon icon="mdi:chevron-up" className={`text-2xl transition-transform ${expanded ? 'rotate-0' : 'rotate-180'}`} />
        </button>
      )}
    </div>
    {submittedAt && by && (
      <div className="mt-3 space-y-1">
        <p className="text-xs text-[#868D9C]">Submitted at {submittedAt}</p>
        <p className="text-xs text-gray-500">By {by}</p>
      </div>
    )}

    {variant === 'rejected' && expanded && rejectionReason && (
      <div className="mt-3">
        <p className="text-sm font-semibold text-gray-700">Rejection reason</p>
        <p className="text-sm text-[#4E5E82]">{rejectionReason}</p>
      </div>
    )}
  </div>
);

export const AvatarManagement = () => {
  const [activeTab, setActiveTab] = useState(0); // 0: System, 1: Pending, 2: Approved, 3: Rejected
  const [reviewOpen, setReviewOpen] = useState(false);
  const [reviewStep, setReviewStep] = useState<'actions' | 'reject'>('actions');
  const [rejectionText, setRejectionText] = useState('');

  type RequestCard = {
    id: string;
    name: string;
    role: string;
    submittedAt: string;
    by: string;
    avatar: string;
    rejectionReason?: string;
  };

  // System avatars: show all available models
  const systemAvatars = (AiAvatarModels || []).map((model: { value: string; iconPathOut: string }) => ({
    id: model.value,
    name: model.value,
    role: 'HR Representative',
    avatar: `/assets/models/${model.iconPathOut}`,
  }));

  // Demo data for other tabs
  const [pending, setPending] = useState<RequestCard[]>(
    [0, 1, 2].map((i) => ({
      id: `p-${i}`,
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody as unknown as string,
    }))
  );
  const [approved, setApproved] = useState<RequestCard[]>(
    [0, 1, 2].map((i) => ({
      id: `a-${i}`,
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody as unknown as string,
    }))
  );
  const [rejected, setRejected] = useState<RequestCard[]>([
    {
      id: 'r-1',
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody as unknown as string,
      rejectionReason: 'Not suitable for our system',
    },
    {
      id: 'r-2',
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody as unknown as string,
      rejectionReason: 'Incomplete requirements',
    },
  ]);
  const [selectedRequest, setSelectedRequest] = useState<RequestCard | null>(null);
  const [expandedRejectedId, setExpandedRejectedId] = useState<string | null>(null);

  const tabs = [
    { title: 'system', component: null },
    { title: 'pending', component: null },
    { title: 'approved', component: null },
    { title: 'rejected', component: null },
  ];

  const getCount = () => {
    if (activeTab === 0) return systemAvatars.length;
    if (activeTab === 1) return pending.length;
    if (activeTab === 2) return approved.length;
    return rejected.length;
  };

  return (
    <div className="space-y-6 p-4 border border-[#F4F4F4] rounded-lg">
      <ScrollableTabs
        data={tabs}
        selectedTab={{
          activeTab,
          setActiveTab,
        }}
        nav={{ routePrefix: '/app/avatars-management' }}
      />

      <div className="flex items-center gap-2 justify-between flex-wrap xssm:flex-nowrap xssm:gap-0">
        <div className="flex items-center gap-2">
          <p className="font-medium text-xl dark:text-white">
            {activeTab === 0 ? 'System Avatars' : activeTab === 1 ? 'Pending Requests' : activeTab === 2 ? 'Approved' : 'Rejected'}
          </p>
          <div className="py-[1px] px-2 text-[#743AF5] bg-[#F9F8FA] rounded-lg  thepassBfour">{getCount()}</div>
        </div>
      </div>

      {activeTab === 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {systemAvatars.map((item) => (
            <AvatarCard key={item.id} name={item.name} role={item.role} avatar={item.avatar} />
          ))}
        </div>
      )}

      {activeTab === 1 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {pending.map((card) => (
            <AvatarCard
              key={card.id}
              name={card.name}
              role={card.role}
              avatar={card.avatar}
              submittedAt={card.submittedAt}
              by={card.by}
              variant="pending"
              onReviewClick={() => {
                setSelectedRequest(card);
                setReviewStep('actions');
                setRejectionText('');
                setReviewOpen(true);
              }}
            />
          ))}
        </div>
      )}

      {activeTab === 2 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {approved.map((card) => (
            <AvatarCard key={card.id} name={card.name} role={card.role} avatar={card.avatar} submittedAt={card.submittedAt} by={card.by} />
          ))}
        </div>
      )}

      {activeTab === 3 && (
        <div className="columns-1 sm:columns-2 lg:columns-3 gap-4">
          {rejected.map((card) => (
            <div key={card.id} className="break-inside-avoid mb-4">
              <AvatarCard
                name={card.name}
                role={card.role}
                avatar={card.avatar}
                submittedAt={card.submittedAt}
                by={card.by}
                variant="rejected"
                rejectionReason={card.rejectionReason}
                expanded={expandedRejectedId === card.id}
                onToggle={() => setExpandedRejectedId((prev) => (prev === card.id ? null : card.id))}
              />
            </div>
          ))}
        </div>
      )}

      <UIDialog isOpen={reviewOpen} onClose={() => setReviewOpen(false)} title="Review Avatar Request" size="md">
        {reviewStep === 'actions' && selectedRequest && (
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <img src={selectedRequest.avatar} alt={selectedRequest.name} className="w-[162px] border h-[162px] rounded-xl object-cover" />
              <div>
                <p className="text-base font-semibold text-gray-800">{selectedRequest.name}</p>
                <p className="text-sm text-gray-500">{selectedRequest.role}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <UIButton
                variant="md"
                colorType="destructive"
                label="Reject"
                onClick={() => setReviewStep('reject')}
                className="w-full bg-[#F13E3E] "
              />
              <UIButton
                variant="md"
                colorType="success"
                label="Approve"
                onClick={() => {
                  if (selectedRequest) {
                    setPending((prev) => prev.filter((p) => p.id !== selectedRequest.id));
                    setApproved((prev) => [{ ...selectedRequest, id: `${selectedRequest.id}-app-${Date.now()}` }, ...prev]);
                  }
                  setReviewOpen(false);
                }}
                className="w-full bg-[#009217] border-green-600 hover:bg-green-700"
              />
            </div>
          </div>
        )}

        {reviewStep === 'reject' && (
          <div className="space-y-4">
            <label className="text-sm font-medium ">Rejection Reason</label>
            <textarea
              placeholder="Type..."
              rows={5}
              className="w-full border border-gray-300 rounded-lg p-3 outline-none focus:ring-2 focus:ring-purple-200"
              value={rejectionText}
              onChange={(e) => setRejectionText(e.target.value)}
            />

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <UIButton variant="md" colorType="secondary" label="Back" onClick={() => setReviewStep('actions')} className="w-full" />
              <UIButton
                variant="md"
                colorType="destructive"
                label="Confirm Rejection"
                onClick={() => {
                  if (selectedRequest) {
                    setPending((prev) => prev.filter((p) => p.id !== selectedRequest.id));
                    setRejected((prev) => [
                      {
                        ...selectedRequest,
                        rejectionReason: rejectionText,
                        id: `${selectedRequest.id}-rej-${Date.now()}`,
                      },
                      ...prev,
                    ]);
                  }
                  setReviewOpen(false);
                }}
                disabled={!rejectionText.trim()}
              />
            </div>
          </div>
        )}
      </UIDialog>
    </div>
  );
};
