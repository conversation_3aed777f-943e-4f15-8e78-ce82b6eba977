// React
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// Core
import { Jumbotron, Button } from 'src';

// Components
import { Stage } from '../components/stage';
import { ChooseTypeAssignAssessment } from '../components/assign/type';
import { SetupAssignAssessment } from '../components/assign/setup-interview';
import { AssignAndScheduleAssessment } from '../components/assign/assign-and-schedule';
import { ReviewAssignAssessment } from '../components/assign/review';
import { TestCreatedSucessfully } from '../../applicants/components/test-created-sucessfully';

// Flowbite
import { Spinner } from 'flowbite-react';
import { Form, initializeForm, RootState, setFieldValue, useAppDispatch, useAppSelector, Api, AiAvatarModels } from 'UI/src';
import { setErrorNotify } from 'UI';
import { useFormik } from 'formik';

export const AssessmentsAssignPage = () => {
  // Hooks
  const navigate = useNavigate();
  const { type, id } = useParams();
  const dispatch = useAppDispatch();

  // State
  const [activeStage, setActiveStage] = useState(0);
  const [disableNextButton, setDisableNextButton] = useState<boolean>(false);
  const [isTestCreatedSuccessfullyVisible, setTestCreatedSuccessFullyVisible] = useState(false);
  const [quizUrl, setQuizURL] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [assessmentTemplate, setSelectedAssessmentTemplate] = useState<string | string[]>('');
  const [applicants, setSelectedApplicantsIds] = useState<any[]>([]);
  const [lastAssessmentType, setLastAssessmentType] = useState(0);
  // const [startDate, setStartDate] = useState(() => {
  //   const dateWithZeroSeconds = new Date();
  //   dateWithZeroSeconds.setSeconds(0);
  //   return dateWithZeroSeconds;
  // });
  // const [dueDate, setDueDate] = useState(() => {
  //   const dateWithFullSeconds = new Date(startDate);
  //   dateWithFullSeconds.setDate(dateWithFullSeconds.getDate() + 2);
  //   dateWithFullSeconds.setSeconds(59);
  //   return dateWithFullSeconds;
  // });

  const [startDate, setStartDate] = useState(() => {
    const now = new Date();
    now.setSeconds(0); // Set seconds to 0
    // Round minutes up to nearest 5
    const minutes = now.getMinutes();
    const roundedMinutes = Math.ceil(minutes / 5) * 5;
    if (roundedMinutes === 60) {
      // If rounding went to 60, increment hour
      now.setHours(now.getHours() + 1);
      now.setMinutes(0);
    } else {
      now.setMinutes(roundedMinutes);
    }
    return now;
  });
  const [dueDate, setDueDate] = useState(() => {
    const date = new Date(startDate);
    date.setDate(date.getDate() + 2); // Add 2 days
    date.setSeconds(59); // Set seconds to 59
    // Round minutes UP to the next multiple of 5
    const minutes = date.getMinutes();
    const roundedMinutes = Math.ceil(minutes / 5) * 5;
    if (roundedMinutes === 60) {
      // If it's 60, reset to 0 and add 1 hour
      date.setHours(date.getHours() + 1);
      date.setMinutes(0);
    } else {
      date.setMinutes(roundedMinutes);
    }
    return date;
  });

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      numberOfQuestions: '',
      estimationTime: 60,
      type: type === 'interview' ? 1 : 0, // 1 for interview, 0 for others
      skips: '',
      applicantId: {},
      notes: '',
      startDate: null,
      dueDate: null,
      category: [],
      subCategory: [],
      difficulty: null,
      modelType: 'gpt-4.1-mini',
      avatarName: AiAvatarModels[0]?.value,
      avatarDescription: 'Technical Manager',
      avatarLang: 'English',
      seniorityLevel: 1,
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  // Stage
  const stage = [
    {
      label: `Choose ${type === 'interview' ? 'Interview Type' : 'Template'}`,
      // label: `Choose ${type && type?.charAt(0).toUpperCase() + type?.slice(1)}`,
      // ...(type === 'interview' && { header: 'Choose Interview Type' }),
      component: (
        <ChooseTypeAssignAssessment
          formData={{}}
          disableButtons={{ disableNextButton, setDisableNextButton }}
          lastAssessmentTypeData={{ lastAssessmentType, setLastAssessmentType }}
          assessmentTemplateData={{ assessmentTemplate, setSelectedAssessmentTemplate }}
        />
      ),
    },
    ...(type === 'interview'
      ? [
          {
            label: 'Interview Preferences',
            // header: 'Setup Interview Preferences',
            component: <SetupAssignAssessment formData={{}} disableButtons={{ disableNextButton, setDisableNextButton }} />,
          },
        ]
      : []),
    {
      label: 'Assign & Scheduling',
      // header: 'Assign to Applicants & Schedule',
      component: (
        <AssignAndScheduleAssessment
          formData={{}}
          disableButtons={{ disableNextButton, setDisableNextButton }}
          applicantsData={{ applicants, setSelectedApplicantsIds }}
          interviewScheduleData={{ startDate, setStartDate, dueDate, setDueDate }}
        />
      ),
    },
    {
      label: 'Review & Confirm',
      // header: `Review ${type?.charAt(0).toUpperCase() + (type as any)?.slice(1)}’s Details`,
      component: <ReviewAssignAssessment formData={{}} />,
    },
  ];

  const handleSubmit = async () => {
    try {
      setLoading(true);
      if (type === 'screening') {
        /* Screening */
        const payload: { [key: string]: any } = {
          quizId: form?.quizId,
          startDate: form?.startDate,
          dueDate: form?.dueDate,
        };
        // if (form?.extraTime >= 1) payload.exceededTime = extraTime;
        if (Object.keys(form?.applicantId).length > 0) {
          payload.applicantId = Object.keys(form.applicantId).filter((key) => form.applicantId[key]); // applicantId is not defined
        }
        const response = await Api.post('submissions/screening/single', payload);
        setQuizURL(response?.data?.quizUrl);
        setTestCreatedSuccessFullyVisible(true);
      } else if (type === 'test') {
        /* Test */
        const payload: { [key: string]: any } = {
          quizId: form?.quizId,
          otherTest: true,
          startDate: form?.startDate,
          dueDate: form?.dueDate,
          type: 'private',
        };
        // if (form?.extraTime >= 1) payload.exceededTime = extraTime;
        if (Object.keys(form?.applicantId).length > 0) {
          payload.applicantId = Object.keys(form.applicantId).filter((key) => form.applicantId[key]); // applicantId is not defined
        }
        const response = await Api.post('submissions/single', payload);
        setQuizURL(response?.data?.quizUrl);
        setTestCreatedSuccessFullyVisible(true);
      } else if (type === 'interview') {
        /* Interview */
        const { applicantId, ...payload } = form;
        if (payload.notes == '') delete payload.notes;
        if (Object.keys(form?.applicantId).length > 0) {
          payload.applicantId = Object.keys(form.applicantId).filter((key) => form.applicantId[key]);
        }

        // Check if this is a template-based interview (selectedBasedTemplate exists)
        if (form.quizId) {
          // Template-based interview
          payload.type = 3; // Set type to 3 for template-based interviews
          payload.templateId = form.quizId; // Send the template ID
        } else {
          // Interactive interview
          payload.type = 2; // Keep type as 2 for interactive interviews
          payload.category = [payload.category];
          payload.numberOfQuestions = form.numberOfQuestions;
        }

        const response = await Api.post('ai-interview/single', payload);
        setQuizURL(response?.data?.quizUrl);
        setTestCreatedSuccessFullyVisible(true);
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setLoading(false);
    }
  };

  const handelGet = async () => {
    try {
      setLoading(true);
      const response = await Api.get(`templates/single/${id}`, {});
      console.log('templates/single', response.data);
      const responseData = Array.isArray(response.data) ? response.data[0] : response.data;
      if (!responseData) throw new Error('No data returned from API');

      // Set form values with default fallbacks
      dispatch(setFieldValue({ path: 'quizId', value: responseData?._id }));
      dispatch(setFieldValue({ path: 'category', value: responseData?.category }));
      dispatch(setFieldValue({ path: 'categoryName', value: responseData?.categoryName }));
      dispatch(setFieldValue({ path: 'subCategory', value: responseData?.subCategory }));
      dispatch(setFieldValue({ path: 'subCategoryName', value: responseData?.subCategoryName }));
      dispatch(setFieldValue({ path: 'difficulty', value: responseData?.difficulty }));
      dispatch(setFieldValue({ path: 'estimationTime', value: responseData?.duration }));
      dispatch(setFieldValue({ path: 'skips', value: responseData?.skips }));
      dispatch(setFieldValue({ path: 'numberOfQuestions', value: responseData?.numOfQuestions }));
      setActiveStage(1);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (id) {
      handelGet();
    }
  }, [id]);

  return (
    <div className="relative">
      <div className="space-y-6">
        <Jumbotron />

        <Stage
          stage={stage}
          selectedStage={{
            activeStage: activeStage,
          }}
        />

        {/* <div className="text-xl font-semibold dark:text-white">{stage[activeStage]?.header}</div> */}

        <Form onSubmit={() => {}}>{stage[activeStage]?.component}</Form>

        <div className="flex justify-end gap-4">
          <Button
            className="w-[107px]"
            label={activeStage === 0 ? 'Cancel' : 'Back'}
            tertiary
            onClick={() => (activeStage === 0 ? navigate(`/app/assessment-templates/${type}/list`) : setActiveStage((prev) => prev - 1))}
          />
          <Button
            className="w-48"
            label={activeStage === stage.length - 2 ? 'Review' : activeStage === stage.length - 1 ? 'Confirm' : 'Next'}
            onClick={() => (activeStage === stage.length - 1 ? handleSubmit() : setActiveStage((prev) => prev + 1))}
            disabled={disableNextButton}
          />
        </div>
      </div>

      {loading && (
        <div className="absolute top-0 bottom-0 left-0 right-0 z-50 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
          <Spinner size="lg" color="purple" />
        </div>
      )}

      {isTestCreatedSuccessfullyVisible && (
        <TestCreatedSucessfully
          assignment={Object.keys(form.applicantId).filter((key) => form.applicantId[key]).length > 0}
          defaultType={type} // @TODO: Capitalize please
          quizUrl={quizUrl}
          onClose={() => {
            setTestCreatedSuccessFullyVisible(false);
            navigate(`/app/assessment-templates/${type}/list`);
          }}
        />
      )}
    </div>
  );
};
