import { Sidebar, SidebarItem, SidebarItemGroup, SidebarItems, SidebarLogo, SidebarCollapse } from 'flowbite-react';
import { BiBuoy } from 'react-icons/bi';
import { HiArrowSmRight, HiChartPie, HiInbox, HiShoppingBag, HiTable, <PERSON><PERSON><PERSON>, HiViewBoards } from 'react-icons/hi';
import { Icon } from '../../../components/icon';
import { Logo } from '../../../components/logo';
import { PermissionProtectedComponent } from 'src/components'
import { useLocation, Link, useNavigate } from 'react-router-dom';
import { menuItems, MenuItem } from 'UI';
import { useState } from 'react';
import { RootState, useAppSelector, useScreenSize, useAuthUtils, UserData } from 'UI';

// Import SVG icons
import userIcon from 'UI/src/assets/user.svg';
import usersIcon from 'UI/src/assets/users.svg';
import boxIcon from 'UI/src/assets/box.svg';
import calendarIcon from 'UI/src/assets/calendar.svg';
import fileIcon from 'UI/src/assets/question-file.svg';
import searchIcon from 'UI/src/assets/search.svg';
import questionIcon from 'UI/src/assets/question-circle.svg';
import userCheckIcon from 'UI/src/assets/user-check.svg';
import userProfileIcon from 'UI/src/assets/user-profile.svg';

interface AppSidebarProps {
  isDrawerVisible: boolean;
  setIsDrawerVisible: (value: boolean) => void;
}

export const MainSidebar = ({ isDrawerVisible, setIsDrawerVisible }: AppSidebarProps) => {
  // Hooks
  const location = useLocation();
  const screen = useScreenSize();
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);
  const navigate = useNavigate();

  // Permissions
  const isPermittedSuperAdmin = Array.isArray(userData?.role) && userData?.role?.includes('super-admin');
  const isPermittedAdmin = Array.isArray(userData?.role) && userData?.role?.includes('admin');
  const isPermittedContentCreator = Array.isArray(userData?.role) && userData?.role?.includes('content-creator');
  const isPermittedHr = Array.isArray(userData?.role) && userData?.role?.includes('hr');

  // Helper function to get the appropriate SVG icon based on lucide name
  const SidebarIcon = ({ definedIcon, className, width, height }: { definedIcon: string; className?: string; width?: string; height?: string }) => {
    switch (definedIcon.toLowerCase()) {
      case 'chartnoaxescolumnincreasing':
        return <img src={boxIcon} alt="box" className={className} width={width} height={height} />;
      case 'network':
        return <img src={usersIcon} alt="users" className={className} width={width} height={height} />;
      case 'users':
        return <img src={usersIcon} alt="users" className={className} width={width} height={height} />;
      case 'clipboardlist':
        return <img src={fileIcon} alt="file" className={className} width={width} height={height} />;
      case 'smile':
        return <img src={userCheckIcon} alt="user check" className={className} width={width} height={height} />;
      case 'filecheck2':
        return <img src={fileIcon} alt="file" className={className} width={width} height={height} />;
      case 'filevideo2':
        return <img src={fileIcon} alt="file" className={className} width={width} height={height} />;
      case 'idcard':
        return <img src={userProfileIcon} alt="user profile" className={className} width={width} height={height} />;
      case 'filechartcolumn':
        return <img src={fileIcon} alt="file" className={className} width={width} height={height} />;
      case 'filequestion':
        return <img src={questionIcon} alt="question" className={className} width={width} height={height} />;
      case 'notebooktext':
        return <img src={fileIcon} alt="file" className={className} width={width} height={height} />;
      default:
        return <img src={userIcon} alt="user" className={className} width={width} height={height} />;
    }
  };

  const [search, setSearch] = useState<string>('');

  const [expandedItems, setExpandedItems] = useState<expandedItemsType>({
    Administration: true,
    Assessments: true,
    Reports: true,
    'Question Bank': true,
  });

  interface expandedItemsType {
    Administration: boolean;
    Assessments: boolean;
    Reports: boolean;
    'Question Bank': boolean;
    [key: string]: boolean;
  }

  const toggleExpand = (itemLabel: string) => {
    const key = itemLabel as keyof expandedItemsType;
    setExpandedItems({
      ...expandedItems,
      [key]: !expandedItems[key],
    });
  };

  const getActiveClasses = (path: string) => {
    return location.pathname.includes(path) ? 'bg-gray-100 dark:bg-gray-700' : '';
  };

  const clearSidebarFilter = () => {
    setSearch('');
  };

  return (
    <div
      className={`w-[250px] rounded-3xl fixed top-1 left-1 z-[45] h-[calc(100vh-0.8rem)] transition-transform bg-white text-sm shadow-[0_10px_25px_-5px_rgba(0,0,0,0.4),0_8px_10px_-2px_rgba(0,0,0,0.2)] overflow-hidden ${isDrawerVisible ? '-translate-x-0' : '-translate-x-[105%]'
        }`}
    >
      <Sidebar aria-label="" className="h-full rounded-3xl">
        {/* Header inside Sidebar */}
        <div className="flex items-center gap-6 p-1 pt-1 border-b border-gray-200  pb-2 mb-2">
          <Icon
            onClick={() => setIsDrawerVisible(!isDrawerVisible)}
            icon={isDrawerVisible ? 'material-symbols:keyboard-double-arrow-left-rounded' : 'material-symbols:keyboard-double-arrow-right-rounded'}
            className={`text-[#8D5BF8] rounded-xl cursor-pointer`}
            width={'30'}
          />
          <div className="flex items-center justify-between cursor-pointer" onClick={() => navigate('/')}>
            <Logo className="h-6 sm:h-7 " />
          </div>
        </div>

        {isDrawerVisible && (
          <div className="px-3 py-2">
            <div className="relative">
              <input
                type="text"
                placeholder="Search..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full px-3 py-2 text-sm text-gray-700 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-300"
              />
              {search && (
                <button
                  onClick={clearSidebarFilter}
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 hover:scale-110 transition-all duration-200"
                >
                  <Icon icon="mdi:close" width="18" />
                </button>
              )}
            </div>
          </div>
        )}

        <div className="flex flex-col justify-between gap-3 bg-white overflow-y-auto pt-2 pb-5 px-2 h-full dark:bg-darkBackgroundCard">
          <ul className="">
            {menuItems({
              isPermittedSuperAdmin,
              isPermittedAdmin,
              isPermittedContentCreator,
              isPermittedHr,
            })
              .filter((menu: MenuItem) => menu?.label.toLowerCase().includes(search.toLowerCase()))
              .map((item, index) => {
                const itemRider = <li key={index} className={item.type === 'header' ? 'text-xs font-semibold text-gray-500 py-2 mt-3 uppercase' : ''}>
                  {item.type === 'header' ? (
                    <div className="px-2 border-b border-gray-200 pb-1">{item.label}</div>
                  ) : item.children ? (
                    <div>
                      <button
                        onClick={() => {
                          toggleExpand(item.label);
                          clearSidebarFilter();
                        }}
                        className="flex justify-between items-center w-full p-2 text-base font-medium text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 group"
                      >
                        {isDrawerVisible && <span className="text-[#83899F] text-base flex-1 text-left whitespace-nowrap">{item.label}</span>}
                        <Icon
                          icon={expandedItems[item.label] ? 'mdi:chevron-up' : 'mdi:chevron-down'}
                          className="text-[#8D5BF8] text-lg"
                          width="20"
                        />
                      </button>

                      {expandedItems[item.label] && (
                        <ul className="py-1 space-y-1 pl-1 ml-3 border-l border-gray-200 dark:border-gray-700 animate-fadeIn transition-all duration-300" key={`${index}-${item.label}`}>
                          {item.children?.map((child, childIndex) => {
                            const childItemRider = <li key={childIndex}>
                              <Link
                                to={child.path}
                                className={`flex items-center p-1 text-sm font-normal  text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 ${getActiveClasses(
                                  child.path
                                )}`}
                                onClick={() => {
                                  clearSidebarFilter();
                                  screen.lt.xl() && setIsDrawerVisible(false);
                                }}
                              >
                                {child?.lucide && (
                                  <SidebarIcon definedIcon={child.lucide.toLowerCase()} className="text-gray-500 mr-2" width="18" height="18" />
                                )}
                                {child.icon && (
                                  <Icon
                                    icon={child.icon}
                                    className="text-gray-500 transition duration-75  dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                    width="18"
                                  />
                                )}
                                {isDrawerVisible && (
                                  <span
                                    className={`ml-3 text-sm font-medium text-[#0A1B39] dark:text-white ${location.pathname.includes(child.path) && 'font-semibold'
                                      }`}
                                  >
                                    {child.label}
                                  </span>
                                )}
                              </Link>
                            </li>
                            return child.permission ?
                              <PermissionProtectedComponent permissions={child.permission}>{childItemRider}</PermissionProtectedComponent>
                              : childItemRider;
                          })}
                        </ul>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.path!}
                      className={`flex items-center p-2 text-base font-normal text-gray-900 rounded-lg dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 ${getActiveClasses(
                        item.path!
                      )}`}
                      onClick={() => {
                        clearSidebarFilter();
                      }}
                    >
                      <Icon
                        icon={item.icon}
                        className="text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                        width="18"
                      />
                      {isDrawerVisible && <span className="ml-3 text-sm font-medium text-[#0A1B39] dark:text-white">{item.label}</span>}
                    </Link>
                  )}
                </li>

                return item.permission ?
                  <PermissionProtectedComponent permissions={item.permission} operator='some'>{itemRider}</PermissionProtectedComponent>
                  : itemRider;
              })}
          </ul>
        </div>
      </Sidebar>
    </div>
  );
};
