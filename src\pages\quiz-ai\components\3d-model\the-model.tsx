// @ts-nocheck
// React
import { memo, useEffect, useRef, forwardRef, useImperativeHandle, useCallback } from 'react';

// Core
import { useAppSelector, useAppDispatch, updateUser, AiAvatarModelLanguages, submissionAiState, setIsSpeaking } from 'UI';
import { AiAvatarModels } from 'UI/src';

// Components
import { TalkingHead } from './talking-head/module';
import VirtualBackground from 'images/virtual-office.jpg';
import { VITE_APP_AI_GOOGLE_API_KEY } from 'UI/src/configs/api';
import { fi } from 'date-fns/locale';

const TalkingHeadComponent = forwardRef(({ setLoadingProgression, setIsFinished, processStatus, transcript }: any, ref: any) => {
  const dispatch = useAppDispatch();
  const userData = useAppSelector((state) => state.auth.user);

  const { submissionAi, loading, aiIsTyping, isSpeaking } = useAppSelector(submissionAiState);

  const selectedAiModel = AiAvatarModels.find((model) => model.value === submissionAi?.interview?.avatarName);
  const selectedLanguage = submissionAi?.interview?.avatarLang;

  const voiceConfig = AiAvatarModelLanguages.find((v: any) => v.lang === selectedLanguage && v.gender === selectedAiModel?.gender);

  const getLipsyncLang = useCallback((language: string) => {
    switch (language) {
      case 'Arabic':
        return 'ar';
      case 'Turkish':
        return 'tr';
      case 'English':
        return 'en';
      default:
        return 'en'; // Default to English
    }
  }, []);

  const lipsyncLang = getLipsyncLang(selectedLanguage);
  const ttsVoice = voiceConfig?.voice;
  const ttsLang = voiceConfig?.langCode;

  const avatarRef = useRef(null);
  const headRef = useRef(null);
  const handleStartSpeaking = () => {
    dispatch(setIsSpeaking(true));
  };

  const handleStopSpeaking = () => {
    dispatch(setIsSpeaking(false));
  };

  useImperativeHandle(ref, () => ({
    async handleSpeak(transcript: any) {
      console.log('transcript', transcript);
      try {
        if (transcript && headRef.current) {
          (headRef.current as any).speakText(transcript);

          if (processStatus === 'Finished') {
            setIsFinished(true);
          }
        }
      } catch (error) {
        // console.error(error); // From G
      }
    },
  }));

  useEffect(() => {
    const loadAvatar = async () => {
      try {
        if (!headRef.current) {
          headRef.current = new TalkingHead(avatarRef.current, {
            ttsEndpoint: 'https://eu-texttospeech.googleapis.com/v1beta1/text:synthesize',
            ttsApikey: VITE_APP_AI_GOOGLE_API_KEY,
            lipsyncModules: ['en', 'fi', 'ar'],
            cameraView: 'head',
            modelPixelRatio: 2,
            cameraDistance: 1.5,
            cameraX: -0.1,
            cameraY: 0.4,
            cameraRotateEnabled: false,
            onStartSpeaking: handleStartSpeaking,
            onStopSpeaking: handleStopSpeaking,
          });

          await headRef.current.showAvatar(
            {
              url: `https://aws-thepass-ai.s3.eu-central-1.amazonaws.com/ai-modules/${selectedAiModel?.value}.glb`,
              body: 'M',
              avatarMood: 'happy',
              ttsLang: ttsLang,
              ttsVoice: ttsVoice,
              lipsyncLang: lipsyncLang,
            },
            (ev) => {
              if (ev.lengthComputable) {
                const val = Math.min(100, Math.round((ev.loaded / ev.total) * 100));
                setLoadingProgression(val);
              }
            }
          );
        }
      } catch (error) {
        // console.error(error); // From G
      }
    };
    loadAvatar();
  }, []);

  return (
    <div className="relative z-0 w-full h-full mx-auto overflow-hidden text-white bg-gray-600 rounded-lg pointer-events-none md:rounded-2xl">
      <div id="stars"></div>
      <div id="stars2"></div>
      <div id="stars3"></div>
      <img src={VirtualBackground} className="absolute left-0 top-0 h-full w-full z-10 blur-[6px]" alt="" />

      <div id="avatar" ref={avatarRef} className="block w-full relative z-50 h-full test "></div>
    </div>
  );
});

export default memo(TalkingHeadComponent);
