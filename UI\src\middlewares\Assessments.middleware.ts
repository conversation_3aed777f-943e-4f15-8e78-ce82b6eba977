import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';
import type { QuizType } from '../types/Quiz.type';

// Fetch single assessment
export const fetchAssessment = createAsyncThunk('assessments/fetchAssessment', async (id: string, { rejectWithValue }) => {
  try {
    const response = await Api.get(`templates/single/${id}`, {});
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to fetch assessment');
  }
});

// Create assessment
export const createAssessment = createAsyncThunk('assessments/createAssessment', async (payload: any, { rejectWithValue }) => {
  try {
    const response = await Api.post('templates/single', payload);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to create assessment');
  }
});

// Update assessment
export const updateAssessment = createAsyncThunk(
  'assessments/updateAssessment',
  async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
    try {
      const response = await Api.put(`templates/single/${id}`, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to update assessment');
    }
  }
);

// Create screening submission
export const createScreeningSubmission = createAsyncThunk('assessments/createScreeningSubmission', async (payload: any, { rejectWithValue }) => {
  try {
    const response = await Api.post('submissions/screening/single', payload);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to create screening submission');
  }
});

// Create regular submission
export const createRegularSubmission = createAsyncThunk('assessments/createRegularSubmission', async (payload: any, { rejectWithValue }) => {
  try {
    const response = await Api.post('submissions/single', payload);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to create submission');
  }
});

// Create AI interview
export const createAssessmentAiInterview = createAsyncThunk('assessments/createAiInterview', async (payload: any, { rejectWithValue }) => {
  try {
    const response = await Api.post('ai-interview/single', payload);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to create AI interview');
  }
});

// Get assessment quiz
export const fetchAssessmentQuiz = createAsyncThunk('assessments/fetchQuiz', async (id: string, { rejectWithValue }) => {
  try {
    const response = await Api.get<QuizType>(`templates/single/${id}`, {});
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to fetch assessment quiz');
  }
});