import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';

// Get quota list
export const fetchQuotaList = createAsyncThunk(
  'addCredits/fetchQuotaList',
  async (_, { rejectWithValue }) => {
    try {
      const response = await Api.get('/quota/list');
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch quota list');
    }
  }
); 
