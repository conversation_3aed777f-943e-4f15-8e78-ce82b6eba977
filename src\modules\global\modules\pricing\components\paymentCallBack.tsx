import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Api } from 'UI/src';
import { useAppDispatch, useAppSelector, updateUser, setErrorNotify, setNotifyMessage, CookieStorage, useUserPermissions } from 'UI';

export default function PaymentCallback() {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState(true);
  const [searchParams] = useSearchParams();
  const userData = useAppSelector((state) => state.auth.user);
  const { handleGetUserRole } = useUserPermissions()

  const id = searchParams.get('id');
  const planId = searchParams.get('planId');
  const pricingKey = searchParams.get('pricingKey');
  const organizationId = searchParams.get('organizationId');

  useEffect(() => {
    // Initial guard for invalid/missing ID
    if (!id || id === 'null') {
      // navigate('/payment-failed', { replace: true });
      window.location.href = `/payment-failed`;
      return;
    }

    const verifyPayment = async () => {
      try {
        // Step 1: Verify payment
        const { data } = await Api.post('/subscription/create-verify', {
          paymentId: id,
          planId,
          organizationId,
          pricing: pricingKey,
        });

        if (data.paymentStatus === 'failed') {
          // navigate('/payment-failed', { replace: true });
          window.location.href = `/payment-failed`;
          return;
        }

        // Merge new features into existing user data
        const updatedUser = {
          ...userData,
          features: data.features,
        };

        // @FIXME: Fix local storage
        CookieStorage.setItem('userData', JSON.stringify(updatedUser));
        dispatch(updateUser(updatedUser));
        dispatch(handleGetUserRole)

        dispatch(setNotifyMessage('Payment successful!'));
        navigate('/app/dashboard', { state: { subscriptionSuccess: true }, replace: true });
      } catch (error: any) {
        dispatch(setErrorNotify(error?.response?.data?.message || 'Payment failed or could not be confirmed.'));
        navigate('/pricing', { replace: true });
      } finally {
        setLoading(false);
      }
    };

    verifyPayment();
    /* TODO: Markos */
  }, [id, planId, organizationId, navigate, dispatch, userData]);

  // Show loading UI until navigation or if an error occurs before navigation
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <h1 className="text-2xl font-bold mb-4">Verifying your payment...</h1>
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <h1 className="text-2xl font-bold mb-4">Processing complete. Redirecting...</h1>
      {/* Or show an error message if something went wrong and we didn't navigate */}
    </div>
  );
}
