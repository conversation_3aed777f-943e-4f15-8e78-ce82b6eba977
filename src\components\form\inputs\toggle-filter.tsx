// React
import { useEffect, useState } from 'react';

// React Dropdown Menu
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';

// React Suite
import 'rsuite/dist/rsuite-no-reset.min.css';
import { DatePicker, DateRangePicker } from 'rsuite';

// UI
import { Icon, Drawer, DrawerFilter } from 'src';
import { useScreenSize, filterType } from 'UI/src';

interface ToggleFilterProps {
  filters: filterType[];
  drawerFilter?: DrawerFilter;
  handleDates?: {
    startDate: {
      value: any;
      update: (value: any) => void;
    };
    endDate: {
      value: any;
      update: (value: any) => void;
    };
  };
  drawerClearAll?: () => void;
  resultsFound?: number;
  drawerInsideDrawer?: boolean;
  tempException?: boolean;
}

export const ToggleFilter = ({
  filters,
  drawerFilter,
  handleDates,
  drawerClearAll,
  resultsFound,
  drawerInsideDrawer,
  tempException,
}: ToggleFilterProps) => {
  // Hook
  const screen = useScreenSize();

  // State
  const [open, setOpen] = useState(false);
  const { beforeToday } = DateRangePicker;

  // Methods
  const handleFilterCountNumber = () => {
    let filterCount = 0;
    if (filters?.length > 0) {
      filters?.map((subFilter) =>
        subFilter!.options.map((subFilterOption) => {
          // Count regular options with truthy value
          if (subFilterOption.value) filterCount++;

          // Check if the option is a date picker (e.g., "Pick a Date")
          if (subFilterOption.label === 'Pick a Date') {
            if (handleDates?.startDate?.value) filterCount++;
            if (handleDates?.endDate?.value) filterCount++;
          }
        })
      );
    } else if (drawerFilter && drawerFilter.filterCountNumber && drawerFilter.filterCountNumber > 0) {
      filterCount = drawerFilter?.filterCountNumber || 0;
    }

    return tempException ? filterCount - 1 : filterCount; // (-1) => Exeption!
  };

  const handleSubFilterCountNumber = (subFilter: filterType) => {
    let subFilterCount = 0;

    // subFilter!.options.length; // Removed unused expression

    // Iterate over subFilter options
    subFilter!.options.map((subFilterOption) => {
      // Increment count if the value is truthy
      if (subFilterOption.value) subFilterCount++;

      // Check for the 'Pick a Date' label
      if (subFilterOption.label === 'Pick a Date') {
        // Check if start or end date is selected
        if (handleDates?.startDate?.value) subFilterCount++;
        if (handleDates?.endDate?.value) subFilterCount++;
      }
    });

    return subFilterCount;
  };

  const renderFilterOptionsManual = (filter: filterType) => {
    return (
      <DropdownMenu.Sub>
        <DropdownMenu.SubTrigger className="flex cursor-default rounded-lg items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-[#212129]">
          <span>{filter!.label}</span>

          <div className="flex gap-2 items-center">
            <div className="bg-[#ece2fd] dark:bg-[#8A43F933] size-6 flex justify-center items-center dark:text-grayTextOnDarkMood text-primaryPurple text-xs rounded-full">
              {handleSubFilterCountNumber(filter)}
            </div>
            {/* <Icon icon="mdi:chevron-right" className="ml-2" width="18" /> */}
          </div>
        </DropdownMenu.SubTrigger>
        <DropdownMenu.SubContent
          className="bg-white mr-22 border border-[#e4e4e7] shadow-sm rounded-lg py-3 w-36  sm:w-[200px]  space-y-2 dark:border-[#27272a] dark:bg-darkGrayBackground"
          sideOffset={screen.ss() ? 8 : -30}
        >
          <div className="text-gray-700 dark:text-white border-b border-b-gray-200 dark:border-b-[#27272a] text-sm font-bold pb-1 px-3">
            {filter!.label}
          </div>
          <div className="max-h-52 custom-scroll overflow-y-auto flex flex-col gap-2 px-3 py-1 me-1.5">
            {filter!.options.length > 0 ? (
              filter!.options.map((option, index) => (
                <div key={index} className="flex items-center justify-between">
                  {option.label === 'Pick a Date' ? (
                    <DropdownMenu.Sub>
                      <DropdownMenu.SubTrigger className="flex cursor-default rounded-lg items-center justify-between w-full px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-[#212129]">
                        <span>{option.label}</span>
                      </DropdownMenu.SubTrigger>
                      <DropdownMenu.SubContent
                        className="bg-white mr-22 border   border-[#e4e4e7] shadow-sm rounded-lg py-3 p-3 space-y-3 dark:border-[#27272a] dark:bg-darkGrayBackground"
                        sideOffset={screen.ss() ? 8 : -30}
                      >
                        <div className="flex items-center gap-2">
                          <p className="dark:text-white"> Start</p>
                          <DatePicker
                            placement="topStart"
                            size={'md'}
                            value={handleDates?.startDate?.value}
                            onChange={(date) => handleDates?.startDate?.update(date)}
                            shouldDisableDate={beforeToday()}
                          />
                        </div>

                        <div className="flex items-center gap-3">
                          <p className="dark:text-white"> End</p>
                          <DatePicker
                            placement="topStart"
                            size={'md'}
                            value={handleDates?.endDate?.value}
                            onChange={(date) => handleDates?.endDate?.update(date)}
                            shouldDisableDate={beforeToday()}
                          />
                        </div>
                      </DropdownMenu.SubContent>
                    </DropdownMenu.Sub>
                  ) : (
                    <label className="flex items-start space-x-2 w-full cursor-pointer">
                      <input
                        type="checkbox"
                        className="form-checkbox h-4 w-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500 cursor-pointer"
                        checked={option.value}
                        onChange={() => option.onChange(!option.value)}
                      />
                      <span
                        style={{ wordBreak: 'break-word' }} // Not exist in tailwind
                        className={`${
                          option.value ? 'text-purple-600 dark:text-purple-400' : 'text-gray-900 dark:text-gray-200'
                        } flex text-sm font-medium items-center`}
                      >
                        {option.label}
                      </span>
                    </label>
                  )}
                </div>
              ))
            ) : (
              <div className="text-sm font-bold text-gray-500">No options</div>
            )}
          </div>
        </DropdownMenu.SubContent>
      </DropdownMenu.Sub>
    );
  };

  const mainFilterButton = (
    <button className="inline-flex items-center gap-2 justify-center h-10 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
      <Icon icon="ion:filter" width="22" className="text-[#743AF5]" />
      <span className="hidden sm:block">Filter</span>
      <span
        className={`${
          handleFilterCountNumber() <= 0 && 'hidden'
        } bg-[#ece2fd] dark:bg-[#1f1a2e] size-6 flex justify-center items-center text-primaryPurple text-xs rounded-full`}
      >
        {handleFilterCountNumber()}
      </span>
    </button>
  );

  const onCloseDrawerFilter = () => {
    if (drawerFilter?.element) {
      setOpen((prev) => !prev);
    } else {
      drawerFilter?.setShowDrawerFilter((prev) => !prev);
    }
  };

  return drawerFilter ? (
    <>
      <div onClick={onCloseDrawerFilter}>{mainFilterButton}</div>
      {drawerFilter?.element && open && (
        <Drawer className="w-screen max-w-[340px]" onClose={onCloseDrawerFilter}>
          <Drawer.SingleView className="!px-0 !py-1 !space-y-0">
            <Drawer.Header
              onClose={onCloseDrawerFilter}
              headerLabel={<span className="text-base self-center text-[#111827] dark:text-white font-medium">Filters</span>}
              className="px-3 pb-2"
              headerChild={
                drawerClearAll && (
                  <p className="underline h-fit pt-1 text-[13px] cursor-pointer text-[#9061F9]" onClick={() => drawerClearAll()}>
                    Clear All
                  </p>
                )
              }
              children={
                resultsFound !== null && (
                  <p className="text-sm">
                    <span className="font-medium">{resultsFound}</span> results found
                  </p>
                )
              }
            />
            <Drawer.Body className="flex flex-col">{drawerFilter?.element}string</Drawer.Body>
          </Drawer.SingleView>
        </Drawer>
      )}
    </>
  ) : (
    <DropdownMenu.Root open={open} onOpenChange={setOpen}>
      <DropdownMenu.Trigger asChild>{mainFilterButton}</DropdownMenu.Trigger>

      <DropdownMenu.Content
        className="min-w-[200px] max-w-[250px] bg-white border border-[#e4e4e7] shadow-sm rounded-lg p-3 mr-2 min-[425px]:mr-12 dark:border-[#27272a] dark:bg-darkGrayBackground z-50"
        sideOffset={5}
      >
        <div className="text-gray-700 dark:text-white border-b border-b-gray-200 dark:border-b-[#27272a] px-4 pb-2">Filters</div>
        <div className="mt-1.5">
          {filters?.map((filter, index) => (
            <div key={index}>{renderFilterOptionsManual(filter)}</div>
          ))}
        </div>

        <div className="flex justify-end gap-2 mt-2 border-t dark:text-white border-gray-200 dark:border-[#27272a]">
          <button
            className="px-4 py-2 pb-1 text-sm font-medium underline"
            onClick={() => {
              // Reset all filter options
              filters?.forEach((filter) => filter!.options.forEach((option) => option.reset()));

              // Reset date pickers inline
              handleDates?.startDate?.update(null);
              handleDates?.endDate?.update(null);
            }}
          >
            Reset
          </button>
        </div>
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};
