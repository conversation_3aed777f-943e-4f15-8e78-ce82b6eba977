// React
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// Core - Updated import to use new table system

// Flowbite
import { Tooltip } from 'flowbite-react';
import { Api, hideConfirm, PlanFeatures, showConfirm, useAppDispatch, useFetchList, useScreenSize } from 'UI/src';
import { setErrorNotify, setNotifyMessage, useUserPermissions, UserPermissions } from 'UI';

import { RootState, useAppSelector, UserData } from 'UI/src';
import type { ColumnDef, questionsListRes } from 'UI';
import { Tags } from 'UI/src';
import { CategoryFieldColumn, Icon, NameFieldColumn, SubcategoryFieldColumn } from '@/components';
// import DataTable from 'UI/src/components/table/components/data-table copy';

interface DeleteQuestionResponse {
  success: boolean;
  message: string;
}

interface DeleteQuestionsRequest {
  ids: string[];
}

export const QuestionsListPage = () => {
  // State
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [showMoreMap, setShowMoreMap] = useState<Record<string, boolean>>({});
  const [backupList, setBackupList] = useState<questionsListRes[]>([]);
  const [needSubscription, setNeedSubscription] = useState(false);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [filterCountNumber, setFilterCountNumber] = useState(0);

  // UI Hooks
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { hasPermission } = useUserPermissions();

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');
  const screen = useScreenSize();

  // User data
  const isPermitted = Array.isArray(userData?.role) && userData?.role.some((role) => ['super-admin', 'admin', 'content-creator'].includes(role));
  const isPermittedAuthor = (rowAuthorName: string) => {
    return userData.name === rowAuthorName;
  };

  const initialFilters = {
    /* FIXME: (Fix multi-select) */
    ...(userData.trackId
      ? {}
      : {
          category: {
            label: 'Category',
            lookup: 'category',
          },
        }),
    subCategory: {
      label: 'Sub Category',
      lookup: 'subcategory',
      parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: userData.trackId ? userData.trackId : null },
    },
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
  };

  const {
    ready,
    loading,
    setLoading,
    list: rawList,
    count,
    search,
    pagination,
    filters,
    setFilters,
    refresh,
  } = useFetchList('questions/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  });

  const list = (rawList ?? []) as questionsListRes[];
  const filterFeedData = Object.keys(initialFilters).map((key) => (key === 'difficulty' ? initialFilters.difficulty.enum : key));

  // Helper functions
  const ConfirmText = (value?: number) => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="hugeicons:archive-02" className="text-[#9061F9]" width="40" />
          </div>
        </div>
        {value ? (
          <p>
            Once confirmed, {value} question{value > 1 && 's'} will be archived permanently!
          </p>
        ) : (
          <p>Once confirmed, This question will be archived permanently!</p>
        )}
      </div>
    );
  };

  // Delete Question
  const handleArchive = async (row: { _id: string }) => {
    dispatch(
      showConfirm({
        message: ConfirmText(),
        options: {
          onConfirm: async () => {
            try {
              const response = await Api.delete(`questions/single/${row._id}`, {});
              dispatch(hideConfirm());
              refresh();
              dispatch(setNotifyMessage('Question deleted successfully!'));
            } catch (error: any) {
              dispatch(hideConfirm());
              dispatch(setErrorNotify((error as any).response?.data?.message));
            }
          },
        },
      })
    );
  };

  // Delete all selected ids
  const handleArchiveSelectedIds = async () => {
    if (selectedIds.length) {
      dispatch(
        showConfirm({
          message: ConfirmText(selectedIds.length),
          options: {
            onConfirm: async () => {
              try {
                setLoading(true);
                await Api.delete('questions/multi', { ids: selectedIds });
                setSelectedIds([]);
                refresh();
                dispatch(setNotifyMessage('Questions deleted successfully!'));
              } catch (error: any) {
                dispatch(setErrorNotify(error.response.data.message));
              } finally {
                dispatch(hideConfirm());
                setLoading(false);
              }
            },
          },
        })
      );
    }
  };

  const clearFilter = () => {
    setFilters({});
  };

  // Column definitions for the new table system
  const columns: ColumnDef<questionsListRes>[] = [
    {
      id: 'title',
      header: 'Question',
      accessorKey: 'title',
      width: '28%',
      cellSlot: ({ row }) => (
        <NameFieldColumn id={row._id} name={row.title} showMoreMap={showMoreMap} onClick={() => navigate(`/app/questions/view/${row._id}`)} />
      ),
      labelOnCard: 'Question',
    },
    {
      id: 'difficulty',
      header: 'Difficulty',
      accessorKey: 'difficulty',
      width: '12%',
      cellSlot: ({ row }) => {
        const getDifficultyText = (level: number): string => {
          switch (level) {
            case 1:
              return 'easy';
            case 2:
              return 'medium';
            case 3:
              return 'hard';
            default:
              return 'easy';
          }
        };

        const getDifficultyColor = (level: number): string => {
          switch (level) {
            case 1:
              return 'bg-[#EEFFF1] text-[#056816]';
            case 2:
              return 'bg-[#FFFCDF] text-[#BA8500]';
            case 3:
              return 'bg-[#FFECE9] text-[#A80000]';
            default:
              return 'bg-[#EEFFF1] text-[#056816]';
          }
        };

        return (
          <div className="w-fit">
            <Tags type={getDifficultyText(row.difficulty)} color={getDifficultyColor(row.difficulty)} />
          </div>
        );
      },
      labelOnCard: 'Difficulty',
    },
    {
      id: 'categoryName',
      header: 'Category',
      accessorKey: 'categoryName',
      width: '10%',
      cellSlot: ({ row }) => <CategoryFieldColumn categoryNameArray={row.categoryName} />,
      labelOnCard: 'Category',
    },
    {
      id: 'subCategoryName',
      header: 'Sub Category',
      accessorKey: 'subCategoryName',
      width: '14%',
      cellSlot: ({ row }) => <SubcategoryFieldColumn subCategoryName={row.subCategoryName} />,
      labelOnCard: 'Sub Category',
    },
    {
      id: 'actions',
      header: 'Actions',
      width: '8%',
      cellSlot: ({ row }) => {
        const buttons = [
          {
            label: 'View',
            customIcon: 'eye',
            iconWidth: '22',
            iconHeight: '22',
            color: 'text-black dark:text-white',
            path: `/app/questions/view/${row._id}`,
          },
          ...(hasPermission(UserPermissions.UPDATE_QUESTION)
            ? [
                {
                  label: 'Edit',
                  customIcon: 'edit',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/questions/edit/${row._id}`,
                },
              ]
            : []),
          ...(isPermittedAuthor(row.authorName) || hasPermission(UserPermissions.ARCHIVE_QUESTION)
            ? [
                {
                  label: 'Archive',
                  customIcon: 'archive',
                  iconWidth: '18',
                  color: 'text-black dark:text-white',
                  onClick: () => handleArchive(row),
                },
              ]
            : []),
        ];

        return (
          <div className="flex items-center gap-2">
            {buttons.map((button, index) => (
              <Tooltip key={index} content={button.label}>
                {button.path ? (
                  <button onClick={() => navigate(button.path!)} className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                    <Icon icon={`lucide:${button.customIcon}`} width={button.iconWidth} height={button.iconHeight} className={button.color} />
                  </button>
                ) : (
                  <button onClick={button.onClick} className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700">
                    <Icon icon={`lucide:${button.customIcon}`} width={button.iconWidth} className={button.color} />
                  </button>
                )}
              </Tooltip>
            ))}
          </div>
        );
      },
      meta: { variant: 'actions' },
      hideOnMobile: false,
    },
  ];

  useEffect(() => {
    if (backupList.length === 0 && list.length > 0) {
      setBackupList(list);
    }
  }, [list, backupList.length]);

  if (!ready) {
    return <div>Loading...</div>; // Or your loading component
  }

  return (
    <>
      <DataTable
        data={list}
        columns={columns}
        loading={loading}
        sectionTitle="Questions"
        sectionBadgeTitle={count}
        // search={{
        //   value: search.value,
        //   placeholder: screen.customScreen ? 'Search by name or author' : 'Name or author',
        //   update: search.update,
        // }}
        // filters={filters}
        // setFilters={setFilters}
        // filterFeedData={filterFeedData}
        addButtonLabel={isPermitted ? 'Create Question' : ''}
        addButtonFeature={PlanFeatures.CREATE_CUSTOM_QUESTIONS}
        addButtonPermission={UserPermissions.CREATE_QUESTION}
        onClickAdd={() => navigate('/app/questions/create')}
        // Pagination
        pagination={{
          pages: pagination.pagesCount,
          currentPage: pagination.currentPage,
          onPageChange: (page) => pagination.update({ page }),
          onNext: () => pagination.update({ page: pagination.currentPage + 1 }),
          onPrevious: () => pagination.update({ page: Math.max(1, pagination.currentPage - 1) }),
        }}
        // Row identification
        getRowId={(row, index) => row._id || String(index)}
        renderRowCard={(row, index) => (
          <div className="bg-white p-4 rounded-lg shadow border">
            <div
              className="font-semibold text-blue-600 cursor-pointer hover:underline mb-2"
              onClick={() => navigate(`/app/questions/view/${row._id}`)}
            >
              {row.title}
            </div>

            <div className="flex flex-wrap gap-2 mb-3">
              <Tags
                type={['easy', 'medium', 'hard'][row.difficulty - 1] || 'easy'}
                color={
                  ['bg-[#EEFFF1] text-[#056816]', 'bg-[#FFFCDF] text-[#BA8500]', 'bg-[#FFECE9] text-[#A80000]'][row.difficulty - 1] ||
                  'bg-[#EEFFF1] text-[#056816]'
                }
              />
            </div>

            <div className="text-sm text-gray-600 mb-2">
              <CategoryFieldColumn categoryNameArray={row.categoryName} />
            </div>

            <div className="text-sm text-gray-600 mb-3">
              <SubcategoryFieldColumn subCategoryName={row.subCategoryName} />
            </div>

            <div className="flex gap-2 pt-2 border-t">
              <button
                onClick={() => navigate(`/app/questions/view/${row._id}`)}
                className="flex items-center gap-1 px-3 py-1 bg-blue-500 text-white rounded text-sm"
              >
                <Icon icon="lucide:eye" width="16" />
                View
              </button>

              {hasPermission(UserPermissions.UPDATE_QUESTION) && (
                <button
                  onClick={() => navigate(`/app/questions/edit/${row._id}`)}
                  className="flex items-center gap-1 px-3 py-1 bg-green-500 text-white rounded text-sm"
                >
                  <Icon icon="lucide:edit" width="16" />
                  Edit
                </button>
              )}

              {(isPermittedAuthor(row.authorName) || hasPermission(UserPermissions.ARCHIVE_QUESTION)) && (
                <button onClick={() => handleArchive(row)} className="flex items-center gap-1 px-3 py-1 bg-red-500 text-white rounded text-sm">
                  <Icon icon="lucide:archive" width="16" />
                  Archive
                </button>
              )}
            </div>
          </div>
        )}
        // Placeholder when no data
        placeholder={{
          title: 'No questions created yet',
          subTitle: 'Add questions to build your question bank.',
          image: '/UI/src/assets/placeholder/NoQuestions.svg',
        }}
      />
    </>
  );
};
