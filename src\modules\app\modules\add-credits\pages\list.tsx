/* eslint-disable */
import React, { useState, useEffect, useContext } from 'react';
import { <PERSON><PERSON>, ImageUploader, Jumbotron, CustomIcon } from 'src';
import { Api } from 'UI/src';
import { useNavigate } from 'react-router-dom';

interface CartItem {
  id: number | string;
  name: string;
  price: number;
  quantity: number;
}

interface FeatureTypes {
  id: number | string;
  _id?: number | string;
  name: string;
  price: number;
}

const features = [
  { id: 1, name: 'Applicants', price: 20 },
  { id: 2, name: 'Tests', price: 15 },
  { id: 3, name: 'Interview', price: 25 },
  { id: 4, name: 'Questions', price: 10 },
  { id: 5, name: 'Assessment', price: 30 },
  { id: 6, name: 'Reports', price: 18 },
  { id: 7, name: 'Analytics', price: 22 },
  { id: 8, name: 'Export', price: 12 },
  { id: 9, name: 'Notifications', price: 8 },
  { id: 10, name: 'Integrations', price: 40 },
  { id: 11, name: 'Support', price: 5 },
  { id: 12, name: 'Custom Feature', price: 50 },
];
const tabs = ['All', 'Test', 'Interview', 'Question', 'Applicant'];
export const AddCreditsComponent = () => {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [selectedTab, setSelectedTab] = useState('All');
  const [features, setFeatures] = useState([]);
  const navigate = useNavigate();
  const handleGet = async () => {
    try {
      const response = await Api.get('/quota/list');
      // Debug response

      // Try to extract array
      let arr = [];
      if (Array.isArray(response?.data)) arr = response.data;
      else if (Array.isArray(response?.data?.items)) arr = response.data.items;
      else if (Array.isArray(response?.data?.features)) arr = response.data.features;
      arr = arr.map((f: FeatureTypes) => ({ ...f, id: f.id || f._id }));
      setFeatures(arr);
    } catch (error) {}
  };
  const availableFeatures = features.filter((feature: FeatureTypes) => !cart.some((item: CartItem) => item.id === feature.id));
  const handleAddToCart = (feature: FeatureTypes) => {
    setCart((prev) => {
      const exists = prev.find((item: CartItem) => item.id === feature.id);
      if (exists) {
        return prev.map((item: CartItem) => (item.id === feature.id ? { ...item, quantity: item.quantity + 1 } : item));
      }
      return [...prev, { ...feature, quantity: 1 }];
    });
  };
  const handleIncrease = (id: number | string) => {
    setCart((prev) => prev.map((item) => (item.id === id ? { ...item, quantity: item.quantity + 1 } : item)));
  };
  const handleDecrease = (id: number | string) => {
    // TODO: Fix this logic error
    // setCart((prev) =>
    //   prev.reduce((acc, item) => {
    //     if (item.id === id) {
    //       if (item.quantity === 1) {
    //         return acc;
    //       } else {
    //         return [...acc, { ...item, quantity: item.quantity - 1 }];
    //       }
    //     }
    //     return [...acc, item];
    //   }, [])
    // );
  };
  const handleRemove = (id: number | string) => {
    setCart((prev) => prev.filter((item: CartItem) => item.id !== id));
  };
  const handleQuantityChange = (id: number | string, value: number) => {
    setCart((prev) => prev.map((item: CartItem) => (item.id === id ? { ...item, quantity: value < 1 ? 1 : value } : item)));
  };
  const total = cart.reduce((sum: number, item: CartItem) => sum + item.price * item.quantity, 0);
  // Fetch features from API on mount
  useEffect(() => {
    handleGet();
  }, []);
  return (
    <div className="w-full space-y-4">
      {/* Title & Description */}
      <Jumbotron />
      {/* Main Content */}
      <div className="flex flex-col lg:flex-row gap-4 lg:gap-6">
        {/* Features Table */}
        {availableFeatures.length === 0 ? (
          <div className="bg-white rounded-2xl  border border-[#B7A7F7] w-full max-w-full lg:max-w-[923px] max-h-36 overflow-auto p-0  ">
            <div className="bg-[#FAFAFF] px-4 md:px-6  py-3 md:py-4 border-b border-[#E5E7EB]">
              <span className="font-semibold text-[#07181F] text-base md:text-lg">Available Features</span>
            </div>
            <div className="px-4 flex items-center justify-center py-6 mt-1  md:px-6 text-gray-400 text-xs md:text-sm text-start p-3 w-full">
              No available features
            </div>
          </div>
        ) : (
          <>
            <div className="bg-white rounded-2xl border border-gray-200 w-full max-w-full lg:max-w-[923px] overflow-auto p-0">
              <div className="bg-[#FAFAFF] px-4 md:px-6 py-3 md:py-4 border-b border-[#E5E7EB]">
                <span className="font-semibold text-[#07181F] text-base md:text-lg">Available Features</span>
              </div>
              {/* Tabs */}
              {/* <div className="flex gap-2 mb-4 m-auto overflow-x-auto whitespace-nowrap scrollbar-thin scrollbar-thumb-gray-200 px-4 md:px-6 pt-4">
                {tabs.map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setSelectedTab(tab)}
                    className={`px-3 py-1 md:px-4 md:py-1.5 rounded-full border m-a text-xs md:text-sm font-medium transition-colors duration-150 ${
                      selectedTab === tab ? 'bg-primary text-[#7E3AF2] border-primary' : 'bg-gray-50 text-gray-500 border-gray-200 hover:bg-gray-100'
                    }`}
                  >
                    {tab}
                  </button>
                ))}
              </div> */}
              <div className="border-t border-[#E5E7EB] w-full"></div>
              {/* Features List */}
              <div className="px-4 md:px-6 pb-4">
                {availableFeatures.map((feature: FeatureTypes) => (
                  <div key={feature.id} className="flex items-center justify-between py-2 md:py-3 border-b border-[#E5E7EB] last:border-b-0">
                    <span className="text-[#100A55] w-24 md:w-32 text-sm text-nowrap md:text-base font-medium">{feature.name}</span>
                    <div className="flex items-center gap-1 md:gap-2">
                      <span className="text-[#8E8E93] font-normal text-base md:text-xl">{feature.price} $</span>
                      <button
                        className="w-7 h-7 md:w-8 md:h-8 flex items-center justify-center ml-5  border-[#B7A7F7] bg-white text-[#7E3AF2] hover:bg-[#F3EDFF] transition"
                        onClick={() => handleAddToCart(feature)}
                      >
                        <CustomIcon definedIcon="addWithBlueSquare" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
        {/* Shopping Cart */}
        {cart.length === 0 ? (
          <div className="rounded-2xl  border border-[#B7A7F7] w-full max-w-full lg:max-w-[923px] max-h-36 overflow-auto p-0  ">
            <div className="bg-[#FAFAFF] px-4 md:px-6  py-3 md:py-4 border-b border-[#E5E7EB]">
              <span className="font-semibold text-[#07181F] text-base md:text-lg">Shopping Cart</span>
            </div>
            <div className="px-4 flex items-center justify-center py-6 mt-1  md:px-6 text-gray-400 text-xs md:text-sm text-start p-3 w-full">
              Your shopping cart is empty. Select a feature to get started.
            </div>
          </div>
        ) : (
          <div className="w-full ">
            <div className=" rounded-2xl border border-[#B7A7F7] w-full max-w-full lg:max-w-[923px]   p-0 relative flex flex-col">
              {/* Header with background and border bottom */}
              <div className="bg-[#FAFAFF] px-4 md:px-6 py-3 md:py-4 border-b border-[#E5E7EB]">
                <span className="font-semibold text-base md:text-lg">Shopping Cart</span>
              </div>
              {/* Cart Items */}
              <div className="px-2 md:px-4 ">
                {cart.map((item: CartItem) => (
                  <div
                    key={item.id}
                    className="flex flex-col md:flex-row md:items-center justify-between p-2 md:p-6 py-2 md:py-3 border-b border-[#E5E7EB] last:border-b-0 gap-2 md:gap-0"
                  >
                    <span className="text-[#100A55] font-medium text-sm md:text-base">{item.name}</span>
                    <div className="flex items-center justify-between md:justify-end gap-2 md:gap-3 lg:gap-5 w-full md:w-auto">
                      <div className="flex items-center gap-1 md:gap-2">
                        <button
                          className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 flex items-center justify-center rounded-full border border-[#B7A7F7] bg-white text-[#7E3AF2] hover:bg-[#F3EDFF] transition text-xs md:text-sm lg:text-base"
                          onClick={() => handleDecrease(item.id)}
                        >
                          -
                        </button>
                        <input
                          type="number"
                          min={1}
                          className="w-12 md:w-16 lg:w-18 h-6 md:h-7 lg:h-8 flex items-center justify-center bg-[#F6F6FA] border border-[#E5E7EB] rounded text-black text-xs md:text-sm lg:text-base font-semibold text-center outline-none"
                          value={item.quantity}
                          onChange={(e) => handleQuantityChange(item.id, Number(e.target.value))}
                        />
                        <button
                          className="w-6 h-6 md:w-7 md:h-7 lg:w-8 lg:h-8 flex items-center justify-center rounded-full border border-[#B7A7F7] bg-white text-[#7E3AF2] hover:bg-[#F3EDFF] transition text-xs md:text-sm lg:text-base"
                          onClick={() => handleIncrease(item.id)}
                        >
                          +
                        </button>
                      </div>
                      <div className="flex items-center gap-2 md:gap-3">
                        <span className="text-[#8E8E93] font-normal text-base lg:text-xl text-center min-w-[40px] md:min-w-[50px] lg:min-w-[60px]">
                          {item.price * item.quantity} $
                        </span>
                        <CustomIcon
                          definedIcon="trash"
                          className="w-4 h-4 md:w-5 md:h-5 lg:w-6 lg:h-6 cursor-pointer hover:opacity-80 transition"
                          stroke="#C24444"
                          onClick={() => handleRemove(item.id)}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              {/* Total & Checkout Bottom */}
              <div className="w-full b p-6  border-t border-[#E5E7EB] z-10">
                <div className="flex items-center justify-between text-xl  md:text-2xl font-semibold mb-0">
                  <span>Total</span>
                  <span>{total} $</span>
                </div>
                <Button
                  label="Checkout"
                  className="mx-auto w-1/2 "
                  onClick={() => {
                    // const result = Object.fromEntries(cart.map((item) => [item.name, item.price]));
                    const result = {
                      pricing: {
                        1: {
                          features: Object.fromEntries(cart.map((item: CartItem) => [item.name, item.quantity, item.price])),
                          // durationInDays: 365,
                          currency: 'USD',
                          discountedAmount: total,
                        },
                      },
                    };
                    // setShoppingCartData(result);
                    navigate('/payment/quota');
                  }}
                />
                {/* <button className="w-1/2 m-auto block py-2 md:py-3 rounded-xl mb-7 bg-[#7E3AF2] text-white font-semibold text-base md:text-lg hover:bg-[#6F3ED8] transition ">
                  Checkout
                </button> */}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
