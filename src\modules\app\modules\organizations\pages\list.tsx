// React
import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

// Core
import { Jumbotron, Button, ScrollableTabs } from 'src';

// Components
import { OrganizationList } from '../components/list/organizations';
import { TicketsList } from '../components/list/tickets';
import { ActivityList } from '../components/list/activity';
import { AvatarList } from '../components/list/avatar';

export const OrganizationsListPage = () => {
  // Hooks
  const navigate = useNavigate();
  const location = useLocation();

  // State
  const [activeTab, setActiveTab] = useState(0);

  // Set active tab from navigation state if available
  useEffect(() => {
    if (location.state?.activeTab !== undefined) {
      setActiveTab(location.state.activeTab);
    }
  }, [location.state]);

  // Data
  const tabs = [
    { title: 'Organizations', component: <OrganizationList /> },
    // { title: 'Tickets', component: <TicketsList /> },
    // { title: 'Activity Logs', component: <ActivityList /> },
    // { title: 'Avatars', component: <AvatarList /> },
  ];

  return (
    <div className="space-y-4">
      <Jumbotron />

      {/* <ScrollableTabs
        data={tabs}
        selectedTab={{
          activeTab: activeTab,
          setActiveTab: setActiveTab,
        }}
        fullWidth
      /> */}

      {tabs[activeTab]?.component}
    </div>
  );
};
