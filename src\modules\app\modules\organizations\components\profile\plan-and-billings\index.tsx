// React
import { useEffect, useState } from 'react';
import { useParams, Navigate, useNavigate } from 'react-router-dom';

// Date format
import { addDays, format, isValid } from 'date-fns';

// Flowbite
import { Tooltip } from 'flowbite-react';

import { OrganizationsPlanOverview, RootState, useAppSelector, UserData, BillingCycle, Tags } from 'UI/src';

// Core
import {
  Icon,
  CustomIcon,
  ToggleFilter,
  Button,
  TestDifficulty,
  TestSeniorityLevel,
  AvarageScore,
  Table,
  EnumText,
  NameFieldColumn,
  CurrencySymbol,
  PaymentStatus,
  FormatDateFieldColumn,
  InvoiceFieldColumn,
} from 'src';

// Components
import { InlineFilter } from '../../inline-filter';
import { PlanManagement } from './plan-management';
import { StaticData, Api, useAppDispatch, useFetchList, useScreenSize } from 'UI/src';
import { setErrorNotify, setNotifyMessage } from 'UI';

export const PlanAndBillingsProfile = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Permissions
  const isPermitted = Array.isArray(userData?.role) && userData?.role.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [isPlanManagementVisible, setPlanManagementVisibilty] = useState(false);
  const [organizationPlanOverviewData, setOrganizationPlanOverviewData] = useState<OrganizationsPlanOverview>();

  // Hooks
  const navigate = useNavigate();
  const screen = useScreenSize();
  const dispatch = useAppDispatch();
  const { id } = useParams();
  const initialFilters = {
    type: {
      label: 'Plan',
      enum: 'PlanType',
    },
    // ...(userData.trackId
    //   ? {}
    //   : {
    //       category: {
    //         label: 'Category',
    //         lookup: 'category',
    //       },
    //     }),
    // status: {
    //   label: 'Status',
    //   enum: 'SubscriptionStatus',
    // },
  };
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters, refresh, handleDates } = useFetchList(
    `subscription/list`,
    {
      id,
      search: '',
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
    }
  );

  // @ts-ignore
  const filterFeedData = Object.keys(initialFilters).map((key) => (key === 'difficulty' ? initialFilters.difficulty.enum : key));

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get<OrganizationsPlanOverview>(`organizations/plan/overview/${id}`, {});
      console.log(`organizations/plan/overview/${id}`, response.data);
      setOrganizationPlanOverviewData(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  const formatDate = (dateString: Date) => {
    if (!dateString) return '—';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short',
    }).format(date);
  };

  const handleStatus = (type: number) => {
    if (type === 2) {
      // Green
      return {
        className: 'bg-[#ecfdf3] text-[#067647] border border-[#abefc6]',
      };
    }
    if (type === 4) {
      // Red
      return {
        className: 'bg-[#fef3f2] text-[#B42318] border border-[#FECDCA]',
      };
    }
    if (type === 1) {
      // Yellow
      return {
        className: 'bg-[#f9f7ce] text-[#ab6512] border border-[#fee3ca]',
      };
    }
    if (type === 3) {
      // Blue
      return {
        className: 'bg-[#eff6ff] text-[#374151]   border border-[#bfdbfe]',
      };
    }
    if (type === 5) {
      return {
        className: 'bg-[#f3f4f6] text-[#1d4ed8]  border border-[#d1d5db]',
      };
    }
  };

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  useEffect(() => {
    handleGet();
  }, []);

  return (
    <div>
      <div className="space-y-4">
        {(organizationPlanOverviewData as any)?.length > 0 && (
          <div className="flex justify-between items-center p-5 rounded-xl shadow-md border bg-gradient-to-b from-white to-[#EADBF7]">
            <div className="space-y-2">
              {(organizationPlanOverviewData as any)?.map?.((plan: any) => (
                <>
                  <h2 className="text-[#5E2EC3] font-semibold text-lg">{plan.name}</h2>
                  <div className="flex items-end gap-2 text-2xl font-bold text-black">
                    {plan.price}
                    <span className="text-sm font-normal text-gray-500">/per year</span>
                    <span className="text-sm text-[#566577] font-semibold">Expiry date {formatDate(plan.endDate)}</span>
                  </div>
                  <Button label="Manage Plan" className="rounded-2xl" tertiary onClick={() => setPlanManagementVisibilty(true)} />
                </>
              ))}
            </div>
          </div>
        )}

        {/* Need to add to table main buttons */}
        {/* <div className="calendar flex flex-row justify-end items-center gap-2 text-gray-700 dark:text-gray-300 rounded-lg h-fit">
          <Datepicker
            className="inline-block w-full sm:w-44"
            // onSelectedDateChanged={() => {}}
            showTodayButton={false}
            showClearButton={false}
            // value=""
          />
        </div> */}

        <Table
          ready={ready}
          loading={loading}
          title="Plans and Billings List"
          searchPlaceholder="Search for plans and billings..."
          count={count}
          search={search}
          filters={filters}
          // setFilters={setFilters}
          // filterFeedData={filterFeedData}
          // drawerFilter={{
          //   filterCountNumber: filterCountNumber,
          //   isShowDrawerFilter: isShowDrawerFilter,
          //   setShowDrawerFilter: setShowDrawerFilter,
          // }}
          pagination={pagination}
          rows={list}
          backupRows={backupList}
          slots={{
            paymentId: (_: string, row: any) => {
              return (
                <div className="flex items-center gap-2">
                  <InvoiceFieldColumn id={row?._id} tol invoice={row?.paymentId} showMoreMap={showMoreMap} />
                </div>
              );
            },
            date: (_: string, row: { createdAt: Date }) => <FormatDateFieldColumn date={row?.createdAt} />,
            status: (value: number) => {
              const typeMap: Record<number, string> = {
                1: 'pending',
                2: 'paid',
                3: 'cancelled',
                4: 'failed',
                5: 'expired',
              };
              const tagType = typeMap[value] ?? 'pending';
              return (
                <div className="w-fit">
                  <Tags type={tagType} />
                </div>
              );
            },
            amount: (_: string, row: { price: number; currency: string }) => {
              return (
                <p>
                  {row?.currency && <CurrencySymbol currency={row?.currency} />}
                  {row?.price}
                </p>
              );
            },
            type: (_: string, row: any) => {
              return (
                <div className="flex flex-col gap-1">
                  {row?.couponCode ? (
                    <>
                      <div className="flex items-center text-sm gap-2 capitalize">
                        <span className="w-2 h-2 block bg-yellow-300 rounded-full"></span>
                        <span className="text-[#535862]">Coupon</span>
                      </div>
                      <span className="text-[#8DA0C0] text-[12px] px-3">{row?.couponCode}</span>
                    </>
                  ) : (
                    <div className="flex items-center text-sm gap-2 capitalize">
                      <span className="w-2 h-2 block bg-[#A379FC] rounded-full"></span>
                      <span className="text-[#535862]">Norm</span>
                    </div>
                  )}
                </div>
              );
            },
            planName: (_: string, row: any) => {
              return (
                <div className="space-y-2">
                  <p className="text-black">{row?.name} Plan</p>
                  <p className="text-sm">{BillingCycle[row?.billingCycle]} Subscription</p>
                </div>
              );
            },
          }}
          columns={[
            {
              key: 'paymentId',
              label: 'Invoice',
              primary: true,
              width: '25%',
            },
            {
              key: 'planName',
              label: 'Purchase',
              // primary: true,
              width: '15%',
              inline: true,
            },
            {
              key: 'amount',
              label: 'Amount',
              // primary: true,
              width: '15%',
              inline: true,
            },
            {
              key: 'type',
              label: 'Type',
              // primary: true,
              width: '15%',
              inline: true,
            },

            {
              key: 'status',
              label: 'Status',
              primary: true,
              width: '18%',
            },
            {
              key: 'date',
              label: 'Created At',
              primary: true,
              width: '15%',
            },
          ]}
          // multiSelectedRow={{
          //   selectedIds: selectedIds,
          //   setSelectedIds: setSelectedIds,
          //   handleArchiveSelectedIds: handleArchiveSelectedIds,
          // }}
          placeholder={{
            title: 'No subscription plans created',
            subTitle: 'Create your first subscription plan to start offering paid services.',
            image: '/UI/src/assets/placeholder/NoPlans.svg',
          }}
          noDataFoundIconWidth="60"
          noDataFoundIconHeight="60"
          showMoreMap={showMoreMap}
          setShowMoreMap={setShowMoreMap}
          // addButtonLabel=""
          // onClickAdd={() => {}}
          // actions={[]}
          hideJumbotron
          isScrollableTabsExists
          addButtonPermission
        />
      </div>

      {isPlanManagementVisible && <PlanManagement onClose={() => setPlanManagementVisibilty(false)} />}
    </div>
  );
};
