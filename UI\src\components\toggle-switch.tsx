import React from 'react';

interface ToggleSwitchProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  color?: 'purple' | 'blue' | 'green' | 'red' | 'yellow' | 'gray';
  sizing?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
  theme?: any; // For compatibility with existing code
}

export const ToggleSwitch: React.FC<ToggleSwitchProps> = ({
  checked,
  onChange,
  color = 'purple',
  sizing = 'md',
  disabled = false,
  className = '',
  theme, // Accept but ignore theme prop for compatibility
}) => {
  const handleClick = () => {
    if (!disabled) {
      onChange(!checked);
    }
  };

  const getSizeClasses = () => {
    switch (sizing) {
      case 'sm':
        return {
          track: 'w-9 h-5',
          thumb: 'w-4 h-4',
          translate: checked ? 'translate-x-4' : 'translate-x-0.5',
        };
      case 'lg':
        return {
          track: 'w-14 h-7',
          thumb: 'w-6 h-6',
          translate: checked ? 'translate-x-7' : 'translate-x-0.5',
        };
      default: // md
        return {
          track: 'w-11 h-6',
          thumb: 'w-5 h-5',
          translate: checked ? 'translate-x-5' : 'translate-x-0.5',
        };
    }
  };

  const getColorClasses = () => {
    if (disabled) {
      return {
        track: 'bg-gray-200 dark:bg-gray-600',
        thumb: 'bg-gray-300 dark:bg-gray-500',
      };
    }

    if (checked) {
      switch (color) {
        case 'blue':
          return {
            track: 'bg-blue-600',
            thumb: 'bg-white',
          };
        case 'green':
          return {
            track: 'bg-green-600',
            thumb: 'bg-white',
          };
        case 'red':
          return {
            track: 'bg-red-600',
            thumb: 'bg-white',
          };
        case 'yellow':
          return {
            track: 'bg-yellow-500',
            thumb: 'bg-white',
          };
        case 'gray':
          return {
            track: 'bg-gray-600',
            thumb: 'bg-white',
          };
        default: // purple
          return {
            track: 'bg-purple-600',
            thumb: 'bg-white',
          };
      }
    } else {
      return {
        track: 'bg-gray-200 dark:bg-gray-600',
        thumb: 'bg-white',
      };
    }
  };

  const sizeClasses = getSizeClasses();
  const colorClasses = getColorClasses();

  return (
    <button
      type="button"
      onClick={handleClick}
      disabled={disabled}
      className={`
        relative inline-flex items-center rounded-full transition-colors duration-200 ease-in-out
        focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
        ${sizeClasses.track}
        ${colorClasses.track}
        ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
        ${className}
      `}
      role="switch"
      aria-checked={checked}
    >
      <span
        className={`
          inline-block rounded-full transition-transform duration-200 ease-in-out
          ${sizeClasses.thumb}
          ${sizeClasses.translate}
          ${colorClasses.thumb}
          shadow-sm
        `}
      />
    </button>
  );
};
