import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';
import type { SubmissionType } from '../types/Submissions.type';
import type { SubmissionBehaviors } from '../types/Submissions.type';

// Fetch single submission
export const fetchSubmissionDetails = createAsyncThunk(
  'submissions/fetchSubmissionDetails',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get<SubmissionType>(`submissions/single/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch submission');
    }
  }
);

// Create submission
export const createSubmission = createAsyncThunk(
  'submissions/createSubmission',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('submissions/single', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create submission');
    }
  }
);

// Delete submission
export const deleteSubmission = createAsyncThunk(
  'submissions/deleteSubmission',
  async (id: string, { rejectWithValue }) => {
    try {
      await Api.delete(`submissions/single/${id}`, {});
      return id;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to delete submission');
    }
  }
);

// Delete multiple submissions
export const deleteMultipleSubmissions = createAsyncThunk(
  'submissions/deleteMultipleSubmissions',
  async (ids: string[], { rejectWithValue }) => {
    try {
      await Api.delete('submissions/multi', { ids });
      return ids;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to delete submissions');
    }
  }
);

// Lock submission
export const lockSubmission = createAsyncThunk(
  'submissions/lockSubmission',
  async ({ submissionId, status }: { submissionId: string; status: string }, { rejectWithValue }) => {
    try {
      await Api.put(`submissions/lock/${submissionId}?status=${status}`, {});
      return { submissionId, status };
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to lock submission');
    }
  }
);

// Lock multiple submissions
export const lockMultipleSubmissions = createAsyncThunk(
  'submissions/lockMultipleSubmissions',
  async ({ ids, status }: { ids: string[]; status: string }, { rejectWithValue }) => {
    try {
      await Api.put('submissions/lock/multi', { ids, status });
      return { ids, status };
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to lock submissions');
    }
  }
);

// Get submission stages
export const fetchSubmissionStages = createAsyncThunk(
  'submissions/fetchStages',
  async ({ submissionId, page, size }: { submissionId: string; page: number; size: number }, { rejectWithValue }) => {
    try {
      const response = await Api.get(`submissions/stages?submissionId=${submissionId}&page=${page}&size=${size}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch submission stages');
    }
  }
);

// Get submission behavior
export const fetchSubmissionBehavior = createAsyncThunk(
  'submissions/fetchBehavior',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get<SubmissionBehaviors>(`submissions/behavior/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch submission behavior');
    }
  }
);

// Get submission stages report
export const fetchSubmissionStagesReport = createAsyncThunk(
  'submissions/fetchStagesReport',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`submissions/stages/report/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch submission stages report');
    }
  }
);

// Get submissions report
export const fetchSubmissionsReport = createAsyncThunk(
  'submissions/fetchReport',
  async (params: any, { rejectWithValue }) => {
    try {
      const response = await Api.get('submissions/report', params || {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch submissions report');
    }
  }
); 
