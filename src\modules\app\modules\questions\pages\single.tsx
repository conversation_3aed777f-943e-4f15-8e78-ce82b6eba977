// React
import { useEffect, useRef, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { addDays, format, isValid, formatDistanceToNow } from 'date-fns';
import { Button, UserPermissions } from 'UI';

import { hideConfirm, initializeForm, QuestionType, RootState, setFieldValue, showConfirm, useAppSelector, UserData } from 'UI/src';
import type { questionsListRes, UpdateQuestionByIdRes } from 'UI';
import { PermissionProtectedComponent } from 'src/components';

// Flowbite
import { Spinner, Tooltip } from 'flowbite-react';

// Core
import {
  Textarea,
  Card,
  // Button,
  Select,
  Icon,
  Jumbotron,
  CustomIcon,
  CategoryFieldColumn,
  SubcategoryFieldColumn,
  TestDifficulty,
  EnumText,
  TextInput,
} from 'src';
import { Api, Regex, useValidate, Form, useAppDispatch } from 'UI/src';
import { setErrorNotify, setNotifyMessage, QuestionTypeEnum } from 'UI';

export const QuestionsSinglePage = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);
  const isViewOnly = useAppSelector((state: RootState) => state.viewOnly.isVisible);

  // Params
  const { id } = useParams<{ id?: string }>();

  // Hooks
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { isRequired, isNotSpaces } = useValidate();

  // Reference
  const subCategoryRef = useRef<HTMLInputElement | null>(null);
  const topicRef = useRef<HTMLInputElement | null>(null);

  // State
  const [exitAfterSubmission, setExitAfterSubmission] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isShowGenerateWithAICard, setShowGenerateWithAICard] = useState<boolean>(false);
  const [showAnswers, setShowAnswers] = useState(false);

  // Form
  const initialFormState: Partial<questionsListRes> & {
    notes: string;
    options: { id: number; label: string }[];
    type: number;
    singleChoiceAnswer: number;
    multiChoiceAnswer: Record<number, boolean>;
    topic: string;
    subCategory: string;
    category: string | null;
  } = {
    // Meta
    title: '',
    difficulty: 0,
    notes: '',
    subCategory: '',
    topic: '',
    category: null,
    // Options
    options: [
      {
        id: 1,
        label: '',
      },
      {
        id: 2,
        label: '',
      },
    ],
    // Categorization
    type: 1,
    // Answers
    singleChoiceAnswer: 1,
    multiChoiceAnswer: {
      1: false,
      2: false,
      3: false,
      4: false,
    },
  };

  const form = useAppSelector((state: RootState) => state.form.data);
  useEffect(() => {
    dispatch(initializeForm(initialFormState));
  }, []);

  const errorTriggerWords = ['difficulty', 'category', 'SubCategory', 'topic', 'title'];

  const handleMinTwoAnswers = () => {
    let counter = 0;
    Object.entries(form.multiChoiceAnswer).forEach(([key, value]) => {
      value && counter++;
    });
    if (form.type === QuestionTypeEnum.Multichoice) {
      if (counter === 2) {
        return true;
      }
      return dispatch(setErrorNotify('Choose two answers'));
    }
    return true;
  };

  const handleGet = async () => {
    try {
      const response = await Api.get<QuestionType>(`questions/single/${id}`, {});
      console.log(`questions/single/${id}`, response.data);
      dispatch(initializeForm(response.data));
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    }
  };

  const scrollToError = (error: string) => {
    errorTriggerWords.map((word: string) => {
      if (error.toLowerCase().includes(word.toLowerCase())) {
        const el = document.getElementById(word);
        el?.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    });
  };

  const ConfirmText = (value?: number) => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="uil:question" className="text-[#9061F9]" width="40" />
          </div>
        </div>
        {value ? (
          <p>
            Once confirmed, {value} question{value > 1 && 's'} will be {id ? 'updated permanently!' : 'created'}
          </p>
        ) : (
          <p>Once confirmed, This question will be {id ? 'updated permanently!' : 'created'} </p>
        )}
      </div>
    );
  };

  const handleCheckEssayQuestion = () => {
    if (form.type === QuestionTypeEnum.Singlechoice) {
      if (form.options[0].label === '' || form.options[1].label === '') {
        dispatch(setErrorNotify("Answers field shouldn't be empty"));
      } else {
        isEditMode() ? handleUpdate() : handleInsert();
      }
    } else if (form.type === QuestionTypeEnum.Multichoice) {
      const filledOptions = form.options.filter((option: { id: number; label: string }) => option.label && option.label.trim() !== '');
      if (filledOptions.length < 3) {
        return dispatch(setErrorNotify('For multiple choice questions, at least three answers are required.'));
      }
      isEditMode() ? handleUpdate() : handleInsert();
    } else if (form.type === QuestionTypeEnum.Essay) {
      isEditMode() ? handleUpdate() : handleInsert();
    }
  };

  const handleInsert = async () => {
    const canSubmit = form.type === QuestionTypeEnum.Multichoice ? handleMinTwoAnswers() : true;

    if (canSubmit) {
      /* FIXME: confirmDialog */
      // dispatch(
      //   showConfirm({
      //     message: ConfirmText(),
      //     options: {
      //       onConfirm: async () => {
      try {
        setIsLoading(true);
        const submissionForm = { ...form };
        if (submissionForm.type === QuestionTypeEnum.Singlechoice || submissionForm.type === QuestionTypeEnum.Multichoice) {
          submissionForm.options = submissionForm.options.filter(
            (option: { id: number; label: string }) => option.label && option.label.trim() !== ''
          );
        }
        await Api.post('questions/single', submissionForm);
        dispatch(setNotifyMessage('Question added successfully!'));
        if (exitAfterSubmission) {
          navigate('/app/questions');
        } else {
          const { topic, difficulty, subCategory, category } = { ...form };
          dispatch(initializeForm({ ...initialFormState, topic, difficulty, subCategory, category }));
        }
      } catch (error: any) {
        scrollToError(error.response?.data.message);
        dispatch(setErrorNotify(error.response?.data.message));
      } finally {
        dispatch(hideConfirm());
        setIsLoading(false);
      }
      //       },
      //     },
      //   })
      // );
    }
  };

  const handleUpdate = async (): Promise<void> => {
    const canSubmit = form.type === QuestionTypeEnum.Multichoice ? handleMinTwoAnswers() : true;

    if (canSubmit) {
      if (form.title != '') {
        /* FIXME: confirmDialog */
        // dispatch(
        //   showConfirm({
        //     message: ConfirmText(),
        //     options: {
        //       onConfirm: async () => {
        try {
          const submissionForm = { ...form };
          if (submissionForm.type === QuestionTypeEnum.Singlechoice || submissionForm.type === QuestionTypeEnum.Multichoice) {
            submissionForm.options = submissionForm.options.filter(
              (option: { id: number; label: string }) => option.label && option.label.trim() !== ''
            );
          }
          const response = await Api.put(`questions/single/${id}`, submissionForm);
          navigate('/app/questions');
          dispatch(setNotifyMessage('Question updated successfully!'));
        } catch (error) {
          dispatch(setErrorNotify((error as any).response.data.message));
        } finally {
          dispatch(hideConfirm());
        }
        //       },
        //     },
        //   })
        // );
      } else {
        document.getElementById('title')?.scrollIntoView({ behavior: 'smooth', block: 'end' });
        dispatch(setErrorNotify(`Content shouldn't be empty`));
      }
    }
  };

  const handleAddOption = () => {
    if (form.options.length >= 4) return;
    const newOption = {
      id: form.options.length + 1,
      label: '',
    };

    dispatch(setFieldValue({ path: 'options', value: [...form.options, newOption] }));
  };

  const handleRemoveOption = (id: number) => {
    // Remove unwanted form options
    const updatedOptions = form.options.filter((option: { id: number; label: string }) => option.id !== id);
    dispatch(setFieldValue({ path: 'options', value: updatedOptions }));

    // Set removed form multiChoiceAnswer to false
    const updateMultiChoiceAnswer = { ...form.multiChoiceAnswer, [id]: false };
    dispatch(setFieldValue({ path: 'multiChoiceAnswer', value: updateMultiChoiceAnswer }));
  };

  const handleSelectCheckbox = (value: boolean, id: number) => {
    const numberOfSelectedAnswers = Object.entries(form.multiChoiceAnswer).filter(([key, value]) => value).length;
    if (numberOfSelectedAnswers >= 2 && value) {
      return dispatch(setErrorNotify('Only two answers are allowed'));
    }
    const newObj = { ...form.multiChoiceAnswer, [id]: value };

    dispatch(setFieldValue({ path: 'multiChoiceAnswer', value: newObj }));
  };

  const handleGenerateAiQuestion = async () => {
    const payload = {
      category: [form?.category],
      subCategory: [form?.subCategory],
      numOfQuestions: 1,
      difficulty: form?.difficulty,
      type: form?.type,
      notes: form?.notes,
    };

    if (form?.type === 2) {
      payload.notes =
        'I want the answers to be 3 or 4 options, only 2 options of them are correct, Tell me the answers to be object keys is numbers of 1 or 2 or 3 or 4 and value is true or false, ' +
        form?.notes;
    }
    if (payload.notes === '') delete payload.notes;

    try {
      setIsLoading(true);
      const response = await Api.post(`ai-interview/generate/single-question`, payload);
      dispatch(setFieldValue({ path: 'title', value: response?.data?.title }));
      if (response?.data?.type === 1) {
        dispatch(
          setFieldValue({
            path: 'options',
            value: [
              { id: 1, label: 'True' },
              { id: 2, label: 'False' },
            ],
          })
        );
        if (response?.data?.answer === 'True') {
          dispatch(setFieldValue({ path: 'singleChoiceAnswer', value: 1 }));
        } else if (response?.data?.answer === 'False') {
          dispatch(setFieldValue({ path: 'singleChoiceAnswer', value: 2 }));
        }
      } else if (response?.data?.type === 2) {
        dispatch(setFieldValue({ path: 'options', value: response?.data?.options }));
        dispatch(setFieldValue({ path: 'multiChoiceAnswer', value: response?.data?.answer }));
      } else if (response?.data?.type === 3) {
        // dispatch(setFieldValue({path:'options', value: response?.data?.options);
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setIsLoading(false);
    }
  };

  const handleNavigate = () => setExitAfterSubmission(true);

  // Getters
  const isEditMode = () => !!form._id;

  // On Mount
  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, [window.location.href]);

  useEffect(() => {
    // Reset title and answers when type changes
    /* FIXME: Add the reset title filed when change type */
    // setFieldValue('title')('');
    dispatch(setFieldValue({ path: 'notes', value: '' }));
    dispatch(setFieldValue({ path: 'singleChoiceAnswer', value: 1 }));
    dispatch(
      setFieldValue({
        path: 'multiChoiceAnswer',
        value: {
          1: false,
          2: false,
          3: false,
          4: false,
        },
      })
    );

    if (form.type === QuestionTypeEnum.Essay) {
      dispatch(setFieldValue({ path: 'options', value: [] }));
    } else {
      if (form.type === QuestionTypeEnum.Singlechoice) {
        dispatch(
          setFieldValue({
            path: 'options',
            value: [
              { id: 1, label: '' },
              { id: 2, label: '' },
            ],
          })
        );
      }
      if (form.type === QuestionTypeEnum.Multichoice && form.options.length <= 2) {
        dispatch(
          setFieldValue({
            path: 'options',
            value: [
              { id: 1, label: '' },
              { id: 2, label: '' },
              { id: 3, label: '' },
            ],
          })
        );
      }
    }
  }, [form.type]);

  const JumbotronModuleInfo = {
    moduleName: 'question',
    routeName: 'questions',
  };

  const formatDate = (customDate: Date) => {
    const date = new Date(customDate || Date.now());
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, 'dd MMMM , yyyy');
  };

  // Render
  const isDisabled =
    form?.category === null ||
    form?.category?.length <= 0 ||
    form?.subCategory === null ||
    form?.subCategory?.length <= 0 ||
    !form?.topic ||
    !form?.difficulty ||
    !form.type;

  return (
    <Form className="space-y-4 relative" onSubmit={handleCheckEssayQuestion}>
      {/* Jumbotron and Edit Button Row */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
        <Jumbotron />
        {/* {isViewOnly && (
          <div className="flex items-center">
            <Button
              colorType="tertiary"
              label={`Edit ${JumbotronModuleInfo.moduleName}`}
              customIcon="blueEdit"
              onClick={() => navigate(`/app/${JumbotronModuleInfo.routeName}/edit/${id}`)}
              className="text-nowrap capitalize"
            />
          </div>
        )} */}
      </div>

      <div className="space-y-4">
        {!isViewOnly && (
          <Card className="space-y-4 !pb-4 !px-0 !py-0 mt-4">
            <div className="flex items-center gap-[5px] px-4 pb-3 py-4 bg-[#F8FAFC] dark:bg-gray-700 rounded-t-md">
              <p className="dark:text-white thepassHthree">Question Setup</p>
              <Tooltip className="z-[100]" content={'These options auto-fill for each new question to save your time. You can still edit them.'}>
                <Icon
                  icon={'solar:info-circle-outline'}
                  width="18"
                  className="text-[#667085] text-opacity-80 dark:text-gray-200 dark:bg-opacity-90 font-medium"
                />
              </Tooltip>
            </div>
            <div className="px-4 space-y-4">
              {/* Category Select */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 w-full">
                {/* {!userData?.trackId && ( */}
                <Select
                  label="Category"
                  requiredLabel
                  name="category"
                  value={form.category}
                  onChange={(newCategory: string) => {
                    // TODO: Markos
                    subCategoryRef.current?.blur();
                    topicRef.current?.blur();
                    dispatch(setFieldValue({ path: 'category', value: newCategory }));
                    dispatch(setFieldValue({ path: 'subCategory', value: null }));
                    dispatch(setFieldValue({ path: 'topic', value: null }));
                  }}
                  lookup="category"
                  optionValueKey="_id"
                  optionLabelKey="name"
                  dropIcon={true}
                  validators={[isRequired()]}
                  creationOptions={{
                    url: 'lookups/category/single',
                    fieldName: 'name',
                    validation: Regex.categorySubcategoryTopic,
                  }}
                  placeholder="Search for category ..."
                  readOnly={isViewOnly}
                />
                {/* )} */}

                {/* Subcategory Select */}
                <Select
                  ref={subCategoryRef}
                  label="Subcategory"
                  requiredLabel
                  name="SubCategory"
                  value={form.subCategory}
                  onChange={(newSubCategory: string) => {
                    dispatch(setFieldValue({ path: 'subCategory', value: newSubCategory }));
                    dispatch(setFieldValue({ path: 'topic', value: null }));
                  }}
                  disabled={!form.category}
                  disabledMessage="Please select category first"
                  lookup="subcategory"
                  params={{ categoryId: form.category }}
                  creationOptions={{
                    url: 'lookups/subCategory/single',
                    fieldName: 'name',
                    validation: Regex.categorySubcategoryTopic,
                  }}
                  optionValueKey="_id"
                  optionLabelKey="name"
                  dropIcon={true}
                  validators={form.category ? [isRequired()] : []}
                  placeholder="Search for subcategory ..."
                  readOnly={isViewOnly}
                />

                {/* Topic Select */}
                <Select
                  ref={topicRef}
                  label="Topic"
                  requiredLabel
                  name="topic"
                  value={form.topic}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'topic', value }))}
                  disabled={!form.subCategory}
                  disabledMessage="Please select subcategory first"
                  lookup="topic"
                  params={{ subcategoryId: form.subCategory }}
                  optionValueKey="_id"
                  optionLabelKey="name"
                  dropIcon={true}
                  validators={form.subCategory ? [isRequired()] : []}
                  creationOptions={{
                    url: 'lookups/topic/single',
                    fieldName: 'name',
                    validation: Regex.categorySubcategoryTopic,
                  }}
                  placeholder="Search for topic ..."
                  readOnly={isViewOnly}
                />

                {/* Difficulty Select */}
                <Select
                  name="difficulty"
                  requiredLabel
                  label="Difficulty"
                  lookup="$QuestionDifficulty"
                  value={form.difficulty}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'difficulty', type: Number, value }))}
                  dropIcon={true}
                  validators={[isRequired()]}
                  validatorsScroll
                  placeholder="Search for difficulty ..."
                  readOnly={isViewOnly}
                  optionValueKey="value"
                  optionLabelKey="label"
                />

                {/* Question Type Select */}
                <Select
                  label="Question Type"
                  requiredLabel
                  name="answerTypeSingle"
                  value={form.type}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'type', type: Number, value }))}
                  lookup="$QuestionTypeEnum"
                  dropIcon={true}
                  validators={[isRequired()]}
                  validatorsScroll
                  placeholder="Search for question type ..."
                  readOnly={isViewOnly}
                  optionValueKey="value"
                  optionLabelKey="label"
                />
              </div>
            </div>
          </Card>
        )}

        {/* view only view */}

        {isViewOnly && (
          // View Only Card
          <Card className="p-4 border border-gray-200 rounded-md space-y-3 ">
            {/* <p className="font-medium">Question Setup</p> */}
            <div className="text-[#667085] flex flex-col sm:flex-row w-full justify-between gap-2 sm:gap-0">
              <p className="text-[#667085] hidden sm:block text-sm thepassBtwo">
                Created by {form.authorName}, {formatDate(form.createdAt)}{' '}
              </p>
              <div className="flex text-sm sm:flex-col sm:hidden">
                <p className="text-[#667085]">Created by </p>
                <p className="font-medium ml-3">
                  {form.authorName}, {formatDate(form.createdAt)}
                </p>
              </div>
              <div className="flex items-center gap-2 text-sm thepassBtwo">
                <p>{form.updatedAt ? `Updated ${formatDistanceToNow(new Date(form.updatedAt), { addSuffix: true })}` : 'Not updated yet'}</p>
                <Icon icon="lucide:history" className="text-[#8D5BF8]" width="20" />
              </div>
            </div>

            <div className="w-full">
              <div className="mt-0 grid gap-3 sm:grid-cols-[1fr_auto] sm:items-center">
                <div>
                  <div className="flex items-center gap-2">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8.66797 11.3333V6" stroke="#8D5BF8" stroke-linecap="round" stroke-linejoin="round" />
                      <path d="M12 11.3359V3.33594" stroke="#8D5BF8" stroke-linecap="round" stroke-linejoin="round" />
                      <path
                        d="M2 2V12.6667C2 13.0203 2.14048 13.3594 2.39052 13.6095C2.64057 13.8595 2.97971 14 3.33333 14H14"
                        stroke="#8D5BF8"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path d="M5.33203 11.3359V9.33594" stroke="#8D5BF8" stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <p className="thepassBtwo text-[#6B7280]">Difficulty:</p>
                    <p className="font-medium text-sm">
                      <EnumText name="QuestionDifficulty" value={form?.difficulty} />
                    </p>
                  </div>

                  <div className="mt-3">
                    <div className="flex flex-row items-center gap-4 ">
                      <CategoryFieldColumn categoryNameArray={form?.categoryName} />
                      <SubcategoryFieldColumn subCategoryName={form?.subCategoryName} />
                    </div>
                  </div>
                </div>
                <PermissionProtectedComponent permissions={UserPermissions.UPDATE_QUESTION}>
                  <div className="flex justify-start sm:justify-end sm:self-center">
                    <Button
                      colorType="tertiary"
                      label={String(`Edit ${JumbotronModuleInfo.moduleName}`)}
                      customIcon={{ definedIcon: 'blueEdit' }}
                      onClick={() => navigate(`/app/${JumbotronModuleInfo.routeName}/edit/${id}`)}
                      className="text-nowrap capitalize "
                    />
                  </div>
                </PermissionProtectedComponent>
              </div>
            </div>
          </Card>
        )}

        {/* Generate With Ai */}
        {!isViewOnly && (
          <Card className="border !p-0">
            <div className="bg-[#F9F8FA] p-4">
              <div className="flex justify-between cursor-pointer " onClick={() => setShowGenerateWithAICard((prev) => !prev)}>
                <div className="flex gap-3">
                  <div className="size-10 flex justify-center items-center bg-[#F1E9FE] rounded-xl">
                    <Icon icon="lucide:brain" className="text-[#743AF5]" width="20" />
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm thepassHfour">Generate with AI ✨</p>
                    <p className="thepassBtwo text-[#6B7280]">Auto-generate questions based on your metadata</p>
                  </div>
                </div>

                <Icon
                  icon={isShowGenerateWithAICard ? 'ic:twotone-keyboard-arrow-up' : 'ic:twotone-keyboard-arrow-down'}
                  className="text-[#6B7280]"
                  width="25"
                />
              </div>
            </div>
            {isShowGenerateWithAICard && (
              <div className="space-y-4 p-4">
                <p className="thepassBone">
                  Notes for AI <span className="text-sm thepassHfour text-[#868D9C]">Optional</span>
                </p>
                <TextInput
                  value={form?.notes}
                  onChange={(value: string) => dispatch(setFieldValue({ path: 'notes', value }))}
                  placeholder="Add context, focus area, or instructions for the AI to tailor you questions..."
                  rows={3}
                  name="notes"
                  validators={[]}
                />

                {/* <Button
                  label="Generate with AI"
                  className="min-w-44"
                  onClick={handleGenerateAiQuestion}
                  disabled={
                    form?.category === null ||
                    form?.category?.length <= 0 ||
                    form?.subCategory === null ||
                    form?.subCategory?.length <= 0 ||
                    !form?.topic ||
                    !form?.difficulty ||
                    !form.type
                  }
                /> */}

                <div className="flex justify-end">
                  <Button
                    // className={`min-w-44 ${
                    //                   isDisabled
                    //                     ? 'bg-[#E8ECF3] text-[#868D9C] cursor-not-allowed'
                    //                     : ' bg-[#743AF5] hover:bg-[#BFA3FB] shadow-[0_0_15.9px_0_#D8D8D8] active:bg-[#6835EE]'
                    //                 }`}

                    label="Generate Question"
                    onClick={handleGenerateAiQuestion}
                    disabled={isDisabled}
                    colorType={!isDisabled ? 'primary' : undefined}
                    className="min-w-44 text-base font-semibold"
                  />
                </div>
              </div>
            )}
          </Card>
        )}

        {isViewOnly && (
          <Card className="space-y-2 !pb-4 !px-0 !py-0 relative">
            {/* Question Content */}
            <div className="pt-4 px-4 space-y-5">
              <div className="space-y-4">
                <div className="flex items-center gap-2 justify-between">
                  <h3 className="thepassHfour text-[#111827] dark:text-white">{form.title}</h3>
                  <div>
                    <span
                      className={`transition-transform duration-200 cursor-pointer ${showAnswers ? 'rotate-180' : ''}`}
                      onClick={() => setShowAnswers((prev) => !prev)}
                    >
                      <Icon icon={showAnswers ? 'ic:twotone-keyboard-arrow-up' : 'ic:twotone-keyboard-arrow-down'} width="24" />
                    </span>
                  </div>
                </div>
              </div>

              {/* Answer Options - Collapsible */}
              {showAnswers && (
                <div className="space-y-4 border border-[#F3F4F6] rounded-xl p-3">
                  <h3 className="text-[#111827] dark:text-white thepassBtwo">Answer Options </h3>
                  <div className="w-full space-y-2 pt-2 flex flex-col gap-2">
                    {form.options.map((option: { id: number; label: string }, idx: number) => {
                      let isCorrect = false;
                      if (form.type === QuestionTypeEnum.Singlechoice) {
                        isCorrect = option.id === form.singleChoiceAnswer;
                      } else if (form.type === QuestionTypeEnum.Multichoice) {
                        isCorrect = form.multiChoiceAnswer[option.id];
                      }
                      const letter = String.fromCharCode(65 + idx); // A, B, C, ...
                      return (
                        <div
                          key={option.id}
                          className={`flex items-center rounded-xl py-3 px-2 gap-3 text-[#111827] dark:text-white transition-all ${
                            isCorrect ? 'border-[#80CE80] bg-[#F0FDF4] border' : 'border-[#E2E8F0] bg-[#f8fafc] border'
                          }`}
                        >
                          {isCorrect && (
                            <span className="ml-2 flex items-center justify-center w-6 h-6 rounded-full bg-green-400">
                              <Icon icon="ic:round-check" className="text-white" width="18" />
                            </span>
                          )}
                          <div className="flex">
                            <span className="text-sm thepassBtwo mr-1">{letter}.</span>
                            <span className="text-sm thepassBtwo flex-1">{option.label}</span>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </Card>
        )}

        {/* Question Card */}
        {!isViewOnly && (
          <Card className="space-y-2 !pb-4 !px-0 !py-0 relative">
            <div className="flex gap-2 py-4 px-4 rounded-t-md bg-[#F9F8FA]">
              <div className="flex items-center gap-3">
                <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect width="40" height="40" rx="8" fill="#F1E9FE" />
                  <path
                    d="M11.5014 28.4975C12.3314 29.3275 13.6714 29.3275 14.5014 28.4975L27.5014 15.4975C28.3314 14.6675 28.3314 13.3275 27.5014 12.4975C26.6714 11.6675 25.3314 11.6675 24.5014 12.4975L11.5014 25.4975C10.6714 26.3275 10.6714 27.6675 11.5014 28.4975Z"
                    stroke="#905CFF"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path d="M26.0117 16.9922L23.0117 13.9922" stroke="#905CFF" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                  <path
                    d="M16.5 10.44L18 10L17.56 11.5L18 13L16.5 12.56L15 13L15.44 11.5L15 10L16.5 10.44Z"
                    stroke="#905CFF"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M12.5 16.44L14 16L13.56 17.5L14 19L12.5 18.56L11 19L11.44 17.5L11 16L12.5 16.44Z"
                    stroke="#905CFF"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M27.5 21.44L29 21L28.56 22.5L29 24L27.5 23.56L26 24L26.44 22.5L26 21L27.5 21.44Z"
                    stroke="#905CFF"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>

                <p className="dark:text-white text-sm thepassHfour">Generate Manually</p>
              </div>
            </div>

            {/* Question Content */}
            <div className=" pb-4 px-4">
              <Textarea
                name="title"
                id="title"
                value={form.title}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'title', value }))}
                validators={[isRequired()]}
                validatorsScroll
                requiredLabel
                readOnly={isViewOnly}
                rows={5}
                placeHolder="Type.."
              />
            </div>

            {/* Answers Section (only for Single Choice and Multiple Choice) */}
            {(form.type === QuestionTypeEnum.Singlechoice || form.type === QuestionTypeEnum.Multichoice) && (
              <div className="space-y-4 px-4">
                <div className="text-[#4E5E82] text-sm thepassHfour">Answers</div>

                {/* Render answer options */}
                {form.options?.map((option: { id: number; label: string }, index: number) => (
                  <div key={option.id} className="flex items-center w-full gap-2">
                    {/* Answer Textarea */}
                    <div className="flex-grow">
                      <Textarea
                        labelClassName="!text-[#798296] text-sm !font-medium !py-2 "
                        name={`answer${option.id}`}
                        className="w-full"
                        value={option.label}
                        onChange={(value: any) => dispatch(setFieldValue({ path: `options.${index}.label`, value }))}
                        requiredLabel={form.type === QuestionTypeEnum.Multichoice ? index < 3 : index < 2}
                        maxHeight="150"
                        validators={[]}
                        inputTypeProps={
                          form.type === QuestionTypeEnum.Singlechoice
                            ? {
                                name: `answer${option.id}`,
                                selectionValue: option.id,
                                value: form.singleChoiceAnswer,
                                onChange: (value: any) => dispatch(setFieldValue({ path: 'singleChoiceAnswer', type: Number, value })),
                                formType: form.type,
                              }
                            : form.type === QuestionTypeEnum.Multichoice
                            ? {
                                name: `answer${option.id}`,
                                value: form.multiChoiceAnswer[option.id],
                                onChange: (e: boolean) => handleSelectCheckbox(e, option.id),
                                formType: form.type,
                              }
                            : null
                        }
                        readOnly={isViewOnly}
                      />
                    </div>
                    {/* Remove Option Button (appears for options beyond the initial two/three) */}
                    {!isViewOnly &&
                      (form.type === QuestionTypeEnum.Singlechoice
                        ? form.options.length > 2 && index > 1
                        : form.type === QuestionTypeEnum.Multichoice && form.options.length > 3 && index > 2) && (
                        <Icon
                          icon="solar:trash-bin-trash-linear"
                          className="cursor-pointer border border-[#D1D5DB] text-[#C72716] w-fit p-1 rounded-md"
                          width="20"
                          onClick={() => handleRemoveOption(option.id)}
                        />
                      )}
                  </div>
                ))}

                {/* Add button to add more options */}
                {!isViewOnly && form.options?.length < 4 && (
                  <div
                    className="border-dashed border border-[#743AF5] w-full my-4 py-2 rounded-lg dark:bg-opacity-80 mx-auto cursor-pointer "
                    onClick={handleAddOption}
                  >
                    <button type="button" onClick={handleAddOption} className="flex items-center justify-center mx-auto">
                      <Icon icon="icon-park-solid:add-one" className="text-primaryPurple text-opacity-60" width={'25'} />
                      <span className="ml-2 text-newQuAnsText thepassBone dark:text-newQuAnsDarkText ">New Answer</span>
                    </button>
                  </div>
                )}
              </div>
            )}
          </Card>
        )}
      </div>

      {!isViewOnly && (
        <div className="flex justify-end">
          <Jumbotron
            buttons
            createAnotherQuestionButton={!id}
            setExitAfterSubmission={setExitAfterSubmission}
            type={!isEditMode() ? 'create' : undefined}
            isShowViewButtons={{
              ...JumbotronModuleInfo,
              customOnClick: handleNavigate,
            }}
          />
        </div>
      )}

      {/* Loading */}
      {isLoading && (
        <div className="absolute z-50 left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
          <Spinner size="lg" color="purple" />
        </div>
      )}
    </Form>
  );
};
