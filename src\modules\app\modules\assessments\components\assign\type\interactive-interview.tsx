// React
import { useState, useRef, useEffect } from 'react';

// Core
import { MultiSelect, Select, TextInput, Card } from 'src';
import { Regex, useValidate, Form, RootState, setFieldValue, useAppDispatch, useAppSelector } from 'UI/src';

interface InteractiveInterviewProps {
  formData: any;
  disableButtons: any;
}

export const InteractiveInterview = ({ formData, disableButtons }: InteractiveInterviewProps) => {
  // Reference
  const subCategoryRef = useRef(null);

  // State
  const [loading, setLoading] = useState(false);
  const [skipsTouched, setSkipsTouched] = useState(false);

  // Hooks
  const { isRequired, isNumber, isValidateMaxAndMinNumber } = useValidate();

  const dispatch = useAppDispatch();

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);

  useEffect(() => {
    disableButtons.setDisableNextButton(
      !form.category ||
        !form.subCategory ||
        !form.difficulty ||
        !form.estimationTime ||
        !form.numberOfQuestions ||
        form.skips >= form.numberOfQuestions
    );
  }, [form]);

  // Handler for skips field to track when it's been touched
  const handleSkipsChange = (value: number) => {
    if (!skipsTouched) setSkipsTouched(true);
    dispatch(setFieldValue({ path: 'skips', type: Number, value }));
  };

  return (
    <div className="border border-[#DEE2E4] rounded-xl overflow-hidden">
      <h2 className="thepassHthree text-[#111827] p-4 border-b border-[#DEE2E4] bg-[#F9F8FA]">Template Setup</h2>

      <Form onSubmit={() => {}} className="grid sm:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
        <Select
          validators={[]}
          label="Category"
          name="category"
          value={form.category}
          disabled={loading}
          onChange={(newCategory: string) => {
            (subCategoryRef.current as any)?.blur();
            dispatch(setFieldValue({ path: 'category', value: newCategory }));
            dispatch(setFieldValue({ path: 'subCategory', value: null }));
          }}
          lookup="category"
          optionValueKey="_id"
          optionLabelKey="name"
          dropIcon
          requiredLabel
          creationOptions={{
            url: 'lookups/category/single',
            fieldName: 'name',
            validation: Regex.categorySubcategoryTopic,
          }}
          placeholder="Search for category..."
        />

        <MultiSelect
          validators={[]}
          key={form.category}
          label="Subcategory"
          requiredLabel
          name="subCategory"
          placeholder="Search for subcategory"
          value={Array.isArray(form.subCategory) ? form.subCategory : []}
          onChange={(newSubCategory: any) => dispatch(setFieldValue({ path: 'subCategory', value: newSubCategory }))}
          disabled={!form.category || loading}
          disabledMessage="Please select category first"
          lookup="subcategory"
          params={{ categoryId: form.category }}
          creationOptions={{
            url: 'lookups/subCategory/single',
            fieldName: 'name',
            validation: Regex.categorySubcategoryTopic,
          }}
          optionValueKey="_id"
          optionLabelKey="name"
          dropIcon
        />

        <Select
          validators={[]}
          disabled={loading}
          name="difficulty"
          label="Difficulty"
          lookup="$QuizDifficulty"
          value={form.difficulty}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'difficulty', value }))}
          dropIcon
          requiredLabel
          placeholder="Choose a difficulty level..."
          optionValueKey="value"
          optionLabelKey="label"
        />

        <TextInput
          disabled={loading}
          name="numberOfQuestions"
          label="Number of Questions"
          placeholder="Number of questions"
          value={form.numberOfQuestions}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'numberOfQuestions', value }))}
          validators={[isNumber(), isRequired()]}
          requiredLabel
        />

        <TextInput
          disabled={loading}
          name="estimationTime"
          label="Estimation time ( minutes )"
          placeholder="Estimation time"
          // labelTooltip="Expected time for the interview in minutes."
          value={form.estimationTime}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'estimationTime', value }))}
          validators={[isNumber(), isRequired(), isValidateMaxAndMinNumber('min', 10), isValidateMaxAndMinNumber('max', 240)]}
          requiredLabel
          type="number"
          min={10}
        />

        <div>
          <TextInput
            disabled={loading}
            name="skips"
            label="Max Skips"
            // labelTooltip="Maximum skips allowed without affecting the score."
            placeholder="Choose maximum allows for applicant to skip questions"
            value={form.skips}
            onChange={handleSkipsChange}
            validators={[isNumber(), isRequired()]}
            requiredLabel
          />
          {skipsTouched && form.skips >= form.numberOfQuestions && (
            <p className="text-red-500 text-sm">Max skips cannot be greater than number of questions</p>
          )}
        </div>
      </Form>
    </div>
  );
};
