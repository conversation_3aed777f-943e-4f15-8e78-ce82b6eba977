// React
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useParams } from 'react-router-dom';

// Flowbite
import { Breadcrumb, Tooltip } from 'flowbite-react';

// UI
import { Icon } from 'src';
import { Api } from 'UI/src';

// Components
import { useBreadcrumb } from 'UI/src/hooks/breadcrumb';
import { QuizType } from 'UI/src';

export const GlobalBreadcrumb = () => {
  // State
  const [details, setDetails] = useState<{}[] | null>(null);

  // Methods
  const { routes, handleRouteClick } = useBreadcrumb();

  // Hooks
  // const { notify } = useNotify();

  // Location
  const location = useLocation();
  const currentUrl = location?.pathname;
  const { quizId } = useParams();

  const quizDetails = async () => {
    try {
      const response = await Api.get(`templates/single/custom/${quizId}`, {});
      console.log(`templates/single/custom/${quizId}`, response.data);
      setDetails([
        // @TODO: Clickable when implement category page
        { label: response?.data?.category?.categoryName },
        { label: response?.data?.title },
      ]);
    } catch (error:any) {
      // notify.error(error.response.data.message);
    }
  };

  useEffect(() => {
    if (quizId) quizDetails();
    else setDetails([]);
  }, [quizId]);

  return (
    <Breadcrumb className={`${currentUrl.includes('report') ? 'hidden' : 'block'} print:hidden`} aria-label="Default breadcrumb example">
      {/* FIXME: Remove any */}
      {routes.map((route: any) =>
        route?.data?.customBreadcrumb ? (
          details?.map((route: any) => (
            <Breadcrumb.Item key={route?.id}>
              <p className="flex items-center justify-center gap-2 text-gray-400 dark:text-white font-medium text-base">{route?.label}</p>
            </Breadcrumb.Item>
          ))
        ) : (
          <Breadcrumb.Item href="#" onClick={(e) => handleRouteClick(e, route?.pathname)} key={route?.pathname}>
            <div className="flex items-center justify-center gap-2 text-[#656C7B] dark:text-gray-400">
              {route?.data?.icon ? (
                route?.data?.tooltip ? (
                  <Tooltip
                    content={route?.data?.tooltip}
                    placement="bottom"
                    arrow={false}
                    className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                  >
                    <Icon icon={route.data.icon} width="22" />
                  </Tooltip>
                ) : (
                  <Icon icon={route.data.icon} width="22" />
                )
              ) : route?.data?.tooltip ? (
                <Tooltip
                  content={route?.data?.tooltip}
                  placement="bottom"
                  arrow={false}
                  className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                >
                  {route?.data?.svg && route?.data?.svg}
                </Tooltip>
              ) : (
                route?.data?.svg && route?.data?.svg
              )}

              <p className="font-medium text-base text-gray-400 dark:text-white hover:underline hover:text-gray-500 dark:hover:text-gray-400">
                {route?.data?.label}
              </p>
            </div>
          </Breadcrumb.Item>
        )
      )}
    </Breadcrumb>
  );
};
