// Core
import { CustomIcon } from 'src';

export const WhyTeamsTrust = () => {
  const data: { id: number; title: string; description: string; icon: string }[] = [
    {
      id: 1,
      title: ' Effortless Screening',
      description: 'Instantly shortlist candidates with intelligent screening no spreadsheet , no stress.',
      icon: 'magnifier',
    },
    {
      id: 2,
      title: 'Tailored Assessments',
      description: 'Use expert-made templates, create your own, or let AI generate your questions.',
      icon: 'paperWithGray',
    },
    {
      id: 3,
      title: 'Interview Smarter with AI',
      description: "Run natural AI interviews that assess each candidate's skills, tone, and potential ",
      icon: 'recorderWithGreen',
    },
    {
      id: 4,
      title: ' Clear Reports & Insights',
      description: 'Download full reports spot top talent, skills gaps, and success rates. ',
      icon: 'chartWithPink',
    },
  ];

  return (
    <div id="why-choose-us" className="py-7  ">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center justify-center">
          <div className="w-fit h-8 text-nowrap bg-[#ddd7ff]/30 rounded-lg flex items-center justify-start mt-8 px-4 py-2 mb-7">
            <h2 className="text-sm text-[#8d5bf8] dark:text-white leading-5 font-semibold tracking-wider uppercase">
              FROM Apply TO hire IN MINUTES{' '}
            </h2>
          </div>

          <div className="flex flex-col items-center text-center justify-center space-y-4 mb-10">
            <p className="text-[#171618] capitalize font-semibold text-base md:text-[55px] ">Why Teams Trust Us</p>
            <div className="max-w-5xl">
              <p className="text-sm sm:thepassSubHone px-8 sm:px-0 text-[#4E5E82] text-center  mb-5">
                Trusted by forward-thinking teams for unbiased evaluation consistent testing, and intelligent talent recommendations
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mx-auto mb-7 gap-6 w-full">
            {data?.map((item) => (
              <div key={item.id} className="bg-[#F5F8FF] flex-row p-4 sm:p-6 md:p-8 rounded-3xl flex gap-2 sm:gap-4">
                <div className="flex lg:hidden">
                  <CustomIcon definedIcon={item.icon} />
                </div>
                <div className="flex flex-col space-y-2 min-w-0">
                  <h2 className="text-[#1B1F3B] font-semibold text-[15.6px] leading-6">{item.title}</h2>
                  <p className="text-[#3A4458] font-normal text-sm leading-5">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
