import React from 'react';

export const TestCardPlaceholder = () => {
  return (
    <div>
      <div className="flex gap-3 px-3">
        <div className="h-9 bg-gray-200 mt-3 rounded-md dark:bg-gray-600 w-11/12 animate-pulse "></div>
        <div className="h-9 bg-gray-200 mt-3 rounded-md dark:bg-gray-600 w-24 animate-pulse "></div>
      </div>
      <div className="border border-gray-300 w-full mt-3 p-3 animate-pulse rounded-md ">
        <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-56 mb-2 border-b-2 border-gray-300 "></div>
        <div className="flex items-center gap-6 border-t-2 border-gray-300">
          <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-40 mb-5 "></div>
          <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-44 mb-5 "></div>
          <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-80 mb-5 "></div>
        </div>

        <div className="flex items-center gap-6 border-t-2 border-gray-300">
          <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-40 mb-3 "></div>
          <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-44 mb-3 "></div>
          <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-80 mb-3 "></div>
        </div>
      </div>

      <div className="border border-gray-300 w-full mt-4 p-3 animate-pulse rounded-md ">
        <div className="flex items-center gap-3">
          <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-60 mb-2 border-b-2 border-gray-300 "></div>
          <div className="h-5 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-28 mb-2 border-b-2 border-gray-300 "></div>
        </div>
        <div className="flex items-center gap-2 border-b-2 border-gray-300">
          <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-6 mb-5 "></div>
          <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-32 mb-5 "></div>
          <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-80 mb-5 "></div>
        </div>

        <div className=" flex items-center justify-around gap-1 bg-gray-100 mt-3 rounded-md dark:bg-gray-600 w-full animate-pulse ">
          <div className="flex items-center gap-2 ">
            <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-6 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-20 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-14 mb-5 "></div>
          </div>

          <div className="flex items-center gap-2 ">
            <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-6 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-36 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-14 mb-5 "></div>
          </div>

          <div className="flex items-center gap-2 ">
            <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-6 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-36 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-14 mb-5 "></div>
          </div>

          <div className="flex items-center gap-2 ">
            <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-6 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-32 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-56 mb-5 "></div>
          </div>
        </div>

        <div className="flex flex-col mt-8 mb-8  align-middle items-center mx-auto gap-1 animate-pulse  ">
          <div className="h-32 bg-gray-300 mt-3 rounded-md dark:bg-gray-600 w-32 "></div>

          <div className="h-3 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-44  "></div>

          <div className="h-4 bg-gray-300 mt-3 rounded-md dark:bg-gray-600 w-2/4 mb-2 "></div>
        </div>
      </div>

      <div className="border border-gray-300 w-full mt-4 p-3 animate-pulse rounded-md ">
        <div className="flex items-center gap-3">
          <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-60 mb-2 border-b-2 border-gray-300 "></div>
          <div className="h-5 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-28 mb-2 border-b-2 border-gray-300 "></div>
        </div>
        <div className="flex items-center gap-2 border-b-2 border-gray-300">
          <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-6 mb-5 "></div>
          <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-32 mb-5 "></div>
          <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-80 mb-5 "></div>
        </div>

        <div className=" flex items-center justify-around gap-1 bg-gray-100 mt-3 rounded-md dark:bg-gray-600 w-full animate-pulse ">
          <div className="flex items-center gap-2 ">
            <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-6 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-20 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-14 mb-5 "></div>
          </div>

          <div className="flex items-center gap-2 ">
            <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-6 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-36 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-14 mb-5 "></div>
          </div>

          <div className="flex items-center gap-2 ">
            <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-6 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-36 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-14 mb-5 "></div>
          </div>

          <div className="flex items-center gap-2 ">
            <div className="h-6 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-6 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-32 mb-5 "></div>
            <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-56 mb-5 "></div>
          </div>
        </div>

        <div className="flex flex-col mt-8 mb-8  align-middle items-center mx-auto gap-1 animate-pulse  ">
          <div className="h-32 bg-gray-300 mt-3 rounded-md dark:bg-gray-600 w-32 "></div>

          <div className="h-3 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-44  "></div>

          <div className="h-4 bg-gray-300 mt-3 rounded-md dark:bg-gray-600 w-2/4 mb-2 "></div>
        </div>
      </div>
    </div>
  );
};
