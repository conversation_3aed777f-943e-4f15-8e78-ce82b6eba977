import React from 'react';
import landingInterviewer from 'images/landing/Group 2078547874.png';
import landingInterviewer2 from 'images/landing/Image [g2-sphere].png';

export const InterviewerNeverSleeps = () => {
  return (
    <div className="py-5 space-y-8 ">
      <div className="flex flex-col items-center justify-center space-y-6">
        <div className="w-fit h-8 text-nowrap bg-[#ddd7ff]/30 rounded-lg flex items-center text-center justify-center px-4 py-2 ">
          <h2 className="text-sm text-[#8d5bf8] dark:text-white  font-semibold tracking-wider uppercase ">Real Conversations. Real Insights</h2>
        </div>
      </div>
      <div className="flex flex-col items-center text-center justify-center">
        <span className="text-[#0D0F2C] dark:text-white text-base md:text-[55px]  font-medium  ">The Interviewer Who Never Sleeps</span>
        <span className="font-medium text-base md:text-[55px]   dark:text-white gradient-text bg-clip-text text-transparent p-1 py-2">
          Always Fair - Always Ready.
        </span>
        <div className="max-w-2xl sm:max-w-5xl sm:px-0 px-8  pt-3">
          <p className="text-sm sm:thepassSubHone  text-text-500  text-center">
            Let our AI interviewer engage applicants in natural, responsive conversations — assessing skills, tone, and personality with precision.
            Smart, 24/7 interviewing that supports your hiring team.{' '}
          </p>
        </div>
        <div className="pt-14 w-full hidden sm:block">
          <img src={landingInterviewer} className="w-full max-w-[1300px] mx-auto  h-auto object-contain" alt="AI Interviewer" />
        </div>
        <div className="pt-14 w-full sm:hidden block">
          <img src={landingInterviewer2} className=" mx-auto  h-auto object-contain" alt="AI Interviewer" />
        </div>
      </div>
    </div>
  );
};
