import { useEffect, useState } from 'react';

// Sizes Table
const getSize = (size = 320) => {
  if (size >= 1536) return '2xl';
  if (size >= 1280) return 'xl';
  if (size >= 1024) return 'lg';
  if (size >= 768) return 'md';
  if (size >= 640) return 'sm';

  return 'xs';
};
const getCustomSize = (size = 425) => {
  if (size >= 425) return 'cs';
  return;
};

export const useScreenSize = () => {
  const [size, setSize] = useState(getSize(window.innerWidth));
  const [customScreen, setCustomScreen] = useState(getCustomSize(window.innerWidth));

  useEffect(() => {
    const listener = () => {
      setSize(getSize(window.innerWidth));
      setCustomScreen(getCustomSize(window.innerWidth));
    };

    window.addEventListener('resize', listener);

    return () => {
      window.removeEventListener('resize', listener);
    };
  }, []);

  return {
    // Size
    size,
    customScreen,

    // Helpers
    xxl: () => size === '2xl',
    xl: () => size === 'xl',
    lg: () => size === 'lg',
    md: () => size === 'md',
    sm: () => size === 'sm',
    xs: () => size === 'xs',
    ss: () => customScreen === 'cs',

    // Extra
    gt: {
      xxl: () => size === '2xl',
      xl: () => ['2xl'].includes(size),
      lg: () => ['xl', '2xl'].includes(size),
      md: () => ['lg', 'xl', '2xl'].includes(size),
      sm: () => ['md', 'lg', 'xl', '2xl'].includes(size),
      xs: () => ['sm', 'md', 'lg', 'xl', '2xl'].includes(size),
    },
    lt: {
      xxl: () => ['xs', 'sm', 'md', 'lg', 'xl'].includes(size),
      xl: () => ['xs', 'sm', 'md', 'lg'].includes(size),
      lg: () => ['xs', 'sm', 'md'].includes(size),
      md: () => ['xs', 'sm'].includes(size),
      sm: () => ['xs'].includes(size),
      xs: () => size === 'xs',
    },
  };
};
