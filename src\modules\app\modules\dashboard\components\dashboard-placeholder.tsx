import React from 'react';

import { Datepicker } from 'flowbite-react';
import { Icon } from 'src';
import CategoriesPlaceholder from './list/categories-placeholder';
import { DashboardCard } from './dashbaord-card';
import SubmissionsChartsPlaceholder from './charts/submissions-charts-placeholder';
import ApplicantChartPlaceholder from './charts/applicants-chart-placeholder';

export const DashboardPlaceholder = () => {
  return (
    <main className="pb-6">
      {/* Datepicker */}
      <div className="sm:flex sm:items-center sm:justify-between mb-4 relative">
        <h1 className="text-gray-700 dark:text-gray-300 text-2xl font-medium mb-2">Dashboard</h1>
        <div className="calendar flex flex-row justify-end items-center gap-2 text-gray-700 dark:text-gray-300 rounded-lg h-fit">
          <div className="flex flex-col sm:flex-row w-full gap-2 sm:gap-4">
            <Datepicker className="inline-block w-full sm:w-44" showTodayButton={false} showClearButton={false} value={'From: -- / -- / ----'} />
            <Datepicker className="inline-block w-full sm:w-44" showTodayButton={false} showClearButton={false} value={'To: -- / -- / ----'} />
          </div>
          <div
            className={`mx-auto sm:mx-0 bg-white dark:bg-[#374151] w-10 h-10 rounded-lg flex justify-center items-center cursor-pointer animate-pulse`}
          >
            <Icon icon="f7:delete-left" width='26' />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
        {/* Card  Blocks */}
        <DashboardCard className='bg-[url("images/dashboard-statistics.png")] bg-[length:100%_45%] bg-no-repeat !p-0'>
          <div className="py-14 px-6 space-y-2 ">
            <div className="w-64 h-4 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
            <div className="w-40 h-2 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
          </div>
          <div className="mt-9">
            <div className="grid grid-cols-2 gap-4 mb-4 relative px-4">
              <CategoriesPlaceholder />
              <CategoriesPlaceholder />
            </div>
            <div className="grid grid-cols-2 gap-4 mb-4 px-4">
              <CategoriesPlaceholder />
              <CategoriesPlaceholder />
            </div>
          </div>
        </DashboardCard>

        {/* Charts */}
        <div className="grid grid-cols-1 gap-4">
          <DashboardCard className="!p-2">
            <SubmissionsChartsPlaceholder />
          </DashboardCard>
          <DashboardCard className="!p-2">
            <ApplicantChartPlaceholder />
          </DashboardCard>
        </div>
      </div>

      {/* Blocks */}

      <DashboardCard>
        <div className="flex flex-col gap-4">
          <div className="w-32 h-3 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
          <div className="flex flex-col sm:flex-row gap-3">
            {[...Array(3)].map((ele, index) => {
              return <div key={index} className="bg-gray-300 dark:bg-gray-600 p-1.5 rounded-md w-full sm:w-40 h-4 animate-pulse"></div>;
            })}
          </div>
        </div>

        <div className="mt-5">
          <div>
            <div className="w-32 h-3 bg-gray-300 rounded-full dark:bg-gray-600 mb-4 animate-pulse"></div>
            <div className="hidden sm:grid sm:grid-cols-6 gap-8 text-secondaryGray dark:text-white text-sm mb-3">
              <div className="sm:col-span-3 w-20 h-2 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
              <div className="w-20 h-2 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
              <div className="w-20 h-2 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
              <div className="w-20 h-2 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
            </div>

            <div className="gird sm:hidden text-secondaryGray dark:text-white text-sm mb-3">
              <div className="w-20 h-2 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
            </div>

            <div className="flex flex-col gap-6">
              {[...Array(5)].map((ele, index) => (
                <div key={index} className="grid grid-cols-2 items-center sm:grid-cols-6 gap-8">
                  <div className="sm:col-span-3 sm:w-48 h-6 bg-gray-300 rounded-full dark:bg-gray-600  animate-pulse"></div>
                  <div className="w-6 h-6 bg-gray-300 rounded-full dark:bg-gray-600 sm:hidden animate-pulse"></div>
                  <div className="w-10 h-2 bg-gray-300 rounded-full dark:bg-gray-600 sm:block hidden animate-pulse"></div>
                  <div className="w-10 h-2 bg-gray-300 rounded-full dark:bg-gray-600 sm:block hidden animate-pulse"></div>
                  <div className="w-10 h-2 bg-gray-300 rounded-full dark:bg-gray-600 sm:block hidden animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </DashboardCard>
    </main>
  );
};
