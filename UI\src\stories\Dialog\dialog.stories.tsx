import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Dialog } from '../../components/dialog';
import { Settings } from 'lucide-react';
import type { DialogSize } from '../../constants/modalSizes';

const meta: Meta<typeof Dialog> = {
  title: 'Components/Dialog',
  component: Dialog,
  tags: ['autodocs'],
  argTypes: {
    isOpen: { control: 'boolean' },
    title: { control: 'text' },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl', '6xl', '7xl', 'full'] as DialogSize[],
    },
    customSize: { control: 'text' },
    icon: { control: false },
    children: { control: false },
    onClose: { action: 'onClose' },
    onOutsideClick: { action: 'onOutsideClick' },
    button: { control: false },
  },
};

export default meta;
type Story = StoryObj<typeof Dialog>;

export const Default: Story = {
  args: {
    isOpen: true,
    title: 'Default Dialog',
    size: 'md',
    icon: <Settings className="w-5 h-5 text-blue-500" />,
    children: <p>This is a simple dialog with content inside.</p>,
    onClose: () => console.log('Closed'),
    button: {
      label: 'Confirm',
      variant: 'md',
      colorType: 'primary',
      onClick: () => console.log('Button clicked'),
    },
  },
};

export const WithCustomSize: Story = {
  args: {
    isOpen: true,
    title: 'Custom Size Dialog',
    customSize: 'max-w-3xl',
    children: <p>This dialog uses a custom max-width.</p>,
    onClose: () => console.log('Closed'),
  },
};

export const NoTitleOrButton: Story = {
  args: {
    isOpen: true,
    children: <p>Dialog with no title and no button.</p>,
    onOutsideClick: () => console.log('Outside click'),
  },
};

export const WithDisabledButton: Story = {
  args: {
    isOpen: true,
    title: 'Disabled Button',
    children: <p>This dialog has a disabled button.</p>,
    button: {
      label: 'Disabled',
      variant: 'sm',
      colorType: 'destructive',
      disabled: true,
    },
    onClose: () => console.log('Closed'),
  },
};

export const LargeDialog: Story = {
  args: {
    isOpen: true,
    title: 'Large Dialog',
    size: 'xl',
    children: <p>This is a large dialog with more content.</p>,
    onClose: () => console.log('Closed'),
    button: {
      label: 'Save Changes',
      variant: 'lg',
      colorType: 'primary',
      onClick: () => console.log('Save clicked'),
    },
  },
};

export const FullWidthDialog: Story = {
  args: {
    isOpen: true,
    title: 'Full Width Dialog',
    size: 'full',
    children: <p>This dialog takes the full width of the screen.</p>,
    onClose: () => console.log('Closed'),
  },
};
