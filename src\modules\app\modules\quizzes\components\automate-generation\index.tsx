// React
import React, { useEffect, useState } from 'react';

// UI
import { <PERSON><PERSON>, <PERSON><PERSON>, ToggleFilter, Icon, NoDataFound, NoDataMatches } from 'src';

// React icons
import { HiOutlineAdjustmentsHorizontal } from 'react-icons/hi2';
import { Api, useScreenSize, QuestionType, useAppDispatch, Placeholder } from 'UI/src';
import { setErrorNotify, setNotifyMessage } from 'UI';

// Props interface
interface QuizSelectionAutomateProps {
  setAutomateSelection: React.Dispatch<React.SetStateAction<boolean>>;
  form: QuizAutomateForm;
  setFieldValue: (field: string) => (value: any) => void;
  handleGetQuestionData: (id: string) => Promise<void>;
  lookups: any;
  selectedQuestionIds: string[];
  setSelectedQuestionIds: React.Dispatch<React.SetStateAction<string[]>>;
  questionDatabaseOfMainTest: QuestionType[];
}

// Form interface
interface QuizAutomateForm {
  numOfQuestions: number;
  category: string;
  subCategory: string[];
  subCategoryFiltration: string[];
  questionsDifficulty: string[];
  questionIds: string[];
}

// Edit mode map type
interface EditModeMap {
  [key: string]: boolean;
}

export const QuizSelectionAutomate = (props: QuizSelectionAutomateProps) => {
  const {
    setAutomateSelection,
    form,
    setFieldValue,
    handleGetQuestionData,
    lookups,
    selectedQuestionIds,
    setSelectedQuestionIds,
    questionDatabaseOfMainTest,
  } = props;
  // State
  const [loading, setLoading] = useState<boolean>(false);
  const [generatedQuestionsIds, setGeneratedQuestionsIds] = useState<string[]>([]);
  const [showFilter, setShowFilter] = useState<boolean>(false);
  const [numberOfTotalQuestionsCanAdd, setNumberOfTotalQuestionsCanAdd] = useState<number | undefined>();
  // Search
  const [search, setSearch] = useState<string>('');
  // Question List
  const [questionDatabase, setQuestionDatabase] = useState<QuestionType[]>([]);
  const [questionsList, setQuestionsList] = useState<QuestionType[]>([]);
  // Edit mode map
  const [isAnyQuestionHasEditMode, setAnyQuestionHasEditMode] = useState<EditModeMap>({});

  // separate loading states for different actions
  const [questionGenerateLoading, setQuestionGenerateLoading] = useState<boolean>(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState<{ page: number }>({ page: 1 });
  const questionsPerPage = 10;
  const indexOfLastQuestion = currentPage.page * questionsPerPage;
  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;
  const currentQuestions = questionsList.slice(indexOfFirstQuestion, indexOfLastQuestion);
  const totalPages = Math.max(Math.ceil(questionsList.length / questionsPerPage), 1);

  // Showing Text in Pagination as table
  const showingText = `${questionsList.length ? currentPage.page * questionsPerPage - questionsPerPage + 1 : questionsList.length} - ${
    currentPage.page * questionsPerPage > questionsList.length ? questionsList.length : currentPage.page * questionsPerPage
  }`;

  // Hooks
  const dispatch = useAppDispatch();
  const screen = useScreenSize();

  const onClose = () => {
    setFieldValue('questionsDifficulty')([]);
    setFieldValue('subCategoryFiltration')([]);
    setFieldValue('numOfQuestions')(null);
    setAutomateSelection(false);
    setSelectedQuestionIds([]);
  };

  // Methods
  const handleGetQuestionGenerateData = async (id: string) => {
    try {
      setQuestionGenerateLoading(true);
      const response = await Api.get<QuestionType>(`questions/single/${id}`, {});
      setQuestionDatabase((prev) => [...prev, response.data]);
      setSelectedQuestionIds((prev) => [...prev, response.data._id]);
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    } finally {
      setQuestionGenerateLoading(false);
    }
  };

  const onSubmit = async () => {
    try {
      setQuestionGenerateLoading(true);
      if (selectedQuestionIds.length) {
        await Promise.all(selectedQuestionIds.map((question: any) => handleGetQuestionData(question)));
      }
      setFieldValue('questionIds')([...form.questionIds, ...selectedQuestionIds]);
      setSelectedQuestionIds([]);
      setQuestionGenerateLoading(false);
      onClose();
    } catch (error) {
      throw error;
    }
  };

  // const noDataFound = {
  //   customIcon: 'questions',
  //   messageHeader: 'No questions have been added yet',
  // };

  const handleGenerateAutomateSubmit = async () => {
    if (!form.questionsDifficulty) {
      return dispatch(setErrorNotify('Please select a questions difficulty.'));
    }
    try {
      setQuestionGenerateLoading(true);
      const response = await Api.post('questions/generate', {
        numOfQuestions: form.numOfQuestions,
        category: form.category,
        subCategory: form.subCategoryFiltration.length > 0 ? form.subCategoryFiltration : form.subCategory,
        questionsDifficulty: form.questionsDifficulty,
        exclude: [...questionDatabase.map((question) => question._id), ...form.questionIds],
      });
      response.data.map((question: QuestionType) => handleGetQuestionGenerateData(question._id));
      setGeneratedQuestionsIds((prev) => [...prev, ...response.data.map((question: QuestionType) => question._id)]);
      const result = response.data.map((item: QuestionType) => item._id);
      if (result.length > 0) {
        dispatch(setNotifyMessage('Questions added successfully!'));
      }
      if (result.length === 0 && form.questionIds.length > 0) {
        dispatch(setErrorNotify('There is no more questions for this category'));
      } else if (result.length === 0) {
        dispatch(setErrorNotify('There is no question for this category'));
      }
      setFieldValue('questionsDifficulty')([]);
      setFieldValue('subCategoryFiltration')([]);
      setFieldValue('numOfQuestions')(0);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setQuestionGenerateLoading(false);
    }
  };

  const drawerFilter = (
    <div className="py-3 border-t mx-3">
      <div className="pt-4">
        <Drawer.FilterSection
          label="Subcategory"
          optional={'true'}
          form={form as any}
          lookups={lookups as any}
          setFieldValue={setFieldValue as any}
          propertyKeyObject={0}
          filter={{ options: [{ value: '', name: '', lable: '' }], key: '' }}
          showSingleClear={true}
          handleSingleClear={() => setFieldValue('subCategoryFiltration')([])}
        />
        <hr className="my-4 border-gray-900 border-opacity-5 dark:border-gray-700" />
        <Drawer.FilterSection
          label="Difficulty"
          optional={'true'}
          form={form as any}
          lookups={lookups as any}
          setFieldValue={setFieldValue as any}
          propertyKeyObject="questionsDifficulty"
          filter={{ options: [{ value: '', name: '', lable: '' }], key: '' }}
          showSingleClear={true}
          handleSingleClear={() => setFieldValue('questionsDifficulty')([])}
        />
      </div>
    </div>
  );

  // Get total question exists of selected subcategories
  const handleTotalQuestions = async (): Promise<void> => {
    try {
      const response = await Api.get<number>('questions/total', {
        subCategoryIds: form?.subCategoryFiltration.length > 0 ? form?.subCategoryFiltration : form?.subCategory,
      });
      setNumberOfTotalQuestionsCanAdd(response?.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  const handleAvailableQuestions = (): number => {
    if (form.subCategoryFiltration.length > 0) {
      return (
        (numberOfTotalQuestionsCanAdd || 0) -
        questionDatabaseOfMainTest?.filter((question: any) => form.subCategoryFiltration.includes(question.subCategory)).length -
        questionDatabase?.filter((question: any) => form.subCategoryFiltration.includes(question.subCategory)).length
      );
    } else {
      return (
        (numberOfTotalQuestionsCanAdd || 0) -
        questionDatabaseOfMainTest?.filter((question: any) => form.subCategory.includes(question.subCategory)).length -
        questionDatabase?.filter((question: any) => form.subCategory.includes(question.subCategory)).length
      );
    }
  };

  const renderMessage = () => {
    const availableQuestions = handleAvailableQuestions();
    if (availableQuestions < 100) {
      if (!form.numOfQuestions) {
        return {};
      } else if (form.numOfQuestions <= availableQuestions) {
        return {};
      } else if (form.numOfQuestions > availableQuestions) {
        return {
          text:
            availableQuestions === 0
              ? 'No more available questions'
              : `Only ${availableQuestions} questions are avaliable in this category, please adjust your number.`,
          isButtonDisabled: true,
          color: 'text-[#C72716]',
          icon: (
            <svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6 8.27222V8.27778M6 2.72222V6.61111M11 5.5C11 8.26144 8.76144 10.5 6 10.5C3.23858 10.5 1 8.26144 1 5.5C1 2.73858 3.23858 0.5 6 0.5C8.76144 0.5 11 2.73858 11 5.5Z"
                stroke="#C72716"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      }
    } else {
      if (!form.numOfQuestions) {
        return {
          text: 'You can add up to 100 questions at a time.',
          isButtonDisabled: true,
          color: 'text-[#667085]',
          icon: null,
        };
      } else if (form.numOfQuestions <= 100) {
        return {
          text: 'You can add up to 100 questions at a time.',
          isButtonDisabled: false,
          color: 'text-[#479E64]',
          icon: (
            <svg width="10" height="8" viewBox="0 0 10 8" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 4.30555L3.46154 6.75L9 1.25" stroke="#479E64" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          ),
        };
      } else if (form.numOfQuestions > 100) {
        return {
          text: 'You can add up to 100 questions at a time.',
          isButtonDisabled: true,
          color: 'text-[#C72716]',
          icon: (
            <svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6 8.27222V8.27778M6 2.72222V6.61111M11 5.5C11 8.26144 8.76144 10.5 6 10.5C3.23858 10.5 1 8.26144 1 5.5C1 2.73858 3.23858 0.5 6 0.5C8.76144 0.5 11 2.73858 11 5.5Z"
                stroke="#C72716"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      } else if (form.numOfQuestions > questionDatabaseOfMainTest?.length) {
        return {
          text: `You’ve selected ${questionDatabase.length} questions. You can add up to ${availableQuestions} more..`,
          isButtonDisabled: true,
          color: 'text-[#C72716]',
          icon: (
            <svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6 8.27222V8.27778M6 2.72222V6.61111M11 5.5C11 8.26144 8.76144 10.5 6 10.5C3.23858 10.5 1 8.26144 1 5.5C1 2.73858 3.23858 0.5 6 0.5C8.76144 0.5 11 2.73858 11 5.5Z"
                stroke="#C72716"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      }
    }
    return {
      text: '',
      isButtonDisabled: false,
      color: '',
      icon: null,
    };
  };

  useEffect(() => {
    handleTotalQuestions();
  }, [form?.subCategoryFiltration]);

  useEffect(() => {
    setQuestionsList([...questionDatabase]);
    setSearch('');
  }, [questionDatabase]);

  useEffect(() => {
    setQuestionsList(questionDatabase.filter((question) => question.title.toLowerCase().includes(search.toLowerCase())));
    setCurrentPage((prev) => ({ ...prev, page: 1 }));
  }, [search]);

  return (
    <Drawer split onClose={onClose as any}>
      <Drawer.SplitView as any>
        {(showFilter || screen.gt.sm()) && (
          <div className="drawer-split-left-side">
            <Drawer.HeaderSection
              headerLabel="Adjust Questions"
              icon={<HiOutlineAdjustmentsHorizontal className="text-xl" />}
              onReset={() => {
                setFieldValue('questionsDifficulty')([]);
                setFieldValue('subCategoryFiltration')([]);
              }}
              onClose={screen.lt.md() ? ((() => setShowFilter(false)) as any) : undefined}
              selectedQuestionsCount={selectedQuestionIds.length}
              resultsFound={''}
            />
            <div className="pt-4">
              <Drawer.FilterSection
                label="Filter By Subcategory"
                optional={'true'}
                form={form as any}
                lookups={lookups as any}
                setFieldValue={setFieldValue as any}
                propertyKeyObject={0}
                filter={{ options: [{ value: '', name: '', lable: '' }], key: '' }}
                showSingleClear={true}
                handleSingleClear={() => setFieldValue('subCategoryFiltration')([])}
              />
              <hr className="my-4 border-gray-900 border-opacity-5 dark:border-gray-700" />
              <Drawer.FilterSection
                label="Filter By Difficulty Level"
                optional={'true'}
                form={form as any}
                lookups={lookups as any}
                setFieldValue={setFieldValue as any}
                propertyKeyObject={1}
                filter={{ options: [{ value: '', name: '', lable: '' }], key: '' }}
                showSingleClear={true}
                handleSingleClear={() => setFieldValue('questionsDifficulty')([])}
              />
            </div>
          </div>
        )}
        <div className="drawer-split-right-side">
          <Drawer.HeaderSection
            headerLabel="Auto Question Selection"
            selectedQuestionsCount={selectedQuestionIds.length}
            onClose={onClose as any}
            icon={null as any}
            onReset={() => {}}
            resultsFound={''}
          />
          <div className="mt-2 text-[#808080] text-[13px] font-normal">
            <span className="font-medium">{handleAvailableQuestions()}</span> Available Questions based on your filter
          </div>
          <Drawer.GenerateQuestions
            form={form as any}
            setFieldValue={setFieldValue as any}
            setGeneratedQuestionsIds={setGeneratedQuestionsIds as any}
            onSubmit={handleGenerateAutomateSubmit as any}
            numberOfTotalQuestionsCanAdd={numberOfTotalQuestionsCanAdd as any}
            isButtonDisabled={renderMessage().isButtonDisabled as any}
            className={'' as any}
          />
          <div className={`${renderMessage().color} text-[13px] font-normal flex align-middle items-center gap-2 `}>
            {renderMessage().icon}
            {renderMessage().text}
          </div>
          <div className="flex items-center gap-3">
            <Drawer.Search
              value={search}
              onInput={(e: React.ChangeEvent<HTMLInputElement>) => setSearch(e.target.value)}
              className="flex w-full"
              children={undefined as any}
            />
            {!screen.gt.sm() && (
              <ToggleFilter
                filters={[]}
                drawerFilter={{
                  element: drawerFilter as any,
                  filterCountNumber: 0,
                  drawerClearAll: () => {
                    setFieldValue('questionsDifficulty')([]);
                    setFieldValue('subCategoryFiltration')([]);
                  },
                  setShowDrawerFilter: () => {},
                }}
                drawerClearAll={() => {
                  setFieldValue('questionsDifficulty')([]);
                  setFieldValue('subCategoryFiltration')([]);
                }}
                resultsFound={handleAvailableQuestions() as any}
                drawerInsideDrawer
              />
            )}
          </div>
          <div className="overflow-y-auto h-full">
            {currentQuestions.length ? (
              <Drawer.Body className={'' as any}>
                <div className="space-y-1">
                  {currentQuestions.map((row: any, index: number) => (
                    <Drawer.QuestionOfTest
                      key={row?._id}
                      index={index}
                      row={row as any}
                      mainQuestionsListForm={form as any}
                      mainSetFieldValueForm={() => {}}
                      questionDatabase={[]}
                      refresh={() => {}}
                      canRemoveQuestion={true}
                      currentPage={currentPage.page}
                      questionsPerPage={questionsPerPage}
                      selectedQuestionIds={selectedQuestionIds as any}
                      setSelectedQuestionIds={setSelectedQuestionIds as any}
                      handleGetQuestionGenerateData={handleGetQuestionGenerateData as any}
                      setQuestionDatabase={setQuestionDatabase as any}
                      generatedQuestionsIds={generatedQuestionsIds as any}
                      setAnyQuestionHasEditMode={setAnyQuestionHasEditMode as any}
                      className={''}
                    >
                      {null}
                    </Drawer.QuestionOfTest>
                  ))}
                </div>
              </Drawer.Body>
            ) : (
              <div className="flex justify-center items-center h-full">
                <div className=" w-2/4 space-y-2">
                  {questionDatabase.length > 0 ? (
                    <NoDataMatches message="No results found." />
                  ) : (
                    <Placeholder image="/UI/src/assets/placeholder/NoQuestions.svg" title="No questions created yet" subTitle="" />
                  )}
                </div>
              </div>
            )}
            {currentQuestions.length > 0 && (
              <Drawer.Footer
                className={'' as any}
                isPaginationActive={questionsList.length > 0}
                paginationData={{
                  showingText: showingText as any,
                  count: questionsList.length,
                  size: questionsPerPage,
                  onPageChange: setCurrentPage as any,
                  currentPage: currentPage.page,
                  pagesCount: totalPages,
                }}
              ></Drawer.Footer>
            )}
          </div>
          {currentQuestions.length > 0 && (
            <Drawer.Footer className={'' as any} isPaginationActive={false as any} paginationData={{} as any}>
              <Drawer.Footer.Button
                label="Cancel"
                disabled={questionGenerateLoading as any}
                tertiary
                onClick={onClose as any}
                className={'' as any}
                mainButton={false as any}
              />
              <Drawer.Footer.Button
                loading={questionGenerateLoading as any}
                label="Done"
                disabled={
                  questionGenerateLoading ||
                  !selectedQuestionIds.length ||
                  !!Object.keys(isAnyQuestionHasEditMode)?.find((question: any) => isAnyQuestionHasEditMode[question])
                }
                onClick={onSubmit as any}
                mainButton={true as any}
                className={'' as any}
              />
            </Drawer.Footer>
          )}
        </div>
      </Drawer.SplitView>
    </Drawer>
  );
};
