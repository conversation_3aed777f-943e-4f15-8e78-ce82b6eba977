// React
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useUserPermissions, UserPermissions } from 'UI';
import { RootState, useAppSelector, UserData, useFetchList, useScreenSize } from 'UI/src';

// Core
import { TestDifficulty, TestSeniorityLevel, AvarageScore, Table, DurationFieldColumn, NameFieldColumn, NumberOfQuestionFieldColumn } from 'src';

// Components
import { CreateTemplateDialog } from '../create/create-template-dialog';

interface ScreeningTabProps {
  onLoadingChange: (value: boolean) => void;
  createTemplateDialog?: any;
}

export const ScreeningTab = ({ onLoadingChange, createTemplateDialog }: ScreeningTabProps) => {
  const { hasPermission } = useUserPermissions();
  // Destructuring
  const { isCreateTemplateVisible, setCreateTemplateVisibilty } = createTemplateDialog || {};

  // Context
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Permissions
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');
  const isAdmin = Array.isArray(userData?.role) && userData?.role.includes('admin');
  const isContentCreator = Array.isArray(userData?.role) && userData?.role.includes('content-creator');
  const isHr = Array.isArray(userData?.role) && userData?.role.includes('hr');

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState<boolean | undefined>();
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);

  // Hooks
  const navigate = useNavigate();
  const screen = useScreenSize();
  const { type } = useParams();
  const initialFilters = {
    // ...(userData.trackId
    //   ? {}
    //   : {
    //       category: {
    //         label: 'Category',
    //         lookup: 'category',
    //       },
    //     }),
    // subCategory: {
    //   label: 'Sub Category',
    //   lookup: 'subcategory',
    //   parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: null },
    // },
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
    seniorityLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },
  };

  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh, handleDates } = useFetchList(
    'quizzes/list/phoneScreening',
    {
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
    }
  );

  const filterFeedData = Object.keys(initialFilters).map((key) => (key === 'difficulty' ? initialFilters.difficulty.enum : key));

  useEffect(() => {
    if (backupList.length === 0 && list.length > 0) {
      setBackupList(list);
    }
  }, [list, backupList.length]);

  // Notify parent component about loading state changes
  useEffect(() => {
    if (onLoadingChange) {
      onLoadingChange(loading || !ready);
    }
  }, [loading, ready, onLoadingChange]);
  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Screening Templates"
        searchPlaceholder="Search for template name..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          applicantName: (_: unknown, row: { _id: string; title: string }) => (
            <NameFieldColumn
              id={row?._id}
              name={row?.title}
              showMoreMap={showMoreMap as any}
              onClick={() => (isSuperAdmin || isAdmin || isContentCreator) && navigate(`/app/assessment-templates/${type}/view/${row?._id}`)}
            />
          ),
          seniortyLevel: (_: unknown, row: { seniorityLevel: number }) => {
            return (
              <div className="w-fit">
                <TestSeniorityLevel seniorityLevel={row?.seniorityLevel} />
              </div>
            );
          },
          numberOfQuestion: (_: unknown, row: { numOfQuestions: number }) => <NumberOfQuestionFieldColumn numOfQuestions={row?.numOfQuestions} />,
          duration: (_: unknown, row: { duration: number }) => <DurationFieldColumn duration={row?.duration} />,
        }}
        columns={[
          {
            key: 'applicantName',
            label: 'Name',
            primary: true,
            width: '22%',
          },
          {
            key: 'seniortyLevel',
            label: 'Seniorty Level',
            // primary: true,
            width: '15%',
            inline: true,
          },
          {
            key: 'numberOfQuestion',
            label: 'No.Question',
            // primary: true,
            width: '13%',
          },
          {
            key: 'duration',
            label: 'Duration',
            // primary: true,
            width: '10%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_: unknown, row: { _id: string }) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/assessment-templates/${type}/view/${row?._id}`,
                },
                ...((isSuperAdmin || isAdmin || isContentCreator) && hasPermission(UserPermissions.UPDATE_ASSESSMENT)
                  ? [
                      {
                        label: 'Update',
                        customIcon: 'tableEdit',
                        iconWidth: '22',
                        iconHeight: '22',
                        color: 'text-black dark:text-white',
                        path: `/app/assessment-templates/${type}/edit/${row?._id}`,
                      },
                    ]
                  : []),
                // {
                //   label: 'Assign',
                //   customIcon: 'assign',
                //   iconWidth: '22',
                //   iconHeight: '22',
                //   color: 'text-black dark:text-white',
                //   // onClick: () => {},
                //   dropDown: [
                //     {
                //       label: 'Interview',
                //       color: 'text-black dark:text-white',
                //       customIcon: 'interview',
                //       element: (
                //         <span
                //           className={`w-24 text-xs px-2 py-1 rounded-full shadow-md font-bold tracking-wide bg-magic-gradient bg-[length:200%]  text-white overflow-hidden`}
                //         >
                //           AI Magic ✨
                //         </span>
                //       ),
                //       onClick: () => {
                //         if (isSuperAdmin || userData?.features?.assignInterview > 0) {
                //           setAssignInterviewTestVisible(true);
                //           setApplicantDetails(row);
                //         } else {
                //           setNeedSubscription(true);
                //         }
                //       },
                //     },
                //   ],
                // },
              ];
            },
          },
        ]}
        groups={[
          {
            name: 'group1',
            keys: [['seniortyLevel'], ['numberOfQuestion', 'duration']],
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
        placeholder={{
          title: 'No assessment created yet',
          subTitle: 'Start by creating a assessment to evaluate applicant`s skills.',
          image: '/UI/src/assets/placeholder/TestImagePlaceholder.svg',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // addButtonLabel=""
        // onClickAdd={() => {}}
        // actions={[]}
        hideJumbotron
        isScrollableTabsExists
      />

      {isCreateTemplateVisible && (
        <CreateTemplateDialog isCreateTemplateVisible={isCreateTemplateVisible} onClose={() => setCreateTemplateVisibilty(false)} refresh={refresh} />
      )}
    </>
  );
};
