// React
import { useState, useEffect } from 'react';

// UI
import { <PERSON><PERSON>, <PERSON><PERSON>, ToggleFilter, NoDataMatches } from 'src';

// Components
import { NoDataFound } from 'src/components/no-data-found-placeholder';

// React icons
import { HiOutlineAdjustmentsHorizontal } from 'react-icons/hi2';

import { useFetchList, useScreenSize, RootState, useAppSelector, UserData, QuestionType, Placeholder } from 'UI/src';

import { ManualSelectPlaceholder } from './placeholder';
// TODO:FIXME:Markos

// Flowbite
import { Spinner } from 'flowbite-react';

// Form type
type QuizManualForm = {
  questionIds: string[];
  category: string;
  subCategory: string[];
  subCategoryFiltration: string[];
  questionsDifficulty: string[];
};

// Props type
type QuizSelectionManualProps = {
  setManualSelection: React.Dispatch<React.SetStateAction<boolean>>;
  selectedQuestionIds: string[];
  setSelectedQuestionIds: React.Dispatch<React.SetStateAction<string[]>>;
  form: QuizManualForm;
  setFormValue: (form: any) => void;
  setFieldValue: (field: string, value: any) => void;
  handleGetQuestionData: (id: string) => Promise<void>;
  questionDatabaseOfMainTest: QuestionType[];
};

export const QuizSelectionManual = ({
  setManualSelection,
  selectedQuestionIds,
  setSelectedQuestionIds,
  form,
  setFormValue,
  setFieldValue,
  handleGetQuestionData,
  questionDatabaseOfMainTest,
}: QuizSelectionManualProps) => {
  // States
  const [showFilter, setShowFilter] = useState(false);
  const [isAnyQuestionHasEditMode, setAnyQuestionHasEditMode] = useState<Record<string, boolean>>({});
  const [backupList, setBackupList] = useState<QuestionType[]>([]);
  const [submitLoading, setSubmitloading] = useState(false);

  // Static data
  const filterLabelTitle = {
    subcategory: { label: 'Subcategory', optional: true, handleSingleClear: () => setFieldValue('subCategoryFiltration', []) },
    QuestionDifficulty: { label: 'Difficulty ', optional: false, handleSingleClear: () => setFieldValue('questionsDifficulty', []) },
  };

  // const noDataFound = {
  //   customIcon: 'questions' as any,
  //   messageHeader: ' No questions have been added yet',
  // };

  // User data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Hooks
  const screen = useScreenSize();
  const {
    ready,
    loading,
    setLoading,
    list: rawList,
    count,
    search,
    pagination,
    filters,
    refresh,
    exclude,
  } = useFetchList('questions/list', {
    search: '',
    pagination: {
      page: 1,
      size: 10,
    },
    filters: {
      // ...(userData.trackId
      //   ? {}
      //   : {
      category: {
        label: 'Category',
        lookup: 'category',
      },
      // }),
      subCategory: {
        label: 'Sub Category',
        lookup: 'subcategory',
        parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: form?.category },
      },
      difficulty: {
        label: 'Difficulty',
        enum: 'QuestionDifficulty',
      },
    },
    exclude: [...form.questionIds, ...questionDatabaseOfMainTest.map((question: QuestionType) => question._id)],
  });
  const list = (rawList ?? []) as QuestionType[];

  // Pagination
  const { page, size } = pagination;
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
  const isPaginationActive = !!pagination.update;

  const onClose = () => {
    setManualSelection(false);
    setSelectedQuestionIds([]);
  };

  const onSubmit = async () => {
    try {
      setSubmitloading(true);
      await Promise.all(selectedQuestionIds.map((question: string) => handleGetQuestionData(question)));
      setFormValue({
        ...form,
        questionIds: [...form.questionIds, ...selectedQuestionIds],
      });
      setSelectedQuestionIds([]);
      setSubmitloading(false);
      onClose();
    } catch (error) {
      throw error;
    }
  };

  const drawerFilter = (
    <div className="py-3 border-t mx-3">
      {(filters as any[]).map((filter, index) => {
        return (
          filter &&
          filter.key !== 'category' &&
          filter.options.length > 0 && (
            <div key={filter.label}>
              <Drawer.FilterSection
                label={filterLabelTitle?.[filter.key as keyof typeof filterLabelTitle]?.label}
                optional={filterLabelTitle?.[filter.key as keyof typeof filterLabelTitle]?.optional as any}
                form={form as any}
                showSingleClear={true}
                handleSingleClear={() => filter.options.forEach((option: any) => option.resetSingle(filter?.originalKey))}
                filter={filter as any}
                propertyKeyObject={filter.key}
                lookups={[]}
                setFieldValue={() => {}}
              />
              {index < (filters as any[]).length - 1 && <hr className="my-4 border-gray-900 border-opacity-5 dark:border-gray-700" />}
            </div>
          )
        );
      })}
    </div>
  );
  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list as any[]);
    }
  }, [list]);

  /* FIXME: This is the worest case to pre select the choosen filters using the fetchList */
  /* TODO: pre defined the choosen subcategories to be selected without purple color */
  useEffect(() => {
    /* To calculate the number of pre defined subcategories */
    // let counter = 0;

    (filters as any[]).map((filter) => {
      if (filter) {
        filter.key === 'category' &&
          filter.options.map((option: any) => form.category.includes(option.name.split('-')[1]) && !option.value && option.onChange(true));
      }
    });

    // filters.map((filter) => {
    /* Select category with the selected track or category */
    // filter.key === 'category' &&
    // filter.options.map((option) => form.category.includes(option.name.split('-')[1]) && !option.value && option.onChange(true));
    /* Select subcategories with the pre defined subcategories */
    // filter.key === 'subcategory' && filter.options.map((option) => option.value && ++counter);
    // });
    // if (counter === 0) {
    //   filters.map(
    //     (filter) =>
    //       filter.key === 'subcategory' &&
    //       filter.options.filter((option) => form.subCategory.includes(option.name.split('-')[1]) && !option.value && option.onChange(true))
    //   );
    // }
  }, [filters]);

  return (
    <Drawer split onClose={onClose}>
      {ready ? (
        <Drawer.SplitView className="">
          {screen.gt.sm() && (
            <Drawer.SplitView.SplitLeftSide>
              <Drawer.HeaderSection
                headerLabel="Filters"
                icon={<HiOutlineAdjustmentsHorizontal className="text-xl" />}
                onReset={() => (filters as any[]).forEach((filter) => filter?.options.forEach((option: any) => option.reset()))}
                onClose={screen.lt.md() ? () => setShowFilter(false) : undefined}
                resultsFound={count.toString()}
                selectedQuestionsCount={selectedQuestionIds.length}
              />

              <div className="pt-4">
                {(filters as any[]).map((filter, index) => {
                  return (
                    filter &&
                    filter.key !== 'category' &&
                    filter.options.length > 0 && (
                      <div key={filter.label}>
                        <Drawer.FilterSection
                          label={filterLabelTitle?.[filter.key as keyof typeof filterLabelTitle]?.label}
                          optional={filterLabelTitle?.[filter.key as keyof typeof filterLabelTitle]?.optional as any}
                          form={form as any}
                          filter={filter as any}
                          showSingleClear={true}
                          handleSingleClear={() => filter.options.forEach((option: any) => option.resetSingle(filter?.originalKey))}
                          propertyKeyObject={filter.key}
                          lookups={[]}
                          setFieldValue={() => {}}
                        />
                        {index < (filters as any[]).length - 1 && <hr className="my-4 border-gray-900 border-opacity-5 dark:border-gray-700" />}
                      </div>
                    )
                  );
                })}
              </div>

              {/* Loading Indicator */}
              {submitLoading && (
                <div className="absolute left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80 z-50">
                  <Spinner size="lg" color="purple" />
                </div>
              )}
            </Drawer.SplitView.SplitLeftSide>
          )}

          <Drawer.SplitView.SplitRightSide>
            <Drawer.HeaderSection
              headerLabel="Manual Selection Questions"
              selectedQuestionsCount={selectedQuestionIds.length}
              onClose={onClose}
              onReset={() => {}}
              icon={null}
              resultsFound={count.toString()}
            />

            <div className="flex items-center gap-3">
              <Drawer.Search
                value={search.value}
                onInput={(e: React.ChangeEvent<HTMLInputElement>) => search.update(e.target.value)}
                className="flex w-full"
                children={undefined}
              />

              {!screen.gt.sm() && (
                <ToggleFilter
                  filters={filters as any}
                  drawerFilter={{ element: drawerFilter, filterCountNumber: 0 } as any}
                  drawerClearAll={() => (filters as any[]).forEach((filter) => filter?.options.forEach((option: any) => option.reset()))}
                  resultsFound={count}
                  drawerInsideDrawer
                  /* 
                    FIXME: WARNING! This property is considered as an Exceptional Case (Only in  Manual Selection & Drawer Filter).
                    This prop should not be used in ToggleFilter anywhere else. 
                  */
                  tempException={true}
                  handleDates={{
                    startDate: { value: '', update: () => {} },
                    endDate: { value: '', update: () => {} },
                  }}
                />
              )}
            </div>
            <div className="overflow-y-auto h-full">
              {list.length ? (
                <Drawer.Body>
                  <div className="space-y-1">
                    {/*  {list.map((row, index) => (
                      <Drawer.QuestionOfTest
                        key={row?._id}
                        index={index}
                        row={row}
                        mainQuestionsListForm={form}
                        currentPage={pagination.page}
                        questionsPerPage={pagination.size}
                        selectedQuestionIds={selectedQuestionIds}
                        setSelectedQuestionIds={setSelectedQuestionIds}
                        refresh={refresh}
                        setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
                      />
                    ))} */}

                    {list.map((item, index) => {
                      const row = {
                        id: item._id,
                        isEditMode: false,
                        pendingTitle: '',
                        title: item.title,
                        question: { _id: item._id, pendingTitle: '', isEditMode: false },
                        timeTaken: { hours: 0, minutes: 0, seconds: 0 },
                        answer: '',
                        difficulty: String(item.difficulty),
                        subCategoryName: item.subCategoryName || '',
                        topicName: item.topicName || '',
                      };
                      return (
                        <Drawer.QuestionOfTest
                          key={row.id}
                          index={index}
                          row={row as any}
                          mainQuestionsListForm={form}
                          mainSetFieldValueForm={() => {}}
                          currentPage={pagination.page}
                          questionsPerPage={pagination.size}
                          selectedQuestionIds={selectedQuestionIds}
                          setSelectedQuestionIds={setSelectedQuestionIds}
                          handleGetQuestionGenerateData={handleGetQuestionData}
                          questionDatabase={questionDatabaseOfMainTest}
                          setQuestionDatabase={() => {}}
                          generatedQuestionsIds={[]}
                          refresh={refresh}
                          canRemoveQuestion={false}
                          setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
                          className=""
                          children={undefined}
                        />
                      );
                    })}
                  </div>
                </Drawer.Body>
              ) : (
                <div className="flex justify-center items-center h-full">
                  <div className=" w-2/4 space-y-2">
                    {backupList.length > 0 ? (
                      <NoDataMatches message="No results found." />
                    ) : (
                      // <NoDataFound noDataFound={noDataFound} width="70" height="70" />
                      <Placeholder image="/UI/src/assets/placeholder/NoQuestions.svg" title="No questions created yet" subTitle="" />
                    )}
                  </div>
                </div>
              )}

              {list.length > 0 && (
                <Drawer.Footer
                  isPaginationActive={isPaginationActive}
                  paginationData={{
                    showingText: showingText,
                    count: count,
                    size: size,
                    onPageChange: pagination.update,
                    currentPage: page,
                    pagesCount: pagesCount,
                  }}
                />
              )}
            </div>

            {list.length > 0 && (
              <Drawer.Footer>
                <Drawer.Footer.Button label="Cancel" tertiary onClick={onClose} />
                <Drawer.Footer.Button
                  label="Done"
                  loading={submitLoading}
                  disabled={
                    !selectedQuestionIds.length || !!Object.keys(isAnyQuestionHasEditMode)?.find((question) => isAnyQuestionHasEditMode[question])
                  }
                  onClick={onSubmit}
                />
              </Drawer.Footer>
            )}
          </Drawer.SplitView.SplitRightSide>
        </Drawer.SplitView>
      ) : (
        <ManualSelectPlaceholder />
      )}
    </Drawer>
  );
};
