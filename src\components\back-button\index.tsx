import { useNavigate, useLocation } from 'react-router-dom';
import { Icon } from '..';
import { backRouteMap } from './config';

interface BackButtonProps {
  className?: string;
}

//*NOTE: in case developer add new detail page developer must add it to "./config" file

export const BackButton = ({ className }: BackButtonProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const currentUrl: string = location.pathname;

  // Find matching back route
  const match = backRouteMap.find((route) => route.pattern.test(currentUrl));

  if (!match) {
    // Not a detail page → don’t render BackButton
    return null;
  }

  const handleNavigate = () => {
    const regexMatch = currentUrl.match(match.pattern);
    if (regexMatch) {
      navigate(match.to(regexMatch));
    }
  };

  return (
    <div className={`cursor-pointer flex items-center gap-2 ${className || ''}`} onClick={handleNavigate}>
      <Icon
        icon="line-md:arrow-left"
        className="text-[#8D5BF8] rounded-xl bg-white dark:bg-darkGrayBackground shadow-[0px_7px_10px_0px_#743AF51A] p-1"
        width="20"
      />
      <p className="text-[#667085] dark:text-gray-300 thepassHtwo">Back</p>
    </div>
  );
};
