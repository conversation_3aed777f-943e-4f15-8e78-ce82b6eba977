// React
import { useState, ReactNode } from 'react';
import { Link, useLocation } from 'react-router-dom';

// Icons
import * as LucideIcons from 'lucide-react';

// Core
import { PermissionProtectedComponent } from 'src/components';
import { AlertNote } from 'UI';

// Object Path
import { get } from 'object-path';

// Types
interface TableColumn {
  key: string;
  label?: string;
  width?: string | number;
  primary?: boolean;
  cardFooterProp?: boolean;
  buttons?: any[];
  locked?: (value: any, row: any) => any[];
  report?: any;
  enum?: string;
  lookup?: string;
  inline?: boolean;
  className?: string;
  displayLbl?: boolean;
}

interface TableRow {
  _id: string;
  [key: string]: any;
}

interface TableAction {
  label?: string;
  color?: string;
  icon?: string;
  element?: ReactNode;
  dropdownlist?: any[];
  [key: string]: any;
}

interface SearchConfig {
  update: (value: string) => void;
  value?: string;
}

interface PaginationConfig {
  page: number;
  size: number;
  update?: (updates: any) => void;
}

interface FilterConfig {
  [key: string]: any;
}

interface SelectedRowConfig {
  selectedIdSingle?: string;
  setSelectedIdSingle?: (id: string) => void;
  selectedIds?: string[];
  setSelectedIds?: (ids: string[]) => void;
  handleArchiveSelectedIds?: () => void;
  handleLockSelectedIds?: () => void;
  lockUnlockAll?: () => void;
  handleDownloadExcelSelectedIds?: () => void;
}

interface TableGroup {
  name: string;
  keys: string[][] | string[];
}

// Flowbite
import {
  Pagination,
  Spinner,
  Tooltip,
  Dropdown,
  Checkbox as FlowbiteCheckbox,
  Radio as FlowbiteRadio,
  Label,
  DropdownItem,
  DropdownDivider,
} from 'flowbite-react';

// UI
import { StaticData, useScreenSize, Placeholder, ToggleSwitch } from 'UI/src';

import { Button, Icon, EnumText, LookupText, CustomIcon, SidebarFilterPage, ToggleFilter, Jumbotron, NoDataFound, NoDataMatches } from 'src';

// Components
import { TablePlaceholder } from '../table-placeholder';
import { TableAction } from './actions';
import { SubscribeDialog } from '../placeholders/subscribe';
import CheckFeatureManagement from '@/composables/feature-management';

export const Table = ({
  title,
  searchPlaceholder,
  addButtonLabel,
  addButtonPath,
  columns,
  rows,
  backupRows,
  rowKey,
  slots,
  loading,
  ready,
  count,
  search,
  pagination,
  filters,
  setFilters,
  filterFeedData,
  drawerFilter,
  actions,
  dropdown,
  breakpoint,
  onClickAdd,
  addButtonPermission,
  addButtonUserPermission,
  singleSelectedRow,
  multiSelectedRow,
  noDataFound,
  placeholder,
  placeholderImage,
  placeholderTitle,
  placeholderSubTitle,
  showMoreMap = {},
  setShowMoreMap,
  drawerClearAll,
  noDataFoundIconWidth,
  noDataFoundIconHeight,
  hideJumbotron,
  isScrollableTabsExists,
  groups,
  cardFooterClassName,
}: any) => {
  // State
  const screen = useScreenSize();
  const [needSubscription, setNeedSubscription] = useState(false);

  // Hooks
  const location = useLocation();
  const { checkFeature } = CheckFeatureManagement();

  // Single Selected Row
  const { selectedIdSingle, setSelectedIdSingle } = singleSelectedRow || {};

  // Multi Selected Row
  const { selectedIds, setSelectedIds, handleArchiveSelectedIds, handleLockSelectedIds, lockUnlockAll, handleDownloadExcelSelectedIds } =
    multiSelectedRow || {};

  // Pagination
  const { page = 1, size = 10 } = pagination || {};
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
  const isPaginationActive = !!(pagination && pagination.update);

  // Handle Ready
  if (!ready) {
    return <TablePlaceholder />;
  }

  const renderCell = (column: any, row: any): React.ReactNode => {
    const value = get(row, column.key);

    if (column.buttons || column.locked || column.report) {
      let buttonsData, lockedData, reportData;
      if (column.buttons) {
        buttonsData = <TableAction row={row} buttons={column.buttons} onCheckSubscription={setNeedSubscription} />;
      }

      if (column.locked) {
        lockedData = (
          <div>
            {column
              .locked(value, row)
              ?.map(
                (
                  { label, switchHandler }: { label: { lock: string; unlock: string }; switchHandler: (id: string, value: boolean) => void },
                  index: number
                ) => {
                  const data = <ToggleSwitch key={index} checked={value} onChange={(newValue) => switchHandler(row._id, newValue)} sizing="sm" />;
                  if (label) {
                    return (
                      <Tooltip
                        key={index}
                        content={value ? label.unlock : label.lock}
                        placement="bottom"
                        arrow={false}
                        className="text-xs bg-gray-700 dark:bg-gray-200 dark:text-gray-900"
                      >
                        {data}
                      </Tooltip>
                    );
                  }
                  return data;
                }
              )}
          </div>
        );
      }

      if (column.report) {
        reportData = (
          <div>
            {column.report(value, row)?.map(({ label, color, handelReport, ...button }: any, index: number) => {
              const data = <Icon className="cursor-pointer" key={index} width="20" style={{ color: color }} {...button} />;
              if (label) {
                return (
                  <Tooltip
                    key={index}
                    content={label}
                    placement="bottom"
                    arrow={false}
                    className="text-xs bg-gray-700 dark:bg-gray-200 dark:text-gray-900"
                  >
                    {data}
                  </Tooltip>
                );
              }
              return data;
            })}
          </div>
        );
      }

      return (
        <div className="flex gap-3">
          {row.status === 3 && reportData}
          {buttonsData}
          {lockedData}
        </div>
      );
    }
    if (column.enum) {
      // const text = <EnumText name={column.enum} value={value} />;
      return (
        <div className="relative w-full">
          {/* <div className="break-all lg:truncate">{text}</div> */}
          {/* {screen.gt.lg() && (
            <Tooltip content={text} placement="bottom" arrow={false} className="text-sm bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit">
              <div className="absolute top-0 left-0 w-full h-full"></div>
            </Tooltip>
          )} */}
        </div>
      );
    }

    if (column.lookup) {
      const text = <LookupText lookup={column.lookup} id={value} />;
      return (
        <div className="relative w-full">
          <div className="break-all lg:truncate">{text}</div>
          {screen.gt.lg() && (
            <Tooltip content={text} placement="bottom" arrow={false} className="text-sm bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit">
              <div className="absolute top-0 left-0 w-full h-full"></div>
            </Tooltip>
          )}
        </div>
      );
    }

    if (slots[column.key]) {
      return slots[column.key](value, row);
    }

    if (!value) {
      return <p>—</p>;
    }

    return <p className="truncate">{value}</p>;
  };

  const toggleShowMore = (rowKey: string) => {
    setShowMoreMap((prevMap: any) => ({
      ...prevMap,
      [rowKey]: !prevMap[rowKey],
    }));
  };

  const handleBlockMobileView = (column: any, row: any, index: number) => (
    <div key={column.key} className="w-full border-[#EAECF0] dark:border-gray-700">
      <p
        className={`px-3 text-sm text-[#667085] font-normal dark:text-gray-400 ${index === 0 ? 'bg-[#F9FAFB] dark:bg-darkGrayBackground border-b py-2 rounded-t-lg' : 'pt-2.5 pb-2'
          }`}
      >
        {column.label}
      </p>
      <div
        className={`${!showMoreMap[row[rowKey]] && 'truncate'} ${index === 0 ? 'py-3' : 'pb-2'
          } px-3 text-sm text-gray-900 dark:text-gray-100 font-normal`}
      >
        {/* Add selection checkbox/radio beside the name (first primary column) */}
        {index === 0 && (singleSelectedRow || multiSelectedRow) ? (
          <div className="flex items-center gap-2">
            {singleSelectedRow && (
              <FlowbiteRadio
                id={`mobile-${row?._id}`}
                value={row?._id}
                checked={selectedIdSingle === row?._id}
                onChange={(value: any) => setSelectedIdSingle(value?.target?.value)}
                className="flex-shrink-0 text-purple-500 cursor-pointer"
              />
            )}
            {multiSelectedRow && (
              <FlowbiteCheckbox
                id={`mobile-${row?._id}`}
                value={row?._id}
                checked={selectedIds.includes(row?._id)}
                onChange={handleCheckboxChange}
                className="flex-shrink-0 text-purple-500 cursor-pointer"
              />
            )}
            <div className="flex-1">{renderCell(column, row)}</div>
          </div>
        ) : (
          renderCell(column, row)
        )}
      </div>
    </div>
  );

  const tableAddBtn =
    (addButtonLabel || onClickAdd) ? (
      <Button
        type="button"
        to={addButtonPath}
        onClick={onClickAdd}
        className="w-fit min-w-20"
        permission={addButtonPermission}
      >
        <div className="w-5 h-5 sm:mr-2">
          <Icon icon="mdi:add" width="22" />
        </div>
        {addButtonLabel && <p className="text-nowrap">{addButtonLabel}</p>}
      </Button>
    ) : null;

  // Multi-select row
  const handleCheckboxChange = (event: any) => {
    const { value, checked } = event.target;
    if (checked) {
      setSelectedIds((prevValues: any[]) => [...prevValues, value]);
    } else {
      setSelectedIds((prevValues: any[]) => prevValues.filter((val: any) => val !== value));
    }
  };
  const handleCheckboxSelectAll = (event: any) => {
    if (event.target.checked) {
      setSelectedIds(rows?.map((row: any) => row._id));
    } else {
      setSelectedIds([]);
    }
  };

  /* Don't forget to enhance the same function in create template with no table */
  const handleHeight = () => {
    if (hideJumbotron) {
      if (isScrollableTabsExists) {
        return {
          table: '2xl:max-h-[calc(100vh-267px)]',
          sidebarFilter: '2xl:max-h-[calc(100vh-267px)]',
        };
      } else
        return {
          table: '2xl:max-h-[calc(100vh-175px)]',
          sidebarFilter: '2xl:max-h-[calc(100vh-175px)]',
        };
    } else {
      if (isScrollableTabsExists) {
        return {
          table: '',
          sidebarFilter: '',
        };
      } else
        return {
          table: '2xl:max-h-[calc(100vh-182px)]',
          sidebarFilter: '2xl:max-h-[calc(100vh-182px)]',
        };
    }
  };

  const isRowMoreThanOne = rows?.length > 1;

  // Handle Render if There's no Loading
  return (
    <section className={`antialiased relative space-y-2 2xl:space-y-0 ${isRowMoreThanOne && 'overflow-hidden'}`}>
      {/* Header */}
      {(!hideJumbotron || actions?.length > 0 || addButtonLabel || onClickAdd) && (
        <div className={`flex flex-wrap justify-between sm:items-center gap-4 mb-3`}>
          {!hideJumbotron && <Jumbotron />}
          {/* Buttons */}
          <div className="flex flex-wrap items-center gap-2">
            {actions?.map((action: any) => {
              if (action?.dropdownlist) {
                return (
                  <div className="relative z-40" key={action?.label || action?.icon}>
                    <Dropdown
                      label=""
                      dismissOnClick={false}
                      renderTrigger={() => (
                        <Button
                          // Make ${screen.gt.xs() && 'min-w-[240px]'} dynamic when this button (dropdown) is used again
                          icon="line-md:link"
                          label={action.label}
                          outline
                        />
                      )}
                    >
                      {action.dropdownlist?.map(({ label, color, icon, element, lucideIcon, ...subButton }: any, index: number) => (
                        <div className="min-w-56" key={label || icon}>
                          <DropdownItem onClick={subButton.onClick}>
                            <div className="flex gap-2 cursor-pointer w-fit" {...subButton}>
                              {icon && <Icon className="cursor-pointer" width={'20'} style={{ color: color }} icon={icon} />}
                              {subButton.customIcon && (
                                <CustomIcon
                                  definedIcon={subButton.customIcon}
                                  className={`cursor-pointer ${color} ${subButton.className}`}
                                  width={subButton.iconWidth || '20'}
                                  height={subButton.iconHeight || '20'}
                                  onClick={subButton.onClick}
                                />
                              )}
                              {lucideIcon &&
                                LucideIcons[lucideIcon as keyof typeof LucideIcons] &&
                                (() => {
                                  const LucideIconComponent: any = LucideIcons[lucideIcon as keyof typeof LucideIcons];
                                  return (
                                    <LucideIconComponent
                                      className={`cursor-pointer ${color} ${subButton.className}`}
                                      width={subButton.iconWidth || '20'}
                                      height={subButton.iconHeight || '20'}
                                      onClick={subButton.onClick}
                                    />
                                  );
                                })()}
                              <p className="text-black text-nowrap dark:text-white">{label}</p>
                              <p>{element}</p>
                            </div>
                          </DropdownItem>
                          {action.dropdownlist?.length - 1 > index && <hr className="dark:border-gray-500" />}
                        </div>
                      ))}
                    </Dropdown>
                  </div>
                );
              } else {
                return (
                  <Button
                    key={action?.label || action?.icon}
                    outline
                    type="button"
                    as={action.to ? Link : undefined}
                    to={action.to}
                    onClick={action.onClick}
                  >
                    {action.icon && (
                      <div className="w-5 h-5 sm:mr-2">
                        <Icon icon={action.icon} width="22" />
                      </div>
                    )}
                    {action.label && <p className="hidden sm:block text-nowrap">{action.label}</p>}
                  </Button>
                );
              }
            })}

            {
              addButtonUserPermission ? (
                <PermissionProtectedComponent permissions={addButtonUserPermission}>
                  { tableAddBtn }
                </PermissionProtectedComponent>
              ) : (
                tableAddBtn
              )
            }

          </div>
        </div>
      )}

      <PermissionProtectedComponent permissions={addButtonUserPermission}>
        {!checkFeature(addButtonPermission as any) && (
          <AlertNote message="Oops! You’ve hit your limit , Add extra credits to keep enjoying all the features." nav={{}} />
        )}
      </PermissionProtectedComponent>

      {(title || search || filters?.length > 0 || drawerFilter) && (
        <div className="bg-white dark:bg-darkBackgroundCard p-3 mb-1">
          <div className="dark:text-white flex flex-wrap flex-col sm:flex-row justify-between sm:items-center gap-4 text-[13px]">
            <div className="flex items-center gap-2">
              <h1 className="dark:text-white flex text-lg text-[#181D27] font-semibold items-center tracking">{title}</h1>
              {count > 0 && (
                <div className="text-[#6941C6] border text-sm border-[#E9EAEB] dark:text-white bg-[#F9F5FF] dark:bg-[#6F3ED8] rounded-full px-3 py-1 h-fit font-medium">
                  {count}
                </div>
              )}
            </div>
            {backupRows?.length > 0 && (
              <div className="flex flex-row items-center justify-between gap-x-2">
                {/* Search bar */}
                {search && (
                  <div className={`rounded-lg flex w-full flex-row items-center space-x-3 space-y-0 justify-between shadow-sm`}>
                    <div className="relative w-full">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                      </div>
                      <input
                        type="text"
                        placeholder={searchPlaceholder}
                        className="bg-gray-white border truncate border-gray-200 text-gray-800 text-[13.5px] rounded-lg block w-full sm:w-[400px] pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0 focus:border-gray-300"
                        value={search.value}
                        onInput={(e) => search.update((e.target as HTMLInputElement).value)}
                      />
                    </div>
                  </div>
                )}

                {/* Filters Button */}
                {(filters.length > 0 || drawerFilter) && (
                  <div className={`${filterFeedData?.length > 0 && '2xl:hidden'}`}>
                    <ToggleFilter filters={filters} drawerFilter={drawerFilter} drawerClearAll={drawerClearAll} resultsFound={count} />
                  </div>
                )}
              </div>
            )}

            {/* Dropdown */}
            {dropdown && (
              <Dropdown
                arrowIcon={false}
                dismissOnClick={false}
                label={
                  <>
                    <div className="w-5 h-5 sm:mr-2">
                      <Icon icon={dropdown.button.icon} width="22" />
                    </div>
                    <p className="hidden sm:block">{dropdown.button.label}</p>
                  </>
                }
                /* FIXME: Fix the gradientMonochrome */
                // gradientMonochrome="purple"
                className="rounded-lg shadow-2xl border sm:!-left-16"
              >
                <section className="w-[200px] py-2 space-y-2">
                  {dropdown.actions.map((action: { onClick: () => void; label: string }, index: number) => (
                    <div key={index}>
                      <DropdownItem onClick={action.onClick}>
                        <p>{action.label}</p>
                      </DropdownItem>
                      {index < dropdown.actions.length - 1 && <DropdownDivider />}
                    </div>
                  ))}
                </section>
              </Dropdown>
            )}
          </div>
        </div>
      )}

      <div className="flex justify-between gap-4 pt-2">
        {/* Sidebar Filter */}
        {filterFeedData?.length > 0 && (
          <div className={`hidden 2xl:block w-full max-w-[270px] ${handleHeight()?.sidebarFilter} `}>
            <SidebarFilterPage
              filterData={{
                filterFeedData,
                setFilters,
              }}
              searchInputField={search}
            />
          </div>
        )}

        <div className="w-full shadow-[2px_2px_20px_2px_#743AF51A] overflow-hidden 2xl:w-fit">
          {/* Content */}
          <div
            className={`bg-white border-2 border-[#F2F4F7] dark:bg-darkBackgroundCard dark:border-[#374151] rounded-lg overflow-y-auto ${handleHeight()?.table
              }`}
          >
            {/* Multi Select */}
            {(multiSelectedRow || handleDownloadExcelSelectedIds || handleArchiveSelectedIds || handleLockSelectedIds) && (
              <div className="flex items-center pt-1 pl-3 space-x-2 sm:justify-start sm:space-x-8 sm:pt-0 dark:text-white">
                {/* {multiSelectedRow && (
                  <div className="flex border border-gray-200  dark:border-[#374151] rounded-lg px-2 py-1 ">
                    <div className="text-primaryPurple border border-gray-200 dark:border-[#374151] rounded-md min-w-6 w-fit text-center px-1">
                      {selectedIds.length}
                    </div>
                    <div className="flex">
                      <span className="hidden px-2 sm:block">Selected</span>
                      <Icon icon="uim:multiply" width={'20'} className="cursor-pointer" onClick={() => setSelectedIds([])} />
                    </div>
                  </div>
                )} */}
                {handleDownloadExcelSelectedIds && (
                  <div
                    className={`flex ${selectedIds.length ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'}`}
                    onClick={handleDownloadExcelSelectedIds}
                  >
                    <Icon icon="simple-icons:microsoftexcel" width={'22'} className="mx-2" />
                    <span className="hidden sm:block">Excel</span>
                  </div>
                )}
                {handleArchiveSelectedIds && (
                  <div
                    className={`flex ${selectedIds.length ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'}`}
                    onClick={handleArchiveSelectedIds}
                  >
                    <Icon icon="hugeicons:archive-02" width={'20'} className="mx-2 text-[#9061F9]" />
                    <span className="hidden sm:block">Archive</span>
                  </div>
                )}
                {handleLockSelectedIds && (
                  <div className="flex">
                    <ToggleSwitch
                      checked={lockUnlockAll}
                      onChange={(newValue) => handleLockSelectedIds(newValue)}
                      sizing="sm"
                      disabled={!selectedIds.length}
                      label={screen.gt.xs() ? 'Lock' : ''}
                    />
                  </div>
                )}
              </div>
            )}

            {/* Table (Large screens) */}
            {screen.gt.md() && (
              <table className="hidden w-full text-xs tracking-wide text-left text-gray-500 table-fixed lg:table dark:text-gray-400">
                <thead
                  className={`text-[12px] text-[#535862]  bg-[#F9F8FA] border-y dark:border-none  border-[#e7e7e7c6] dark:bg-gray-700 dark:text-gray-400 ${isRowMoreThanOne && 'sticky'
                    } top-0 z-20`}
                >
                  <tr>
                    {singleSelectedRow && <th className="w-4 p-3" />}
                    {multiSelectedRow && (
                      <th className="w-4 p-3">
                        <FlowbiteCheckbox
                          onChange={handleCheckboxSelectAll}
                          checked={selectedIds.length === rows.length && selectedIds.length > 0}
                          className="text-purple-500 cursor-pointer"
                        />
                      </th>
                    )}
                    {columns?.map((column: { width: string; label: string; key: string }) => (
                      <th key={column.key} className="p-4 font-semibold text-start" style={{ width: column.width }}>
                        {column.label}
                      </th>
                    ))}
                  </tr>
                </thead>
                {rows.length > 0 && (
                  <tbody className={`${isRowMoreThanOne && 'overflow-hidden'}`}>
                    {rows?.map((row: any) => (
                      <tr key={row[rowKey]} className="font-medium border-b dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800">
                        {singleSelectedRow && (
                          <td className="p-4">
                            <FlowbiteRadio
                              value={row?._id}
                              checked={selectedIdSingle === row?._id}
                              onChange={(value: any) => setSelectedIdSingle(value?.target?.value)}
                              className="text-purple-500 cursor-pointer"
                            />
                          </td>
                        )}
                        {multiSelectedRow && (
                          <td className="p-4">
                            <FlowbiteCheckbox
                              value={row?._id}
                              checked={selectedIds.includes(row?._id)}
                              onChange={handleCheckboxChange}
                              className="text-purple-500 cursor-pointer"
                            />
                          </td>
                        )}
                        {columns.map((column: { key: string }) => (
                          <td key={column.key} className="p-4 text-sm font-medium text-start whitespace-nowrap dark:text-gray-400">
                            {renderCell(column, row)}
                          </td>
                        ))}
                      </tr>
                    ))}
                  </tbody>
                )}
              </table>
            )}

            {/* Grid (Mobile screens) */}
            {screen.sm() && (
              <section className="hidden grid-cols-2 gap-4 m-4 overflow-hidden text-xs text-gray-500 sm:grid lg:hidden md:grid-cols-3 dark:text-gray-400">
                {rows.map((row: any) => (
                  <Label
                    htmlFor={row?._id}
                    key={row[rowKey]}
                    className={`border dark:border-gray-600 p-3 rounded-lg space-y-2 ${(singleSelectedRow || multiSelectedRow) && row._id === selectedIdSingle ? 'border-[#8D5BF8] bg-[#F9F6FF]' : 'border-gray-200'
                      } ${(singleSelectedRow || multiSelectedRow) && 'cursor-pointer'}`}
                  >
                    {!columns.some((column: { key: string }) => column.key === 'actions') && (singleSelectedRow || multiSelectedRow) && (
                      <>
                        {singleSelectedRow && (
                          <FlowbiteRadio
                            id={row?._id}
                            value={row?._id}
                            checked={selectedIdSingle === row?._id}
                            onChange={(value: any) => setSelectedIdSingle(value?.target?.value)}
                            className="text-purple-500 cursor-pointer"
                          />
                        )}

                        {multiSelectedRow && (
                          <FlowbiteCheckbox
                            id={row?._id}
                            value={row?._id}
                            checked={selectedIds.includes(row?._id)}
                            onChange={handleCheckboxChange}
                            className="text-purple-500 cursor-pointer"
                          />
                        )}
                      </>
                    )}

                    {columns
                      .sort((firCol: any, secCol: any) => (secCol.key === 'actions' ? 1 : 0) - (firCol.key === 'actions' ? 1 : 0))
                      ?.map((column: any) =>
                        column.key === 'actions' ? (
                          <div key={column.key} className={`flex justify-between`}>
                            <div>
                              {singleSelectedRow && (
                                <FlowbiteRadio
                                  id={row?._id}
                                  value={row?._id}
                                  checked={selectedIdSingle === row?._id}
                                  onChange={(value: any) => setSelectedIdSingle(value?.target?.value)}
                                  className="text-purple-500 cursor-pointer"
                                />
                              )}

                              {multiSelectedRow && (
                                <FlowbiteCheckbox
                                  id={row?._id}
                                  value={row?._id}
                                  checked={selectedIds.includes(row?._id)}
                                  onChange={handleCheckboxChange}
                                  className="text-purple-500 cursor-pointer"
                                />
                              )}
                            </div>

                            {renderCell(column, row)}
                          </div>
                        ) : (
                          <div key={column.key} className={`text-gray-500 ${column?.inline && 'inline-flex me-2'}`}>
                            {renderCell(column, row)}
                          </div>
                        )
                      )}
                  </Label>
                ))}
              </section>
            )}

            {/* Truncated (Mobile screen) */}
            {screen.lt.sm() && (
              <section className="px-3 py-0 space-y-4 overflow-hidden text-gray-900 sm:hidden h-fit dark:text-white">
                {rows?.map((row: any) => (
                  <div
                    key={row[rowKey]}
                    className={`border border-gray-200 dark:border-gray-600 rounded-lg relative divide-y divide-gray-200 dark:divide-gray-600 ${!showMoreMap[row[rowKey]] && 'overflow-hidden'
                      }`}
                  >
                    {/* Show primary rows */}
                    {columns?.map((column: { primary: string }, index: number) => column.primary && handleBlockMobileView(column, row, index))}

                    {/* More details toggle */}
                    <div
                      onClick={() => toggleShowMore(row[rowKey])}
                      className={`flex justify-between items-center px-3 py-1 bg-[#F9FAFB] dark:bg-darkGrayBackground cursor-pointer ${!showMoreMap[row[rowKey]] && 'rounded-b-lg'
                        }`}
                    >
                      <span className="text-[#98A2B3] text-sm">{showMoreMap[row[rowKey]] ? 'Less details' : 'More details'}</span>
                      <Icon
                        width={'30'}
                        className="text-[#98A2B3]"
                        icon={
                          showMoreMap[row[rowKey]]
                            ? 'material-symbols-light:keyboard-arrow-up-rounded'
                            : 'material-symbols-light:keyboard-arrow-down-rounded'
                        }
                      />
                    </div>

                    {/* Show the rest of rows */}
                    {columns.map(
                      (column: TableColumn, index: number) => !column.primary && showMoreMap[row[rowKey]] && handleBlockMobileView(column, row, index)
                    )}
                  </div>
                ))}
              </section>
            )}

            {/* Grid (Tablet screens) */}
            {screen.md() && (
              <section className="hidden grid-cols-2 gap-4 m-4 overflow-hidden text-xs text-gray-500 sm:grid lg:hidden md:grid-cols-2 dark:text-gray-400">
                {rows?.map((row: TableRow) => (
                  <Label
                    htmlFor={row?._id}
                    key={row[rowKey]}
                    className={`border dark:border-gray-600 p-3 rounded-lg space-y-2 ${(singleSelectedRow || multiSelectedRow) && row._id === selectedIdSingle ? 'border-[#8D5BF8] bg-[#F9F6FF]' : 'border-gray-200'
                      } ${(singleSelectedRow || multiSelectedRow) && 'cursor-pointer'}`}
                  >
                    {(() => {
                      const actions = columns.find((column: TableColumn) => column.key === 'actions');
                      const bodyColumns = columns
                        .sort((firCol: TableColumn, secCol: TableColumn) => (secCol.key === 'actions' ? 1 : 0) - (firCol.key === 'actions' ? 1 : 0))
                        .filter((column: TableColumn) => !column.cardFooterProp);

                      const footerColumns = columns.filter((column: TableColumn) => column.cardFooterProp);

                      // Check if groups exist and have items
                      const hasGroups = groups && groups.length > 0;

                      // Track which columns have been rendered
                      const renderedColumns = new Set();

                      // Track which groups have been rendered
                      const renderedGroups = new Set();

                      // Map to quickly find which group and subgroup a column belongs to
                      const columnToGroupMap: { [key: string]: any } = {};
                      if (hasGroups) {
                        groups.forEach((group: TableGroup) => {
                          // Handle nested arrays of keys (subgroups)
                          if (Array.isArray(group.keys[0])) {
                            (group.keys as string[][]).forEach((subgroup: string[], subgroupIndex: number) => {
                              subgroup.forEach((key: string) => {
                                columnToGroupMap[key] = {
                                  groupName: group.name,
                                  subgroupIndex,
                                };
                              });
                            });
                          } else {
                            // Handle flat arrays for backward compatibility
                            (group.keys as string[]).forEach((key: string) => {
                              columnToGroupMap[key] = {
                                groupName: group.name,
                                subgroupIndex: 0,
                              };
                            });
                          }
                        });
                      }

                      const renderedCell = (column: any) => (
                        <div key={column.key} className={`text-gray-500 flex gap-2 items-center`}>
                          {column.label && column.displayLbl && <div className="text-[12px] text-[#65676D]">{column.label}:</div>}
                          {
                            <div className={`text-gray-500 ${column.inline && 'inline-flex me-2'} ${column.className}`}>
                              {renderCell(column, row)}
                            </div>
                          }
                        </div>
                      );

                      return (
                        <>
                          {
                            <div className={`flex ${singleSelectedRow || multiSelectedRow ? 'justify-between' : 'justify-end'}`}>
                              <div>
                                {singleSelectedRow && (
                                  <FlowbiteRadio
                                    id={row?._id}
                                    value={row?._id}
                                    checked={selectedIdSingle === row?._id}
                                    onChange={(value: any) => setSelectedIdSingle(value?.target?.value)}
                                    className="text-purple-500 cursor-pointer"
                                  />
                                )}

                                {multiSelectedRow && (
                                  <FlowbiteCheckbox
                                    id={row?._id}
                                    value={row?._id}
                                    checked={selectedIds.includes(row?._id)}
                                    onChange={handleCheckboxChange}
                                    className="text-purple-500 cursor-pointer"
                                  />
                                )}
                              </div>

                              {actions && renderCell(actions, row)}
                            </div>
                          }

                          {hasGroups ? (
                            <>
                              {/* Process columns in their original order */}
                              {bodyColumns
                                .filter((column: TableColumn) => column.key !== 'actions')
                                ?.map((column: TableColumn) => {
                                  const columnKey = column.key;

                                  // Skip if already rendered
                                  if (renderedColumns.has(columnKey)) {
                                    return null;
                                  }

                                  // If column is part of a group
                                  if (columnToGroupMap[columnKey]) {
                                    const { groupName, subgroupIndex } = columnToGroupMap[columnKey];

                                    // Skip if we've already rendered this group
                                    if (renderedGroups.has(groupName)) {
                                      return null;
                                    }

                                    // Mark this group as rendered
                                    renderedGroups.add(groupName);

                                    const group = groups.find((g: TableGroup) => g.name === groupName);

                                    // Render the entire group with subgroups
                                    return (
                                      <div key={groupName} className="flex justify-between">
                                        {group && Array.isArray(group.keys[0]) ? (
                                          // Handle nested arrays (subgroups)
                                          (group.keys as string[][]).map((subgroupKeys: string[], idx: number) => (
                                            <div key={`${groupName}-subgroup-${idx}`} className="flex gap-2">
                                              {subgroupKeys.map((key: string) => {
                                                const groupColumn = columns.find((col: TableColumn) => col.key === key);
                                                if (groupColumn) {
                                                  renderedColumns.add(key);
                                                  return (
                                                    <div key={key} className={`text-gray-500 ${groupColumn?.inline && 'inline-flex me-2'}`}>
                                                      {renderCell(groupColumn, row)}
                                                    </div>
                                                  );
                                                }
                                                return null;
                                              })}
                                            </div>
                                          ))
                                        ) : (
                                          // Handle flat arrays for backward compatibility
                                          <div className="flex gap-2">
                                            {group &&
                                              (group.keys as string[]).map((key: string) => {
                                                const groupColumn = columns.find((col: TableColumn) => col.key === key);
                                                if (groupColumn) {
                                                  renderedColumns.add(key);
                                                  return (
                                                    <div key={key} className={`text-gray-500 ${groupColumn?.inline && 'inline-flex me-2'}`}>
                                                      {renderCell(groupColumn, row)}
                                                    </div>
                                                  );
                                                }
                                                return null;
                                              })}
                                          </div>
                                        )}
                                      </div>
                                    );
                                  }
                                  // If column is not part of any group
                                  else {
                                    renderedColumns.add(columnKey);
                                    return renderedCell(column);
                                  }
                                })}
                            </>
                          ) : (
                            bodyColumns.map((column: TableColumn) => column.key !== 'actions' && renderedCell(column))
                          )}

                          {footerColumns.length > 0 && (
                            <>
                              <div className="h-0.5 w-full bg-gray-200 mb-12"></div>
                              <div className={`flex gap-2 ${cardFooterClassName}`}>
                                {footerColumns.map((column: TableColumn) => renderedCell(column))}
                              </div>
                            </>
                          )}
                        </>
                      );
                    })()}
                  </Label>
                ))}
              </section>
            )}

            {/* No data created || No results found */}
            {!rows.length && (
              <div className="my-8">
                {backupRows.length > 0 ? (
                  // <NoDataMatches />
                  <Placeholder title="No results found" image="/UI/src/assets/placeholder/NoResults.svg" />
                ) : (
                  // <NoDataFound noDataFound={noDataFound} width={noDataFoundIconWidth} height={noDataFoundIconHeight} />
                  <Placeholder
                    title={placeholder?.title || placeholderTitle}
                    subTitle={placeholder?.subTitle || placeholderSubTitle}
                    image={placeholder?.image || placeholderImage}
                  />
                )}
              </div>
            )}

            {/* Loading */}
            {loading && (
              <div className="absolute top-0 bottom-0 left-0 right-0 z-50 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
                <Spinner size="lg" color="purple" />
              </div>
            )}

            {/* Pagination */}
            {isPaginationActive && count > size && (
              <nav
                className={`flex flex-row justify-center items-center px-4 pt-1 pb-2 bg-[#F9F8FA] dark:bg-darkGrayBackground ${isRowMoreThanOne && 'sticky'
                  } -bottom-0.5 z-20`}
              >
                {/* <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                  Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
                  <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
                </span> */}
                {count > size && (
                  <Pagination
                    theme={StaticData.paginationTheme}
                    currentPage={page}
                    onPageChange={(page) => pagination?.update?.({ page })}
                    showIcons
                    totalPages={pagesCount}
                    layout={(screen as any)?.gt?.[breakpoint]?.() ? 'pagination' : 'navigation'}
                    previousLabel="Previous"
                    nextLabel="Next"
                  />
                )}
              </nav>
            )}
          </div>
        </div>
      </div>
      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}
    </section>
  );
};
