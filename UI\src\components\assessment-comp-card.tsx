// Date Format
import { formatDistanceToNow } from 'date-fns';

// Core
import Button from './button';
import * as Enums from '../../../UI/src/constants/enums';
import { CustomIcon } from '../../../src/components/custom-icons';

// Lucide icons
import { Clock5, HelpCircle, ChartColumnIncreasing, User, type LucideIcon } from 'lucide-react';

interface AssessmentCompCardProps {
  // Basic info
  createdByName?: string;
  createdByDate?: string;
  updatedDate?: string;
  name: string;

  // Assessment details
  seniority: string;
  difficulty: string;
  questionsNumber: number;
  duration: number; // in minutes

  // Categories
  categoryName: string | { categoryId?: string; categoryName?: string }[];
  subCategories?: string[];

  // Status flags
  isPoor?: boolean;
  isInteractive?: boolean;

  // Radio button selection
  isSelected?: boolean;
  onRadioSelect?: (isSelected: boolean) => void;
  radioValue?: string | number; // Unique identifier for this card

  // Actions
  onReviewQuestionsClick?: () => void;

  // Optional styling
  className?: string;
}

export const AssessmentCompCard: React.FC<AssessmentCompCardProps> = ({
  createdByName,
  createdByDate,
  updatedDate,
  name,
  seniority,
  difficulty,
  questionsNumber,
  duration,
  categoryName,
  subCategories = [],
  isPoor = false,
  isInteractive = false,
  isSelected = false,
  onRadioSelect,
  radioValue,
  onReviewQuestionsClick,
  className = '',
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric',
    });
  };

  const handleRadioChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (onRadioSelect) {
      onRadioSelect(event.target.checked);
    }
  };

  /* TODO: Make this global component to be used in: assessmnet-comp-card , programming-test-list, programmin-test-single */
  const blocksData: { header: string; subHeader: any; definedIcon: LucideIcon }[] = [
    {
      header: 'Seniority',
      definedIcon: User,
      subHeader: isNaN(+seniority) ? seniority : Enums.QuizDifficulty[seniority as keyof typeof Enums.QuizDifficulty],
    },
    {
      header: 'Difficulty',
      definedIcon: ChartColumnIncreasing,
      subHeader: isNaN(+difficulty) ? difficulty : Enums.QuestionDifficulty[difficulty as keyof typeof Enums.QuestionDifficulty],
    },
    {
      header: 'Questions',
      definedIcon: HelpCircle,
      subHeader: questionsNumber.toString(),
    },
    {
      header: 'Duration',
      definedIcon: Clock5,
      subHeader: `${duration} min`,
    },
  ];

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 space-y-4 ${className}`}>
      {/* Header Section */}
      {((createdByDate && createdByDate) || updatedDate) && (
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          {/* Created by info */}
          {createdByDate && createdByDate && (
            <div className="text-sm text-gray-500 mb-2 sm:mb-0">
              Created by {createdByName}, {formatDate(createdByDate)}
            </div>
          )}

          {/* Updated date and Review button */}
          {updatedDate && (
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <span>Updated {formatDistanceToNow(new Date(updatedDate), { addSuffix: true })}</span>
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M2.5 10C2.5 11.4834 2.93987 12.9334 3.76398 14.1668C4.58809 15.4001 5.75943 16.3614 7.12987 16.9291C8.50032 17.4968 10.0083 17.6453 11.4632 17.3559C12.918 17.0665 14.2544 16.3522 15.3033 15.3033C16.3522 14.2544 17.0665 12.918 17.3559 11.4632C17.6453 10.0083 17.4968 8.50032 16.9291 7.12987C16.3614 5.75943 15.4001 4.58809 14.1668 3.76398C12.9334 2.93987 11.4834 2.5 10 2.5C7.90329 2.50789 5.89081 3.32602 4.38333 4.78333L2.5 6.66667"
                  stroke="#8D5BF8"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path d="M2.5 2.5V6.66667H6.66667" stroke="#8D5BF8" strokeLinecap="round" strokeLinejoin="round" />
                <path d="M10 5.83594V10.0026L13.3333 11.6693" stroke="#8D5BF8" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </div>
          )}
        </div>
      )}

      {/* Title and Status Tags */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
        <div className="w-full flex justify-between items-center gap-2">
          <div className="flex items-center gap-3">
            {/* Radio Button */}
            <input
              type="radio"
              id={`radio-${radioValue || name}`}
              name="assessment-selection"
              checked={isSelected}
              onChange={handleRadioChange}
              value={radioValue || name}
              className="w-4 h-4 text-[#743AF5] bg-gray-100 border-gray-300 focus:ring-[#743AF5] focus:ring-2"
            />
            <label htmlFor={`radio-${radioValue || name}`} className="text-lg thepassHone font-semibold text-[#1B1F3B] cursor-pointer">
              {name}
            </label>
          </div>

          {onReviewQuestionsClick && (
            <Button
              label="Review Questions"
              onClick={onReviewQuestionsClick}
              iconWidth="20"
              iconHeight="20"
              colorType="tertiary"
              variant="sm"
              customIcon={{
                definedIcon: 'eye',
                stroke: '#fff',
              }}
            />
          )}
        </div>

        <div className="flex gap-2">
          {isPoor && <div className="px-3 py-1 bg-red-100 text-red-800 text-xs font-medium rounded-full">Poor</div>}
          {isInteractive && (
            <div className="px-3 py-1 bg-purple-100 text-purple-800 text-xs font-medium rounded-full flex items-center gap-1">
              <CustomIcon definedIcon="blueStars" width="12" height="12" />
              Interactive
            </div>
          )}
        </div>
      </div>

      {/* TODO: Make this global component to be used in: assessmnet-comp-card , programming-test-list, programmin-test-single */}
      {/* Key Details */}
      <div className="flex flex-wrap gap-6 mb-4">
        {blocksData?.map((block) => {
          const BlockIcon = block.definedIcon;
          return (
            <div key={block.header} className="flex items-center gap-2">
              <BlockIcon className="size-5 text-[#743AF5]" />
              <span className="thepassBtwo text-[#4E5E82]">{block?.header}:</span>
              <span className="thepassBtwo text-[#2A3348]">{block?.subHeader}</span>
            </div>
          );
        })}
      </div>

      {/* FIXME: shared functions - table columns */}
      {/* Categories */}
      <div className="flex flex-wrap gap-3 capitalize">
        <div className="px-7 py-1.5 bg-[#F3F4F6] border-[#E5E7EB] text-[#4D4D4D] text-sm rounded-md">{categoryName}</div>
        {Array.isArray(subCategories) ? (
          subCategories?.map((subCategory, index) => (
            <div key={index} className="px-7 py-1.5 text-[#2A3348] thepassBfour rounded-full border border-[#E7E7E7]">
              {subCategory}
            </div>
          ))
        ) : (
          <div className="px-7 py-1.5 text-[#2A3348] thepassBfour rounded-full border border-[#E7E7E7]">{subCategories}</div>
        )}
      </div>
    </div>
  );
};
