import { Icon } from '@/components';
import transformation from 'images/landing/transformation.png';
import transformationBg from 'images/landing/transofrmation-bg.png';
import { Button } from 'UI/src';
export const TransformationSection = () => {
  return (
    <div
      className="relative w-full flex items-center justify-center bg-cover bg-center bg-no-repeat py-22 mt-16 lg:mt-[50px]"
      style={{
        backgroundImage: `url(${transformationBg})`,
      }}
    >
      {/* Content Container */}
      <div className="max-w-8xl mx-auto px-4 sm:px-6 lg:px-2">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-50 items-center">
          {/* Left Content */}
          <div className="lg:text-left mx-auto text-center text-nowrap">
            {/* Main Heading */}
            <h2 className="text-[32px] sm:text-[55px] font-semibold mb-6 leading-tight">
              <span>Ready to transform</span>
              <br />
              <span>your hiring process?</span>
            </h2>

            {/* Subtitle */}
            <p className="text-sm sm:thepassSubHone text-wrap text-[#4E5E82] mb-8 max-w-lg leading-relaxed">
              Join hundreds of companies already transforming how they hire with faster, smarter, and bias-free recruitment powered by AI.
            </p>

            {/* Buttons */}
            <div className="flex flex-col justify-center lg:justify-start sm:flex-row gap-4">
              {/* TODO: Markos */}
              <Button
                label={
                  <div className="flex gap-2">
                    <p>Get Started</p>
                    <Icon icon="weui:arrow-filled" width="10" />
                  </div>
                }
                to="/auth/register"
                className="px-8 py-3"
                colorType="magic"
              />

              {/* <Button 
                label="Book demo" 
                tertiary 
                size="lg" 
                className="px-8 py-3"
              /> */}
            </div>
          </div>

          {/* Right Image */}
          <div className="flex justify-center lg:justify-end">
            <img
              src={transformation}
              alt="Transformation illustration showing hiring process with AI-powered features"
              className="w-full max-w-md lg:max-w-lg xl:max-w-xl h-auto object-contain"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
