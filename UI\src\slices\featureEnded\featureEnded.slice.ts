import { createSlice } from '@reduxjs/toolkit';
import type { RootState } from '../../store';

interface featureEndedState {
  isFeatureEnded: boolean;
}

const initialState: featureEndedState = {
  isFeatureEnded: false,
};

const featureEndedSlice = createSlice({
  name: 'featureEnded',
  initialState,
  reducers: {
    setFeatureEnded: (state, { payload }) => {
      state.isFeatureEnded = payload;
    },
  },
});

export const featureEndedState = (state: RootState) => state.viewOnly;
export const { setFeatureEnded } = featureEndedSlice.actions;
export default featureEndedSlice.reducer;
