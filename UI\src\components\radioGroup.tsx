import { Radio } from './radio';
import type { RadioState } from './radio';

interface RadioOption {
  label: string;
  value: string;
  disabled?: boolean;
}

interface RadioGroupProps {
  name: string;
  options: RadioOption[];
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  className?: string;
}

export const RadioGroup: React.FC<RadioGroupProps> = ({ name, options, value, onChange, disabled = false, className = '' }) => {
  return (
    <div className={`flex flex-col gap-4 ${className}`}>
      {options.map((option) => {
        let state: RadioState = 'default';
        if (disabled || option.disabled) state = 'disabled';

        return (
          <div
            key={option.value}
            className={`flex items-center rounded-lg px-4 py-2 transition-all duration-150 border border-[#DEE2E4] bg-white hover:bg-[#F1E9FE] focus:border-2 focus:border-[#A47BFA] ${
              disabled || option.disabled ? 'opacity-50' : ''
            }`}
            tabIndex={-1}
          >
            <Radio
              name={name}
              label={option.label}
              checked={value === option.value}
              onChange={() => onChange(option.value)}
              state={state}
              disabled={disabled || option.disabled}
            />
          </div>
        );
      })}
    </div>
  );
};
