// React
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// Rsuite
import { DatePicker, DateRangePicker } from 'rsuite';

// Date format
import { format } from 'date-fns';

// Core
import { Icon, TestSeniorityLevel, Table, AvarageScore, NameFieldColumn, EmailFieldColumn, Card } from 'src';

// Components
import { ApplicantsSingleDialog } from '../../../../applicants/components/single-dialog';
import { ResultStatusApplicant } from '../../../../../../../components/shared-functions/result-status';

import { AlertNote, AssessmentCompCard, RootState, setFieldValue, Tags, useAppDispatch, useAppSelector, useFetchList, UserData } from 'UI/src';
import { Applicant } from '@/modules/app/modules/applicants/components/applicant-data';

interface AssignAndScheduleAssessmentProps {
  formData: any;
  disableButtons: {
    disableNextButton: boolean;
    setDisableNextButton: (value: boolean) => void;
  };
  applicantsData: {
    applicants: any[];
    setSelectedApplicantsIds: (ids: any[]) => void;
  };
  interviewScheduleData: any;
}

export const AssignAndScheduleAssessment = ({
  formData,
  disableButtons,
  applicantsData,
  interviewScheduleData,
}: AssignAndScheduleAssessmentProps) => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Hooks
  const { type } = useParams();

  // Permissions
  const isPermitted = Array.isArray(userData?.role) && userData?.role.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');

  // State
  const [showNote, setShowNote] = useState(true);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [isShowApplicnatCard, setShowApplicnatCard] = useState(false);

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const { applicants, setSelectedApplicantsIds } = applicantsData;
  const { startDate, setStartDate, dueDate, setDueDate } = interviewScheduleData;

  // Datepicker
  const { beforeToday } = DateRangePicker;

  // Hooks
  const initialFilters = {
    seniorityLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },
  };
  const { ready, loading, list, count, filters, setFilters, search, pagination, refresh } = useFetchList('applicants/list', {
    search: '',
    pagination: {
      page: 1,
      size: 10,
    },
    filters: initialFilters,
  });

  const handleValideDate = () => {
    if (form.startDate && form.dueDate) {
      const differenceInMilliseconds = new Date(form.dueDate).getTime() - new Date(form.startDate).getTime();
      const days = Math.floor(differenceInMilliseconds / (1000 * 60 * 60 * 24));

      if (days === 0) {
        const totalMinutes = Math.floor(differenceInMilliseconds / (1000 * 60));
        const hours = Math.floor(totalMinutes / 60);
        const minutes = totalMinutes % 60;
        return `${hours} hours, ${minutes} minutes`;
      } else if (days === 1) return '1 day';
      else return `${days} days`;
    } else return '—';
  };
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(setFieldValue({ path: 'applicantId', value: Object.fromEntries(applicants.map((key) => [key, true])) }));
  }, [applicants]);

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  // Start & End date time from this page renderd
  useEffect(() => {
    dispatch(setFieldValue({ path: 'startDate', value: startDate }));
    dispatch(setFieldValue({ path: 'dueDate', value: dueDate }));
  }, [startDate, dueDate]);

  useEffect(() => {
    // disableButtons.setDisableNextButton(Object.keys(form.applicantId).filter((key) => form.applicantId[key]).length <= 0);
    disableButtons.setDisableNextButton(new Date(form.dueDate).getTime() - new Date(form.startDate).getTime() <= 0);
  }, [form.startDate, form.dueDate]);

  return (
    <>
      <div className="relative space-y-4">
        <AssessmentCompCard
          name={form.title}
          difficulty={form.difficulty}
          duration={form.duration || form.estimationTime}
          seniority={form.seniorityLevel}
          questionsNumber={form.questionIds?.length || form.questions?.length || form.numberOfQuestions || 0}
          categoryName={form?.categoryName}
          subCategories={form?.subCategoryName}
          createdByName={form.authorName}
          createdByDate={new Date(form.dueDate).toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' })}
          updatedDate={form.updatedAt}
        />

        <div className="p-4 border border-[#DEE2E4] space-y-4 rounded-lg">
          <p className="thepassHthree capitalize">Schedule {type}</p>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <p className="thepassBtwo text-[#1B1F3B]">
                Start Date <span className="text-red-600 ml-[1px]">*</span>
              </p>
              <DatePicker
                format="dd/MM/yyyy hh:mm aa"
                value={form.startDate}
                onChange={(value: any) => {
                  dispatch(setFieldValue({ path: 'startDate', value }));
                  setStartDate(value);
                }}
                className="w-full grow"
                shouldDisableDate={beforeToday()}
                placement="topStart"
                showMeridiem
              />
            </div>

            <div className="space-y-2">
              <p className="thepassBtwo text-[#1B1F3B]">
                End Date <span className="text-red-600 ml-[1px]">*</span>
              </p>
              <DatePicker
                format="dd/MM/yyyy hh:mm aa"
                value={form.dueDate}
                onChange={(value: any) => {
                  dispatch(setFieldValue({ path: 'dueDate', value }));
                  setDueDate(value);
                }}
                className="w-full"
                shouldDisableDate={beforeToday()}
                placement="topStart"
                showMeridiem
              />
            </div>
          </div>

          {showNote && <AlertNote message="Test link is available for 3 days starting from your start date" />}

          <div className="p-4 space-y-3 border border-[#743AF5] rounded-lg">
            <div className="flex items-center gap-2 text-[#6544AB] text-base font-semibold">
              <Icon icon="akar-icons:link-chain" width="22" />
              <p className="text-[#1B1F3B]">Public Link Generation</p>
            </div>
            <p className="thepassHfour text-[#4E5E82]">
              A public link will be generated automatically after you finish the scheduling. You can choose to assign applicants now or skip this step
              — the link will be ready either way.
            </p>
          </div>
        </div>

        <Card className={`!p-4 space-y-3 ${!isShowApplicnatCard && 'cursor-pointer'}`}>
          <div
            className="flex justify-between gap-2"
            onClick={() => setShowApplicnatCard((prev) => !prev)}
            style={{ cursor: !isShowApplicnatCard ? 'pointer' : 'default' }}
          >
            <div className="space-y-2">
              <p>
                <span className="thepassHthree text-[#1B1F3B]">Applicants list</span> <span className="thepassBthree text-[#4E5E82]">Optional</span>
              </p>
              <p className="font-normal text-[#4F535B]">
                You can send the assessment directly via Email to selected candidates from your applicant list.
              </p>
            </div>

            <Icon
              icon={isShowApplicnatCard ? 'ic:twotone-keyboard-arrow-up' : 'ic:twotone-keyboard-arrow-down'}
              className="text-[#6B7280] cursor-pointer"
              width="30"
            />
          </div>

          {isShowApplicnatCard && (
            <Table
              addButtonPermission={false}
              ready={ready}
              loading={loading}
              title="Applicants"
              searchPlaceholder="Search for name or email..."
              count={count}
              search={search}
              filters={filters}
              setFilters={setFilters}
              pagination={pagination}
              rows={list}
              backupRows={backupList}
              slots={{
                applicantName: (_: unknown, row: { _id: string; name: string }) => (
                  <NameFieldColumn onClick={() => {}} id={row?._id} name={row?.name} showMoreMap={showMoreMap} />
                ),
                applicantEmail: (_: unknown, row: { _id: string; email: string }) => (
                  <EmailFieldColumn id={row?._id} email={row?.email} showMoreMap={showMoreMap} />
                ),
                seniorityLevel: (_: unknown, row: { seniorityLevel: number }) => {
                  const getSeniorityLevelText = (level: number): string => {
                    switch (level) {
                      case 1:
                        return 'intern';
                      case 2:
                        return 'fresh';
                      case 3:
                        return 'junior';
                      case 4:
                        return 'mid-level';
                      case 5:
                        return 'senior';
                      default:
                        return '-';
                    }
                  };

                  return (
                    <div className="w-fit">
                      <Tags type={getSeniorityLevelText(row?.seniorityLevel)} color="bg-transparent" />
                    </div>
                  );
                },

                status: (_: unknown, row: { status: number }) => {
                  const getStatusText = (status: any): string => {
                    if (status === 1) {
                      return 'available';
                    } else if (status === 2) {
                      return 'busy';
                    } else {
                      return 'busy'; // default case
                    }
                  };

                  const getStatusColor = (status: any): string => {
                    if (status === 1) {
                      return 'text-green-800';
                    } else {
                      return 'text-red-800';
                    }
                  };

                  const getStatusIcon = (status: any) => {
                    if (status === 1) {
                      return <div className="w-2 h-2 bg-green-500 rounded-full"></div>;
                    } else {
                      return <div className="w-2 h-2 bg-red-500 rounded-full"></div>;
                    }
                  };

                  return (
                    <div className="w-fit">
                      <Tags type={getStatusText(row?.status)} color={getStatusColor(row?.status)} icon={getStatusIcon(row?.status)} />
                    </div>
                  );
                },

                averageScore: (_: unknown, row: { averageScore: number }) => {
                  const getScoreColor = (score: number) => {
                    if (score >= 0 && score < 50) {
                      return 'bg-[#FFECE9] text-[#A80000]';
                    } else if (score >= 50 && score < 75) {
                      return 'bg-[#FFEDD8] text-[#E9760F]';
                    } else if (score >= 75 && score < 100) {
                      return 'bg-[#FFFCDF] text-[#BA8500]';
                    } else if (score >= 100) {
                      return 'bg-[#EEFFF1] text-[#056816]';
                    }
                    return 'bg-gray-100 text-gray-800';
                  };

                  const getScoreText = (score: number) => {
                    if (score === null || score === undefined) return '—';
                    return `${score}%`;
                  };

                  return (
                    <div className="w-fit">
                      <Tags type="score" color={getScoreColor(row?.averageScore)}>
                        {getScoreText(row?.averageScore)}
                      </Tags>
                    </div>
                  );
                },
              }}
              columns={[
                {
                  key: 'applicantName',
                  label: 'Name',
                  primary: true,
                  width: '22%',
                },
                {
                  key: 'applicantEmail',
                  label: 'Email',
                  primary: true,
                  width: '23%',
                },
                {
                  key: 'seniorityLevel',
                  label: 'Seniority',
                  primary: true,
                  width: '18%',
                  inline: true,
                },
                {
                  key: 'status',
                  label: 'Status',
                  width: '19%',
                  inline: true,
                },
                {
                  key: 'averageScore',
                  label: 'Average Score',
                  width: '17%',
                },
              ]}
              multiSelectedRow={{
                selectedIds: applicants,
                setSelectedIds: setSelectedApplicantsIds,
              }}
              placeholder={{
                title: 'No assessment created yet',
                subTitle: 'Start by creating assessment to evaluate applicants skills.',
                image: '/UI/src/assets/placeholder/TestImagePlaceholder.svg',
              }}
              noDataFoundIconWidth="60"
              noDataFoundIconHeight="60"
              // showMoreMap={showMoreMap}
              setShowMoreMap={setShowMoreMap}
              hideJumbotron
              isScrollableTabsExists
            />
          )}
        </Card>
      </div>

      {/* Create new applicant */}
      {isCreateDialogVisible && <ApplicantsSingleDialog onClose={() => setCreateDialogVisibility(false)} onCreate={refresh} />}
    </>
  );
};
