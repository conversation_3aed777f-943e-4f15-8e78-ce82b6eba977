import React, { useState, useEffect, useRef, type FC } from 'react';
import { createPortal } from 'react-dom';
import EmojiPicker from 'emoji-picker-react';
import { Icon } from 'src';
interface CustomEmojiPickerProps {
  value?: string;
  onChange: (emoji: string) => void;
  label?: string;
  placeholder?: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export const CustomEmojiPicker: FC<CustomEmojiPickerProps> = ({
  value = '',
  onChange,
  label = 'Emoji',
  placeholder = <Icon icon={'fa6-regular:face-smile'} className="text-gray-500" />,
  className = '',
  disabled = false,
}) => {
  const [showPicker, setShowPicker] = useState(false);
  const [isPositioned, setIsPositioned] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const pickerRef = useRef<HTMLDivElement>(null); 
  const [pickerPosition, setPickerPosition] = useState({ top: 0, left: 0 });

  const handleEmojiClick = (emojiObject: any) => {
    onChange(emojiObject.emoji);
    setShowPicker(false);
    setIsPositioned(false);
  };

  const updatePickerPosition = () => {
    if (containerRef.current) {
      const button = containerRef.current.querySelector('button');
      if (button) {
        const buttonRect = button.getBoundingClientRect();
        const top = buttonRect.bottom + window.scrollY + 8;
        const left = buttonRect.left + window.scrollX;
        setPickerPosition({ top, left });
        setIsPositioned(true);
      }
    }
  };

  const handleTogglePicker = (e: React.MouseEvent) => {
    e.stopPropagation(); 
    if (!disabled) {
      const newShowPicker = !showPicker;
      setShowPicker(newShowPicker);
      if (newShowPicker) {
        // Reset positioning state
        setIsPositioned(false);
        // Calculate position with a small delay to ensure DOM is ready
        setTimeout(() => {
          updatePickerPosition();
        }, 10);
      } else {
        setIsPositioned(false);
      }
    }
  };
  // Update position on scroll and resize
  useEffect(() => {
    if (showPicker) {
      const handleScroll = () => {
        requestAnimationFrame(updatePickerPosition);
      };
      const handleResize = () => {
        requestAnimationFrame(updatePickerPosition);
      };
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);
      return () => {
        window.removeEventListener('scroll', handleScroll, true);
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [showPicker]);

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event:any) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        // Check if click is on emoji picker
        const emojiPickerElement = document.querySelector('.EmojiPickerReact');
        if (emojiPickerElement && !emojiPickerElement.contains(event.target)) {
          setShowPicker(false);
          setIsPositioned(false);
        }
      }
    };
    if (showPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showPicker]);
  return (
    <>
      <div className={`relative ${className}`} ref={containerRef}>
        <label className="block text-sm font-medium text-gray-700 mb-2">{label}</label>
        <div className="flex items-center gap-3">
          <button
            type="button"
            disabled={disabled}
            onClick={handleTogglePicker}
            className={`flex items-center justify-center w-12 h-11 border-2 border-gray-300 rounded-lg transition-colors ${
              disabled ? 'bg-gray-100 cursor-not-allowed' : 'hover:border-gray-400 cursor-pointer'
            }`}
          >
            {value ? <span className="text-2xl">{value}</span> : <span className="text-gray-400 text-xl">{placeholder}</span>}
          </button>
        </div>
      </div>
      {/* Render emoji picker outside modal using portal */}
      {showPicker &&
        !disabled &&
        typeof window !== 'undefined' &&
        createPortal(
          <div
            ref={pickerRef}
            className="fixed z-[9999]"
            style={{
              top: `${pickerPosition.top}px`,
              left: `${pickerPosition.left}px`,
              transform: 'translateZ(0)', // Force hardware acceleration
              opacity: isPositioned ? 1 : 0, // Hide until positioned
              transition: 'opacity 0.1s ease-in-out',
            }}
          >
            <EmojiPicker onEmojiClick={handleEmojiClick} width={300} height={400} />
          </div>,
          document.body
        )}
    </>
  );
};
