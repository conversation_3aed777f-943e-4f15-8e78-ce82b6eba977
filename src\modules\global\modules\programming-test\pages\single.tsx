// React
import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

// UI
import { TextInput, EnumText, Icon, PhoneNumberInput } from 'src';
import { Form } from 'UI/src/components/form';

// React icons
import { FaUserGraduate, FaUser, FaStar, FaMedal } from 'react-icons/fa';
import { Regex, Api, useValidate, RootState, setFieldValue, useAppSelector, UsersListItem, Button } from 'UI/src';

// Components
import { TestDetailsGlobal } from '../components/details';
import { GlobalBreadcrumb } from '../../../components/breadcrumb';
import { useAppDispatch } from 'UI/src';
import { setErrorNotify, setNotifyMessage, QuizDifficulty } from 'UI';
import celebration from 'images/celebration.gif';
import doneMark from 'images/Vector.svg';
import { useFormik } from 'formik';
import { Clock5, HelpCircle, ChartColumnIncreasing, User, <PERSON> } from 'lucide-react';
import { DatePicker, DateRangePicker } from 'rsuite';
import { format, differenceInDays } from 'date-fns';

type TestDetailsType = {
  category: {
    categoryId: string;
    categoryName: string;
  }[];
  difficulty: number;
  duration: number;
  numOfQuestions: number;
  subcategoryDetails: {
    count: number;
    subCategoryName: string;
    topics: string[];
  }[];
  title: string;
  _id: string;
};

type TestUrlType = {
  quizUrl: string;
  submissionId: string;
  applicantId: string;
};

export const TestDetails = () => {
  // State
  const [showNote, setShowNote] = useState(true);
  const [isCreateApplicant, setIsCreateApplicant] = useState(false);
  const [details, setDetails] = useState<TestDetailsType | null>(null);
  const [testUrl, setTestUrl] = useState<TestUrlType | null>(null);
  const [expandedAll, setExpandedAll] = useState(false); // State to control expand/collapse for all sections
  const navigate = useNavigate();

  const [startDate, setStartDate] = useState(() => {
    const now = new Date();
    now.setSeconds(0); // Set seconds to 0
    // Round minutes up to nearest 5
    const minutes = now.getMinutes();
    const roundedMinutes = Math.ceil(minutes / 5) * 5;
    if (roundedMinutes === 60) {
      // If rounding went to 60, increment hour
      now.setHours(now.getHours() + 1);
      now.setMinutes(0);
    } else {
      now.setMinutes(roundedMinutes);
    }
    return now;
  });

  const [dueDate, setDueDate] = useState(() => {
    const date = new Date(startDate);
    date.setDate(date.getDate() + 2); // Add 2 days
    date.setSeconds(59); // Set seconds to 59
    // Round minutes UP to the next multiple of 5
    const minutes = date.getMinutes();
    const roundedMinutes = Math.ceil(minutes / 5) * 5;
    if (roundedMinutes === 60) {
      // If it's 60, reset to 0 and add 1 hour
      date.setHours(date.getHours() + 1);
      date.setMinutes(0);
    } else {
      date.setMinutes(roundedMinutes);
    }
    return date;
  });

  // Hooks
  const { isRequired, minLength, maxLength, validateRegex, countryCodeNumberValid } = useValidate();
  const userData: UsersListItem = useAppSelector((state: RootState) => state.auth.user);
  const { id } = useParams();
  const dispatch = useAppDispatch();

  const formik = useFormik({
    initialValues: {
      quizId: id,
      name: '',
      email: '',
      type: 'public',
      // willSendEmail: false,
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  const { beforeToday } = DateRangePicker;

  const form = useAppSelector((state: RootState) => state.form.data);

  console.log(useAppSelector((state: RootState) => state.form.data));

  // Methods
  const generateDescription = () => {
    const totalCategories: number = details?.subcategoryDetails.length || 0;
    const totalTopics: number = details?.subcategoryDetails.reduce((sum, sub) => sum + sub.topics.length, 0) || 0;

    let difficultyText = '';
    switch (details?.difficulty) {
      case 1:
        difficultyText = 'suitable for interns';
        break;
      case 2:
        difficultyText = 'ideal for fresh graduates';
        break;
      case 3:
        difficultyText = 'great for junior-level candidates';
        break;
      case 4:
        difficultyText = 'targeted for mid-level professionals';
        break;
      case 5:
        difficultyText = 'challenging for senior professionals';
        break;
      default:
        difficultyText = '';
    }

    return (
      <>
        This test covers{' '}
        <span className="font-semibold">
          {' '}
          {totalCategories} {totalCategories > 1 ? 'categories' : 'category'}{' '}
        </span>{' '}
        and includes{' '}
        <span className="font-semibold">
          {' '}
          {totalTopics} {totalTopics > 1 ? 'topics' : 'topic'}{' '}
        </span>
        . It is <span className="font-semibold">{difficultyText} </span>, ensuring the right balance of knowledge this level.
      </>
    );
  };

  const handleAddApplicant = async () => {
    try {
      const response = await Api.post(`submissions/single`, {
        ...form,
        quizId: id,
        type: userData?.access_token ? 'private' : 'public',
      });
      setTestUrl(response.data);
      setIsCreateApplicant(true);
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    }
  };

  const quizDetails = async () => {
    try {
      const response = await Api.get<TestDetailsType>(`templates/single/custom/${id}`);
      setDetails(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    }
  };

  const handleSubscribe = () => {
    if (userData?.access_token && testUrl) {
      navigator.clipboard.writeText(testUrl?.quizUrl);
      dispatch(setNotifyMessage('Link copied'));
    } else {
      navigate('/pricing');
    }
  };

  let difficultyIcon;
  let difficultyColor;
  let iconSize = 'text-[15px]';

  if (details) {
    switch (details.difficulty) {
      case 1:
        difficultyIcon = <FaUserGraduate className={`${iconSize} text-teal-700`} />; // Intern
        difficultyColor = ' text-teal-700 ';
        break;

      // Star Icon fresh level
      case 2:
        difficultyIcon = <FaUser className={`${iconSize} text-sky-800`} />; // Fresh
        difficultyColor = 'text-sky-800 ';
        break;
      // Medal Star junior
      case 3:
        difficultyIcon = <FaStar className={`${iconSize} text-amber-700`} />; // Junior
        difficultyColor = ' text-amber-700 ';
        break;
      // betetr medal star midlevel
      case 4:
        difficultyIcon = <FaMedal className={`${iconSize} text-orange-700`} />; // Mid-level
        difficultyColor = 'text-orange-700';
        break;

      // Tropy icon for senior with star
      case 5:
        difficultyIcon = <Icon icon="solar:crown-star-bold" width="18" className={`${iconSize} text-red-800`} />; // Senior
        difficultyColor = 'text-red-800';
        break;
      default:
        difficultyIcon = null;
    }
  }

  useEffect(() => {
    if (id) {
      quizDetails();
    }
  }, []);

  useEffect(() => {
    dispatch(setFieldValue({ path: 'startDate', value: startDate }));
    dispatch(setFieldValue({ path: 'dueDate', value: dueDate }));
  }, [startDate, dueDate]);

  return (
    <div className="min-w-[80vw] max-w-[967px] mx-3 sm:mx-11 lg:mx-auto space-y-6 flex flex-col items-center justify-center mb-4">
      {/* <GlobalBreadcrumb /> */}
      <div className="flex flex-col items-center space-y-3">
        <p className="font-medium text-[#1B1F3B] text-5xl">Assign To Applicant</p>
        <p className="text-[#4E5E82] thepassSubH1 text-lg">Easily assign it to an applicant via name and email</p>
      </div>

      {details && (
        <div className="flex flex-col justify-center w-full max-w-3xl items-center space-y-5">
          {/* Test Details */}
          <div className="w-3/4 flex-grow border border-[#DEE2E4] rounded-2xl py-5 px-5 space-y-2">
            {/* Allow the left side to grow */}
            <div className="flex flex-col justify-between items-start">
              <div className="space-y-4">
                <h1 className="text-xl font-semibold capitalize thepassSubH1 mb-3">{details.title}</h1>
                <div className="flex flex-wrap items-center text-[#798296] dark:text-[#838398] text-base gap-3 mb-2">
                  {/* <div className={`flex items-center text-sm gap-1`}>{difficultyIcon}</div> */}

                  {/* <div className="flex items-center gap-1">
                    <User className="size-[18px] text-[#743AF5]" />
                    <p className="text-sm text-[#4E5E82]">Seniority:</p>
                    <p className="text-[#1B1F3B] text-sm font-medium">
                      <EnumText name="QuizDifficulty" value={details.seniorityLevel} />
                    </p>
                  </div> */}

                  {/* TODO: Make this global component to be used in: assessmnet-comp-card , programming-test-list, programmin-test-single */}
                  <div className="flex items-center gap-1">
                    <ChartColumnIncreasing className="size-[18px] text-[#743AF5]" />
                    <p className="text-sm text-[#4E5E82]">Difficulty:</p>
                    <p className="text-[#1B1F3B] thepassBtwo">
                      <EnumText name="QuestionDifficulty" value={details.difficulty} />
                    </p>
                  </div>

                  <div className="flex items-center gap-1">
                    <HelpCircle className="size-[18px] text-[#743AF5]" />
                    <p className="text-sm text-[#4E5E82]">Questions:</p>
                    <p className="text-[#1B1F3B] thepassBtwo">{details.numOfQuestions}</p>
                  </div>

                  <div className="flex items-center gap-1">
                    <Clock5 className="size-4 text-[#743AF5]" />
                    <p className="text-sm text-[#4E5E82]">Duration:</p>

                    <p className="text-[#1B1F3B] thepassBtwo">
                      <span> {details.duration} </span> min
                    </p>
                  </div>
                </div>

                <div className="flex gap-3 mt-5 capitalize ">
                  {details.category.map((item) => (
                    <div className="border border-transparent bg-[#F3F4F6] font-medium rounded-lg px-3 py-1 text-xs w-fit h-fit text-[#2A3348]">
                      {item.categoryName}
                    </div>
                  ))}
                  {details.subcategoryDetails.map((item) => (
                    <div className="border border-[#E7E7E7] font-medium rounded-3xl px-3 py-1 text-xs w-fit h-fit text-[#2A3348]">
                      {item.subCategoryName}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="!w-3/4 p-4 border border-[#DEE2E4] space-y-4 rounded-2xl">
            <p className="text-xl font-semibold capitalize thepassSubH1">Schedule Test</p>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="thepassBtwo font-semibold">
                  Start Date <span className="text-red-600 ml-[1px]">*</span>
                </p>
                <DatePicker
                  format="30 April, 2025 At 12:00 PM"
                  value={form.startDate}
                  onChange={(value: any) => {
                    dispatch(setFieldValue({ path: 'startDate', value }));
                    setStartDate(value);
                  }}
                  className="w-full grow"
                  shouldDisableDate={beforeToday()}
                  placement="topStart"
                  showMeridiem
                />
              </div>

              <div className="space-y-2">
                <p className="thepassBtwo font-semibold">
                  End Date <span className="text-red-600 ml-[1px]">*</span>
                </p>
                <DatePicker
                  format="30 April, 2025 At 12:00 PM"
                  value={form.dueDate}
                  onChange={(value: any) => {
                    dispatch(setFieldValue({ path: 'dueDate', value }));
                    setDueDate(value);
                  }}
                  className="w-full"
                  shouldDisableDate={beforeToday()}
                  placement="topStart"
                  showMeridiem
                />
              </div>
            </div>

            {showNote && (
              <div className="flex items-center gap-3 p-3 bg-[#EAF3FF] border border-[#EAEEF4] rounded-xl">
                <div className="flex items-center gap-2">
                  <Icon icon="material-symbols:info-i-rounded" width="15" className="p-0.5 bg-[#7CCCEF] text-white rounded-lg" />
                  <span className="text-sm font-semibold dark:text-white">Note</span>
                </div>

                <p className="text-[#2F3F53] dark:text-white text-sm">
                  This test is available for {differenceInDays(new Date(form.dueDate), new Date(form.startDate))} day
                  {differenceInDays(new Date(form.dueDate), new Date(form.startDate)) > 0 && 's'} starting from you start date
                </p>
              </div>
            )}
          </div>

          <div className="!w-3/4 text-sm border border-[#743AF5] rounded-2xl p-4 space-y-4">
            <div className="items-center font-medium text-sm flex gap-1">
              <Link className="text-[#8D5BF8]" />
              Public Link Generation
            </div>
            <p className="text-[#4E5E82] thepassHfour">
              A public link will be generated automatically after you finish the scheduling. You can choose to assign applicants via email now or skip
              this step — the link will be ready either way.
            </p>
          </div>

          {/* Add Applicant Form */}
          <div className="w-3/4 h-fit flex-none border dark:bg-gray-800 border-[#DEE2E4] dark:border-none p-6  rounded-2xl">
            {!isCreateApplicant ? (
              <>
                <h1 className="text-lg font-semibold capitalize thepassSubH1 mb-6">Applicant's Details</h1>
                <Form className="flex flex-col gap-5" onSubmit={handleAddApplicant}>
                  <TextInput
                    name="name"
                    // TODO: Markos
                    label="Name"
                    placeholder="Enter applicant's name"
                    value={form.name}
                    onChange={(value: any) => dispatch(setFieldValue({ path: 'name', value }))}
                    validators={[isRequired(), minLength(3), maxLength(100), validateRegex(Regex.name)]}
                  />
                  <TextInput
                    name="email"
                    // TODO: Markos
                    label="Email"
                    placeholder="Enter Applicant's Email"
                    value={form.email}
                    onChange={(value: any) => dispatch(setFieldValue({ path: 'email', value }))}
                    validators={[isRequired(), validateRegex(Regex.email)]}
                    customPlaceholder={
                      <span className="text-gray-400 dark:text-gray-500 flex gap-2">
                        <Icon icon="mdi:email-outline" width="20" /> Enter Applicant Email
                      </span>
                    }
                  />

                  <PhoneNumberInput
                    name="mobileNumber"
                    label="Mobile"
                    value={form.mobileNumber}
                    onChange={(value: any) => dispatch(setFieldValue({ path: 'mobileNumber', value }))}
                    requiredLabel
                    validators={[countryCodeNumberValid()]}
                  />

                  <div className="pt-2 space-y-4">
                    <div className="flex gap-2">
                      <Button type="button" label="Back" colorType="secondary" className="w-full" onClick={() => navigate(-1)} />
                      <Button type="submit" label="Assign" className="w-full" />
                    </div>
                  </div>
                </Form>
              </>
            ) : (
              <>
                <div className="flex justify-center items-center mb-6">
                  {userData?.access_token ? (
                    <img src={celebration} alt="My GIF" className="w-[145px] h-[145px]" />
                  ) : (
                    <img src={doneMark} alt="done mark" />
                  )}
                </div>
                <div className="flex justify-center items-center mb-6">
                  <p className="text-base md:text-xl font-medium text-[#374151] dark:text-[#D6DBE3] text-center">
                    {userData?.access_token ? 'You can now copy the test link directly' : 'Test Created Successfully!'}
                  </p>
                </div>
                <Form className="flex flex-col gap-4" onSubmit={handleSubscribe}>
                  <div
                    className={`${
                      !userData?.access_token && 'select-none cursor-not-allowed'
                    } overflow-hidden p-4 rounded-lg bg-[#F4F4F4] dark:bg-gray-700 dark:text-white text-sm font-medium text-black mb-2 break-all`}
                  >
                    {userData?.access_token ? (
                      testUrl?.quizUrl
                    ) : (
                      <>
                        {testUrl?.quizUrl.slice(0, Math.ceil(testUrl.quizUrl.length - 30))}
                        <span className="blur-sm">{testUrl?.quizUrl.slice(Math.ceil(testUrl.quizUrl.length - 10))}</span>
                      </>
                    )}
                  </div>

                  {!userData?.access_token && (
                    <div className="flex justify-start">
                      <p className="text-base font-medium text-secondaryGray dark:text-[#D6DBE3]">Please subscribe to get the complete link!</p>
                    </div>
                  )}
                  <div className="flex gap-2">
                    <Button type="submit" label={userData?.access_token ? 'Copy Link' : 'Subscribe'} className="cursor-pointer w-full" />
                  </div>
                  {!userData?.access_token && (
                    <p className="flex flex-col text-center text-base font-medium text-secondaryGray dark:text-grayTextOnDarkMood md:flex-row md:gap-2 justify-center">
                      Already Subscribed?
                      <span className="font-normal text-primaryPurple cursor-pointer" onClick={() => navigate('../../auth/login')}>
                        Login here
                      </span>
                    </p>
                  )}
                </Form>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
