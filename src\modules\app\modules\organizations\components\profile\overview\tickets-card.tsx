// Core
import { Icon } from 'src';

export const TicketsCard = () => {
  const differencePercentage = -2.5;

  return (
    <div className="h-full space-y-4 p-4 rounded-xl">
      {false ? (
        <>
          {/* Middle Body */}
          <div className="flex flex-wrap gap-2 justify-between items-center">
            <p className="text-3xl font-bold dark:text-white">100</p>
            <div className="flex gap-3">
              <p className={`${differencePercentage > 0 ? 'text-[#52C93F]' : 'text-[#FF0000]'}`}>
                {differencePercentage > 0 ? <span>&uarr;</span> : <span>&darr;</span>} {Math.abs(differencePercentage)}%
              </p>
              <p className="text-[#606060] dark:text-[#d1d1d1]">past week</p>
            </div>
          </div>

          {/* Custom Child */}
          <div className="space-y-4">
            <div className="flex justify-between gap-5">
              <div className="flex items-center gap-1">
                <div className="w-[10px] h-[10px] bg-[#4880FF] rounded-full" />
                <p className="text-sm dark:text-white">Open 30</p>
              </div>
              <div className="flex items-center gap-1">
                <div className="w-[10px] h-[10px] bg-[#52C93F] rounded-full" />
                <p className="text-sm dark:text-white">Resolved 70</p>
              </div>
            </div>
            <div className="flex justify-between items-center gap-2">
              <p className="text-[#656575] dark:text-[#d1d1d1] text-sm">Average Response Time : 2 Days</p>
              <p className="flex items-center gap-2 text-[#52C93F]">
                <Icon icon="mingcute:arrow-up-line" /> 60%
              </p>
            </div>
          </div>
        </>
      ) : (
        <div className={`flex flex-col mt-5 items-center text-center`}>
          <Icon icon="iconoir:warning-circle" className="dark:text-gray-500 text-gray-400" width="50" />
          <p className={`text-gray-400 mt-2`}>No tickets Found</p>
        </div>
      )}
    </div>
  );
};
