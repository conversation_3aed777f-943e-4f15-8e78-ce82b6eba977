// Core
import { ChartsLinear } from 'src';

export const SystemHealthStability = () => {
  const chartsData = [
    { x: 1, uptime: 20, downtime: 60, response: 90 },
    { x: 2, uptime: 40, downtime: 55, response: 85 },
    { x: 3, uptime: 35, downtime: 30, response: 70 },
    { x: 4, uptime: 60, downtime: 50, response: 75 },
    { x: 5, uptime: 30, downtime: 80, response: 85 },
    { x: 6, uptime: 40, downtime: 75, response: 90 },
    { x: 7, uptime: 70, downtime: 65, response: 95 },
    { x: 8, uptime: 50, downtime: 45, response: 85 },
    { x: 9, uptime: 60, downtime: 50, response: 80 },
    { x: 10, uptime: 100, downtime: 55, response: 90 },
  ];

  const chartsDataLabels = [
    { dataKey: 'uptime', stroke: '#3b82f6', fill: 'url(#colorUptime)', name: 'Uptime' },
    { dataKey: 'downtime', stroke: '#10b981', fill: 'url(#colorDowntime)', name: 'Downtime' },
    { dataKey: 'response', stroke: '#a855f7', fill: 'url(#colorResponse)', name: 'Response Time' },
  ];

  return (
    <div className="p-2">
      <div className="-ml-8">
        <ChartsLinear data={chartsData} labels={chartsDataLabels} />
      </div>
    </div>
  );
};
