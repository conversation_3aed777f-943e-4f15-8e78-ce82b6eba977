import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Icon, Logo, TextInput, Textarea } from 'src';
import { Dialog, Button } from 'UI';
import { Api, useValidate, Form, initializeForm, RootState, setFieldValue, useAppDispatch, useAppSelector } from 'UI/src';
import { setErrorNotify } from 'UI';
import checkImage from 'images/check.png';
import { useFormik } from 'formik';
export const ContactUsGlobal = () => {
  // State
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<null | {}>();
  const dispatch = useAppDispatch();
  const formik = useFormik({
    initialValues: {
      fullName: '',
      email: '',
      message: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });
  const form = useAppSelector((state: RootState) => state.form.data);

  // Hooks
  const { isRequired, minLength, validateRegex } = useValidate();
  const navigate = useNavigate();

  const handleSubmit = async () => {
    try {
      setLoading(true);
      const type = 'contact-us';
      const response = await Api.post('/mails/contact', { type, ...form });
      setData(response);
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    } finally {
      setLoading(false);
    }
  };

  const onClose = () => {
    setData(null);
    navigate('/');
  };

  return (
    <section className="flex flex-col items-center justify-center px-6 mx-auto h-full dark:bg-darkGrayBackground">
      <div className="w-full bg-white rounded-xl dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700 mb-7 ">
        <div className="p-6 space-y-4 md:space-y-6 sm:p-8 border rounded-lg border-gray-200">
          <a
            onClick={() => navigate('/')}
            className="flex items-center justify-start text-2xl font-semibold text-gray-900 dark:text-white cursor-pointer"
          >
            <Logo className="h-8" />
          </a>
          <Form className="flex max-w-md flex-col gap-5" onSubmit={handleSubmit}>
            <TextInput
              name="fullName"
              // TODO: Markos
              label="Name"
              placeholder="Name"
              disabled={loading}
              value={form.fullName}
              onChange={(value: string) => dispatch(setFieldValue({ path: 'fullName', value }))}
              validators={[isRequired(), validateRegex(/^[a-zA-Z\s]+$/)]}
            />

            <TextInput
              name="email"
              // TODO: Markos
              label="Email"
              customPlaceholder={
                <span className="text-gray-400 dark:text-gray-500 flex gap-2">
                  <Icon icon="mdi:email-outline" width="20" /> Enter
                </span>
              }
              disabled={loading}
              value={form.email}
              onChange={(value: string) => dispatch(setFieldValue({ path: 'email', value }))}
              validators={[isRequired(), validateRegex(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)]}
            />

            <Textarea
              // TODO: Markos
              label="Message"
              name="message"
              placeholder="Leave us a message.."
              type="textarea"
              rows="4"
              value={form.message}
              onChange={(value: string) => dispatch(setFieldValue({ path: 'message', value }))}
            />

            <Button
              type="submit"
              colorType="primary"
              label="Send a message"
              // icon="mdi:send"
              disabled={loading}
              loading={loading}
            />
          </Form>
        </div>
      </div>
      {data && (
        <Dialog isOpen size="lg" onClose={onClose}>
          <div className="space-y-6 text-center">
            <img className="mx-auto mt-4 mb-10" src={checkImage} />
            <div className="space-y-5">
              <p className="text-gray-900 dark:text-white text-xl">Your message has been successfully sent!</p>
              <p className="text-gray-500 dark:text-white">We will contact you as soon as possible</p>
            </div>

            <Button className="w-full" colorType="primary" label="Ok" onClick={onClose} />
          </div>
        </Dialog>
      )}
    </section>
  );
};
