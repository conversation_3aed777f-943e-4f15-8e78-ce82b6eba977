// React
import { useEffect, useState } from 'react';
import { OrganizationType, Regex, useValidate, Form, initializeForm, RootState, useAppDispatch, useAppSelector, setFieldValue } from 'UI/src';
import { fetchOrganization, updateOrganization } from 'UI/src/middlewares/Organizations.middleware';
import { useParams } from 'react-router-dom';

// Core
import { Button, TextInput, Icon, RadioGroup, Dialog } from 'src';
import { setErrorNotify, setNotifyMessage } from 'UI';
import { useFormik } from 'formik';

export const EditProfile = ({ onClose, refetch }: { onClose: () => void; refetch: () => void }) => {
  // State
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState({ password: false, confirmPassword: false });
  const [showError, setShowError] = useState(false);

  // Hooks
  const { id } = useParams();
  const dispatch = useAppDispatch();
  const { isRequired, minLength, maxLength, validateRegex, validatePasswordRegex, isNotSpaces } = useValidate();

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      mobileNumber: '',
      location: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });
  const handleGet = async () => {
    if (!id) return;
    try {
      const result = await dispatch(fetchOrganization(id)).unwrap();
      console.log(`organizations/single/${id}`, result);
      dispatch(initializeForm(result));
    } catch (error: any) {
      dispatch(setErrorNotify(error?.message || 'Failed to fetch organization'));
    }
  };

  const handlePost = async () => {
    if (!id) return;
    if (form.password && form.confirmPassword !== form.password) {
      setShowError(true);
    } else {
      try {
        await dispatch(updateOrganization({ id, data: form })).unwrap();
        dispatch(setNotifyMessage('Profile updated successfully'));
        onClose();
        refetch();
      } catch (error: any) {
        dispatch(setErrorNotify(error?.message || 'Failed to update organization'));
      }
    }
  };

  useEffect(() => {
    handleGet();
  }, []);

  useEffect(() => {
    if (form.confirmPassword === form.password) setShowError(false);
  }, [form.confirmPassword]);

  return (
    <Dialog modalHeader="Edit Profile" size="lg" show popup onClose={onClose} overflowVisible={true}>
      <Form className="space-y-5" onSubmit={handlePost}>
        {/* TODO: Markos */}
        <TextInput
          name="name"
          label="Name"
          placeholder="Name"
          disabled={loading}
          value={form.name}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'name', value }))}
          validators={[isRequired(), minLength(3), maxLength(100), validateRegex(Regex.name)]}
        />
        <TextInput
          name="email"
          label="Email"
          placeholder="Email"
          disabled={loading}
          value={form.email}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'email', value }))}
          validators={[isRequired(), validateRegex(Regex.email)]}
        />

        <div className="flex w-full">
          <div className="w-full relative">
            <TextInput
              label="Password"
              name="password"
              placeholder="Password"
              autoComplete="new-password"
              type={showPassword.password ? 'text' : 'password'}
              value={form.password}
              rightIcon={() => {}}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'password', value }))}
              validators={
                !id
                  ? [isRequired(), isNotSpaces(), minLength(8), maxLength(50), validatePasswordRegex(Regex.password)]
                  : form.password
                  ? [isNotSpaces(), minLength(8), maxLength(50), validatePasswordRegex(Regex.password)]
                  : []
              }
              requiredLabel
            />
          </div>

          <div className="mt-[29px] absolute right-1" onClick={() => setShowPassword((prev) => ({ ...prev, password: !prev.password }))}>
            <Icon
              className="ml-3 p-5 w-8 h-8 rounded-md cursor-pointer text-gray-500 dark:text-gray-400"
              width="25"
              icon={!showPassword.password ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
            />
          </div>
        </div>

        <div className="flex w-full">
          <div className="w-full relative">
            <TextInput
              label="Confirm Password"
              name="confirm password"
              placeholder="Confirm password"
              type={showPassword.confirmPassword ? 'text' : 'password'}
              value={form.confirmPassword}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'confirmPassword', value }))}
              disabled={!form.password}
              rightIcon={() => {}}
            />
          </div>

          <div
            className="mt-[29px] absolute right-1"
            onClick={() => setShowPassword((prev) => ({ ...prev, confirmPassword: !prev.confirmPassword }))}
          >
            <Icon
              className="ml-3 p-5  w-8 h-8 rounded-md cursor-pointer text-gray-500 dark:text-gray-400"
              width="25"
              icon={!showPassword.confirmPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
            />
          </div>
        </div>
        {(form.confirmPassword && form.confirmPassword !== form.password) || showError ? (
          <label className="text-red-500 text-sm">Confirm password doesn't match the password</label>
        ) : null}

        <Button type="submit" label="Update" icon="mdi:send" loading={loading} className="w-full" gradientMonochrome="purple" />
      </Form>
    </Dialog>
  );
};
