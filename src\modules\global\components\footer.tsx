import { useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import thepass2 from 'images/Thepass-2.svg';

export const Footer = () => {
  const location = useLocation();
  const navigate = useNavigate();

  const handleHashNavigation = (e: any, path: any) => {
    if (path.includes('#')) {
      e.preventDefault();

      const hash = path.split('#')[1];

      if (location.pathname === '/' || path.startsWith('/#')) {
        const element = document.getElementById(hash);
        if (element) {
          const headerHeight = 80;
          const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
          const offsetPosition = elementPosition - headerHeight;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth',
          });
        }
      } else {
        // Navigate to home page first, then scroll to the section
        navigate('/');

        // Use multiple attempts to find the element with increasing delays
        const attemptScroll = (attempts = 0) => {
          setTimeout(() => {
            const element = document.getElementById(hash);
            console.log(`Footer Attempt ${attempts + 1}: Looking for element with id "${hash}"`, element);
            if (element) {
              const headerHeight = 80;
              const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
              const offsetPosition = elementPosition - headerHeight;
              window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth',
              });
            } else if (attempts < 5) {
              // Try again with more delay
              attemptScroll(attempts + 1);
            }
          }, 200 + attempts * 100); // Start with 200ms, increase by 100ms each attempt
        };

        attemptScroll();
      }
    }
  };

  // Handle smooth scrolling to hash sections with header offset
  useEffect(() => {
    const handleHashScroll = () => {
      const hash = location.hash;
      if (hash) {
        const element = document.getElementById(hash.substring(1));
        if (element) {
          const headerHeight = 80;
          const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
          const offsetPosition = elementPosition - headerHeight;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth',
          });
        }
      }
    };

    // Small delay to ensure DOM is ready
    const timeoutId = setTimeout(handleHashScroll, 100);
    return () => clearTimeout(timeoutId);
  }, [location.hash, location.pathname]);

  const quickLinks = [
    { label: 'How it Works', path: '/#hero-section' },
    { label: 'Assessments', path: '/programming-test' },
    { label: 'Why Choose Us', path: '/#why-choose-us' },
    { label: 'Pricing', path: '/pricing' },
    { label: 'Contact Us', path: '/contact-us' },
    { label: 'Privacy Policy', path: '/terms' },
  ];

  const paymentMethods = [
    {
      name: 'Visa',
      logo: (
        <svg width="32" height="28" viewBox="0 0 23 17" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="0.5" y="0.992188" width="22" height="15" rx="2" fill="white" stroke="#D9D9D9" />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M7.02517 11.3323H5.63182L4.58697 7.28841C4.53738 7.10239 4.43208 6.93793 4.27718 6.86042C3.89063 6.66564 3.46468 6.51063 3 6.43244V6.27675H5.24458C5.55437 6.27675 5.78671 6.51063 5.82543 6.78224L6.36756 9.69925L7.76023 6.27675H9.11486L7.02517 11.3323ZM9.88932 11.3323H8.57341L9.65698 6.27675H10.9729L9.88932 11.3323ZM12.6754 7.6773C12.7141 7.40501 12.9464 7.24932 13.2175 7.24932C13.6434 7.21023 14.1074 7.28841 14.4947 7.48252L14.727 6.39403C14.3398 6.23834 13.9138 6.16016 13.5273 6.16016C12.2501 6.16016 11.3207 6.86043 11.3207 7.83232C11.3207 8.57168 11.979 8.95989 12.4437 9.19377C12.9464 9.42697 13.14 9.58266 13.1013 9.81586C13.1013 10.1657 12.7141 10.3213 12.3275 10.3213C11.8629 10.3213 11.3982 10.2047 10.9729 10.01L10.7406 11.0991C11.2052 11.2932 11.708 11.3714 12.1726 11.3714C13.6047 11.4098 14.4947 10.7102 14.4947 9.66017C14.4947 8.3378 12.6754 8.2603 12.6754 7.6773ZM19.1 11.3323L18.0552 6.27675H16.9329C16.7005 6.27675 16.4682 6.43244 16.3907 6.66564L14.4559 11.3323H15.8106L16.081 10.5936H17.7454L17.9003 11.3323H19.1ZM17.1265 7.63821L17.513 9.54357H16.4294L17.1265 7.63821Z"
            fill="#172B85"
          />
        </svg>
      ),
    },
    {
      name: 'MasterCard',
      logo: (
        <svg width="32" height="28" viewBox="0 0 23 17" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="0.5" y="0.992188" width="22" height="15" rx="2" fill="white" stroke="#D9D9D9" />
          <path
            d="M14.9746 3.48438C17.7501 3.48451 20 5.69795 20 8.42773C19.9997 11.1573 17.75 13.37 14.9746 13.3701C13.7301 13.3701 12.5916 12.9244 11.7139 12.1875C10.8362 12.9241 9.69833 13.3701 8.4541 13.3701C5.67862 13.3699 3.42871 11.1565 3.42871 8.42676C3.42898 5.69723 5.67879 3.48456 8.4541 3.48438C9.6981 3.48438 10.8363 3.92966 11.7139 4.66602C12.5916 3.92935 13.7303 3.48438 14.9746 3.48438Z"
            fill="#ED0006"
          />
          <path
            d="M14.9746 3.48438C17.7503 3.48438 20.001 5.69787 20.001 8.42773C20.0007 11.1574 17.7501 13.3701 14.9746 13.3701C13.7301 13.3701 12.5926 12.9235 11.7148 12.1865C12.7945 11.28 13.4803 9.93333 13.4805 8.42773C13.4805 6.92172 12.7949 5.5736 11.7148 4.66699C12.5925 3.93028 13.7303 3.48442 14.9746 3.48438Z"
            fill="#F9A000"
          />
          <path
            d="M11.7139 4.66797C12.7941 5.5745 13.4794 6.92261 13.4795 8.42871C13.4795 9.93479 12.794 11.2828 11.7139 12.1895C10.6338 11.2828 9.94824 9.9347 9.94824 8.42871C9.94836 6.9227 10.6337 5.5745 11.7139 4.66797Z"
            fill="#FF5E00"
          />
        </svg>
      ),
    },
    {
      name: 'Mada',
      logo: (
        <svg width="32" height="28" viewBox="0 0 23 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clipPath="url(#clip0_18379_25383)">
            <path
              d="M21 0H3C1.89543 0 1 0.89543 1 2V13C1 14.1046 1.89543 15 3 15H21C22.1046 15 23 14.1046 23 13V2C23 0.89543 22.1046 0 21 0Z"
              fill="white"
              stroke="#D9D9D9"
            />
            <path d="M11 4H3V6H11V4Z" fill="#259BD6" />
            <path d="M11 9H3V11H11V9Z" fill="#84B740" />
            <path
              d="M16 4.5H15.2702C15.0953 4.5 14.9256 4.55952 14.789 4.66877L14.6014 4.81889C14.5373 4.87016 14.5 4.94777 14.5 5.02984V5.02984C14.5 5.17905 14.621 5.3 14.7702 5.3H15H15.2298C15.4047 5.3 15.5744 5.35952 15.711 5.46877L15.8438 5.57506C15.9425 5.65403 16 5.77359 16 5.9V5.9C16 6.02641 15.9425 6.14597 15.8438 6.22494L15.711 6.33123C15.5744 6.44048 15.4047 6.5 15.2298 6.5H15H14.5H14H13.5H13H12.7702C12.5953 6.5 12.4256 6.44048 12.289 6.33123V6.33123C12.1064 6.18508 12 5.9638 12 5.72984V5.7V5.3V5.27016C12 5.0362 12.1064 4.81492 12.289 4.66877V4.66877C12.4256 4.55952 12.5953 4.5 12.7702 4.5H13"
              stroke="black"
              strokeWidth="0.5"
            />
            <path
              d="M19.6581 6.5H19.9534H20.2488H20.2834C20.4511 6.5 20.6127 6.43713 20.7363 6.3238V6.3238C20.8553 6.21468 20.9309 6.06626 20.9492 5.90584L20.9806 5.63029C20.9935 5.51731 20.9906 5.40309 20.9721 5.29089L20.9551 5.18776C20.9326 5.05146 20.8761 4.92303 20.7908 4.81435L20.7265 4.73234C20.6113 4.58566 20.4352 4.5 20.2488 4.5V4.5H19.9534V4.5C19.7903 4.5 19.6581 4.63223 19.6581 4.79534V4.9V5.46V6.5ZM19.6581 6.5C19.6581 6.5 19.1032 6.5 18.4767 6.5C18.7774 6.5 18.3783 6.5 18.1277 6.5M18.1277 6.5H17.7518H17.2953H17H18.1277ZM18.1277 6.257V5.46V5.23278C18.1277 5.069 18.0728 4.90993 17.9718 4.78098V4.78098C17.8329 4.60362 17.6202 4.5 17.3949 4.5H17.3759H17"
              stroke="black"
              strokeWidth="0.5"
              strokeMiterlimit="16"
            />
            <path
              d="M11.7191 10.5V9.28C11.7191 9.12267 11.7565 8.97867 11.8311 8.848C11.9058 8.71467 12.0138 8.60933 12.1551 8.532C12.2965 8.452 12.4658 8.412 12.6631 8.412C12.7591 8.412 12.8511 8.424 12.9391 8.448C13.0271 8.472 13.1085 8.51067 13.1831 8.564C13.2605 8.61467 13.3258 8.67867 13.3791 8.756H13.3831C13.4365 8.67867 13.5005 8.61467 13.5751 8.564C13.6525 8.51067 13.7351 8.472 13.8231 8.448C13.9138 8.424 14.0071 8.412 14.1031 8.412C14.3031 8.412 14.4725 8.452 14.6111 8.532C14.7525 8.60933 14.8605 8.71467 14.9351 8.848C15.0098 8.97867 15.0471 9.12267 15.0471 9.28V10.5H14.5911V9.28C14.5911 9.192 14.5685 9.112 14.5231 9.04C14.4805 8.968 14.4231 8.91067 14.3511 8.868C14.2791 8.82533 14.1965 8.804 14.1031 8.804C14.0125 8.804 13.9298 8.82533 13.8551 8.868C13.7805 8.91067 13.7205 8.968 13.6751 9.04C13.6325 9.112 13.6111 9.192 13.6111 9.28V10.5H13.1551V9.28C13.1551 9.192 13.1325 9.112 13.0871 9.04C13.0418 8.968 12.9818 8.91067 12.9071 8.868C12.8351 8.82533 12.7538 8.804 12.6631 8.804C12.5725 8.804 12.4898 8.82533 12.4151 8.868C12.3431 8.91067 12.2845 8.968 12.2391 9.04C12.1965 9.112 12.1751 9.192 12.1751 9.28V10.5H11.7191ZM16.286 10.548C16.166 10.548 16.0514 10.5373 15.942 10.516C15.8354 10.4947 15.7407 10.4587 15.658 10.408C15.578 10.3573 15.514 10.288 15.466 10.2C15.4207 10.112 15.398 10.0027 15.398 9.872C15.398 9.70933 15.4394 9.58133 15.522 9.488C15.6074 9.392 15.7167 9.324 15.85 9.284C15.986 9.24133 16.1274 9.22 16.274 9.22C16.3514 9.22 16.4234 9.224 16.49 9.232C16.5594 9.24 16.6234 9.25067 16.682 9.264V9.176C16.682 9.09867 16.6674 9.02667 16.638 8.96C16.6114 8.89333 16.5634 8.83867 16.494 8.796C16.4274 8.75333 16.3327 8.732 16.21 8.732C16.0927 8.732 15.986 8.74133 15.89 8.76C15.794 8.776 15.718 8.79467 15.662 8.816L15.606 8.452C15.662 8.428 15.75 8.40667 15.87 8.388C15.9927 8.36933 16.122 8.36 16.258 8.36C16.474 8.36 16.6434 8.39467 16.766 8.464C16.8914 8.53067 16.9807 8.62533 17.034 8.748C17.0874 8.87067 17.114 9.01333 17.114 9.176V10.46C17.0287 10.476 16.9114 10.4947 16.762 10.516C16.6154 10.5373 16.4567 10.548 16.286 10.548ZM16.298 10.18C16.3754 10.18 16.4474 10.1787 16.514 10.176C16.5834 10.1707 16.6394 10.164 16.682 10.156V9.596C16.642 9.58533 16.5914 9.576 16.53 9.568C16.4687 9.56 16.4074 9.556 16.346 9.556C16.2634 9.556 16.182 9.56533 16.102 9.584C16.0247 9.6 15.962 9.63067 15.914 9.676C15.866 9.71867 15.842 9.78 15.842 9.86C15.842 9.98 15.8834 10.064 15.966 10.112C16.0487 10.1573 16.1594 10.18 16.298 10.18ZM18.3923 10.548C18.187 10.548 18.0096 10.504 17.8603 10.416C17.711 10.328 17.5963 10.2027 17.5163 10.04C17.4363 9.87733 17.3963 9.684 17.3963 9.46C17.3963 9.23867 17.431 9.04667 17.5003 8.884C17.5696 8.72133 17.6683 8.59467 17.7963 8.504C17.927 8.41067 18.0843 8.364 18.2683 8.364C18.3643 8.364 18.451 8.376 18.5283 8.4C18.6083 8.42133 18.6723 8.44667 18.7203 8.476V7.468L19.1643 7.392V10.436C19.1056 10.4547 19.035 10.4733 18.9523 10.492C18.8723 10.508 18.7843 10.5213 18.6883 10.532C18.595 10.5427 18.4963 10.548 18.3923 10.548ZM18.3923 10.156C18.467 10.156 18.531 10.1533 18.5843 10.148C18.6403 10.1427 18.6856 10.136 18.7203 10.128V8.88C18.6776 8.848 18.6216 8.81867 18.5523 8.792C18.483 8.76533 18.4096 8.752 18.3323 8.752C18.2203 8.752 18.1283 8.78133 18.0563 8.84C17.987 8.89867 17.935 8.98 17.9003 9.084C17.8683 9.188 17.8523 9.30933 17.8523 9.448C17.8523 9.66933 17.9003 9.84267 17.9963 9.968C18.0923 10.0933 18.2243 10.156 18.3923 10.156ZM20.4426 10.548C20.3226 10.548 20.2079 10.5373 20.0986 10.516C19.9919 10.4947 19.8973 10.4587 19.8146 10.408C19.7346 10.3573 19.6706 10.288 19.6226 10.2C19.5773 10.112 19.5546 10.0027 19.5546 9.872C19.5546 9.70933 19.5959 9.58133 19.6786 9.488C19.7639 9.392 19.8733 9.324 20.0066 9.284C20.1426 9.24133 20.2839 9.22 20.4306 9.22C20.5079 9.22 20.5799 9.224 20.6466 9.232C20.7159 9.24 20.7799 9.25067 20.8386 9.264V9.176C20.8386 9.09867 20.8239 9.02667 20.7946 8.96C20.7679 8.89333 20.7199 8.83867 20.6506 8.796C20.5839 8.75333 20.4893 8.732 20.3666 8.732C20.2493 8.732 20.1426 8.74133 20.0466 8.76C19.9506 8.776 19.8746 8.79467 19.8186 8.816L19.7626 8.452C19.8186 8.428 19.9066 8.40667 20.0266 8.388C20.1493 8.36933 20.2786 8.36 20.4146 8.36C20.6306 8.36 20.7999 8.39467 20.9226 8.464C21.0479 8.53067 21.1373 8.62533 21.1906 8.748C21.2439 8.87067 21.2706 9.01333 21.2706 9.176V10.46C21.1853 10.476 21.0679 10.4947 20.9186 10.516C20.7719 10.5373 20.6133 10.548 20.4426 10.548ZM20.4546 10.18C20.5319 10.18 20.6039 10.1787 20.6706 10.176C20.7399 10.1707 20.7959 10.164 20.8386 10.156V9.596C20.7986 9.58533 20.7479 9.576 20.6866 9.568C20.6253 9.56 20.5639 9.556 20.5026 9.556C20.4199 9.556 20.3386 9.56533 20.2586 9.584C20.1813 9.6 20.1186 9.63067 20.0706 9.676C20.0226 9.71867 19.9986 9.78 19.9986 9.86C19.9986 9.98 20.0399 10.064 20.1226 10.112C20.2053 10.1573 20.3159 10.18 20.4546 10.18Z"
              fill="black"
            />
          </g>
          <defs>
            <clipPath id="clip0_18379_25383">
              <rect width="23" height="16" fill="white" />
            </clipPath>
          </defs>
        </svg>
      ),
    },
    {
      name: 'Apple Pay',
      logo: (
        <svg width="32" height="28" viewBox="0 0 23 17" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="0.5" y="0.992188" width="22" height="15" rx="2" fill="white" stroke="#D9D9D9" />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M5.48506 6.4488C5.79394 6.47523 6.10283 6.29019 6.29588 6.0556C6.48571 5.81439 6.6112 5.49058 6.57902 5.16016C6.30553 5.17337 5.96769 5.34519 5.77464 5.5864C5.59767 5.79456 5.44645 6.13159 5.48506 6.4488ZM9.15951 10.7046V5.55335H11.0418C12.0135 5.55335 12.6924 6.24063 12.6924 7.2451C12.6924 8.24958 12.0006 8.94346 11.016 8.94346H9.93815V10.7046H9.15951ZM6.57579 6.51159C6.30367 6.4955 6.05537 6.59563 5.85481 6.6765C5.72575 6.72855 5.61646 6.77262 5.53331 6.77262C5.44 6.77262 5.32619 6.72619 5.19841 6.67406C5.03098 6.60576 4.83957 6.52766 4.63883 6.53141C4.17872 6.53802 3.75079 6.80566 3.51591 7.2319C3.03328 8.08439 3.39042 9.34659 3.85697 10.0405C4.08541 10.3841 4.3589 10.7608 4.71927 10.7476C4.87781 10.7414 4.99185 10.6917 5.10988 10.6403C5.24575 10.5811 5.38691 10.5196 5.60731 10.5196C5.82007 10.5196 5.95505 10.5795 6.08463 10.637C6.20783 10.6917 6.32615 10.7442 6.50179 10.741C6.87502 10.7344 7.1099 10.3973 7.33835 10.0537C7.58488 9.68487 7.69322 9.32492 7.70966 9.2703L7.71158 9.26398C7.71119 9.26358 7.70817 9.26216 7.70284 9.25966L7.70278 9.25963L7.70277 9.25962C7.62036 9.22088 6.99046 8.92474 6.98442 8.13064C6.97835 7.46411 7.48404 7.12641 7.56364 7.07325L7.56364 7.07324L7.56365 7.07324C7.56849 7.07001 7.57175 7.06783 7.57323 7.06669C7.25148 6.57767 6.74954 6.5248 6.57579 6.51159ZM14.1145 10.7442C14.6036 10.7442 15.0573 10.4898 15.2632 10.0867H15.2793V10.7046H16V8.14053C16 7.39708 15.4209 6.91797 14.5296 6.91797C13.7027 6.91797 13.0914 7.40369 13.0688 8.07114H13.7703C13.8282 7.75393 14.1145 7.54577 14.5071 7.54577C14.9833 7.54577 15.2503 7.77376 15.2503 8.19339V8.47755L14.2786 8.53703C13.3745 8.5932 12.8854 8.97318 12.8854 9.63402C12.8854 10.3015 13.3906 10.7442 14.1145 10.7442ZM14.3236 10.133C13.9086 10.133 13.6447 9.92811 13.6447 9.61421C13.6447 9.2904 13.8989 9.10206 14.3848 9.07232L15.2503 9.01615V9.30692C15.2503 9.78933 14.8513 10.133 14.3236 10.133ZM18.3907 10.9062C18.0785 11.8082 17.7214 12.1056 16.9621 12.1056C16.9041 12.1056 16.7111 12.099 16.666 12.0858V11.4679C16.7143 11.4745 16.8334 11.4811 16.8945 11.4811C17.2388 11.4811 17.4318 11.3324 17.5509 10.9458L17.6217 10.7178L16.3025 6.96755H17.1165L18.0335 10.0107H18.0496L18.9666 6.96755H19.7581L18.3907 10.9062ZM9.93808 6.22742H10.8358C11.5115 6.22742 11.8976 6.59749 11.8976 7.24842C11.8976 7.89934 11.5115 8.27272 10.8326 8.27272H9.93808V6.22742Z"
            fill="black"
          />
        </svg>
      ),
    },
    {
      label: 'Terms of Service',
      path: '/terms',
      visible: true,
    },
  ];

  return (
    <div className=" w-full bg-[#1a1b3a]  flex items-center justify-center">
      <footer className="bg-[#1a1b3a] text-white  ">
        <div className="container mx-auto px-6 py-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-9 lg:gap-48">
            {/* Company Description */}
            <div className="space-y-4  ">
              <div className="flex items-center space-x-2">
                <img src={thepass2} className="h-8" alt="Logo" />
              </div>
              <p className="text-gray-300 text-sm leading-relaxed max-w-[340px]">
                Thepass is an <span className="gradient-text">AI Powered Hiring Platform.</span> <br />
                Our Assessments Analytics Tools help teams identify top talents—no manual scoring, scheduling, or bias.
              </p>
              {/* LinkedIn Icon */}
              <div className="pt-2">
                <div
                  onClick={() => window.open('https://www.linkedin.com/company/thepass-ai', '_blank')}
                  className="inline-flex items-center justify-center w-8 h-8  rounded hover:cursor-pointer"
                >
                  <svg width="25" height="26" viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M23.1494 0.445312H1.8457C0.825195 0.445312 0 1.25098 0 2.24708V23.6388C0 24.6348 0.825195 25.4453 1.8457 25.4453H23.1494C24.17 25.4453 25 24.6348 25 23.6436V2.24708C25 1.25098 24.17 0.445312 23.1494 0.445312ZM7.41698 21.7491H3.70606V9.81542H7.41698V21.7491ZM5.56153 8.18945C4.37013 8.18945 3.4082 7.22753 3.4082 6.04102C3.4082 4.8545 4.37013 3.89258 5.56153 3.89258C6.74805 3.89258 7.70995 4.8545 7.70995 6.04102C7.70995 7.22266 6.74805 8.18945 5.56153 8.18945ZM21.3038 21.7491H17.5977V15.9482C17.5977 14.5664 17.5733 12.7842 15.6689 12.7842C13.7402 12.7842 13.4473 14.293 13.4473 15.8506V21.7491H9.74609V9.81542H13.3008V11.4463H13.3496C13.8428 10.5088 15.0537 9.51758 16.8555 9.51758C20.6103 9.51758 21.3038 11.9883 21.3038 15.2012V21.7491Z"
                      fill="white"
                    />
                  </svg>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div className="space-y-3 flex flex-col pt-2 md:items-center items-start">
              <h3 className="text-lg font-semibold  text-[#8098F9] gradient-text">Quick Links</h3>
              <ul className="space-y-3">
                {quickLinks.map((link, index) => (
                  <li key={index} onClick={(e) => handleHashNavigation(e, link.path)}>
                    <Link to={link.path} className="text-gray-300 text-sm hover:text-white transition-colors duration-200">
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact */}
            <div className="space-y-3 flex pt-2 flex-col items-start sm:items-start">
              <h3 className="text-lg font-semibold text-[#8098F9] gradient-text">Contact</h3>
              <span className="text-gray-300 text-sm cursor-default "><EMAIL></span>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="flex justify-between border-t border-gray-600 mt-5 pt-8">
            <div className="flex flex-col  lg:flex-row justify-between items-center md:items-start space-y-4 lg:space-y-0">
              {/* Security Indicators */}
              <div className=" space-y-3">
                <div className="flex items-center  space-x-6">
                  <div className="flex items-center  text-nowrap space-x-2">
                    <svg width="15" height="19" viewBox="0 0 15 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M7.87267 0.578564C7.63808 0.463395 7.36192 0.463395 7.12733 0.578564L0.460658 3.85129C0.178333 3.98989 0 4.2732 0 4.5831V9.4922C0 11.6704 0.717117 13.4942 2.00245 14.9872C3.27438 16.4646 5.07059 17.5822 7.1845 18.4313C7.38667 18.5125 7.61333 18.5125 7.8155 18.4313C9.92942 17.5822 11.7256 16.4646 12.9976 14.9872C14.2829 13.4942 15 11.6704 15 9.4922V4.5831C15 4.2732 14.8217 3.98989 14.5393 3.85129L7.87267 0.578564ZM1.66667 9.4922V5.08876L7.5 2.22512L13.3333 5.08876V9.4922C13.3333 11.2861 12.7542 12.7351 11.7247 13.9308C10.7384 15.0765 9.31008 16.0215 7.5 16.7868C5.68988 16.0215 4.26159 15.0765 3.27532 13.9308C2.24585 12.7351 1.66667 11.2861 1.66667 9.4922ZM11.4643 7.57211C11.7649 7.22998 11.7262 6.71333 11.3777 6.41815C11.0293 6.12298 10.503 6.16105 10.2023 6.50319L6.46892 10.7519L4.75018 9.09685C4.4216 8.78046 3.89399 8.78545 3.57172 9.10806C3.24946 9.43067 3.25458 9.94866 3.58315 10.2651L5.93608 12.5308C6.1005 12.6892 6.32425 12.7736 6.55425 12.7642C6.78433 12.7548 7.00017 12.6523 7.15058 12.4812L11.4643 7.57211Z"
                        fill="#26A64A"
                      />
                    </svg>
                    <span className="text-gray-300 text-sm">Secure Payments</span>
                  </div>

                  <div className="flex items-center text-nowrap space-x-2">
                    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M3.66699 6.17805C3.98127 6.15885 4.36872 6.15885 4.86699 6.15885H9.13366C9.63192 6.15885 10.0194 6.15885 10.3337 6.17805M3.66699 6.17805C3.27478 6.20199 2.99652 6.25585 2.75901 6.37685C2.38269 6.56859 2.07673 6.87452 1.88498 7.25085C1.66699 7.67872 1.66699 8.23872 1.66699 9.35885V10.2922C1.66699 11.4123 1.66699 11.9723 1.88498 12.4002C2.07673 12.7765 2.38269 13.0825 2.75901 13.2742C3.18683 13.4922 3.74689 13.4922 4.86699 13.4922H9.13366C10.2538 13.4922 10.8138 13.4922 11.2417 13.2742C11.618 13.0825 11.9239 12.7765 12.1157 12.4002C12.3337 11.9723 12.3337 11.4123 12.3337 10.2922V9.35885C12.3337 8.23872 12.3337 7.67872 12.1157 7.25085C11.9239 6.87452 11.618 6.56859 11.2417 6.37685C11.0041 6.25585 10.7259 6.20199 10.3337 6.17805M3.66699 6.17805V4.82552C3.66699 2.98457 5.15938 1.49219 7.00033 1.49219C8.84126 1.49219 10.3337 2.98457 10.3337 4.82552V6.17805"
                        stroke="#26A64A"
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                    <span className="text-gray-300 text-sm">SSL Encrypted</span>
                  </div>
                </div>

                {/* Payment Methods */}
                <div className="flex items-center space-x-1">
                  <span className="text-gray-300 text-sm mr-2">We accept</span>
                  {paymentMethods.map((method, index) => (
                    <div key={index} className="h-6 flex items-center">
                      {method.logo}
                    </div>
                  ))}
                </div>
              </div>
              <p className="text-gray-400 text-nowrap pt-6  sm:hidden block text-sm">© 2024 ThePass Limited. All rights reserved.</p>
            </div>
            {/* Copyright */}
            <p className="text-gray-400 text-nowrap sm:block hidden pt-6 text-sm">© 2024 ThePass Limited. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};
