import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

export const Card = ({ children, className, onClick, ...props }: CardProps) => (
  <div
    className={`max-w p-6 bg-white border border-gray-200 rounded-lg shadow-sm dark:bg-darkBackgroundCard dark:border-[#374151] ${className}`}
    onClick={onClick}
    {...props}
  >
    {children}
  </div>
);
