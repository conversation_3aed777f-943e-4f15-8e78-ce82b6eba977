import React, { useState, useEffect, FC } from 'react';

// Components
import { Select } from 'src';
import { Dialog, Button } from 'UI';
import { QuizzesSearch, initializeForm, resetForm, RootState, useAppDispatch, useAppSelector, Api, setFieldValue } from 'UI/src';
import { setErrorNotify, setNotifyMessage } from 'UI';
import { Form } from 'UI/src/components/form';
import { useFormik } from 'formik';

// Types

type SubmissionsLockDialogProps = {
  onClose: () => void;
  onFinish: () => void;
};

type Quiz = {
  _id: string;
  title: string;
};

type LockForm = {
  quizId: string;
};

export const SubmissionsLockDialog: FC<SubmissionsLockDialogProps> = ({ onClose, onFinish }) => {
  // Hooks
  const dispatch = useAppDispatch();

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      quizId: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  // State
  const [loading, setLoading] = useState(false);
  const [quizzes, setQuizzes] = useState<QuizzesSearch>();

  // Methods
  const handleSearch = async (keyword: string) => {
    try {
      const result = await Api.get<QuizzesSearch>('quizzes/search', { keyword });
      console.log('quizzes/search', result.data);
      setQuizzes(result?.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    }
  };
  const handleSubmit = async () => {
    try {
      setLoading(true);

      await Api.put('submissions/bulk-lock', form);

      // Reset form
      dispatch(resetForm());

      // Update Parent
      onFinish();
      onClose();

      // Notify
      dispatch(setNotifyMessage('Test locked successfully!'));
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog isOpen size="md" title={'Lock Bulk Test'} onClose={onClose}>
      <Form className="space-y-4" onSubmit={handleSubmit}>
        <Select
          name="quizId"
          label="Test"
          placeholder="Find tests..."
          value={form.quizId}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'quizId', value }))}
          onSearch={handleSearch}
          disabled={loading}
          //           lookup={quizzes.map(({ title, _id }) => ({
          lookup={(quizzes || []).map(({ title, _id }) => ({
            label: title,
            value: _id,
          }))}
          validators={[]}
        />

        <div className="pt-2 space-y-4">
          <Button
            type="submit"
            label="Lock All"
            colorType="primary"
            icon="material-symbols:lock-outline"
            className="w-full"
            loading={loading}
            disabled={loading || !form.quizId}
          />
        </div>
      </Form>
    </Dialog>
  );
};
