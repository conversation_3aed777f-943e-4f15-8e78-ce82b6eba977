// React
import { useState } from 'react';

// Core
import { Drawer, CustomIcon, Button } from 'src';

export const PlanManagement = ({ onClose }: { onClose: () => void }) => {
  // Tabs
  const tabsBlocks = [
    { label: 'Monthly', type: 1 },
    { label: 'Yearly', type: 2 },
  ];

  // State
  const [activeTab, setActiveTab] = useState(1);

  const data = [
    {
      id: 1,
      plan: 'Free',
      planStatus: 1,
      expires: '24/5/2025',
      price: 0,
      per: 'month',
      featuresPlan: [
        { up: '3 AI Interviews' },
        { up: '3 Tests' },
        { up: '1 User' },
        { up: 'PDF Reports' },
        { down: 'Test Multiple Applicants with Single Link' },
        { down: 'Access to Given & Correct Answers' },
        { down: 'Custom Branding' },
      ],
      type: 1,
    },
    {
      id: 2,
      plan: 'Basic Plan',
      // planStatus: 1,
      // expires: '24/5/2025',
      price: 49,
      per: 'month',
      featuresPlan: [
        { up: '20 AI Interviews' },
        { up: '20 Tests' },
        { up: '1 User' },
        { up: 'PDF Reports' },
        { up: 'Test Multiple Applicants with Single Link' },
        { up: 'Access to Given & Correct Answers' },
        { down: 'Custom Branding' },
      ],
      type: 1,
    },
    {
      id: 3,
      plan: 'Professional',
      // planStatus: 1,
      // expires: '24/5/2025',
      price: 149,
      per: 'month',
      featuresPlan: [
        { up: '3 AI Interviews' },
        { up: '3 Tests' },
        { up: '1 User' },
        { up: 'PDF Reports' },
        { up: 'Test Multiple Applicants with Single Link' },
        { up: 'Access to Given & Correct Answers' },
        { up: 'Custom Branding' },
        { down: false },
      ],
      type: 1,
    },
    {
      id: 4,
      plan: 'Enterprise',
      // planStatus: 1,
      // expires: '24/5/2025',
      // price: 149,
      description: 'Let’s build a plan that fits your team’s hiring goals.',
      per: 'month',
      featuresPlan: [
        { up: '3 AI Interviews' },
        { up: '3 Tests' },
        { up: '1 User' },
        { up: 'PDF Reports' },
        { up: 'Test Multiple Applicants with Single Link' },
        { up: 'Access to Given & Correct Answers' },
        { up: 'Custom Branding' },
        { down: false },
      ],
    },
    {
      id: 5,
      plan: 'Free',
      planStatus: 2,
      expires: '24/5/2025',
      price: 0,
      per: 'month',
      featuresPlan: [
        { up: '3 AI Interviews' },
        { up: '3 Tests' },
        { up: '1 User' },
        { up: 'PDF Reports' },
        { down: 'Test Multiple Applicants with Single Link' },
        { down: 'Access to Given & Correct Answers' },
        { down: 'Custom Branding' },
      ],
      type: 1,
    },
    {
      id: 6,
      plan: 'Free',
      planStatus: 1,
      expires: '24/5/2025',
      price: 0,
      per: 'year',
      featuresPlan: [
        { up: '3 AI Interviews' },
        { up: '3 Tests' },
        { up: '1 User' },
        { up: 'PDF Reports' },
        { down: 'Test Multiple Applicants with Single Link' },
        { down: 'Access to Given & Correct Answers' },
        { down: 'Custom Branding' },
      ],
      type: 2,
    },
  ];

  const handleButtonLabel = (planStatus: number | null, price: number | null) => {
    if (price !== 0 && !price) {
      return 'Contact Us';
    } else if (!planStatus) {
      return 'Upgrade';
    } else if (planStatus === 1) {
      return 'Deactivate Subscription';
    } else if (planStatus === 2) {
      return 'Avtivate Subscription';
    }
  };

  return (
    <Drawer onClose={onClose}>
      <Drawer.SingleView>
        <div className="space-y-3 h-full overflow-auto">
          <div className="flex items-center gap-2">
            <img src="https://placehold.co/40" alt="Avatar" className="rounded-full" />
            <div>
              <p className="text-[#834CFF] dark:text-white font-medium">Martena Weber</p>
              <p className="text-[#656575]"><EMAIL></p>
            </div>
          </div>

          <div className="space-y-2">
            <h2 className="text-2xl font-bold">Plan Management</h2>
            <p className="text-[#707F8F]">Review and update the subscription plan for this organization.</p>
          </div>

          {/* Tabs in blocks - can be in separated compnented if needed */}
          <div className="w-fit flex gap-3 px-2 py-1.5 text-sm border rounded-xl bg-[#FBFBFB]">
            {tabsBlocks.map((tab) => {
              return (
                <div
                  key={tab.label}
                  onClick={() => setActiveTab(tab.type)}
                  className={`px-5 py-0.5 rounded-lg cursor-pointer ${activeTab === tab.type ? 'bg-white font-medium border' : 'text-[#797878]'}`}
                >
                  {tab.label}
                </div>
              );
            })}
          </div>

          <div className="grid sm:grid-cols-2 gap-6">
            {data
              .filter((singleData) => !singleData.type || singleData.type === activeTab)
              .map((singleData) => (
                <div
                  key={singleData.id}
                  className={`p-6 rounded-xl border border-gray-200 shadow-md space-y-4 ${
                    singleData.price !== 0 && !singleData.price && 'bg-gradient-to-b from-white to-[#EADBF7]'
                  }`}
                >
                  {/* Header */}
                  <div className="flex justify-between items-center">
                    <div className="text-lg font-semibold text-[#5f5f5f]">{singleData.plan}</div>
                    {singleData.planStatus && (
                      <span
                        className={`px-2.5 py-1 text-xs font-medium border rounded-full ${
                          singleData.planStatus === 1 ? 'bg-[#f9f5ff] text-[#6941c6] ' : 'bg-[#EAECF0] text-[#6B7280]'
                        }`}
                      >
                        {singleData.planStatus === 1 ? 'Current Plan' : 'Deactivated'}
                      </span>
                    )}
                    {singleData.expires && <div className="text-sm text-gray-500">Expires on {singleData.expires}</div>}
                  </div>

                  {/* Price */}
                  {singleData.price && singleData.price >= 0 && (
                    <div className="flex gap-2 items-end">
                      <div className="text-3xl font-bold text-black">${singleData.price}</div>
                      <div className="text-sm text-gray-500 pb-1">/per {singleData.per}</div>
                    </div>
                  )}

                  {/* Description */}
                  {singleData.description && <p className="text-[#566577]">{singleData.description}</p>}

                  {/* Button */}
                  <Button
                    label={handleButtonLabel(singleData.planStatus ? singleData.planStatus : null, singleData.price ? singleData.price : null)}
                    className="w-full rounded-2xl"
                    tertiary={!!singleData.planStatus}
                  />

                  <hr className="border-t-2 border-dashed border-gray-300" />

                  {/* Features */}
                  <ul className="space-y-4 text-sm font-medium">
                    {singleData.featuresPlan.map((feature, index) => (
                      <li key={index} className="flex items-center gap-2">
                        {feature.up && <CustomIcon definedIcon="greenRightSign" />}
                        {feature.down && <CustomIcon definedIcon="redWrongSign" />}
                        {feature.up || feature.down}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
          </div>
        </div>
      </Drawer.SingleView>
    </Drawer>
  );
};
