import React from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';

const PaymentFailed = () => {
  const [searchParams] = useSearchParams();
  const reason = searchParams.get('reason');
  const navigate = useNavigate();

  return (
    <div style={styles.overlay}>
      {/* TODO: Markos */}
      <div
        style={{
          backgroundColor: '#fff',
          padding: '2rem',
          borderRadius: '12px',
          textAlign: 'center',
          maxWidth: '400px',
          width: '100%',
          boxShadow: '0 0 20px rgba(0,0,0,0.15)',
        }}
      >
        <h1 style={styles.icon}>❌</h1>
        <h2 style={styles.title}>Payment Failed</h2>
        <p style={styles.message}>Sorry, your payment could not be processed.</p>
        {reason && (
          <p style={styles.reason}>
            <strong>Reason:</strong> {decodeURIComponent(reason)}
          </p>
        )}
        <div style={styles.buttonRow}>
          <button style={styles.retryButton} onClick={() => navigate(-1)}>
            Try Again
          </button>
          <button style={styles.cancelButton} onClick={() => navigate('/pricing')}>
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

const styles = {
  overlay: {
    minHeight: '100vh',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: '1rem',
  },
  icon: {
    fontSize: '3rem',
    color: '#e53e3e',
    marginBottom: '0.5rem',
  },
  title: {
    fontSize: '1.5rem',
    marginBottom: '1rem',
    color: '#e53e3e',
  },
  message: {
    marginBottom: '0.5rem',
    color: '#333',
  },
  reason: {
    fontSize: '0.9rem',
    color: '#666',
    marginBottom: '1.5rem',
  },
  buttonRow: {
    display: 'flex',
    gap: '1rem',
    justifyContent: 'center',
  },
  retryButton: {
    backgroundColor: '#e53e3e',
    color: 'white',
    border: 'none',
    padding: '0.5rem 1rem',
    borderRadius: '6px',
    cursor: 'pointer',
  },
  cancelButton: {
    backgroundColor: '#edf2f7',
    color: '#2d3748',
    border: 'none',
    padding: '0.5rem 1rem',
    borderRadius: '6px',
    cursor: 'pointer',
  },
};

export default PaymentFailed;
