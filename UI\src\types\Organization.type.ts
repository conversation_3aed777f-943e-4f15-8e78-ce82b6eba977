export interface OrganizationFeaturesType {
  applicants: number;
  users: number;
  avatars: number;
  createCustomQuestions: number;
  createCustomTests: number;
  createCustomInterviews: number;
  aiQuestions: number;
  aiTests: number;
  aiInterviews: number;
  assignTests: number;
  assignCustomInterviews: number;
  assignInteractiveInterviews: number;
  exportReports: number;
  applicantAssessmentReport: number;
  avatarCloning: number;
}

export interface OrganizationType {
  _id: string;
  name: string;
  email: string;
  bio: string;
  logo: string;
  joinDate: string;
  location: string;
  features: OrganizationFeaturesType;
  subscriptionType: number;
  planId: string;
  subscriptionId: string;
  pricingKey: number;
}

export type OrganizationsList = {
  data: {
    _id: string;
    name: string;
    email: string;
    status: number;
    planId: string;
    createdAt: string;
    lastActive: string;
    engagementStatus: string;
    logo: {};
    planName: string;
  }[];
};

export interface OrganizationsUsers {
  totalUsers: number;
  userStatistics: {
    role: string;
    value: number;
  }[];
}

export interface OrganizationsGrowth {
  applicantCount: number;
  usersCount: number;
  usersGrowth: {
    month: number;
    count: number;
  };
  applicantGrowth: {
    month: number;
    count: number;
  };
}

export interface OrganizationsOverview {
  engagementScore: number;
  applicantCount: number;
}

export interface OrganizationsEngagement {
  interviewCount: number;
  predefinedAssessmentCount: number;
  readyAssessmentCount: number;
  readyAssessmentsGrowth: {
    month: number;
    count: number;
  };
  predefinedAssessmentGrowth: {
    month: number;
    count: number;
  };
  interviewGrowth: {
    month: number;
    count: number;
  };
}

export interface OrganizationsPlanOverview {
  _id: string;
  billingCycle: string;
  autoRenewal: boolean;
  name: string;
  price: number;
  currency: string;
  durationInDays: number;
  discount: number;
  discountedAmount: number;
  couponCode: string;
  usage: {
    assessments: {
      limit: number;
      used: number;
    };
    exports: {
      limit: number;
      used: number;
    };
  };
  percentageUsageOverall: number;
  features: any;
  usageWarning: any;
  endDate: any;
  planSummary: {};
}

export interface OrganizationsOverallUsage {
  totalLimit: number;
  totalUsed: number;
  totalRemaining: number;
  percentageUsed: number;
  nearlyExhaustedCount: number;
}
