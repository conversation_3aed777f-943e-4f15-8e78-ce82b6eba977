import { Spinner } from 'flowbite-react';
import Pagination from './pagination';
import MobileCard from './mobile-card';
import { SidebarFilterPage } from '../../../../../src';
import {
  //TableState, 
  type DataTableProps
} from '../config';
import {
  renderCell,
} from '../helpers'
import DesktopGrid from './desktop-grid';
import Toolbar from './toolbar';
import { Placeholder } from './placeholder';

/* eslint-disable @typescript-eslint/no-explicit-any */
// Design-system shell that renders a table using column defs
function DataTable<T extends Record<string, any>>({
  data,
  columns,
  loading,
  sectionTitle,
  sectionBadgeTitle,
  search,
  filters,
  addButtonLabel,
  onClickAdd,
  state,
  onStateChange,
  pagination,
  filterFeedData,
  setFilters,
  getRowId,
  renderRowCard,
  tableClassName,
  placeholder = {
    title: 'No results found',
    image: 'UI/src/assets/placeholder/NoResults.svg'
  },
  addButtonFeature,
  addButtonPermission,
  addButtonPermissionOperator = 'every',
}: DataTableProps<T>) {

  const mobileCards = (row: T, rowIndex: number) => {
    if (renderRowCard) return renderRowCard(row, rowIndex);
    // Default: map visible columns to TableCard rows
    const rows = columns
      .filter((c) => !c.hideOnMobile && c.meta?.variant !== 'actions')
      .map((c) => ({
        label: c.labelOnCard ?? (typeof c.header === 'string' ? (c.header as string) : c.id ?? ''),
        value: renderCell(row, c, rowIndex),
      }));
    return <MobileCard rows={rows} 
    />;
  };

  return (
    <div className="w-full">
      {/* Loading overlay */}
      {loading && (
        <div className="absolute top-0 bottom-0 left-0 right-0 z-50 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
          <Spinner size="lg" color="purple" />
        </div>
      )}
      {/* Toolbar */}
      <Toolbar
        data={data}
        columns={columns}
        setFilters={setFilters}
        sectionTitle={sectionTitle}
        sectionBadgeTitle={sectionBadgeTitle}
        search={search}
        filters={filters}
        addButtonLabel={addButtonLabel}
        onClickAdd={onClickAdd}
        addButtonFeature={addButtonFeature}
        addButtonPermission={addButtonPermission}
        addButtonPermissionOperator={addButtonPermissionOperator}
      />

      <div className="min-w-full inline-block align-middle">
        {filterFeedData && filterFeedData?.length > 0 && (
          <div className={`hidden 2xl:block w-full max-w-[270px]  `}>
            <SidebarFilterPage
              filterData={{
                filterFeedData,
                setFilters,
              }}
              searchInputField={{ update: search?.update as (value: string) => void, value: search?.value as string }}
            />
          </div>
        )}

        {/* Mobile cards */}
        <div className="flex lg:hidden gap-4 flex-row flex-wrap m-auto">
          {data.map((row, i) => (
            <div key={String(getRowId ? getRowId(row, i) : i)}>{mobileCards(row, i)}</div>
          ))}
        </div>

        {/* Desktop table */}
        <div className="border border-[#DEE2E4] shadow-down-table rounded-[12px]">
          <div className="hidden lg:block">
            <DesktopGrid
              data={data}
              columns={columns}
              setFilters={setFilters}
              onClickAdd={onClickAdd}
              state={state}
              onStateChange={onStateChange}
              getRowId={getRowId}
              renderRowCard={renderRowCard}
              tableClassName={tableClassName}
            />
          </div>

          {/* No data created || No results found */}
          {!data.length && !loading && (
            <div className="my-8">
              <Placeholder
                title={placeholder?.title}
                subTitle={placeholder?.subTitle}
                image={placeholder?.image}
              />
            </div>
          )}

          {/* Pagination */}
          {pagination && (
            <Pagination
              pages={pagination.pages}
              currentPage={pagination.currentPage}
              onPageClick={(page) => pagination.onPageChange(page)}
              onNextClick={pagination.onNext || (() => pagination.onPageChange(pagination.currentPage + 1))}
              onPreviousClick={pagination.onPrevious || (() => pagination.onPageChange(pagination.currentPage - 1))}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default DataTable;
