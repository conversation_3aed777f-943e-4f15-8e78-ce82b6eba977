import React, { FC, useEffect, useState } from 'react';
import { RoleListItem } from './role-list-item';

// Components
import { Select, TextInput } from 'src';
import { Dialog, Button } from 'UI';
import { useValidate, Form, initializeForm, RootState, setFieldValue, useAppDispatch, useAppSelector, Regex, Api } from 'UI/src';
import { fetchRole, createRole, updateRole, searchRolePermissions } from 'UI/src/middlewares/Roles.middleware';
import { setErrorNotify, setNotifyMessage } from 'UI';
import { useFormik } from 'formik';

// Types
type RolesCreationDialogProps = {
  onClose: () => void;
  id: string | null;
  closeCreateDialogVisibility: () => void;
  onCreate: () => void;
};

type FormState = {
  name: string;
  permissions: string[];
};

type PermissionOption = {
  name: string;
};

export const RolesCreationDialog: FC<RolesCreationDialogProps> = ({ onClose, id, closeCreateDialogVisibility, onCreate }) => {
  // State
  const [child, setChild] = useState<PermissionOption[]>([]);

  // Hooks
  const dispatch = useAppDispatch();
  const { isRequired, isNotSpaces, minLength, maxLength, validateRegex } = useValidate();
  const formik = useFormik({
    initialValues: {
      name: '',
      permissions: [],
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  const form = useAppSelector((state: RootState) => state.form.data);

  // State
  const [loading, setLoading] = useState<boolean>(false);

  // Methods
  const handleGet = async () => {
    if (!id) return;
    try {
      const result = await dispatch(fetchRole(id)).unwrap();
      console.log(`roles/single/${id}`, result);
      dispatch(initializeForm(result));
    } catch (error: any) {
      dispatch(setErrorNotify(error?.message || 'Failed to fetch role'));
    }
  };
  //    const handleInsert = async () => {
  const handleInsert = async (formData: Record<string, any>) => {
    const data = formData as FormState;
    try {
      await dispatch(createRole(form)).unwrap();
      onCreate();
      dispatch(setNotifyMessage('Role added successfully!'));
      closeCreateDialogVisibility();
    } catch (error: any) {
      dispatch(setErrorNotify(error?.message || 'Failed to create role'));
    }
  };
  const handleUpdate = async () => {
    if (!id) return;
    try {
      await dispatch(updateRole({ id, data: form })).unwrap();
      onCreate();
      dispatch(setNotifyMessage('Role updated successfully!'));
      closeCreateDialogVisibility();
    } catch (error: any) {
      dispatch(setErrorNotify(error?.message || 'Failed to update role'));
    }
  };

  const handleSearch = async (keyword: string) => {
    try {
      const result = await Api.get('roles/single/permissions/search', {
        keyword: keyword,
        exclude: form.permissions,
      });

      const transformedPermissions: PermissionOption[] = result.data.map((permission: string) => ({ name: permission }));
      setChild(transformedPermissions);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  // On Mount
  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, [id]);

  return (
    <Dialog isOpen size="md" title={id ? 'Update Role' : 'Create Role'} onClose={onClose}>
      {/* Creation Form */}
      <Form className="space-y-4" onSubmit={id ? handleUpdate : handleInsert}>
        <TextInput
          name="name"
          label="Role Name"
          value={form.name}
          placeholder="Enter role name"
          onChange={(value: any) => dispatch(setFieldValue({ path: 'name', value }))}
          validators={[isRequired(), minLength(2), maxLength(50), isNotSpaces(), validateRegex(Regex.name)]}
        />
        <div className="col-span-1 md:col-span-3 space-y-4">
          <Select
            filterOnly
            name="permissions"
            label="Assign Permissions"
            placeholder="Select or search for permissions"
            onSearch={handleSearch}
            lookup={child}
            optionValueKey="name"
            optionLabelKey="name"
            multiSelect={true}
            onChange={(permissions: any) => (
              dispatch(
                initializeForm({
                  ...form,
                  permissions: [...form.permissions, permissions],
                })
              ),
              setChild([])
            )}
            value={form.permissions.length}
            validators={[isRequired()]}
          />
          <p className="text-sm font-medium text-gray-900 dark:text-white">Selected Permissions ({form.permissions.length})</p>
          <ul
            className={`${
              form.permissions.length > 0 && 'border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600'
            } text-sm font-medium text-gray-900 bg-white dark:text-white`}
          >
            {form.permissions.map((permissionId: string, index: number) => (
              <RoleListItem
                key={index}
                id={id || ''}
                onRemove={(id) =>
                  dispatch(
                    initializeForm({
                      ...form,
                      permissions: form.permissions.filter((target: any) => id !== target),
                    })
                  )
                }
                isLastItem={index === form.permissions.length - 1}
              />
            ))}
          </ul>
        </div>

        <div className="pt-2 space-y-4">
          <Button colorType="primary" type="submit" label={id ? 'Update' : 'Create'} className="w-full " loading={loading} disabled={loading} />
        </div>
      </Form>
    </Dialog>
  );
};
