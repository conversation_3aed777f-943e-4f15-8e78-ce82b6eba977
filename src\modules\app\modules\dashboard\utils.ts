import { Dispatch, SetStateAction } from 'react';
import { Api } from 'UI/src';
import { AssessmentsCount, LookupsCategory, setErrorNotify, useAppDispatch, UsersList } from 'UI/src';
import { AssessmentsStatistics, QuizzesStatistic } from 'UI/src/types/Dashboard.type';
// In case we need it
export const handleGetAssessments = async ({
  setAssessments,
  calcStart,
  calcEnd,
}: {
  setAssessments: (data: AssessmentsStatistics) => void;
  calcStart: Date | number;
  calcEnd: Date | number;
}): Promise<void> => {
  const dispatch = useAppDispatch();
  try {
    let startISO = new Date(calcStart).toISOString();
    let endISO = new Date(calcEnd).toISOString();
    if (startISO === '1970-01-01T00:00:00.000Z') {
      startISO = '';
    } else {
      startISO = startISO;
    }
    if (endISO === '1970-01-01T00:00:00.000Z') {
      endISO = '';
    } else {
      endISO = endISO;
    }

    const response = await Api.get<AssessmentsStatistics>('dashboard/assessment/statistics', {
      params: {
        start: startISO && endISO && startISO,
        end: startISO && endISO && endISO,
      },
    });

    console.log('dashboard/assessment/statistics', response.data);
    setAssessments(response.data);
  } catch (error: any) {
    dispatch(setErrorNotify(error.response.data.message));
  }
};

export const handleGetApplicants = async ({
  setApplicant,
  calcStart,
  calcEnd,
}: {
  setApplicant: (data: UsersList) => void;
  calcStart: Date | number;
  calcEnd: Date | number;
}): Promise<void> => {
  try {
    let startISO = new Date(calcStart).toISOString();
    let endISO = new Date(calcEnd).toISOString();
    if (startISO === '1970-01-01T00:00:00.000Z') {
      startISO = '';
    } else {
      startISO = startISO;
    }
    if (endISO === '1970-01-01T00:00:00.000Z') {
      endISO = '';
    } else {
      endISO = endISO;
    }

    const response = await Api.get('/dashboard/list/applicants', {
      params: {
        start: startISO && endISO && startISO,
        end: startISO && endISO && endISO,
      },
    });

    console.log('/dashboard/list/applicants', response.data);
    setApplicant(response.data);
  } catch (error: any) {
    const dispatch = useAppDispatch();
    dispatch(setErrorNotify(error.response.data.message));
  }
};

// Users
export const handleGetUsers = async ({
  setUsers,
  calcStart,
  calcEnd,
}: {
  setUsers: (data: UsersList) => void;
  calcStart: Date | number;
  calcEnd: Date | number;
}): Promise<void> => {
  try {
    let startISO: string | null = new Date(calcStart).toISOString();
    let endISO: string | null = new Date(calcEnd).toISOString();
    if (startISO === '1970-01-01T00:00:00.000Z') {
      startISO = '';
    } else {
      startISO = startISO;
    }
    if (endISO === '1970-01-01T00:00:00.000Z') {
      endISO = '';
    } else {
      endISO = endISO;
    }

    const response = await Api.get<UsersList>('/dashboard/list/users', {
      params: {
        start: startISO && endISO && startISO,
        end: startISO && endISO && endISO,
      },
    });

    console.log('/dashboard/list/users', response.data);
    setUsers(response.data);
  } catch (error: any) {
    const dispatch = useAppDispatch();
    dispatch(setErrorNotify(error.response.data.message));
  }
};

// Tests
export const handleGetTests = async ({
  setTest,
  setTestByCategory,
  calcStart,
  calcEnd,
}: {
  setTest: (data: QuizzesStatistic) => void;
  setTestByCategory: (data: any) => void;
  calcStart: Date | number;
  calcEnd: Date | number;
}): Promise<void> => {
  try {
    let startISO: string | null = new Date(calcStart).toISOString();
    let endISO: string | null = new Date(calcEnd).toISOString();
    if (startISO === '1970-01-01T00:00:00.000Z') {
      startISO = '';
    } else {
      startISO = startISO;
    }
    if (endISO === '1970-01-01T00:00:00.000Z') {
      endISO = '';
    } else {
      endISO = endISO;
    }

    const response = await Api.get<QuizzesStatistic>('/dashboard/list/quizzes', {
      params: {
        start: startISO && endISO && startISO,
        end: startISO && endISO && endISO,
      },
    });
    console.log('/dashboard/list/quizzes', response.data);

    const mapQuizzesToCategory = (quizzesByCategory: any[]): Record<string, any> => {
      const result: Record<string, any> = {};

      quizzesByCategory.forEach((category: any) => {
        const categoryName = category._id;
        const subcategories = category.subcategories.map((subcategory: any) => {
          let Fresh = 0,
            Junior = 0,
            MidLevel = 0,
            Senior = 0;

          // Iterate over each quiz in the subcategory and assign based on difficulty
          subcategory.quizzes.forEach((quiz: { difficulty: number }) => {
            const difficulty = quiz.difficulty;
            if (difficulty === 1) {
              Fresh += 1;
            } else if (difficulty === 2) {
              Junior += 1;
            } else if (difficulty === 3) {
              MidLevel += 1;
            } else if (difficulty >= 4) {
              Senior += 1;
            }
          });

          // Create the object for this subcategory
          return {
            name: subcategory.subcategoryName,
            Fresh: Fresh,
            Junior: Junior,
            MidLevel: MidLevel,
            Senior: Senior,
          };
        });

        result[categoryName] = subcategories;
      });

      return result;
    };

    setTestByCategory(mapQuizzesToCategory(response.data?.quizzesByCategory));
    // @TODO: make sure that interviewsByCategory is correct after endpoint is ready
    setTest(response.data);
  } catch (error: any) {
    const dispatch = useAppDispatch();
    dispatch(setErrorNotify(error.response.data.message));
  }
};

// Interviews
export const handleGetInterview = async ({ setInterview, setInterviewByCategory, setSubmission, calcStart, calcEnd }: any) => {
  try {
    let startISO = new Date(calcStart).toISOString();
    let endISO = new Date(calcEnd).toISOString();
    if (startISO === '1970-01-01T00:00:00.000Z') {
      startISO = '';
    } else {
      startISO = startISO;
    }
    if (endISO === '1970-01-01T00:00:00.000Z') {
      endISO = '';
    } else {
      endISO = endISO;
    }

    const response = await Api.get<AssessmentsStatistics>('/dashboard/list/assessments', {
      params: {
        start: startISO && endISO && startISO,
        end: startISO && endISO && endISO,
      },
    });
    console.log('/dashboard/list/assessments', response.data);

    const mapInterviewToCategory = (interviewByCategory: []) => {
      const result: { [key: string]: any } = {};

      interviewByCategory?.forEach((category: { _id: string; subcategories: any[] }) => {
        const categoryName = category._id;
        const subcategories = category.subcategories.map((subcategory) => {
          let Fresh = 0,
            Junior = 0,
            MidLevel = 0,
            Senior = 0;

          // Iterate over each quiz in the subcategory and assign based on difficulty
          subcategory.interviews.forEach((quiz: { difficulty: number }) => {
            const difficulty = quiz.difficulty;
            if (difficulty === 1) {
              Fresh += 1;
            } else if (difficulty === 2) {
              Junior += 1;
            } else if (difficulty === 3) {
              MidLevel += 1;
            } else if (difficulty >= 4) {
              Senior += 1;
            }
          });

          // Create the object for this subcategory
          return {
            name: subcategory.subcategoryName,
            Fresh: Fresh,
            Junior: Junior,
            MidLevel: MidLevel,
            Senior: Senior,
          };
        });

        result[categoryName] = subcategories;
      });

      return result;
    };

    setInterviewByCategory(mapInterviewToCategory(response.data.interviewStatistic.interviewByCategory));

    setInterview(response.data.interviewStatistic);
    setSubmission(response.data.submissionsStatistic);
  } catch (error: any) {
    const dispatch = useAppDispatch();
    dispatch(setErrorNotify(error.response.data.message));
  }
};

// Assessment Count
export const handleGetAssessmentCount = async ({
  setAssessmentCount,
  calcStart,
  calcEnd,
}: {
  setAssessmentCount: Dispatch<SetStateAction<AssessmentsCount>>;
  calcStart: Date;
  calcEnd: Date;
}) => {
  const dispatch = useAppDispatch();
  try {
    let startISO = new Date(calcStart).toISOString();
    let endISO = new Date(calcEnd).toISOString();
    if (startISO === '1970-01-01T00:00:00.000Z') {
      startISO = '';
    } else {
      startISO = startISO;
    }
    if (endISO === '1970-01-01T00:00:00.000Z') {
      endISO = '';
    } else {
      endISO = endISO;
    }

    const response = await Api.get<AssessmentsCount>('dashboard/assessment/count', {
      params: {
        start: startISO && endISO && startISO,
        end: startISO && endISO && endISO,
      },
    });
    console.log('dashboard/assessment/count', response.data);
    setAssessmentCount(response.data);
  } catch (error: any) {
    dispatch(setErrorNotify(error.response?.data?.message));
  }
};

// Categories
export const handleGetCategories = async ({ setCategories }: { setCategories: Dispatch<SetStateAction<LookupsCategory>> }) => {
  const dispatch = useAppDispatch();
  try {
    const response = await Api.get<LookupsCategory>('lookups/category', {});
    console.log('lookups/category', response.data);
    setCategories(response.data);
  } catch (error: any) {
    dispatch(setErrorNotify(error.response?.data?.message || 'Failed to fetch categories'));
  }
};
