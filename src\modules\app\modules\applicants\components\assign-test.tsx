// React
import { useState, useEffect, Dispatch, SetStateAction } from 'react';

// Core
import { Icon, ToggleFilter, Drawer, NoDataMatches } from 'src';

// Components
import { AssignTestListData } from './assign-test-list-data';
import { TestCreatedSucessfully } from './test-created-sucessfully';
import { AssignTestPlaceHolder } from './assign-test-placeholder';
import { TimeSettingsDialog } from './assign-time-settings';
import { Api, useFetchList, useAppDispatch, Placeholder } from 'UI/src';
import { setErrorNotify } from 'UI';
import { Applicant, ApplicantDetails } from './applicant-data';

interface AssignTestProps {
  setAssignTestVisibility: (value: boolean) => void;
  applicantDetails: Applicant | null;
  setApplicantDetails: Dispatch<SetStateAction<Applicant | null>>;
  refreshMainTable: () => void;
}

export const AssignTest: React.FC<AssignTestProps> = ({
  setAssignTestVisibility,
  applicantDetails,
  setApplicantDetails,
  refreshMainTable = () => {},
}) => {
  // State
  const [expandedAll, setExpandedAll] = useState<{ [key: string]: boolean }>({});
  const [quizUrl, setQuizUrl] = useState('');
  const [submissionId, setSubmissionId] = useState('');
  const [isTestCreatedSucessfullyVisible, setTestCreatedSucessfullyVisibilty] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [dueDate, setDueDate] = useState(() => {
    const result = new Date(startDate);
    result.setDate(result.getDate() + 1);
    return result;
  });
  const [extraTime, setExtraTime] = useState<number>(0);
  const [isTimeSettingsVisible, setTimeSettingsVisible] = useState(false);
  const [backupList, setBackupList] = useState([]);
  const [submitLoading, setSubmitLoading] = useState(false);
  const type = 'test';

  // Hooks
  const dispatch = useAppDispatch();
  const { ready, loading, setLoading, list, count, filters, search, pagination, refresh } = useFetchList('templates/list?type=test', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: {
      difficulty: {
        label: 'Difficulty',
        enum: 'QuizDifficulty',
      },

      ...(applicantDetails?.track
        ? {}
        : {
            category: {
              label: 'Category',
              lookup: 'category',
            },
          }),

      subCategory: {
        label: 'Sub Category',
        lookup: 'subcategory',
        parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: applicantDetails?.track },
      },
    },
    id: applicantDetails?._id,
  });

  // Computed
  const { page, size } = pagination;
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
  const isPaginationActive = !!pagination.update;

  // Methods
  const handleExpandAll = () => {
    const allExpanded = list.every((data: { _id: string }) => expandedAll[data._id]);
    if (allExpanded) setExpandedAll({});
    else list.map((data: { _id: string }) => setExpandedAll((prev) => ({ ...prev, [data._id]: true })));
  };

  useEffect(() => {
    if (backupList.length === 0 && list.length > 0) {
      setBackupList(list);
    }
  }, [list, backupList.length]);

  const onClose = () => {
    setAssignTestVisibility(false);
    setApplicantDetails(null);
  };

  const handleSubmit = async () => {
    if (!submissionId) {
      dispatch(setErrorNotify('Please select test first'));
    } else if (!startDate) {
      dispatch(setErrorNotify('Please select start date'));
    } else if (!dueDate) {
      dispatch(setErrorNotify('Please select end date'));
    } else {
      try {
        // setLoading(true); if needed
        // Use separate loading state for submit
        setSubmitLoading(true);

        const payload: any = {
          quizId: submissionId,
          // willSendEmail: false,
          otherTest: true,
          dueDate: dueDate,
          startDate: startDate,
        };
        if (extraTime >= 1) payload.exceededTime = extraTime; // @FIXME: payload.exceededTime is not defined in the payload
        // @TODO:Handle multi applicant assignment
        if (applicantDetails?._id) {
          payload.applicantId = [applicantDetails._id]; // @FIXME: payload.applicantId is not defined in the payload
        }
        const response = await Api.post('submissions/single', payload);
        setQuizUrl(response.data.quizUrl);
        setTestCreatedSucessfullyVisibilty(true);
      } catch (error: any) {
        dispatch(setErrorNotify(error.response.data.message));
      } finally {
        // setLoading(false);  if needed
        // Use separate loading state for submit
        setSubmitLoading(false);
      }
    }
  };

  const noDataFound = {
    icon: 'healthicons:i-exam-multiple-choice-outline',
    message: 'No tests created yet',
  };

  return (
    <Drawer onClose={onClose}>
      <Drawer.SingleView>
        <Drawer.Header
          headerLabel={applicantDetails?.email ? 'Assign Test' : 'Generate Test Link'}
          headerSubLabel={applicantDetails?.email}
          onClose={onClose}
          className="border-b border-[#E5E7EB] pb-2"
        />
        <div className="h-full overflow-y-auto space-y-5">
          <Drawer.Body.DatePicker
            startDate={startDate}
            dueDate={dueDate}
            extraTime={extraTime}
            setExtraTime={setExtraTime as () => void}
            setTimeSettingsVisible={setTimeSettingsVisible as () => void}
            type={type}
          />

          <div className="space-y-2 bg-white dark:bg-darkBackgroundCard">
            <h2 className="text-base font-medium">
              {applicantDetails?.trackName} Tests (<span className="font-semibold">{count}</span>)
            </h2>
            <div className="flex flex-col xslg:flex-row xslg:items-center gap-3 justify-between">
              <div className="flex grow gap-3">
                {/* Search bar - Filters Button */}
                <div className="relative grow">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search for tests by name"
                    className="bg-gray-white border truncate border-gray-200 text-gray-800 text-[13.5px] rounded-lg block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0 focus:border-gray-300"
                    value={search.value}
                    onInput={(e: React.ChangeEvent<HTMLInputElement>) => search.update(e.target.value)}
                  />
                </div>

                <ToggleFilter filters={filters} />
              </div>

              <div
                className="hover:underline focus:outline-none text-primaryPurple text-opacity-90 font-medium text-sm cursor-pointer"
                onClick={handleExpandAll}
              >
                {list.every((data: { _id: string }) => expandedAll[data._id]) ? 'Collapse all sections' : 'Expand all sections'}
              </div>
            </div>
          </div>

          {list?.length ? (
            <Drawer.Body className="space-y-4">
              {!ready && <AssignTestPlaceHolder />}

              <div className="space-y-3">
                {list.map((singleQuiz, index) => (
                  <AssignTestListData
                    key={index}
                    singleQuiz={singleQuiz}
                    expandedAll={expandedAll}
                    setExpandedAll={setExpandedAll}
                    setSubmissionId={setSubmissionId}
                  />
                ))}
              </div>
            </Drawer.Body>
          ) : (
            <div className="flex justify-center align-middle   min-h-[calc(70vh-4rem)] items-center ">
              <div className=" w-2/4 space-y-2  ">
                {/* No data created || No results found */}
                {backupList.length > 0 ? (
                  <NoDataMatches message="No results found" />
                ) : (
                  // <NoDataFound noDataFound={noDataFound} width="70" height="70" />
                  <Placeholder
                    image="/UI/src/assets/placeholder/TestImagePlaceholder.svg"
                    title="No tests created yet"
                    subTitle="Start by creating a test to evaluate applicants' skills."
                  />
                )}
              </div>
            </div>
          )}

          {list?.length > 0 && (
            <Drawer.Footer
              className="sticky top-full"
              isPaginationActive={isPaginationActive}
              paginationData={{
                showingText: showingText,
                count: count,
                size: size,
                onPageChange: pagination?.update,
                currentPage: page,
                pagesCount: pagesCount,
              }}
            />
          )}
        </div>

        {list?.length > 0 && (
          <Drawer.Footer>
            <Drawer.Footer.Button label="Cancel" disabled={submitLoading} tertiary onClick={onClose} />
            <Drawer.Footer.Button
              label={applicantDetails?.email ? 'Assign Test' : 'Generate Link'}
              // if needed, add loading={loading} to disable the button
              loading={submitLoading}
              disabled={!submissionId || submitLoading}
              onClick={handleSubmit}
              mainButton
            />
          </Drawer.Footer>
        )}
      </Drawer.SingleView>

      {/* Test Ceated Sucessfully Visible */}
      {isTestCreatedSucessfullyVisible && (
        <TestCreatedSucessfully
          assignment={applicantDetails?.email ? true : false}
          quizUrl={quizUrl}
          onClose={() => {
            applicantDetails?.email && refreshMainTable();
            onClose();
          }}
        />
      )}

      {/* Time Setting */}
      {isTimeSettingsVisible && (
        <TimeSettingsDialog
          onClose={() => setTimeSettingsVisible(false)}
          startDate={startDate}
          setStartDate={setStartDate}
          dueDate={dueDate}
          setDueDate={setDueDate}
          type={type}
        />
      )}
    </Drawer>
  );
};
