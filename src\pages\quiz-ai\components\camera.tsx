import { useRef, useEffect, useState, memo } from 'react';
import { Icon } from 'src';

const Camera = ({ isSpeaking }: { isSpeaking: boolean }) => {
  const webcamRef = useRef(null);
  const [cameraAvailable, setCameraAvailable] = useState(true);
  // const screen = useScreenSize();

  useEffect(() => {
    // Get access to the user's webcam
    async function startWebcam() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });
        if (webcamRef.current) {
          (webcamRef.current as any).srcObject = stream;
          setCameraAvailable(true);
        }
      } catch (error) {
        console.error('Error accessing webcam:', error);
        setCameraAvailable(false);
      }
    }
    startWebcam();
  }, []);

  return (
    <div className="relative h-full w-full ">
      {cameraAvailable ? (
        <video ref={webcamRef} className=" rounded-lg md:rounded-2xl w-full h-full object-cover" autoPlay muted />
      ) : (
        <div
          className={`flex rounded-lg md:rounded-2xl items-center justify-center w-full border-2 dark:border-transparent h-full dark:bg-[#212223] bg-[#F4F4F4] border-[#ebecf4] transition-all duration-300 ${
            isSpeaking ? '!border-primaryPurple' : ''
          }`}
        >
          <div className="relative flex justify-center w-[30%] max-w-[160px] items-center aspect-square bg-[#D9dbdf] bg-opacity-60 dark:bg-gray-700 text-white dark:text-gray-400 rounded-full">
            <svg width="100%" height="100%" viewBox="0 0 84 84" fill="none" xmlns="http://www.w3.org/2000/svg">
              {/* Main Profile Circle */}
              <circle cx="42" cy="42" r="41" fill="none" stroke="white" strokeWidth="1" opacity="0.4" />

              {/* Profile Head and Body */}
              <path d="M42 24C34 24 28 30 28 38C28 45.8 34.5 52 42 52C49.5 52 56 45.8 56 38C56 30 50 24 42 24Z" fill="white" />

              {/* Profile Shoulders */}
              <path d="M70 72C61.5 79 50 83 42 83C34 83 22.5 79 14 72C15 67.5 18 63.5 22 60C33 52 51 52 62 60C66 63.5 69 67.5 70 72Z" fill="white" />
            </svg>
          </div>
        </div>
      )}
      {/* <div className="absolute bottom-1 left-3 rounded-md dark:bg-black/30 bg-[#EAEAEA] bg-opacity-50 dark:text-[#f9f9f9] text-[#667085] py-1 px-3 dark:shadow-lg">
        <p className="font-semibold text-sm md:text-base">{submissionAi.applicant.name}</p>
        <p className="text-xs dark:text-gray-300 md:text-sm">{submissionAi.applicant.trackName}</p>
      </div> */}
    </div>
  );
};

export default memo(Camera);
