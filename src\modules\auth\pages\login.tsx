import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { GoogleLogin, GoogleOAuthProvider, CredentialResponse } from '@react-oauth/google';

import { Api, initializeForm, Regex, RootState, setFieldValue, useAppSelector, useValidate } from 'UI/src';

import { setNotifyMessage, setErrorNotify } from 'UI';
// Core
import { Form, TextInput, Button, Icon, Logo } from 'src';
import { useAppDispatch, updateUser, useUserPermissions, CookieStorage } from 'UI';
import { VITE_GOOGLE_CLIENT_ID } from 'UI/src/configs/api';
import { useFormik } from 'formik';

export const LoginPage = () => {
  const dispatch = useAppDispatch();
  const { handleGetUserRole } = useUserPermissions();
  // State
  const [loading, setLoading] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const formik = useFormik({
    initialValues: {
      email: '',
      password: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });
  const form = useAppSelector((state: RootState) => state.form.data);

  // Hooks
  const { isRequired, minLength, validateRegex } = useValidate();
  const navigate = useNavigate();

  const handleSubmit = async () => {
    setLoading(true);

    try {
      const { data } = await Api.post('auth/login', form);
      navigate('/app');
      dispatch(setNotifyMessage('Login succeeded!'));
      dispatch(updateUser(data));
      dispatch(handleGetUserRole);
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.statusCode === 401 ? 'Invalid Email or Password' : error.response.data.message));
      setLoading(false);
    }
  };

  const clientId = `${VITE_GOOGLE_CLIENT_ID}.apps.googleusercontent.com`;

  const handleGoogleAccountLoginSuccess = async (credentialResponse: CredentialResponse) => {
    try {
      const { credential } = credentialResponse;

      const { data } = await Api.post('auth/google-login', { idToken: credential });

      if (data?.access_token) {
        // @FIXME: Fix local storage
        CookieStorage.setItem('userData', JSON.stringify(data));
        dispatch(updateUser(data));
        dispatch(handleGetUserRole);
        navigate('/app');
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message || 'Login failed'));
    } finally {
      setLoading(false);
    }
  };

  const handleShow = () => {
    setShowPassword(!showPassword);
  };

  return (
    <section className="bg-gray-50 dark:bg-gray-900 h-screen">
      <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto h-full lg:py-0">
        <div className="w-full bg-white rounded-xl shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
          <div className="p-6 space-y-4 md:space-y-8 sm:p-8">
            <div className="flex flex-col gap-5">
              <a
                onClick={() => navigate('/')}
                className="flex items-center justify-start text-2xl font-semibold text-gray-900 dark:text-white cursor-pointer"
              >
                <Logo className="h-8" />
              </a>
              <h1 className="text-lg font-semibold leading-tight tracking-tight text-[#4E5E82]">Login To Your Account</h1>
            </div>
            <Form className="flex max-w-md flex-col gap-5" onSubmit={handleSubmit}>
              <TextInput
                name="email"
                // TODO: Markos
                label="Email"
                disabled={loading}
                value={form.email}
                onChange={(value: string) => dispatch(setFieldValue({ path: 'email', value }))}
                validators={[isRequired(), validateRegex(Regex.email)]}
                customPlaceholder={
                  <span className="text-gray-400 dark:text-gray-500 flex gap-2">
                    <Icon icon="mdi:email-outline" width="20" /> Enter
                  </span>
                }
              />

              <div className="w-full relative">
                <TextInput
                  name={'password'}
                  // TODO: Markos
                  label="Password"
                  placeholder="Enter"
                  type={showPassword ? 'text' : 'password'}
                  disabled={loading}
                  value={form.password}
                  onChange={(value: string) => dispatch(setFieldValue({ path: 'password', value }))}
                  validators={[isRequired(), minLength(3)]}
                  className="!w-full block "
                  rightIcon={() => {}}
                />

                <span className="flex justify-around items-center absolute -right-9 top-7  cursor-pointer" onClick={() => handleShow()}>
                  <Icon
                    className="mr-10 p-5 w-8 h-8 rounded-md cursor-pointer  text-gray-500 dark:text-gray-400"
                    width="25"
                    icon={!showPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                  />
                </span>
              </div>

              <Button type="submit" label="Login" disabled={loading} loading={loading} className="mt-4 cursor-pointer" gradientMonochrome="purple" />

              <div className="relative flex items-center px-12">
                <hr className="flex-grow border-gray-300 dark:border-gray-600" />
                <span className="mx-4 text-sm text-gray-500 dark:text-gray-400">Or log in with</span>
                <hr className="flex-grow border-gray-300 dark:border-gray-600" />
              </div>

              {/* Google Sign In */}
              <div className="flex justify-center">
                <GoogleOAuthProvider clientId={clientId}>
                  <div className="[&_.nsm7Bb-HzV7m-LgbsSe-BPrWId]:hidden [&_.nsm7Bb-HzV7m-LgbsSe-Bz112c]:!mr-0">
                    <GoogleLogin
                      onSuccess={handleGoogleAccountLoginSuccess}
                      onError={() => dispatch(setErrorNotify('Google login failed'))}
                      shape="circle"
                      size="large"
                    />
                  </div>
                </GoogleOAuthProvider>
              </div>

              <p className="text-center text-md text-gray-500 dark:text-gray-300">
                Don't have an account?{' '}
                <span onClick={() => navigate('/auth/register')} className="text-[#743AF5] cursor-pointer hover:underline">
                  Sign Up
                </span>
              </p>
            </Form>
          </div>
        </div>
      </div>
    </section>
  );
};
