// Core
import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, But<PERSON> } from 'src';

// Components
import { CurrentPlanSummary } from './current-plan-summary';
import { UsersCard } from './users-card';
import { TicketsCard } from './tickets-card';
import { FeatureEngagement } from './feature-engagement';
import { OrganizationGrowthIndividual } from './organization-growth-individual';
import { useParams } from 'react-router-dom';
import { OrganizationsOverview, useAppDispatch, Api } from 'UI/src';
import { setErrorNotify } from 'UI';

export const OverviewProfile = ({ selectedTab }: { selectedTab: { setActiveTab: (value: number) => void } }) => {
  const [organizationAtRiskData, setOrganizationAtRiskData] = useState(null);
  const [selectedOrganizationId, setSelectedOrganizationId] = useState(null);
  const [organizationOverview, setOrganizationOverview] = useState<() => void>();
  const [organizationData, setOrganizationData] = useState<OrganizationsOverview>();
  const dispatch = useAppDispatch();
  const { id } = useParams();

  const handleGet = async () => {
    try {
      const response = await Api.get('superAdmin/organizations/risk', {});
      console.log('superAdmin/organizations/risk', response.data);
      setOrganizationAtRiskData(response.data);
      if (response.data && response.data.length > 0) {
        setSelectedOrganizationId(response.data[0]._id);
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  const getOrganizationOverview = async (data: any) => {
    try {
      const response = await Api.get<OrganizationsOverview>(`organizations/overview/${id}`, {});
      console.log(`organizations/overview/${id}`, response.data);
      setOrganizationData(response.data);

      const formatedData = () => {
        const data = [
          {
            id: 1,
            title: 'Applicants',
            icon: (
              <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M10.375 14.5167L12.0625 15.9453L16 12.612M12.7188 10.4468C12.4424 10.3049 12.136 10.2035 11.8097 10.1513C11.7389 10.1401 11.6639 10.132 11.5789 10.1262C11.3678 10.112 11.2622 10.1049 11.1213 10.1142C10.9749 10.1239 10.8932 10.1367 10.7518 10.1722C10.6158 10.2063 10.371 10.3049 9.88112 10.5021C9.17416 10.7868 8.38909 10.9453 7.5625 10.9453C6.73591 10.9453 5.95087 10.7868 5.24389 10.5021C4.75402 10.3049 4.50908 10.2062 4.3731 10.1721C4.23175 10.1367 4.14984 10.1239 4.00338 10.1141C3.86248 10.1048 3.75699 10.112 3.54602 10.1263C3.45972 10.1321 3.38359 10.1404 3.31148 10.152C2.15727 10.3377 1.25394 11.1406 1.04498 12.1666C1 12.3875 1 12.6524 1 13.1821V14.612C1 15.0787 1 15.3121 1.10218 15.4903C1.19207 15.6471 1.33548 15.7746 1.51188 15.8545C1.71243 15.9453 1.97495 15.9453 2.5 15.9453H7.09375M11.3125 4.27865C11.3125 6.1196 9.63353 7.61198 7.5625 7.61198C5.49143 7.61198 3.8125 6.1196 3.8125 4.27865C3.8125 2.4377 5.49143 0.945312 7.5625 0.945312C9.63353 0.945312 11.3125 2.4377 11.3125 4.27865Z"
                  stroke="#702DFF"
                  strokeOpacity="0.69"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            ),
            iconColor: 'bg-[#e4e4ff]',
            count: response?.data?.applicantCount,
          },
          {
            id: 2,
            title: 'Engagement Overview',
            icon: (
              <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M2.20833 10.6667H12.2083C12.5765 10.6667 12.875 10.9651 12.875 11.3333C12.875 11.7015 12.5765 12 12.2083 12H1.54167C1.17348 12 0.875 11.7015 0.875 11.3333V0.666667C0.875 0.298477 1.17348 0 1.54167 0C1.90986 0 2.20833 0.298477 2.20833 0.666667V10.6667Z"
                  fill="#4AD991"
                />
                <path
                  opacity="0.5"
                  d="M4.69209 7.78802C4.44027 8.05663 4.01838 8.07024 3.74977 7.81842C3.48116 7.5666 3.46755 7.14471 3.71937 6.8761L6.21937 4.20943C6.46291 3.94966 6.86761 3.92713 7.13848 4.15827L9.11163 5.84203L11.6825 2.58563C11.9106 2.29664 12.3298 2.24732 12.6188 2.47547C12.9078 2.70362 12.9571 3.12284 12.729 3.41182L9.72899 7.21182C9.49466 7.50863 9.06065 7.55132 8.77298 7.30585L6.75695 5.5855L4.69209 7.78802Z"
                  fill="#4AD991"
                />
              </svg>
            ),
            iconColor: 'bg-[#d9f7e7]',
            percentage: response?.data?.engagementScore ?? 0,
            rating: 'down',
            ratingValue: 1.5,
          },
          {
            id: 3,
            title: 'Recruitment Cycle',
            icon: (
              <svg width="15" height="17" viewBox="0 0 15 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M6.49529 10.1551H8.76235C10.7289 10.1551 12.2967 8.62607 12.2967 6.78248C12.2967 6.18029 12.2967 4.44506 12.2967 3.86157C12.2967 2.00156 10.7111 0.488281 8.76235 0.488281C5.99976 0.488281 7.07238 0.488281 6.49529 0.488281C4.54643 0.488281 2.96094 2.00156 2.96094 3.86157C2.96094 4.47122 2.96094 6.09582 2.96094 6.78248C2.96094 8.62392 4.52544 10.1551 6.49529 10.1551ZM8.76235 1.43601C10.0623 1.43601 11.3035 2.38558 11.3035 3.98541H10.1601C8.58987 3.98541 7.27088 2.9 7.03087 1.43601H8.76235ZM3.9541 3.86157C3.9541 2.70679 4.81591 1.70226 6.00943 1.481C5.89343 2.60406 5.064 3.54541 3.9541 3.86656V3.86157ZM3.9541 4.84443C5.08227 4.6063 6.02697 3.8991 6.5568 2.9551C7.27929 4.14684 8.62874 4.93314 10.1601 4.93314H11.3035V6.78248C11.3035 8.1112 10.1728 9.20741 8.76235 9.20741H6.49529C5.08035 9.20741 3.9541 8.10791 3.9541 6.78248V4.84443Z"
                  fill="#2B57D6"
                  fillOpacity="0.76"
                />
                <path
                  d="M9.667 11.0179C9.6668 11.0179 9.66667 11.0178 9.66647 11.0177C9.66627 11.0177 9.66601 11.0176 9.66581 11.0175C8.42621 10.6972 6.82449 10.6973 5.58509 11.0177C5.58489 11.0178 5.58476 11.0179 5.58456 11.0179C2.47118 11.8204 0.296875 14.5326 0.296875 17.6139C0.296875 17.8756 0.519212 18.0878 0.793458 18.0878C1.08955 18.0878 14.162 18.0878 14.4581 18.0878C14.7323 18.0878 14.9547 17.8756 14.9547 17.6139C14.9547 14.5312 12.7804 11.819 9.667 11.0179ZM5.40725 16.5466L6.19509 17.1401H1.31004C1.50986 14.7774 3.19056 12.7401 5.56403 12.0117L6.23217 12.6832L5.23702 16.0459C5.18246 16.2303 5.2496 16.4279 5.40725 16.5466ZM7.62578 17.0102L6.28295 15.9988L7.2648 12.6812C7.3124 12.5203 7.26764 12.3473 7.14701 12.2261L6.70075 11.7776C7.30214 11.7088 7.94942 11.7088 8.55075 11.7777L8.10455 12.2261C7.98392 12.3473 7.93909 12.5203 7.98676 12.6812L8.96861 15.9988L7.62578 17.0102ZM9.05647 17.14L9.84425 16.5466C10.0019 16.4279 10.069 16.2303 10.0145 16.0459L9.01933 12.6832C9.53484 12.1651 9.38203 12.3187 9.68753 12.0116C12.0611 12.7389 13.7418 14.7761 13.9415 17.14H9.05647Z"
                  fill="#2B57D6"
                  fillOpacity="0.76"
                />
              </svg>
            ),
            iconColor: 'bg-[#e6e6ff]',
            count: 20,
            rating: 'up',
            ratingValue: 1.5,
          },
          {
            id: 4,
            title: 'Churn Risk',
            icon: (
              <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M1.35938 16.4987V14.4154C1.35938 11.884 3.4114 9.83203 5.94271 9.83203H10.526M4.69271 16.4987V13.9987M11.776 13.1654V11.4987M11.776 14.9987V14.832M11.3594 4.41536C11.3594 6.25631 9.86696 7.7487 8.02604 7.7487C6.18509 7.7487 4.69271 6.25631 4.69271 4.41536C4.69271 2.57441 6.18509 1.08203 8.02604 1.08203C9.86696 1.08203 11.3594 2.57441 11.3594 4.41536ZM8.28354 16.4987H15.2685C15.5783 16.4987 15.7797 16.1727 15.6412 15.8957L12.1487 8.9107C11.9952 8.60361 11.5569 8.60361 11.4034 8.9107L7.91088 15.8957C7.77238 16.1727 7.97379 16.4987 8.28354 16.4987Z"
                  stroke="#F13E3E"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            ),
            iconColor: 'bg-[#ffded2]',
            percentage: 20,
            rating: 'down',
            ratingValue: 1.5,
          },
        ];

        return data.map((singleData) => (
          <div key={singleData?.id} className="flex items-start justify-between px-4 py-3  dark:border-gray-700 rounded-2xl">
            <div className="space-y-1">
              <p className="text-[#5F5F5F] dark:text-white text-sm font-medium">{singleData.title}</p>
              <div className="flex items-center gap-2 text-sm">
                <p className="font-semibold dark:text-white">
                  {singleData.count && singleData.count} {singleData.percentage && singleData.percentage + '%'}
                </p>
              </div>
            </div>
            <div className={`flex justify-center items-center ${singleData.iconColor} rounded-full size-8`}>{singleData.icon}</div>
          </div>
        ));
      };
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  // Styles
  const cardClassNames = 'p-3 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg';

  const CardTitle = ({ icon, title, onClick }: { icon?: any; title: string; onClick?: { title: string; onClick: () => void } }) => {
    return (
      <div className="min-h-12 flex flex-wrap justify-between gap-2 border-b dark:border-gray-700 px-2 pb-2">
        <div className="flex items-center gap-2">
          {icon && icon}
          <div className="flex items-center gap-3 font-semibold text-[#313D4F] dark:text-white">{title}</div>
        </div>
        {onClick && <Button onClick={onClick.onClick} label={onClick.title} tertiary size="sm" />}
      </div>
    );
  };

  const blockCards = () => {
    const data = [
      {
        id: 1,
        title: 'Applicants',
        icon: (
          <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              opacity="0.21"
              d="M0.5 16C0.5 7.16344 7.66344 0 16.5 0C25.3366 0 32.5 7.16344 32.5 16C32.5 24.8366 25.3366 32 16.5 32C7.66344 32 0.5 24.8366 0.5 16Z"
              fill="#F1E9FE"
            />
            <path
              opacity="0.587821"
              d="M20.7676 13.1572C21.9456 13.1575 22.9004 14.113 22.9004 15.291C22.9001 16.4688 21.9454 17.4236 20.7676 17.4238C19.5895 17.4238 18.6341 16.469 18.6338 15.291C18.6338 14.1128 19.5894 13.1572 20.7676 13.1572ZM14.3682 9.60156C15.939 9.60172 17.2119 10.8754 17.2119 12.4463C17.2118 14.017 15.9389 15.2899 14.3682 15.29C12.7973 15.29 11.5236 14.0171 11.5234 12.4463C11.5234 10.8753 12.7972 9.60156 14.3682 9.60156Z"
              fill="#743AF5"
            />
            <path
              d="M20.4848 18.1357C22.9067 18.1626 24.8838 19.3857 25.0327 21.9736C25.0387 22.0781 25.0325 22.4004 24.6469 22.4004H21.9057C21.9057 20.8004 21.3767 19.3239 20.4848 18.1357ZM14.3569 16.7109C17.7615 16.711 20.5587 18.3419 20.7671 21.8311C20.7754 21.9702 20.7669 22.3994 20.2329 22.3994H8.48582C8.30747 22.3994 7.9542 22.0149 7.96921 21.8301C8.24499 18.4364 10.9996 16.7109 14.3569 16.7109Z"
              fill="#743AF5"
            />
          </svg>
        ),
        // iconColor: 'bg-[#e4e4ff]',
        count: organizationData?.applicantCount,
        rating: 'up',
        ratingValue: 1.5,
      },
      {
        id: 2,
        title: 'Engagement',
        icon: (
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              opacity="0.21"
              d="M16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16C4.5101e-07 7.16344 7.16344 4.5098e-07 16 0Z"
              fill="#EEFFF1"
            />
            <path
              d="M10.1905 21.8053H22.635C23.0931 21.8053 23.4646 22.1768 23.4646 22.635C23.4646 23.0931 23.0931 23.4646 22.635 23.4646H9.36088C8.90269 23.4646 8.53125 23.0931 8.53125 22.635V9.36088C8.53125 8.90269 8.90269 8.53125 9.36088 8.53125C9.81907 8.53125 10.1905 8.90269 10.1905 9.36088V21.8053Z"
              fill="#009217"
            />
            <path
              opacity="0.5"
              d="M13.2864 18.2272C12.9731 18.5614 12.448 18.5784 12.1138 18.265C11.7795 17.9516 11.7626 17.4266 12.0759 17.0923L15.1871 13.7738C15.4901 13.4505 15.9938 13.4225 16.3308 13.7101L18.7863 15.8055L21.9856 11.7531C22.2695 11.3935 22.7912 11.3321 23.1508 11.616C23.5105 11.8999 23.5718 12.4216 23.2879 12.7812L19.5546 17.5101C19.263 17.8795 18.7229 17.9326 18.3649 17.6271L15.856 15.4863L13.2864 18.2272Z"
              fill="#009217"
            />
          </svg>
        ),
        // iconColor: 'bg-[#d9f7e7]',
        percentage: organizationData?.engagementScore ?? 0,
        rating: 'down',
        ratingValue: 1.5,
      },
      {
        id: 3,
        title: 'Tickets',
        icon: (
          <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              opacity="0.21"
              d="M0.5 16C0.5 7.16344 7.66344 0 16.5 0C25.3366 0 32.5 7.16344 32.5 16C32.5 24.8366 25.3366 32 16.5 32C7.66344 32 0.5 24.8366 0.5 16Z"
              fill="#E0F3FB"
            />
            <path
              d="M22.3761 18.4729L18.9786 21.8704C17.5836 23.2654 15.3186 23.2654 13.9161 21.8704L10.6236 18.5779C9.22862 17.1829 9.22862 14.9179 10.6236 13.5154L14.0286 10.1254C14.7411 9.4129 15.7236 9.0304 16.7286 9.0829L20.4786 9.2629C21.9786 9.3304 23.1711 10.5229 23.2461 12.0154L23.4261 15.7654C23.4711 16.7779 23.0886 17.7604 22.3761 18.4729Z"
              stroke="#11ABE6"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M18.375 16C17.3395 16 16.5 15.1605 16.5 14.125C16.5 13.0895 17.3395 12.25 18.375 12.25C19.4105 12.25 20.25 13.0895 20.25 14.125C20.25 15.1605 19.4105 16 18.375 16Z"
              stroke="#11ABE6"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        ),
        // iconColor: 'bg-[#e6e6ff]',
        count: 0,
        rating: 'up',
        ratingValue: 1.5,
      },
      {
        id: 4,
        title: 'Users',
        icon: (
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              opacity="0.3"
              d="M16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16C4.5101e-07 7.16344 7.16344 4.5098e-07 16 0Z"
              fill="#FFFCDF"
            />
            <path
              d="M19 21.75V20.25C19 19.4544 18.6839 18.6913 18.1213 18.1287C17.5587 17.5661 16.7956 17.25 16 17.25H11.5C10.7044 17.25 9.94129 17.5661 9.37868 18.1287C8.81607 18.6913 8.5 19.4544 8.5 20.25V21.75"
              stroke="#BA8500"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M19 8.34375C19.6433 8.51053 20.213 8.8862 20.6198 9.4118C21.0265 9.9374 21.2471 10.5832 21.2471 11.2478C21.2471 11.9123 21.0265 12.5581 20.6198 13.0837C20.213 13.6093 19.6433 13.985 19 14.1517"
              stroke="#BA8500"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M23.5 21.7462V20.2462C23.4995 19.5815 23.2783 18.9358 22.871 18.4105C22.4638 17.8851 21.8936 17.5099 21.25 17.3438"
              stroke="#BA8500"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M13.75 14.25C15.4069 14.25 16.75 12.9069 16.75 11.25C16.75 9.59315 15.4069 8.25 13.75 8.25C12.0931 8.25 10.75 9.59315 10.75 11.25C10.75 12.9069 12.0931 14.25 13.75 14.25Z"
              stroke="#BA8500"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        ),
        // iconColor: 'bg-[#ffded2]',
        percentage: 0,
        rating: 'down',
        ratingValue: 1.5,
      },
    ];
    return data.map((singleData) => (
      <div
        key={singleData?.id}
        className="flex items-start justify-between px-4 py-3 shadow-[0px_7px_10px_0px_#743AF51A] dark:border-gray-700 rounded-2xl"
      >
        <div className="space-y-1">
          <p className="text-[#5F5F5F] dark:text-white text-sm font-medium">{singleData.title}</p>
          <div className="flex items-center gap-2 text-sm">
            <p className="font-semibold dark:text-white">
              {singleData.count && singleData.count} {singleData.percentage && singleData.percentage + '%'}
            </p>
          </div>
        </div>
        <div className={`flex justify-center items-center  rounded-full size-8`}>{singleData.icon}</div>
      </div>
    ));
  };

  useEffect(() => {
    handleGet();
  }, []);

  useEffect(() => {
    if (selectedOrganizationId) {
      getOrganizationOverview(selectedOrganizationId);
    }
  }, [selectedOrganizationId]);

  return (
    <div className="space-y-3">
      <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-3">{blockCards()}</div>

      <main className="grid grid-cols-1 lg:grid-cols-10 gap-4">
        <div className={`h-full lg:col-span-7 ${cardClassNames}`}>
          {/* <CardTitle title="Users" onClick={{ title: 'View All', onClick: () => selectedTab.setActiveTab(1) }} />
          <UsersCard /> */}
          <CardTitle title="Organization Growth" />
          <OrganizationGrowthIndividual />
        </div>

        <div className={`lg:col-span-3 h-full`}>
          <CurrentPlanSummary />
        </div>

        {/* <div className={`h-full col-span-2 ${cardClassNames}`}>
          <CardTitle
            icon={
              <svg width="35" height="35" viewBox="0 0 35 35" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  opacity="0.21"
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M35 17.5C35 27.165 27.165 35 17.5 35C7.83502 35 0 27.165 0 17.5C0 7.83502 7.83502 0 17.5 0C27.165 0 35 7.83502 35 17.5Z"
                  fill="#FFD78A"
                />
                <path
                  d="M22.4887 9.00351H18.098L8.00366 19.071L15.9118 26.9966L25.9962 16.9385V12.5035L22.4887 9.00351ZM25.3299 16.6616L15.913 26.0535L8.94616 19.0716L18.373 9.66913H22.2124L25.3299 12.7791V16.661V16.6616Z"
                  fill="#E84C29"
                />
                <path
                  d="M20.667 12.9991C20.667 13.7323 21.2645 14.3291 22.0001 14.3291C22.7357 14.3291 23.3332 13.7329 23.3332 12.9991C23.3332 12.2654 22.7357 11.6685 22.0001 11.6685C21.2645 11.6685 20.667 12.2654 20.667 12.9991ZM22.6664 12.9991C22.6664 13.3648 22.367 13.6629 22.0001 13.6629C21.6332 13.6629 21.3339 13.3654 21.3339 12.9991C21.3339 12.6329 21.6326 12.3348 22.0001 12.3348C22.3676 12.3348 22.6664 12.6329 22.6664 12.9991Z"
                  fill="#E84C29"
                />
              </svg>
            }
            title="Tickets"
            onClick={{ title: 'View All', onClick: () => selectedTab.setActiveTab(3) }}
          />
          <TicketsCard />
        </div> */}
      </main>
      {/* <div className={`h-[550px] ${cardClassNames}`}>
        <CardTitle title="Organization Growth" />
        <OrganizationGrowthIndividual />
      </div> */}

      <main className="grid grid-cols-1 gap-4">
        <div className={`${cardClassNames}`}>
          <CardTitle title="Feature Engagement" />
          <FeatureEngagement />
        </div>
      </main>
    </div>
  );
};
