import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import Cookies from 'js-cookie';

import { type RootState } from '../../store';
import type { UsersListItem, CurrentUserRole } from '../../types/User.type';

interface InitialState {
  user: UsersListItem;
  userRole: CurrentUserRole;
}
const initialState: InitialState = {
  user: Cookies.get('userData') ? JSON.parse(Cookies.get('userData') as string) : {},
  userRole: Cookies.get('userRoleData') ? JSON.parse(Cookies.get('userRoleData') as string) : {},
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    updateUserAction: (state, action: PayloadAction<UsersListItem>) => {
      state.user = action.payload;
      Cookies.set('userData', JSON.stringify(action.payload), { expires: 7, path: '/' });
    },
    updateUser: (state, action: PayloadAction<UsersListItem>) => {
      state.user = action.payload;
      Cookies.set('userData', JSON.stringify(action.payload), { expires: 7, path: '/' });
    },
    updateUserRole: (state, action: PayloadAction<CurrentUserRole>) => {
      state.userRole = action.payload;
      Cookies.set('userRoleData', JSON.stringify(action.payload), { expires: 7, path: '/' });
    },
  },
  extraReducers: (builder) => { },
});

export const { updateUserAction, updateUser, updateUserRole } = authSlice.actions;
export const authState = (state: RootState) => state.auth;
export default authSlice.reducer;
