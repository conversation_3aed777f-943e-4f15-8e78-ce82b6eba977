// React
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Link, useLocation } from 'react-router-dom';

// UI
import { Icon, Jumbotron, Button, CustomIcon, ToggleFilter, NoDataMatches, EnumText, SubscribeDialog } from 'src';
import { FaUserGraduate, FaUser, FaStar, FaMedal } from 'react-icons/fa';

// Flowbite
import { Pagination, Spinner } from 'flowbite-react';

import { Api, StaticData, RootState, useAppSelector, UserData, useFetchList, useScreenSize, QuizDifficulty } from 'UI/src';

// Components
import { TestCreatedSucessfully } from '../components/test-created-sucessfully';
import { PhoneScreenManagementPlaceHolder } from '../components/phone-screen-placeholder';
import { setErrorNotify } from 'UI';
import { useAppDispatch, Placeholder } from 'UI/src';

export const PhoneScreeningListPage = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');

  // Hooks
  const screen = useScreenSize();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // State
  const [needSubscription, setNeedSubscription] = useState(false);
  const [isTestCreatedSucessfullyVisible, setTestCreatedSucessfullyVisibility] = useState(false);
  const [quizUrl, setQuizUrl] = useState(false);
  const [backupList, setBackupList] = useState([]);

  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh } = useFetchList('quizzes/list/phoneScreening', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: {
      difficulty: {
        label: 'Level',
        enum: 'QuizDifficulty',
      },
    },
  });

  // Pagination
  const isPaginationActive = !!pagination.update;
  const { page, size }: any = pagination;
  const pagesCount = Math.max(Math.ceil(count / size), 1);
  const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;

  const sidebarFiltersOrder = ['difficulty'];

  useEffect(() => {
    if (backupList.length === 0 && list.length > 0) {
      setBackupList(list);
    }
    // setSidebarSearch(search);
    // setSidebarFilter({ sidebarFiltersOrder, filters, setFilters });
  }, [list, backupList.length]);

  // Methods
  const onCloseTestCreatedSucessfully = () => {
    setTestCreatedSucessfullyVisibility(false);
  };

  const difficulty = (singleBlock: { difficulty: number }) => {
    let difficultyIcon;
    let difficultyColor;
    let iconSize = 'text-sm';

    switch (singleBlock?.difficulty) {
      case 1:
        difficultyIcon = <FaUserGraduate className={`${iconSize} text-teal-700`} />; // Intern
        difficultyColor = ' text-teal-700 ';
        break;

      // Star Icon fresh level
      case 2:
        difficultyIcon = <FaUser className={`${iconSize} text-sky-800`} />; // Fresh
        difficultyColor = 'text-sky-800 ';
        break;
      // Medal Star junior
      case 3:
        difficultyIcon = <FaStar className={`${iconSize} text-amber-700`} />; // Junior
        difficultyColor = ' text-amber-700 ';
        break;
      // betetr medal star midlevel
      case 4:
        difficultyIcon = <FaMedal className={`${iconSize} text-orange-700`} />; // Mid-level
        difficultyColor = 'text-orange-700';
        break;

      // Tropy icon for senior with star
      case 5:
        difficultyIcon = <Icon icon="solar:crown-star-bold" width="16" className={`text-red-800`} />; // Senior
        difficultyColor = 'text-red-800';
        break;
      default:
        difficultyIcon = null;
    }
    return { difficultyIcon, difficultyColor };
  };

  const generateTestLink = async (id: string) => {
    try {
      setLoading(true);
      const payload = { quizId: id };
      const response = await Api.post(`submissions/single`, payload);
      setQuizUrl(response.data.quizUrl);
      setTestCreatedSucessfullyVisibility(true);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setLoading(false);
    }
  };

  if (!ready) {
    return <PhoneScreenManagementPlaceHolder />;
  }

  // const noDataFound = {
  //   // definedIcon: 'screening',
  //   customIcon: 'screening' as any,
  //   message: 'No screening created yet',
  // };

  const screeningViewNavigationURL = (id: string) => `/app/screening/view/${id}`;

  return (
    <div className="relative">
      <div className="space-y-4">
        <div className="flex flex-wrap justify-between sm:items-center gap-4">
          <Jumbotron />
          <Button
            type="button"
            as={Link}
            to={true && '/app/screening/create'}
            onClick={() => {
              // if (isSuperAdmin || userData?.features?.screening > 0) {
              // } else {
              //   setNeedSubscription(true);
              // }
            }}
            className="w-fit min-w-20"
          >
            <div className="sm:mr-2 h-5 w-5">
              <Icon icon="mdi:add" width="22" />
            </div>
            <p className="text-nowrap">Create Screening</p>
          </Button>
        </div>

        <div className="space-y-4 rounded-2xl">
          <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4 bg-white dark:bg-darkGrayBackground sticky top-[60px] z-30 dark:text-white text-[13px]">
            <div className="flex items-center gap-2">
              <h1 className="text-lg text-gray-800 dark:text-white font-medium">Screening List</h1>
              <div className="px-2 py-1 text-[#8A43F9] dark:text-white bg-[#ece2fb] dark:bg-[#6F3ED8] text-[13px] font-medium rounded-full">
                {count}
              </div>
            </div>

            <div className="flex gap-x-2 flex-row justify-between items-center">
              {/* Search bar */}
              <div className="rounded-lg flex w-full flex-row items-center space-x-3 space-y-0 justify-between shadow-sm ">
                <div className="relative w-full">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search for screening"
                    className="bg-gray-white border truncate border-gray-200 text-gray-800 text-[13.5px] rounded-lg  block w-full sm:w-[270px] pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0 focus:border-gray-300"
                    value={search.value}
                    onChange={(e) => search.update(e.target.value)}
                  />
                </div>
              </div>

              {/* Filters Button */}
              <ToggleFilter filters={filters} />
            </div>
          </div>

          {list?.length > 0 ? (
            <div className="grid sm:grid-cols-2 lg:grid-cols-3  gap-x-5 gap-y-6">
              {list.map((singleBlock: { _id: string; title: string; difficulty: string; status: string }) => (
                <div
                  key={singleBlock?._id}
                  className="flex flex-col dark:bg-darkBackgroundCard dark:text-white px-2.5 xssm:px-4 py-4 border-2 border-[#F6F7FA] dark:border-gray-600 rounded-2xl shadow-md space-y-3"
                >
                  <Link className="text-lg font-semibold leading-8 cursor-pointer capitalize" to={screeningViewNavigationURL(singleBlock?._id)}>
                    {singleBlock?.title}
                  </Link>
                  <div className="flex gap-1 xssm:gap-3 items-center flex-wrap text-sm text-[#6B7280] font-medium">
                    <span
                      className={`inline-flex items-center py-1 font-medium rounded-full capitalize ${
                        difficulty({ ...singleBlock, difficulty: Number(singleBlock.difficulty) })?.difficultyColor
                      } `}
                    >
                      <span className="mr-1 hidden xsmd:flex items-center justify-center">
                        {difficulty({ ...singleBlock, difficulty: Number(singleBlock.difficulty) })?.difficultyIcon}
                      </span>
                      {/* <EnumText name={'QuizDifficulty'} value={singleBlock?.difficulty} /> */}
                      {QuizDifficulty[singleBlock?.difficulty as any]}
                    </span>
                    <span>•</span>
                    <p className="flex items-center gap-2">
                      <svg className="hidden xsmd:block" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M8 15.5C3.85775 15.5 0.5 12.1423 0.5 8C0.5 3.85775 3.85775 0.5 8 0.5C12.1423 0.5 15.5 3.85775 15.5 8C15.5 12.1423 12.1423 15.5 8 15.5ZM8.75 8V4.25H7.25V9.5H11.75V8H8.75Z"
                          fill="#6B7280"
                        />
                      </svg>
                      <span>{(singleBlock as any)?.duration || 0} mins</span>
                    </p>
                    <span>•</span>
                    <p className="flex items-center gap-2">
                      {/* <CustomIcon definedIcon="questions" className="text-[#8A43F9]" /> */}
                      <svg className="hidden xsmd:block" width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M16.6667 5.12548C16.5558 4.28401 16.1114 3.38319 15.3644 2.63598C14.6174 1.88896 13.7167 1.44484 12.8754 1.33384C12.4946 1.28364 11.9863 1.07322 11.6817 0.839481C11.008 0.322815 10.0569 0 9.00021 0C7.94351 0 6.99247 0.322815 6.31896 0.839481C6.0143 1.07322 5.50596 1.28364 5.12519 1.33384C4.2837 1.44484 3.38326 1.88896 2.63623 2.63598C1.8892 3.38319 1.44483 4.28404 1.33387 5.12548C1.28386 5.50624 1.07324 6.01415 0.839693 6.31883C0.322821 6.99233 0 7.94336 0 9C0 10.0567 0.322821 11.0077 0.839498 11.6812C1.07324 11.9859 1.28367 12.494 1.33368 12.8746C1.44467 13.716 1.889 14.6169 2.63603 15.3641C3.38306 16.111 4.2837 16.5552 5.12499 16.6662C5.50577 16.7164 6.01411 16.9268 6.31876 17.1606C6.99227 17.6772 7.94332 18 9.00002 18C10.0567 18 11.0078 17.6772 11.6813 17.1606C11.986 16.9268 12.4943 16.7164 12.875 16.6662C13.7166 16.5552 14.617 16.111 15.364 15.3641C16.111 14.6169 16.5554 13.716 16.6664 12.8746C16.7164 12.4938 16.927 11.9859 17.1605 11.6812C17.6772 11.0077 18 10.0567 18 9C18 7.94336 17.6772 6.9923 17.1605 6.31883C16.9272 6.01415 16.7168 5.50624 16.6667 5.12548ZM10.0776 14.1039C10.0776 14.3241 9.89906 14.5024 9.6791 14.5024H8.38807C8.16811 14.5024 7.98955 14.3241 7.98955 14.1039V12.813C7.98955 12.5928 8.16807 12.4145 8.38807 12.4145H9.6791C9.89906 12.4145 10.0776 12.5928 10.0776 12.813V14.1039ZM12.5188 7.93359C12.2585 8.34486 11.7026 8.90457 10.8505 9.61294C10.4096 9.97955 10.1358 10.2743 10.0294 10.4972C9.94969 10.6644 9.90226 10.9304 9.88793 11.2952C9.87933 11.5153 9.70599 11.6935 9.48576 11.6935H8.38788C8.16788 11.6935 7.98737 11.5819 7.98516 11.4445C7.98317 11.317 7.98197 11.234 7.98197 11.1956C7.98197 10.5813 8.08361 10.076 8.28662 9.67965C8.48988 9.28335 8.89596 8.83763 9.50527 8.34206C10.1147 7.84672 10.4786 7.52212 10.5975 7.3687C10.7808 7.12601 10.8724 6.85842 10.8724 6.56611C10.8724 6.15982 10.7104 5.81193 10.3858 5.522C10.0612 5.2321 9.62423 5.08722 9.07432 5.08722C8.6965 5.08722 8.36316 5.16393 8.07342 5.31734C7.87895 5.42036 7.60438 5.65448 7.47187 5.83023C7.32402 6.0263 7.20427 6.26221 7.11261 6.53798C7.04307 6.74681 6.83143 6.90061 6.61287 6.87353L5.48685 6.73385C5.26825 6.70673 5.0995 6.50611 5.13854 6.2895C5.26805 5.57217 5.63011 4.9509 6.2245 4.42586C6.9255 3.80658 7.84567 3.49695 8.98504 3.49695C10.184 3.49695 11.1374 3.81035 11.8458 4.43703C12.5542 5.06371 12.9085 5.79318 12.9085 6.62546C12.9089 7.08655 12.7788 7.52251 12.5188 7.93359Z"
                          fill="#8A43F9"
                        />
                      </svg>

                      <span>
                        {(singleBlock as any)?.numOfQuestions || 0} {((singleBlock as any)?.numOfQuestions || 0) > 1 ? 'Questions' : 'Question'}
                      </span>
                    </p>

                    {/* <EnumText name={'QuizDifficulty'} value={singleBlock?.difficulty} /> */}
                  </div>
                  <Link
                    className="text-[#808080] dark:text-white text-sm leading-5 h-full cursor-pointer line-clamp-2"
                    to={screeningViewNavigationURL(singleBlock?._id)}
                  >
                    {(singleBlock as any)?.description || 'No description available'}
                  </Link>

                  {/* Actions */}
                  <div className="flex gap-2 !mt-6">
                    <Button
                      tertiary
                      label="View"
                      customIcon="eye"
                      iconWidth="22"
                      iconHeight="22"
                      as={Link}
                      to={screeningViewNavigationURL(singleBlock?._id)}
                    />
                    <Button tertiary label="Generate Link" icon="akar-icons:link-chain" onClick={() => generateTestLink(singleBlock?._id)} />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex justify-center align-middle   min-h-[calc(70vh-4rem)] items-center ">
              <div className=" w-2/4 space-y-2  ">
                {/* No data created || No results found */}
                {backupList.length > 0 ? (
                  <NoDataMatches message="No results found" />
                ) : (
                  <Placeholder
                    image="/UI/src/assets/placeholder/TestImagePlaceholder.svg"
                    title="No screening created yet"
                    subTitle="Start by creating a screening to evaluate applicant`s skills."
                  />
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Loading */}
      {loading && (
        <div className="absolute left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
          <Spinner size="lg" color="purple" />
        </div>
      )}

      {/* Pagination */}
      {isPaginationActive && (
        <nav className="flex flex-row justify-between items-center space-y-0 p-4" aria-label="Table navigation">
          <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
            Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
            <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
          </span>
          {count > size && (
            <Pagination
              theme={StaticData.paginationTheme}
              currentPage={page}
              onPageChange={(page) => pagination.update({ page })}
              showIcons
              totalPages={pagesCount}
              layout={screen.gt.md() ? 'pagination' : 'navigation'}
              previousLabel="Previous"
              nextLabel="Next"
            />
          )}
        </nav>
      )}

      {isTestCreatedSucessfullyVisible && <TestCreatedSucessfully quizUrl={quizUrl} onClose={onCloseTestCreatedSucessfully} />}

      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}
    </div>
  );
};
