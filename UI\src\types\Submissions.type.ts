export interface SubmissionType {
  _id: string;
  organizationId: string;
  sourceLink: string;
  customTest: true;
  otherTest: true;
  isTemporary: true;
  randomId: string;
  applicantId: string;
  questions: string[];
  essayGrades: string[];
  quiz: {};
  title: string;
  numOfQuestions: number;
  duration: number;
  exceededTime: number;
  score: number;
  cheatingPercentage: number;
  startDate: string;
  dueDate: string;
  expired: true;
  timeTaken: number;
  weirdBehavior: {};
  questionsSummary: {};
  archive: true;
  submittedAt: string;
  locked: true;
  startedAt: string;
  author: {};
}

export interface SubmissionApp {
  score: number;
  applicantId: string;
  applicantName: string;
  applicantEmail: string;
}

export type SubmissionItemList = SubmissionApp & {
  _id: string;
  quizTitle: string;
  quizDifficulty: string;
  createdAt: string;
  status: string;
  locked: boolean;
  authorName: {};
  archive: boolean;
  categoryName: string;
  subCategoryName: string;
  cheatingPercentage: number;
};

export interface SubmissionList {
  items: SubmissionItemList[];
  count: number;
}

export interface SubmissionPublic {
  authorName: string;
  createdAt: string;
  expired: true;
  dueDate: string;
  totalUsage: number;
  submittedDocuments: number;
  completionRate: number;
  title: string;
  averageScore: number;
  assessmentUrl: string;
}

export interface SubmissionsGenerated {
  items: SubmissionPublic[];
  count: number;
}

export type SubmissionsApplicantAssignedItem = SubmissionApp & {
  timeTaken: number;
  track: string;
  seniorityLevel: string;
  createdAt: string;
  status: string;
  duration: number;
  assessmentId: string;
};

export interface SubmissionsApplicantAssigned {
  items: SubmissionsApplicantAssignedItem[];
  count: number;
}

export type SubmissionApplicant = SubmissionApp & {
  _id: string;
  timeTaken: number;
};

export interface SubmissionBehaviors {
  ipChangeCount: number;
  tabSwitchedCount: number;
  openContextMenuCount: number;
}
