// React
import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// Core
import {
  ScrollableTabs,
  Jumbotron,
  CustomIcon,
  Button,
  SubscribeDialog,
  CategoryFieldColumn,
  SubcategoryFieldColumn,
  PermissionProtectedComponent,
} from 'src';

import {
  QuizType,
  Api,
  initializeForm,
  RootState,
  setErrorNotify,
  useAppSelector,
  UserData,
  UsersListItem,
  PlanFeatures,
  QuizDifficulty,
  QuestionDifficulty,
  UserPermissions,
  AlertNote,
} from 'UI/src';

// Components
import { SingleContent } from '../components/single/content';
import { useAppDispatch, AssessmentCompCard } from 'UI/src';

import { formatDistanceToNow } from 'date-fns';
import CheckFeatureManagement from '@/composables/feature-management';

export const AssessmentSingle = () => {
  // States
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [needSubscription, setNeedSubscription] = useState(false);

  // User Data
  const userData: UsersListItem = useAppSelector((state: RootState) => state.auth.user);

  // Permissions
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');
  const isAdmin = Array.isArray(userData?.role) && userData?.role.includes('admin');
  const isContentCreator = Array.isArray(userData?.role) && userData?.role.includes('content-creator');
  const isHr = Array.isArray(userData?.role) && userData?.role.includes('hr');

  // Hooks
  const navigate = useNavigate();
  const { id, type } = useParams();
  const dispatch = useAppDispatch();
  const { checkFeature } = CheckFeatureManagement();

  const typeTitle: { [key: string]: any } = {
    interview: 'Interview',
    test: 'Test',
    screening: 'Screening',
  };

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  useEffect(() => {
    dispatch(initializeForm({}));
  }, []);

  const tabs = [
    {
      title: 'Content',
      data: <SingleContent anyQuestionHasEditMode={undefined} formData={{ form }} />,
    },
  ];

  const checkPermissionAssign = () => {
    if (type === 'test') {
      return PlanFeatures.ASSIGN_TESTS;
    } else {
      // @FIXME:
      // Based on permission
      // Needs to control the interview type appeared
      return PlanFeatures.ASSIGN_CUSTOM_INTERVIEWS;
    }
  };

  const handleGetQuizzData = async () => {
    try {
      setLoading(true);
      const response = await Api.get(`templates/single/${id}`, {});
      console.log('quizzes/single', response.data); // Data Returned
      dispatch(initializeForm(response.data));
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setLoading(false);
    }
  };

  const getBlocksCards = () => {
    if (!form) return [];

    return [
      {
        header: 'Seniority Level',
        subHeader: form.seniorityLevel ? QuizDifficulty[form.seniorityLevel] : null,
        icon: 'manInSuit',
      },
      {
        header: 'Questions',
        // Handle both questionIds and questions array for screening
        subHeader: form.questionIds?.length || form.questions?.length || 0,
        icon: 'questionInBorder',
      },
      {
        header: type === 'screening' || type === 'test' ? 'Duration' : 'Estimation time',
        subHeader: form.duration,
        icon: 'clockThree',
      },
      {
        header: 'Difficulty',
        subHeader: form.difficulty ? QuestionDifficulty[form.difficulty] : null,
        icon: 'charts',
      },
    ];
  };

  useEffect(() => {
    handleGetQuizzData();
  }, []);

  return (
    <>
      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
          <Jumbotron />

          <div className="flex items-center gap-2">
            {(isSuperAdmin || isAdmin || isHr) && (
              <Button
                label={`Assign ${typeTitle[type as string] || 'Assessment'}`}
                permission={checkPermissionAssign()}
                onClick={() => navigate(`/app/assessment-templates/${type}/assign/${id}`)}
              />
            )}

            {(isSuperAdmin || isAdmin || isContentCreator) && (
              <Button label="Edit Template" tertiary onClick={() => navigate(`/app/assessment-templates/${type}/edit/${id}`)} />
            )}
          </div>
        </div>

        <PermissionProtectedComponent permissions={UserPermissions.ASSIGN_ASSESSMENT}>
          {!checkFeature(type === 'test' ? (PlanFeatures.ASSIGN_TESTS as any) : (PlanFeatures.ASSIGN_TESTS as any)) && (
            <AlertNote message="Oops! You’ve hit your limit , Add extra credits to keep enjoying all the features." nav={{}} />
          )}
        </PermissionProtectedComponent>

        <AssessmentCompCard
          name={form.title}
          difficulty={QuestionDifficulty[form.difficulty]}
          duration={form.duration}
          seniority={QuizDifficulty[form.seniorityLevel]}
          questionsNumber={form.questionIds?.length || form.questions?.length || 0}
          categoryName={form?.categoryName}
          subCategories={form?.subCategoryName}
          createdByName={form.authorName}
          createdByDate={new Date(form.createdAt).toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' })}
          updatedDate={form.updatedAt}
        />

        {/* <div className="pb-4">
          <ScrollableTabs
            data={tabs}
            selectedTab={{
              activeTab: activeTab,
              setActiveTab: setActiveTab,
            }}
          />
        </div> */}

        {tabs[activeTab].data}
      </div>

      {/* Need Subscription */}
      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}
    </>
  );
};
