// React
import { Label } from 'flowbite-react';
import { useState } from 'react';

// Core
import { Dialog, CheckBox, Button } from 'UI/src';

export const ExportReportsData = ({ onClose }: { onClose: () => void }) => {
  const [checkedItems, setCheckedItems] = useState({
    'submitted at': false,
    'time taken': false,
    score: false,
    'score breakdown': false,
    'start at': false,
    'suspicious behavior': false,
  });

  return (
    <Dialog size="lg" isOpen onClose={onClose} title="Select Reports Data">
      <div className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          {Object.keys(checkedItems)?.map((data) => (
            <div key={data} className="border border-[#DEE2E4] rounded-xl px-4 py-3 flex items-center gap-2 cursor-pointer hover:shadow-md">
              <CheckBox
                id={data}
                checked={checkedItems[data as keyof typeof checkedItems]}
                onChange={(value) => setCheckedItems((prev) => ({ ...prev, [data]: value }))}
                className="text-[#743AF5]"
              />
              <Label htmlFor={data} className="!thepassHfour capitalize cursor-pointer">
                {data}
              </Label>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-2 gap-2">
          <Button label="Cancel" colorType="tertiary" onClick={onClose} />
          <Button
            label="Export"
            colorType="primary"
            onClick={() => {
              onClose();
              console.log('Export');
            }}
          />
        </div>
      </div>
    </Dialog>
  );
};
