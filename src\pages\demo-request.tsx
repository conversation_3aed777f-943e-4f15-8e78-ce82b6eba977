import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { TextInput, Button, Logo } from 'src';
import Swal from 'sweetalert2';
import { Api, Regex, useValidate, initializeForm, setFieldValue, useAppDispatch, useAppSelector } from 'UI/src';
import { setErrorNotify } from 'UI';
import { Form } from 'UI/src/components/form';
import { useFormik } from 'formik';

export const DemoRequest = () => {
  const { isRequired, minLength, maxLength, validateRegex } = useValidate();

  // State
  const [loading, setLoading] = useState(false);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const formik = useFormik({
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });
  const form = useAppSelector((state) => state.form.data);

  const handleSubmit = async () => {
    try {
      await Api.post('/demo-requests/create', form);

      await Swal.fire({
        title: 'Your Request is Send!',
        text: 'We will contact you as soon as possible',
        icon: 'success',
      });

      navigate('/');
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    }
  };

  return (
    <section className="flex md:block bg-gray-50  dark:bg-gray-900 h-screen">
      <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto md:h-full lg:py-0">
        <a
          onClick={() => navigate('/')}
          className="flex items-center justify-start mb-6 text-2xl font-semibold text-gray-900 dark:text-white cursor-pointer"
        >
          <Logo className="h-10" />
        </a>
        <div className="w-full bg-white rounded-xl shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700">
          <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
            <h1 className="text-xl font-bold leading-tight tracking-tight text-gray-900 md:text-2xl dark:text-white">Request a Demo</h1>
            <Form className="flex max-w-md flex-col gap-5" onSubmit={handleSubmit}>
              <TextInput
                name={'firstName'}
                label="Frist Name"
                placeholder="Frist Name"
                disabled={loading}
                value={form.firstName}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'firstName', value }))}
                validators={[isRequired(), minLength(3), maxLength(100), validateRegex(Regex.name)]}
              />
              <TextInput
                name={'lastName'}
                label="Last Name"
                placeholder="Last Name"
                disabled={loading}
                value={form.lastName}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'lastName', value }))}
                validators={[isRequired(), minLength(3), maxLength(100), validateRegex(Regex.name)]}
              />
              <TextInput
                name={'email'}
                label="Email"
                placeholder="Email"
                disabled={loading}
                value={form.email}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'email', value }))}
                validators={[isRequired(), validateRegex(Regex.email)]}
              />
              <div className="flex flex-col gap-2">
                <Button
                  type="submit"
                  label="Send"
                  icon="mdi:send"
                  disabled={loading}
                  loading={loading}
                  className="mt-4"
                  gradientMonochrome="purple"
                />
              </div>
            </Form>
          </div>
        </div>
      </div>
    </section>
  );
};
