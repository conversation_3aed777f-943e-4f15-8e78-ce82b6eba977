import React, { FC } from 'react';

import { Dialog } from 'UI';

import { WeiredBehavior } from './weired-behavior';

interface Stage {
  weirdBehavior?: any[];
}

interface WeirdBehaviorSingleDialogProps {
  onClose: () => void;
  onCreate: () => void;
  id: string | number;
  stage: Stage;
}

export const WeirdBehaviorSingleDialog: FC<WeirdBehaviorSingleDialogProps> = ({ onClose, onCreate, id, stage }) => {
  return (
    <Dialog size="lg" isOpen title={'Weird Behavior Analysis'} onClose={onClose}>
      <div className="dark:text-white">{stage?.weirdBehavior?.length ? <WeiredBehavior stage={stage} /> : <p>No Data</p>}</div>
    </Dialog>
  );
};
