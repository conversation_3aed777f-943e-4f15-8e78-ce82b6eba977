import React, { useState } from 'react';
import { EnumText, Icon } from 'src';
import { Tooltip } from 'flowbite-react';
import { QuizDifficulty } from 'UI/src';

interface QuizzesProps {
  title: string;
  duration: number;
  difficulty: number;
  number: number;
}

export default function Quizzes({ title, duration, difficulty, number }: QuizzesProps) {
  const [showMore, setShowMore] = useState(false);
  return (
    <>
      <div className="hidden sm:grid sm:grid-cols-6 gap-8 ">
        <div className="flex gap-1 col-span-1 sm:col-span-3 px-0 sm:px-3.5 py-0 sm:py-2 rounded-xl bg-[#F2F7FF] dark:bg-[#363844] text-[#1C2434] dark:text-white text-sm relative">
          <p className="truncate">{title}</p>
          <Tooltip content={title} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
            <div className="w-full h-full absolute left-0 top-0"></div>
          </Tooltip>
        </div>

        <div className="text-[#1C2434] dark:text-white text-sm self-center ">
          <p> {duration} </p>
        </div>
        <div className="text-[#1C2434] dark:text-white text-sm self-center">
          {/* <EnumText name={'QuizDifficulty'} value={difficulty} /> */}
          {QuizDifficulty[difficulty]}
        </div>
        <p className="text-[#1C2434] dark:text-white text-sm self-center">{number}</p>
      </div>
      {/* Smaller screen */}
      <div
        style={{
          maxHeight: showMore ? 'none' : '40px',
          overflow: 'hidden',
          transition: 'all 0.3s ease-out',
        }}
        className="grid grid-cols-4 sm:hidden gap-8"
      >
        <div className="grid grid-cols-1 col-span-3 gap-4">
          <div className="flex gap-1  px-3.5 py-2 rounded-xl bg-[#F2F7FF] dark:bg-[#43444f] text-[#1C2434] dark:text-white text-sm">
            <p className={showMore ? 'break-words' : 'truncate'}>{title}</p>
          </div>
          <div className="px-3 grid grid-cols-1 gap-2">
            <div className="grid grid-cols-3 gap-5 text-xs text-secondaryGray">
              <p>Duration</p>
              <p>Difficulty</p>
              <p>Number</p>
            </div>
            <div className="grid grid-cols-3 gap-5 text-[#1C2434]">
              <div className="dark:text-white text-xs ">
                <p>{duration}</p>
              </div>
              <div className="dark:text-white text-xs">
                {/* <EnumText name={'QuizDifficulty'} value={difficulty} /> */}
                {QuizDifficulty[difficulty]}
              </div>
              <p className="dark:text-white text-xs">{number}</p>
            </div>
          </div>
        </div>
        <div
          onClick={() => setShowMore(!showMore)}
          className="w-8 h-8 mt-1 border-[0.5px] border-[#F2F7FF] dark:border-[#2D324F] rounded-full flex justify-center items-center cursor-pointer"
        >
          <Icon className="text-[#8484E1]" width="28" icon={showMore ? 'ic:round-keyboard-arrow-up' : 'ic:round-keyboard-arrow-down'}></Icon>
        </div>
      </div>
    </>
  );
}
