export interface SubscriptionType {
  _id: string;
  status: string;
  couponCode: string;
  originalAmount: number;
  discountPercentage: number;
  paymentId: string;
  autoRenewal: boolean;
  createdAt: string;
  endDate: string;
  price: number;
  billingCycle: number;
  name: string;
  type: number;
  currency: string;
  durationInDays: number;
}

export interface SubscriptionList {
  items: SubscriptionType[];
  count: number;
}
