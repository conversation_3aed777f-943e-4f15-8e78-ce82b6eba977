// React
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// Core
import { CustomIcon, Icon } from 'src';

// Flowbite
import { Card, Pagination } from 'flowbite-react';

// Date format
import { format, isValid, formatDistanceToNow } from 'date-fns';

import {
  ApplicantType,
  RootState,
  useAppDispatch,
  useAppSelector,
  useLookups,
  useScreenSize,
  Api,
  StaticData,
  QuizDifficulty,
  AiAvatarModels,
  InterviewLanguages,
  AssessmentCompCard,
} from 'UI/src';
import { setErrorNotify } from 'UI/src';

interface SingleApplicantTypes {
  _id: string;
  name: string;
  email: string;
}

export const ReviewAssignAssessment = ({ formData }: { formData: any }) => {
  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  // State
  const [applicantsData, setApplicantsData] = useState({});
  const [currentPage, setCurrentPage] = useState(1);

  // Hooks
  const dispatch = useAppDispatch();
  const { type } = useParams();
  const screen = useScreenSize();

  // Pagination settings
  const itemsPerPage = 5;

  // Dynamic blocks cards based on form data
  const getBlocksCards = () => {
    const cards = [];

    // Experience Level
    if (form.seniorityLevel) {
      const seniorityLabel =
        Object.entries(QuizDifficulty).find(([key, value]) => typeof value === 'number' && value === form.seniorityLevel)?.[0] || 'Not specified';
      cards.push({ header: 'Seniority Level', subHeader: seniorityLabel, icon: 'manInSuit' });
    }

    // Questions
    if (form.numberOfQuestions) {
      cards.push({ header: 'Questions', subHeader: form.numberOfQuestions, icon: 'questionInBorder' });
    }

    // Duration
    if (form.estimationTime) {
      cards.push({ header: 'Duration', subHeader: `${form.estimationTime} mins`, icon: 'clockThree' });
    }

    // Difficulty
    if (form.seniorityLevel) {
      const difficultyLabel =
        Object.entries(QuizDifficulty).find(([key, value]) => typeof value === 'number' && value === form.seniorityLevel)?.[0] || 'Not specified';
      cards.push({ header: 'Difficulty', subHeader: difficultyLabel, icon: 'charts' });
    }

    return cards;
  };

  const formatDate = (customDate: string) => {
    const date = new Date(customDate || Date.now());
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, "MMMM dd, yyyy, 'at' hh:mm a");
  };

  const handleGetApplicant = async (id: string) => {
    try {
      const response = await Api.get<ApplicantType>(`applicants/single/${id}`, {});
      console.log('applicants/single/', response.data);
      setApplicantsData((prev) => ({ ...prev, [id]: response.data }));
    } catch (error: any) {
      dispatch(setErrorNotify(error.response?.data?.message || 'Failed to fetch applicant data'));
    }
  };

  const fetchApplicants = async () => {
    try {
      if (form.applicantId) {
        Object.keys(form.applicantId)
          .filter((key) => form.applicantId[key])
          .forEach((id) => handleGetApplicant(id));
      }
    } catch (error) {
      dispatch(setErrorNotify('Failed to fetch applicants'));
    }
  };

  useEffect(() => {
    fetchApplicants();
  }, []);

  // Get selected applicants count
  const selectedApplicantsCount = form.applicantId ? Object.keys(form.applicantId).filter((key) => form.applicantId[key]).length : 0;

  // Get paginated applicants
  const getPaginatedApplicants = () => {
    const applicantsList = Object.values(applicantsData);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return applicantsList.slice(startIndex, endIndex);
  };

  // Calculate total pages
  const totalPages = Math.ceil(Object.keys(applicantsData).length / itemsPerPage);

  // Use the paginated applicants in the render
  const paginatedApplicants = getPaginatedApplicants();

  return (
    <div className="space-y-4">
      {/* <div className="flex justify-between items-center">
          <p className="text-2xl font-semibold capitalize">
            {form.title} {type}
          </p>

          {type === 'interview' && form.type === 2 && (
            <button className="flex flex-wrap items-center gap-2 px-6 py-2 border border-[#E7E7E7] rounded-xl">
              <CustomIcon definedIcon="interActiveAiInterviewStars" />
              <span className="text-[#5A399E] text-sm font-semibold">Interactive AI Interview</span>
            </button>
          )}
        </div> */}

      <AssessmentCompCard
        name={form.title}
        difficulty={form.difficulty}
        duration={form.duration}
        seniority={form.seniorityLevel}
        questionsNumber={form.questionIds?.length || form.questions?.length || 0}
        categoryName={form?.categoryName}
        subCategories={form?.subCategoryName}
        createdByName={form.authorName}
        createdByDate={new Date(form.dueDate).toLocaleDateString('en-GB', { day: 'numeric', month: 'long', year: 'numeric' })}
        // updatedDate={form.updatedAt}
      />

      <div className="p-3 space-y-3 bg-[#F5F9FF] dark:bg-transparent border border-[#EAEEF4] rounded-lg">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <Icon icon="material-symbols:info-i-rounded" width="15" className="p-0.5 bg-[#7CCCEF] text-white rounded-lg" />
            <span className="thepassBone">Note</span>
          </div>
          <p className="thepassBthree text-[#2F3F53]">
            Test link is available for 3 days starting from <span className="thepassBone">{formatDate(form.startDate)}</span>
          </p>
        </div>
      </div>

      {/* <div className="p-4 space-y-3 bg-[#F9FAFB] border border-[#EFF0F3] rounded-lg">
        <p className="text-[#6B7280] font-medium">
          <span className="capitalize">{type}</span> is available from:
        </p>
        <p className="flex items-center flex-wrap gap-2 text-[#3C3D3E] dark:text-white text-[15px] font-medium">
          <span>{formatDate(form.startDate)}</span>
          <Icon icon="line-md:arrow-right" width="16" />
          <span>{formatDate(form.dueDate)}</span>
        </p>
      </div> */}

      {type === 'interview' && form.avatarName && (
        <>
          <hr />
          <p className="font-semibold">Interviewer</p>

          <div className="flex flex-wrap justify-between items-center gap-2 px-6 py-4 bg-[#F3F4F6] rounded-xl">
            <div className="flex items-center gap-3">
              {Object.entries(AiAvatarModels).find(([_, value]) => value === form?.avatarName)?.[0] ? (
                <img
                  className="size-[75px] rounded-full"
                  src={`/assets/models/${AiAvatarModels.find((model: { value: string }) => model.value === form.avatarName)?.iconPath}`}
                  alt="Avatar"
                />
              ) : (
                <div className="size-[75px] rounded-full bg-gray-200 flex items-center justify-center">
                  <Icon icon="mdi:account" width="40" />
                </div>
              )}
              <div>
                <p className="text-xl font-semibold text-[#181D27] capitalize">{form.avatarName}</p>
                <p className="text-[#535862] text-sm">{form.avatarDescription}</p>
              </div>
            </div>

            {form.avatarLang && (
              <div className="flex items-center gap-2">
                <Icon
                  icon={
                    InterviewLanguages.find((lang: { value: string }) => form.avatarLang === lang.value)?.icon ||
                    'twemoji:flag-for-flag-united-states'
                  }
                  width="40"
                />
                <p className="font-semibold capitalize">{form.avatarLang}</p>
              </div>
            )}
          </div>
        </>
      )}

      {selectedApplicantsCount > 0 && (
        <div className="border border-[#DEE2E4] rounded-xl overflow-hidden">
          <p className="thepassHthree text-[#111827] p-4 bg-[#F9F8FA]">Assigned to :{/* ({selectedApplicantsCount}) */}</p>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 p-4 gap-4">
            {paginatedApplicants?.map((singleApplicant: any, index: number, array) => (
              <div key={singleApplicant?._id} className="shadow-sm rounded-xl p-4">
                <div className="flex gap-4">
                  <div className="size-11 flex justify-center items-center bg-[#EDE9FE] text-[#8D5BF8] font-semibold border border-[#E1E4E8] rounded-full">
                    {singleApplicant?.name
                      ?.split(' ')
                      .map((word: string[]) => word[0])
                      .join('')
                      .toUpperCase()}
                  </div>
                  <div>
                    <p className="text-[#181D27] font-medium">{singleApplicant.name}</p>
                    <p className="text-[#535862] text-[13px]">{singleApplicant.email}</p>
                  </div>
                </div>
                {/* {index < array.length - 1 && <hr />} */}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Pagination */}
      {Object.keys(applicantsData)?.length > itemsPerPage && (
        <div className="flex justify-center items-center px-4 my-1">
          <Pagination
            theme={StaticData.paginationTheme}
            currentPage={currentPage}
            onPageChange={(page) => setCurrentPage(page)}
            showIcons
            totalPages={totalPages}
            layout={screen.gt.md() ? 'pagination' : 'navigation'}
            previousLabel={screen.lt.sm() ? '' : 'Previous'}
            nextLabel={screen.lt.sm() ? '' : 'Next'}
          />
        </div>
      )}
    </div>
  );
};
