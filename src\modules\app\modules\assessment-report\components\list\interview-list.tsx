// React
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Core
import { TestDifficulty, TestSeniorityLevel, AvarageScore, Table, DurationFieldColumn, NameFieldColumn, CategoryFieldColumn } from 'src';
import { ComponentOverlayInterview } from '../single/component-overlay-interview';
import { ComponentInterviewRecord } from '../single/interview-recored';

import { useFetchList, RootState, useAppSelector, UserData, Tags, useAppDispatch, setBackupListInterview } from 'UI/src';

import { ApplicantDetails } from '../../../applicants/components/applicant-data';

// Define the type for your row based on all used fields
interface RowType {
  _id: string;
  title: string;
  templateId: string;
  type: number;
  categoryName?: string[];
  seniorityLevel: number;
  difficulty: number;
  duration: string | number;
  assignedCount?: number;
  averageScore?: number | string;
  sourceLink?: string;
}

export const InterviewList = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // State
  const [showMoreMap, setShowMoreMap] = useState<Record<string, boolean>>({});
  const [backupList, setBackupList] = useState<RowType[]>([]);
  const [showOverlay, setShowOverlay] = useState(false);
  const [selectedRow, setSelectedRow] = useState<RowType | null>(null);
  const [showInterviewRecord, setShowInterviewRecord] = useState(false);
  const [selectedApplicantRow, setSelectedApplicantRow] = useState<any | null>(null);
  const [applicantDetails, setApplicantDetails] = useState<ApplicantDetails | null>(null);

  // Hooks
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const initialFilters = {
    // ...(userData.trackId
    //   ? {}
    //   : {
    category: {
      label: 'Category',
      lookup: 'category',
    },
    // }),
    seniorityLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
    averageScore: {
      label: 'Average Score',
      enum: 'AverageScore',
    },
  };
  const { ready, loading, list, count, search, pagination, filters, setFilters } = useFetchList('ai-interview/generated-interviews', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  });

  const handleInterviewRecord = async (row: any) => {
    setShowInterviewRecord(true);
    setApplicantDetails(row);
  };

  const handleOnClickCondition = (row: RowType) => {
    /* 
      templateId or type === 3 -> view
      else
      randomId -> popUp
    */
    return row?.templateId || row?.type === 3;
  };

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
      dispatch(setBackupListInterview(list.length));
    }
  }, [list]);

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Interview Reports"
        searchPlaceholder="Search for template name..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          templateName: (_: unknown, row: RowType) => (
            <NameFieldColumn
              id={row?._id}
              name={row?.title ? row?.title : 'Interactive Interview'}
              showMoreMap={showMoreMap}
              onClick={() => {
                if (handleOnClickCondition(row)) {
                  navigate(`/app/assessment-report/view/interview/${row?.templateId}`);
                } else {
                  setShowOverlay(true);
                  setSelectedRow(row);
                }
              }}
            />
          ),
          category: (_: unknown, row: RowType) => <CategoryFieldColumn categoryNameArray={row?.categoryName} />,
          seniorityLevel: (_: unknown, row: { seniorityLevel: number }) => {
            const getSeniorityLevelText = (level: any): string => {
              // Convert to number if it's a string
              const numLevel = typeof level === 'string' ? parseInt(level) : level;

              switch (numLevel) {
                case 1:
                  return 'intern';
                case 2:
                  return 'fresh';
                case 3:
                  return 'junior';
                case 4:
                  return 'mid-level';
                case 5:
                  return 'senior';
                default:
                  return 'intern';
              }
            };

            return (
              <div className="w-fit">
                <Tags type={getSeniorityLevelText(row?.seniorityLevel)} color="bg-transparent" />
              </div>
            );
          },
          difficulty: (_: unknown, row: { difficulty: number }) => {
            const getDifficultyText = (level: number): string => {
              switch (level) {
                case 1:
                  return 'easy';
                case 2:
                  return 'medium';
                case 3:
                  return 'hard';
                default:
                  return 'easy';
              }
            };

            const getDifficultyColor = (level: number): string => {
              switch (level) {
                case 1:
                  return 'bg-[#EEFFF1] text-[#056816]';
                case 2:
                  return 'bg-[#FFFCDF] text-[#BA8500]';
                case 3:
                  return 'bg-[#FFECE9] text-[#A80000]';
                default:
                  return 'bg-[#EEFFF1] text-[#056816]';
              }
            };

            return (
              <div className="w-fit">
                <Tags type={getDifficultyText(row?.difficulty)} color={getDifficultyColor(row?.difficulty)} />
              </div>
            );
          },
          estimation: (_: unknown, row: RowType) => <DurationFieldColumn duration={row?.duration} />,
          usage: (_: unknown, row: RowType) => <div className="capitalize font-medium text-sm text-[#535862] truncate">{row?.assignedCount}</div>,
          averageScore: (_: unknown, row: { averageScore: number }) => {
            const getScoreColor = (score: number) => {
              if (score >= 0 && score < 50) {
                return 'bg-[#FFECE9] text-[#A80000]';
              } else if (score >= 50 && score < 75) {
                return 'bg-[#FFEDD8] text-[#E9760F]';
              } else if (score >= 75 && score < 100) {
                return 'bg-[#FFFCDF] text-[#BA8500]';
              } else if (score >= 100) {
                return 'bg-[#EEFFF1] text-[#056816]';
              }
              return 'bg-gray-100 text-gray-800';
            };

            const getScoreText = (score: number) => {
              if (score === null || score === undefined) return '—';
              return `${score}%`;
            };

            return (
              <div className="w-fit">
                <Tags type="score" color={getScoreColor(row?.averageScore)}>
                  {getScoreText(row?.averageScore)}
                </Tags>
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'templateName',
            label: 'Name',
            primary: true,
            width: '22%',
          },
          {
            key: 'category',
            label: 'Category',
            primary: true,
            width: '23%',
            cardFooterProp: true,
          },
          {
            key: 'seniorityLevel',
            label: 'Seniority Level',
            primary: true,
            width: '18%',
            inline: true,
          },
          {
            key: 'difficulty',
            label: 'Difficulty',
            width: '15%',
            inline: true,
          },
          {
            key: 'estimation',
            label: 'Estimation',
            width: '15%',
          },
          {
            key: 'averageScore',
            label: 'Average Score',
            width: '18%',
          },
          {
            key: 'usage',
            label: 'Usage',
            width: '10%',
          },

          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_: unknown, row: RowType) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: handleOnClickCondition(row) ? `/app/assessment-report/view/interview/${row?.templateId}/generated links` : undefined,
                  onClick: !handleOnClickCondition(row)
                    ? () => {
                        setSelectedRow(row);
                        setShowOverlay(true);
                      }
                    : undefined,
                },
              ];
            },
          },
        ]}
        groups={[
          {
            name: 'group1',
            keys: [['seniorityLevel', 'difficulty'], ['estimation']], // from table
          },
        ]}
        placeholder={{
          title: 'No reports generated yet',
          subTitle: 'Assign assessments to applicants to generate comprehensive evaluation reports.',
          image: '/UI/src/assets/placeholder/NoReports.svg',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap as any}
        setShowMoreMap={setShowMoreMap}
        hideJumbotron
        isScrollableTabsExists
        addButtonPermission={undefined}
      />

      {showOverlay && selectedRow && (
        <ComponentOverlayInterview
          id={selectedRow.sourceLink || selectedRow._id}
          type={'interview'}
          onClose={() => setShowOverlay(false)}
          onInterviewRecord={handleInterviewRecord}
        />
      )}

      {showInterviewRecord && applicantDetails && (
        <ComponentInterviewRecord
          onClose={() => setShowInterviewRecord(false)}
          assessmentId={applicantDetails.assessmentId as string}
          applicantId={applicantDetails.applicantId as string}
        />
      )}
    </>
  );
};
