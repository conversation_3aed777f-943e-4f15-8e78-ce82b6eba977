import React, { FC, useState } from 'react';
import { RoleListItem } from './role-list-item';
// ToggleSwitch removed from flowbite-react, using custom component

// Components
import { Dialog, Select, Button, TextInput, Icon, Checkbox, ToggleFilter } from 'src';
import { useValidate, Form, initializeForm, RootState, setFieldValue, useAppDispatch, useAppSelector, Regex, Api, StaticData, useFetchList, setSearchResult, RoleUser, ToggleSwitch } from 'UI/src';
import { fetchRole, createRole, updateRole, fetchRoleUsers, searchRolePermissions } from 'UI/src/middlewares/Roles.middleware';
import { searchUsers } from 'UI/src/middlewares/Users.middleware';
import { setErrorNotify, setNotifyMessage, UserPermissionsGroups, type PermissionGroup, type Permission } from 'UI';
import { UserListItem } from './user-list-item';

// Types
type MultiStepRolesCreationDialogProps = {
  onClose: () => void;
  id: string | null;
  closeCreateDialogVisibility: () => void;
  onCreate: () => void;
  view?: boolean;
};

type FormState = {
  name: string;
  permissions: string[];
  userIds: string[];
};

type PermissionOption = {
  name: string;
};

type Step = {
  label: string;
};

type AssignedUsersPagination = {
  page: number;
  size: number;
}

const STEPS: Step[] = [{ label: 'Role Information' }, { label: 'Permissions' }, { label: 'Review' }];

export const MultiStepRolesCreationDialog: FC<MultiStepRolesCreationDialogProps> = ({
  onClose,
  id,
  closeCreateDialogVisibility,
  onCreate,
  view = false,
}) => {
  // State
  const [currentStep, setCurrentStep] = useState(0);
  const [child, setChild] = useState<PermissionOption[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [toggleAssignOption, setToggleAssignOption] = useState<boolean>(false);
  // const [searchUsersKeyword, setSearchUsersKeyword] = useState<string>('');
  const [permissionData, setPermissionData] = useState<PermissionGroup[]>(UserPermissionsGroups);
  const [usersList, setUsersList] = useState<RoleUser[]>([]);
  const [assignedUsers, setAssignedUsers] = useState<RoleUser[]>([]);
  const [assignedUsersCount, setAssignedUsersCount] = useState<number>(0);
  const [assignedUsersPagination, setAssignedUsersPagination] = useState<AssignedUsersPagination>({
    page: 1,
    size: 20
  });
  const [assignedUsersSearch, setAssignedUsersSearch] = useState<string>('');

  // Hooks
  const dispatch = useAppDispatch();
  const { isRequired, isNotSpaces, minLength, maxLength, validateRegex } = useValidate();
  const form = useAppSelector((state: RootState) => state.form.data);

  // Check if we're editing an existing role
  React.useEffect(() => {
    if (id) {
      setIsEditing(true);
      handleGet();
      handleGetRoleUsers();
    } else {
      setIsEditing(false);
      // Reset form for new role creation
      dispatch(initializeForm({}));
    }
  }, [id]);

  // Handle form data loading
  React.useEffect(() => {
    if (isEditing && form.name) {
      console.log('Form data loaded for editing:', form);
    }
  }, [isEditing, form.name]);

  const handleClose = () => {
    // Reset state when closing
    setCurrentStep(0);
    setIsEditing(false);
    dispatch(initializeForm({}));
    onClose();
  };

  // Methods
  const handleUpdateAllPermissions = (permissions: number[]) => {
    setPermissionData((prev) => {

      let updated = [...prev];

      updated = updated.map((group) => {
        let { checked, children } = group;
        children = children.map((item) => {
          return {
            ...item,
            checked: permissions.includes(item.value),
          }
        })
        checked = children.every((permission) => permission.checked)
        return { ...group, checked, children }
      })


      return updated;
    });
  }

const handleUpdateAllUsers = (usersData: RoleUser[]) => {
  setUsersList((prev) => {
    const updated = prev.map((user) => {
     return {
        ...user,
        checked: usersData.some((item) => item._id === user._id),
      }
    })
    return updated;
  });
};
  const handleGet = async () => {
    if (!id) return;
    try {
      const result = await dispatch(fetchRole(id)).unwrap();
      console.log(`roles/single/${id}`, result);
      const permissions = result.permissions.map((item: { value: number }) => item.value);
      const formData = {
        ...result,
        permissions
      }

      dispatch(initializeForm(formData));
      handleUpdateAllPermissions(permissions);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.message || 'Failed to fetch role'));
    }
  };

  const handleGetRoleUsers = async () => {
    if (!id) return;
    try {
      const { items, count } = await dispatch(fetchRoleUsers({
        id,
        pagination: assignedUsersPagination,
        search: assignedUsersSearch
      })).unwrap();
      setAssignedUsers(items);
      handleUpdateAllUsers(items);
      setAssignedUsersCount(count);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.message || 'Failed to fetch role'));
    }
  };

  const { loading: usersLoading,
    list: rawUsersData,
    count: usersCount,
    refresh: refreshUsers,
    search: searchUsersKeyword,
    pagination: usersPagination } = useFetchList('users/list', {
    });


  React.useEffect(() => {
    const data = rawUsersData.map((item) => {
      const { _id, name, email } = item;
      return { _id, name, email, checked: false }
    });
    setUsersList(data)
  }, [rawUsersData]);

  const handleNext = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSearch = async (keyword: string) => {
    try {
      const result = await Api.get('roles/single/permissions/search', {
        keyword: keyword,
        exclude: form.permissions || [],
      });

      const transformedPermissions: PermissionOption[] = result.data.map((permission: string) => ({ name: permission }));
      setChild(transformedPermissions);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };


  const handleCreateRole = async () => {
    try {
      setLoading(true);

      // Validate required fields
      if (!form.name || form.name.trim().length < 2) {
        dispatch(setErrorNotify('Role name is required and must be at least 2 characters long'));
        return;
      }

      // Prepare the data to send
      const roleData = {
        name: form.name.trim(),
        permissions: form.permissions || ['1'],
        ...(form.inheritFrom && { inheritFrom: form.inheritFrom }),
        userIds: form.userIds
      };

      console.log('Submitting role data:', roleData);
      console.log('Is editing:', isEditing);
      console.log('Role ID:', id);

      if (isEditing && id) {
        // Update existing role
        console.log('Updating role with ID:', id);
        await dispatch(updateRole({ id, data: roleData })).unwrap();
        dispatch(setNotifyMessage('Role updated successfully!'));
      } else {
        // Create new role
        console.log('Creating new role');
        await dispatch(createRole(roleData)).unwrap();
        dispatch(setNotifyMessage('Role added successfully!'));
      }
      onCreate();
      closeCreateDialogVisibility();
    } catch (error: any) {
      console.error('Error creating/updating role:', error);
      dispatch(setErrorNotify(error?.message || `Failed to ${isEditing ? 'update' : 'create'} role`));
    } finally {
      setLoading(false);
    }
  };

  const canProceedToNext = () => {
    switch (currentStep) {
      case 0: // Role Information
        // When editing, allow proceeding if name exists or if we're creating a new role with valid name
        if (isEditing) {
          return form.name && form.name.trim().length >= 2;
        }
        return form.name && form.name.trim().length >= 2;
      case 1: // Add Permissions
        return true; // Allow proceeding even without permissions initially
      default:
        return true;
    }
  };

  const canSubmit = () => {
    if (isEditing) {
      return form.name && form.name.trim().length >= 2;
    }
    return form.name && form.name.trim().length >= 2;
  };

  const handleUpdateFormPermissions = (groupPermissionsNewValue: PermissionGroup[], groupIndex: number, updated: "group" | "permissions", permissionIndex?: number) => {
    let newPermissions = form.permissions ? form.permissions : [];

    if (updated === 'group') {
      const group = groupPermissionsNewValue[groupIndex];
      const groupPermissions = group.children.map((child) => child.value);

      if (group.checked) {
        newPermissions = Array.from(new Set([...newPermissions, ...groupPermissions]));
      } else {
        newPermissions = newPermissions.filter((perm: number) => !groupPermissions.includes(perm));
      }
    } else {
      if (permissionIndex) {
        const permission = groupPermissionsNewValue[groupIndex].children[permissionIndex];
        newPermissions = !permission.checked ? newPermissions.filter((perm: number) => perm !== permission.value) : [...newPermissions, permission.value]
      }
    }

    dispatch(setFieldValue({
      path: 'permissions',
      value: newPermissions
    }));
  };

  const handlePermissionGroup = (groupIndex: number) => {
    setPermissionData((prev) => {
      const updated = [...prev];

      // clone group
      const group = { ...updated[groupIndex] };

      // toggle group value
      const newValue = !group.checked;

      // update children
      group.children = group.children.map((permission) => ({
        ...permission,
        checked: newValue,
      }));

      group.checked = newValue;
      updated[groupIndex] = group;

      handleUpdateFormPermissions(updated, groupIndex, 'group')

      return updated;
    });
  };

  const handleUpdatePermissions = (groupIndex: number, permissionIndex: number) => {
    setPermissionData((prev) => {
      const updated = [...prev];

      const group = { ...updated[groupIndex] };

      const children = [...group.children];

      children[permissionIndex] = {
        ...children[permissionIndex],
        checked: !children[permissionIndex].checked,
      };

      group.children = children;
      group.checked = children.every((permission) => permission.checked);
      updated[groupIndex] = group;

      handleUpdateFormPermissions(updated, groupIndex, 'permissions', permissionIndex);

      return updated;
    });
  }

  const handleUpdateUsersData = (user: RoleUser) => {
    setUsersList((prev) => {
      const updated = [...prev];
      const userIndex = updated.findIndex((item) => item._id === user._id)
      updated[userIndex] = { ...updated[userIndex], checked: !updated[userIndex].checked };
      const formUserIds = updated.filter((user) => user.checked).map((user) => user._id);

      dispatch(setFieldValue({
        path: 'userIds',
        value: [...formUserIds]
      }));

      return updated;
    })
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Role Information
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <TextInput
                name="name"
                label="Role Name"
                value={form.name || ''}
                placeholder="e.g: Hiring Manager"
                onChange={(value: any) => dispatch(setFieldValue({ path: 'name', value }))}
                validators={[isRequired(), minLength(2), maxLength(50), isNotSpaces(), validateRegex(Regex.name)]}
              />
              <TextInput
                name="inheritFrom"
                label="Inherit from existing role (optional)"
                placeholder="Select role to inherit permissions from"
                onChange={(value: any) => dispatch(setFieldValue({ path: 'inheritFrom', value }))}
              />
            </div>
          </div>
        );

      case 1: // Add Permissions
        return (
          <div className="space-y-7">
            {permissionData.map((set, index) => (
              <div className="space-y-1" key={index}>
                <div className="flex items-center gap-1">
                  <Checkbox
                    fullWidth={false}
                    value={set.checked}
                    onChange={() => handlePermissionGroup(index)}
                    theme={StaticData?.customThemeCheckbox}
                    className="cursor-pointer"
                    preventSendingMail={false}
                    isCustomLabel={false}
                    label={set.label}
                    labelClass={'text-thepassBtwo'}
                  />
                </div>
                <div className="rowflex-grow grid grid-cols-3 sm:grid-cols-4 lg:gap-3 sm:gap-x-8 px-2 sm:px-1">
                  {set.children.map((permission, permissionIndex) => (
                    <div className="flex items-center gap-1" key={`${index}-${permissionIndex}`}>
                      <Checkbox
                        fullWidth={false}
                        value={permission.checked}
                        onChange={() => handleUpdatePermissions(index, permissionIndex)}
                        theme={StaticData?.customThemeCheckbox}
                        className="cursor-pointer"
                        preventSendingMail={false}
                        isCustomLabel={false}
                        label={permission.name}
                        labelClass={'text-thepassBtwo'}
                      />
                    </div>
                  ))}
                </div>
                {index <= UserPermissionsGroups.length - 2 && <div className="bg-gray-200 h-[1px] mt-2"></div>}
              </div>
            ))}
          </div>
        );

      case 2: // Review & Assign
        return (
          <div>
            <p className="font-medium mb-5">Role Preview</p>
            <div className="rounded-lg border border-[#DEE2E4] p-3 space-y-4">
              <div className="flex gap-3">
                <div className="flex gap-2 items-center">
                  <p>
                    <Icon icon="lucide:shield-user" width="20" />
                  </p>
                  <p className="text-sm text-[#4A5568]">Role Name:</p>
                </div>

                <p className="text-sm font-semibold">{form.name}</p>
              </div>
              <p className="text-sm text-[#3A4458]">Permissions</p>
              <div className="flex flex-wrap gap-3">
                {permissionData.filter((set) => set.children.some((item) => item.checked)).map((set) => (
                  set.children.map((item) => (
                    <div className="border border-[#DEE2E4] rounded-lg w-fit py-1 px-3 flex items-center gap-x-0.5">
                      <span className='text-thepassBtwo'>{`${set.label} > `}</span>
                      <span className='thepassBfour'>{item.label.toLocaleLowerCase()}</span>
                    </div>
                  ))
                ))}
              </div>
            </div>
            <div className="flex items-center gap-4 mt-9 mb-5">
              Assign users to this role (optional)
              <ToggleSwitch
                sizing="md"
                checked={toggleAssignOption}
                value={toggleAssignOption.toString()}
                onChange={() => setToggleAssignOption((prev) => !prev)}
                color="info"
              />
            </div>
            {toggleAssignOption && (
              <div className="border border-[#E2E3E4] rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <div className="flex gap-4">
                    Users <p className="px-2 py-[2px] text-sm bg-[#F1E9FE] text-[#743AF5] rounded-lg w-fit h-fit">{usersCount}</p>
                  </div>
                  <div>
                    <div className={`rounded-lg flex w-full flex-row items-center space-x-3 space-y-0 justify-between shadow-sm`}>
                      <div className="relative w-full">
                        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                          <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                        </div>
                        <input
                          name="searchUsersKeyword"
                          placeholder="Search for Users"
                          className="truncate border-gray-200 text-gray-800 text-[13.5px] rounded-lg block w-full sm:min-w-96 pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0 focus:border-gray-300"
                          value={searchUsersKeyword.value}
                          onInput={(e: React.FormEvent<HTMLInputElement>) => {
                            searchUsersKeyword.update(e.currentTarget.value.trim());
                            refreshUsers();
                          }}
                        />
                      </div>
                    </div>
                    {/* <ToggleFilter filters={filters} /> */}
                  </div>
                </div>
                <div className="flex flex-wrap gap-9 mt-8">
                  {usersList.map((user, index) => (
                    <UserListItem user={user} onChangeValue={(user: RoleUser) => handleUpdateUsersData(user)} key={index} isView />
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {STEPS.map((step, index) => (
        <React.Fragment key={step.label}>
          <div className="flex items-center">
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-200 ${currentStep >= index ? 'bg-purple-600 text-white shadow-lg' : 'bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-400'
                }`}
            >
              {currentStep > index ? <span className="text-white text-lg">✓</span> : index + 1}
            </div>
            <span
              className={`ml-3 text-sm font-medium transition-colors duration-200 ${currentStep >= index ? 'text-purple-600 dark:text-purple-400 font-semibold' : 'text-gray-500 dark:text-gray-400'
                }`}
            >
              {step.label}
            </span>
          </div>
          {index < STEPS.length - 1 && (
            <div
              className={`w-16 h-0.5 mx-6 transition-all duration-200 ${currentStep > index ? 'bg-purple-600' : 'bg-gray-200 dark:bg-gray-700'}`}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  );

  return (
    <Dialog
      show
      popup
      size="4xl"
      modalHeader={view ? 'View Role' : isEditing ? 'Edit Role' : 'Create New Role'}
      onClose={handleClose}
      overflowVisible={true}
    >
      {view ? (
        <div className="space-y-7">
          <div className="space-y-3">
            <div className="flex gap-3">
              <div className="flex gap-2 items-center">
                <p>
                  <Icon icon="lucide:shield-user" width="20" />
                </p>
                <p className="text-sm text-[#4A5568]">Role Name:</p>
              </div>

              <p className="text-sm font-semibold">{form.name}</p>
            </div>
            <div className="flex gap-4 items-center">
              <p className="text-sm">Assigned Permissions</p>
              <p className="px-2 py-[2px] text-sm bg-[#F1E9FE] text-[#743AF5] rounded-lg">{form.permissions?.length}</p>
            </div>
            <div className="flex flex-wrap gap-3 w-full rounded-lg border border-[#DEE2E4] bg-[#F9FAFB] p-3">
              {permissionData.filter((set) => set.children.some((item) => item.checked)).map((set) => (
                set.children.map((item) => (
                  <div className="border border-[#DEE2E4] rounded-lg w-fit py-1 px-3 flex items-center gap-x-0.5">
                    <span className='text-thepassBtwo'>{`${set.label} > `}</span>
                    <span className='thepassBfour'>{item.label.toLocaleLowerCase()}</span>
                  </div>
                ))
              ))}
            </div>
          </div>
          <div className="space-y-8">
            <div className="space-y-3">
              <div className="flex gap-4 items-center">
                <p className="text-sm">Assigned Users</p>
                <p className="px-2 py-[2px] text-sm bg-[#F1E9FE] text-[#743AF5] rounded-lg">{form.numOfUsers}</p>
              </div>
              <div>
                <div className={`rounded-lg flex w-full flex-row items-center space-x-3 space-y-0 justify-between shadow-sm`}>
                  <div className="relative w-full">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search for Users"
                      className="bg-gray-white border truncate border-gray-200 text-gray-800 text-[13.5px] rounded-lg block w-full pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0 focus:border-gray-300"
                      value={assignedUsersSearch}
                      onInput={(e) => {
                        setAssignedUsersSearch(e.currentTarget.value.trim());
                        setAssignedUsersPagination({ page: 1, size: 20 });
                        handleGetRoleUsers();
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="flex justify-start gap-5 flex-wrap">
              {assignedUsers.map((user) => (
                <div className="flex items-center gap-3" key={user._id}>
                  <UserListItem user={user} key={user._id} />
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Step Indicator */}
          {renderStepIndicator()}

          {/* Step Content */}
          <div className="min-h-[320px] px-2">{renderStepContent()}</div>

          {/* Action Buttons */}
          <div className="grid grid-cols-2 gap-3 justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
            {currentStep === 0 ? (
              <Button type="button" label="Cancel" tertiary onClick={handleClose} disabled={loading} className="px-6 py-2" />
            ) : (
              <Button type="button" label="Back" tertiary onClick={handleBack} disabled={loading} className="px-6 py-2" />
            )}

            {currentStep < STEPS.length - 1 ? (
              <Button
                type="button"
                label={`Next: ${STEPS[currentStep + 1].label.split(' ')[0]}`}
                onClick={handleNext}
                disabled={!canProceedToNext() || loading}
                className="px-8 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium"
              />
            ) : (
              <Button
                type="button"
                label={isEditing ? 'Update Role' : 'Create Role'}
                onClick={handleCreateRole}
                loading={loading}
                disabled={loading || !canSubmit()}
                className="px-8 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium"
              />
            )}
          </div>
        </div>
      )}
    </Dialog>
  );
};
