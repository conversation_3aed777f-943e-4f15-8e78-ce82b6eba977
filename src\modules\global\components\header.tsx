import { useLocation, Link, useNavigate } from 'react-router-dom';

import { Icon, Logo } from 'src';
import { useScreenSize, RootState, useAppSelector, UserData, Button } from 'UI/src';

interface headerProps {
  showThemeIcon: boolean;
  isDrawerVisible: boolean;
  setIsDrawerVisible: (value: boolean) => void;
  hideTitleOnSM?: boolean;
}

export const Header = ({ showThemeIcon, isDrawerVisible, setIsDrawerVisible, hideTitleOnSM }: headerProps) => {
  // Hooks
  const location = useLocation();
  const navigate = useNavigate();
  const screen = useScreenSize();

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Computed
  const getActiveClasess = (itemPath: string) => {
    const currentPath = location.pathname;
    const currentHash = location.hash;

    // Debug logging

    // Check if current path matches the item path
    const isPathActive = currentPath.includes(itemPath);
    const isHashActive = currentHash.includes(itemPath.slice(1));

    if (isPathActive || isHashActive) {
      console.log('✅ Active path found:', itemPath);
      return 'text-primaryPurple dark:text-primaryPurple';
    }

    return 'text-[#656C86] dark:text-white';
  };

  const handleHashNavigation = (e: React.MouseEvent<HTMLLIElement>, path: string) => {
    if (path.includes('#')) {
      e.preventDefault();

      const hash = path.split('#')[1];

      if (location.pathname === '/' || path.startsWith('/#')) {
        const element = document.getElementById(hash);
        if (element) {
          const headerHeight = 80;
          const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
          const offsetPosition = elementPosition - headerHeight;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth',
          });
        }
      } else {
        // Navigate to home page first, then scroll to the section
        navigate('/');

        // Use multiple attempts to find the element with increasing delays
        const attemptScroll = (attempts = 0) => {
          setTimeout(() => {
            const element = document.getElementById(hash);

            if (element) {
              const headerHeight = 80;
              const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
              const offsetPosition = elementPosition - headerHeight;

              window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth',
              });
            } else if (attempts < 5) {
              // Try again with more delay
              attemptScroll(attempts + 1);
            }
          }, 200 + attempts * 100); // Start with 200ms, increase by 100ms each attempt
        };

        attemptScroll();
      }
    }
  };

  // Items
  const menuItems = [
    {
      label: 'How it Works',
      path: '/#how-It-Works',
      icon: 'ri:mail-send-line',
      // visible: !location.pathname.includes('quick-assign'),
    },
    {
      label: 'Library',
      path: '/programming-test',
      icon: 'ri:mail-send-line',
      // visible: !location.pathname.includes('quick-assign'),
    },
    {
      label: 'Why Choose Us',
      path: '/#why-choose-us',
      icon: 'mdi:information-outline',
      // visible: !location.pathname.includes('quick-assign'),
    },

    {
      label: 'Pricing',
      path: '/pricing',
      icon: 'arcticons:priceconverter',
      // visible: !location.pathname.includes('quick-assign'),
    },
    {
      label: 'Contact Us',
      path: '/contact-us',
      icon: 'healthicons:contact-support',
      // visible: !location.pathname.includes('quick-assign'),
    },
    // {
    //   label: 'Terms of Service',
    //   path: '/terms',
    //   icon: 'mdi:file-document-outline',
    //   visible: true,
    // },
  ];

  //methods
  const handleNavigate = () => {
    if (userData?.access_token) {
      navigate('/app/dashboard');
    } else {
      navigate('/auth/register');
    }
  };

  return (
    <nav className="bg-white dark:bg-darkBackgroundCard mb-1 px-4 pt-[14px] pb-[10px] dark:border-[#374151] fixed left-0 right-0 top-0 z-[60]">
      <div className="md:mx-6 lg:mx-auto lg:max-w-[89%] xl:max-w-[87%]">
        <div className="flex justify-between ">
          <div className="flex justify-between items-center ">
            {screen.lt.xl() && (
              <button
                onClick={() => setIsDrawerVisible(!isDrawerVisible)}
                className="p-2 mr-2 text-gray-600 rounded-lg cursor-pointer hover:text-gray-900 hover:bg-gray-100 focus:bg-gray-100 dark:focus:bg-gray-700 focus:ring-2 focus:ring-gray-100 dark:focus:ring-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white"
              >
                <Icon icon="streamline:interface-setting-menu-1-button-parallel-horizontal-lines-menu-navigation-three-hamburger" />
              </button>
            )}
            <div className="flex items-center justify-between  cursor-pointer" onClick={() => navigate('/')}>
              <Logo className="h-8" icon={screen.gt.sm() && screen.lt.lg() ? true : false} />
            </div>
          </div>
          <div className="hidden xl:flex justify-between items-center ">
            <ul className="flex flex-row items-center justify-around gap-10 capitalize">
              {menuItems.map((item) => (
                <li key={item.path} onClick={(e) => handleHashNavigation(e, item.path)}>
                  <Link to={item.path} className={`cursor-pointer text-base font-normal transition-all duration-75 ease-in `}>
                    <span
                      className={`${getActiveClasess(
                        item.path
                      )}  hover:text-primaryPurple  dark:hover:text-primaryPurple max-md:text-xs max-lg:text-xs md:text-sm 2xl:text-base lg:text-base`}
                    >
                      {item.label}
                    </span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div className="flex items-center gap-4 md:gap-3 xl:gap-4">
            {/* <!-- Dark --> */}
            {/* {showThemeIcon && (
              <button
                type="button"
                className="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 flex items-center justify-center"
                onClick={switchDarkMode}
              >
                <Icon icon={isDark ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'} width="20px" />
              </button>
            )} */}

            {!location.pathname.includes('login') && (
              <Button
                label={!userData?.access_token ? 'Login' : 'View Account'}
                onClick={() => navigate('/auth/login')}
                className={hideTitleOnSM ? 'hidden xl:flex' : ''}
                colorType="secondary"
              />
            )}

            {!location.pathname.includes('register') && !userData?.access_token && (
              <Button
                label={
                  <div className="flex gap-1 items-center">
                    Get Started <Icon className="mt-.5" icon="lsicon:arrow-right-outline" width="19" />
                  </div>
                }
                onClick={() => navigate('/auth/register')}
                colorType="magic"
              />
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};
