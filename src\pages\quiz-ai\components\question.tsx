const Option = ({ text, onClick }: { text: string; onClick: (text: string) => void }) => (
  <div
    className="text-sm font-semibold py-1.5 px-3 rounded-full border border-slate-300 hover:bg-slate-50  transition-all cursor-pointer w-full"
    onClick={() => onClick(text)}
  >
    {text}
  </div>
);

const TrueFalseOption = ({ text, onClick }: { text: string; onClick: (text: string) => void }) => (
  <div
    onClick={() => onClick(text.toLowerCase())}
    className="flex border border-slate-300 hover:bg-slate-50   transition-all cursor-pointer rounded-full py-1.5 px-8"
  >
    {text}
  </div>
);

export const Question = ({ question, sendManualReply, loading }: any) => {
  return (
    <div className="p-5 py-2 ms-5">
      {question?.type === 'mcq' && (
        <div className={`flex flex-col gap-2 ${loading && 'pointer-events-none opacity-50'}`}>
          {question.options.map((option: any) => (
            <Option key={option} text={option} onClick={sendManualReply} />
          ))}
        </div>
      )}
      {question?.type === 'true_false' && (
        <div className={`flex gap-2 mt-4 text-sm font-semibold ${loading && 'pointer-events-none opacity-50'}`}>
          <TrueFalseOption text="True" onClick={sendManualReply} />
          <TrueFalseOption text="False" onClick={sendManualReply} />
        </div>
      )}
    </div>
  );
};
