export interface UserProfile {
  id: string;
  email: string;
  name: string;
  roleName: string;
  gender: number;
}

export interface Roles {
  roleId: string;
  name: string;
  viewName: string;
}

export interface Track {
  trackId: string;
  name: string;
}

//
export interface UserData {
  _id: string;
  name: string;
  email: string;
  gender: number;
  role: {
    roleId: string;
    name: string;
  };
  roles: string[];
  organizationId: string;
  authorId: string;
  fullName: string;
  createdAt: string;
  trackId?: string;
  access_token: string;
}

export type UsersListItem = UserData & {
  roleId: string;
  roleName: string;
  roleViewName: string;
  authorName: string;
  track: {
    trackId: string;
    name: string;
  };
  status: number;
  lastActive: string;
};

export interface UsersList {
  items: UsersListItem[];
  count: number;
}

export type CreateUserReq = {
  name: string;
  organizationId: string;
  email: string;
  roles: string;
  gender: number;
  track: string;
  provider: string;
  password: string;
  googleId: string;
};

export type GetUserIdRes = {
  _id: string;
  name: string;
  email: string;
  gender: number;
  roles: Roles;
  authorId: string;
  fullName: string;
  createdAt: string;
};
export interface Roles {
  roleId: string;
  name: string;
  viewName: string;
}

export interface CurrentUserPermission {
  key: string;
  value: number;
}


export type CurrentUserRole = {
  name: string;
  permissions: CurrentUserPermission[];
};

export type GetUsersOrganizationRes = {
  _id: string;
  name: string;
  email: string;
  gender: number;
  createdAt: string;
  roleName: string;
  roleId: string;
  authorId: string;
  authorName: string;
  track: Track;
  status: number;
  lastActive: string;
};
export interface Track {
  trackId: string;
  name: string;
}

export type GetUsersOrganizationReq = {
  id: string;
  search?: string;
  page?: number;
  size?: number;
  gender?: string[];
  role?: string[];
  scope?: string[];
};

export type GetUsersOrganizationListRes = {
  list: GetUsersOrganizationRes[];
  count: number;
};
