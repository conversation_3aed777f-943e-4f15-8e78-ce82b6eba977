// React
import { useEffect, useState } from 'react';
// Date formate
import { format, isValid } from 'date-fns';

import { useParams } from 'react-router-dom';

// Core
import { CustomIcon, Icon, Table, FormatDateFieldColumn } from 'src';
import { Tags } from 'UI/src';

// Components

import { ComponentOverlayInterview } from './component-overlay-interview';
import { ComponentInterviewRecord } from './interview-recored';
import { useFetchList, useAppDispatch } from 'UI/src';
import { setNotifyMessage } from 'UI';

interface GeneratedLinkRow {
  _id: string;
  sourceLink?: string;
  assessmentUrl: string;
  createdAt: string;
  assignedCount: number;
  expired: boolean | null;
  completionRate?: number;
  averageScore?: number;
  assessmentId?: string;
  applicantId?: string;
  [key: string]: any;
}

type ShowMoreMap = Record<string, boolean>;

export const GeneratedLinks = () => {
  const { quizId, type } = useParams<{ quizId: string; type: string }>();
  const [selectedRow, setSelectedRow] = useState<GeneratedLinkRow | null>(null);
  const [showMoreMap, setShowMoreMap] = useState<ShowMoreMap>({});
  const [backupList, setBackupList] = useState<GeneratedLinkRow[]>([]);
  const [showInterviewRecord, setShowInterviewRecord] = useState(false);
  const [applicantDetails, setApplicantDetails] = useState<GeneratedLinkRow | null>(null);
  const [showOverlay, setShowOverlay] = useState(false);

  const dispatch = useAppDispatch();
  const initialFilters = {
    status: {
      label: 'Status',
      enum: 'SubscriptionStatus',
    },
    averageScore: {
      label: 'Average Score',
      enum: 'AverageScore',
    },
  };

  const { ready, loading, list, count, pagination, filters, setFilters } = useFetchList(
    `${type === 'interview' ? 'ai-interview' : 'submissions'}/generated-links`,
    {
      search: '',
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
      id: quizId,
    }
  );

  const handleInterviewRecord = async (row: GeneratedLinkRow) => {
    setShowInterviewRecord(true);
    setApplicantDetails(row);
  };

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list as GeneratedLinkRow[]);
    }
  }, [list]);

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Generated Links"
        searchPlaceholder="Search for links..."
        count={count}
        filters={filters}
        setFilters={setFilters}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          generatedLink: (_: unknown, row: GeneratedLinkRow) => (
            <>
              <div
                className="flex gap-2 max-w-[300px] cursor-pointer"
                onClick={() => {
                  setShowOverlay(true);
                  setSelectedRow(row);
                }}
              >
                <p className="text-[#1f2937]  text-[14px]  dark:text-white font-medium truncate">{row?.assessmentUrl}</p>
                <CustomIcon
                  definedIcon="copy"
                  stroke="#A47BFA"
                  className="cursor-pointer"
                  width={'18'}
                  height={'18'}
                  onClick={() => {
                    navigator.clipboard.writeText(row?.assessmentUrl);
                    dispatch(setNotifyMessage('Link copied'));
                  }}
                />
              </div>
              <p className="flex gap-1 text-[#656575] truncate">
                Created At <FormatDateFieldColumn date={row?.createdAt} />
              </p>
            </>
          ),
          usage: (_: unknown, row: GeneratedLinkRow) => (
            <div className="flex items-center gap-2">
              <Icon icon="icon-park-solid:peoples" width="18" />
              <div className="capitalize font-medium text-sm text-[#535862] truncate">{row?.assignedCount}</div>
            </div>
          ),
          activeStatus: (_: unknown, row: GeneratedLinkRow) => (
            <div className="w-fit">
              <Tags type={row?.expired === null ? 'active' : 'expired'} color={row?.expired === null ? ' text-[#11ABE6] ' : ' text-[#F13E3E]'} />
            </div>
          ),
          completionRate: (_: unknown, row: GeneratedLinkRow) => {
            if (row?.assignedCount === 0) {
              return (
                <div className="w-fit">
                  <span className="text-[#667085]">—</span>
                </div>
              );
            }

            const getScoreColor = (score: number | undefined) => {
              if (score === undefined || score === null) {
                return 'bg-gray-100 text-gray-800';
              }
              if (score >= 0 && score < 50) {
                return 'bg-[#FFECE9] text-[#A80000]';
              } else if (score >= 50 && score < 75) {
                return 'bg-[#FFEDD8] text-[#E9760F]';
              } else if (score >= 75 && score < 100) {
                return 'bg-[#FFFCDF] text-[#BA8500]';
              } else if (score >= 100) {
                return 'bg-[#EEFFF1] text-[#056816]';
              }
              return 'bg-gray-100 text-gray-800';
            };

            const getScoreText = (score: number | undefined) => {
              if (score === null || score === undefined) return '—';
              return `${score}%`;
            };

            return (
              <div className="w-fit">
                <Tags type="score" color={getScoreColor(row?.completionRate)}>
                  {getScoreText(row?.completionRate)}
                </Tags>
              </div>
            );
          },
          averageScore: (_: unknown, row: GeneratedLinkRow) => {
            if (row?.assignedCount === 0) {
              return (
                <div className="w-fit">
                  <span className="text-[#667085]">—</span>
                </div>
              );
            }

            const getScoreColor = (score: number | undefined) => {
              if (score === undefined || score === null) {
                return 'bg-gray-100 text-gray-800';
              }
              if (score >= 0 && score < 50) {
                return 'bg-[#FFECE9] text-[#A80000]';
              } else if (score >= 50 && score < 75) {
                return 'bg-[#FFEDD8] text-[#E9760F]';
              } else if (score >= 75 && score < 100) {
                return 'bg-[#FFFCDF] text-[#BA8500]';
              } else if (score >= 100) {
                return 'bg-[#EEFFF1] text-[#056816]';
              }
              return 'bg-gray-100 text-gray-800';
            };

            const getScoreText = (score: number | undefined) => {
              if (score === null || score === undefined) return '—';
              return `${score}%`;
            };

            return (
              <div className="w-fit">
                <Tags type="score" color={getScoreColor(row?.averageScore)}>
                  {getScoreText(row?.averageScore)}
                </Tags>
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'generatedLink',
            label: 'Generated Link',
            primary: true,
            width: '20%',
          },
          {
            key: 'usage',
            label: 'Usage',
            primary: true,
            width: '10%',
          },
          {
            key: 'averageScore',
            label: 'Average Score',
            width: '17%',
            inline: true,
          },
          {
            key: 'completionRate',
            label: 'Completion Rate',
            width: '15%',
            inline: true,
          },
          {
            key: 'activeStatus',
            label: 'Link Status',
            width: '10%',
          },

          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_: unknown, row: GeneratedLinkRow) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  onClick: () => {
                    setShowOverlay(true);
                    setSelectedRow(row);
                  },
                },
              ];
            },
          },
        ]}
        noDataFound={{
          customIcon: 'generatedLinksNotFound',
          message: 'No Links Generated Yet',
        }}
        placeholder={{
          title: 'No Links Generated Yet',
          image: '/UI/src/assets/placeholder/NoQuestions.svg',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        addButtonPermission={true}
        setShowMoreMap={setShowMoreMap}
        hideJumbotron
        isScrollableTabsExists
      />
      {showOverlay && selectedRow && (
        <ComponentOverlayInterview
          id={selectedRow.sourceLink || selectedRow._id}
          type={type || ''}
          onClose={() => setShowOverlay(false)}
          onInterviewRecord={handleInterviewRecord}
        />
      )}
      {showInterviewRecord && applicantDetails && (
        <ComponentInterviewRecord
          onClose={() => setShowInterviewRecord(false)}
          assessmentId={applicantDetails.assessmentId}
          applicantId={applicantDetails.applicantId}
        />
      )}
    </>
  );
};
