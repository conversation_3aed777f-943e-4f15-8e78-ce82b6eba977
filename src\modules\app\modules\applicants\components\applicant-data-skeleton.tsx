export const ApplicantDataSkeleton = () => {
  return (
    <div className="space-y-9 border w-full  text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 flex align-middle justify-start">
      <div className="w-full ">
        <div className="flex flex-col sm:flex-row  w-full align-middle px-3    gap-4 items-center">
          {/* image */}
          <div className="">
            <div className=" h-[100px] w-[100px] mb-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div className="flex w-full sm:flex-row flex-col  justify-between">
            {/* description */}
            <div className="w-full ">
              <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w- mb-2 "></div>

              <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-2/6 "></div>
            </div>

            <div className="w-full flex flex-col sm:items-end p-0   items-start ">
              <div className="h-11 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-1/3 mb-1 "></div>
              <div className="h-4 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-2/6 "></div>
            </div>
          </div>
        </div>
        <div className="w-full flex sm:flex-row flex-col gap-3 sm:gap-1">
          <div className="h-7 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-2/12 mb-1 "></div>
          <div className="h-7 bg-gray-300 mt-3 rounded-full dark:bg-gray-600 w-2/12 mb-1 "></div>
        </div>
        {/* blocks */}
        <div className="border mt-5 w-full">
          <div className="min-w-40 flex sm:flex-row flex-col lg:items-center   ">
            <div className="flex flex-col  min-w-40  ">
              <div className=" flex flex-col w-full gap-3 sm:border-r border-b lg:border-b-0 p-4">
                <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-2/4 mb-4 "></div>
                <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-1/5 mb-1 "></div>
              </div>
            </div>

            <div className="flex flex-col  min-w-40">
              <div className="flex flex-col w-full gap-3 sm:border-r border-b lg:border-b-0 p-4">
                <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-2/4 mb-4 "></div>
                <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-1/5 mb-1 "></div>
              </div>
            </div>

            <div className="flex flex-col  min-w-40">
              <div className=" flex flex-col w-full gap-3 sm:border-r border-b lg:border-b-0 p-4">
                <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-2/4 mb-4 "></div>
                <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-1/5 mb-1 "></div>
              </div>
            </div>

            <div className="flex flex-col   grow w-full">
              <div className="grow flex flex-col  w-full gap-3 sm:border-r border-b lg:border-b-0 p-4">
                <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-2/4 mb-4 "></div>
                <div className="flex w-full items-center gap-3 ">
                  <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-10  "></div>
                  <div className="flex items-center align-middle gap-8">
                    <div className="flex align-middle items-center w-full gap-1">
                      <div className="h-6 bg-gray-300  rounded-full dark:bg-gray-600 w-6  "></div>
                      <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-20  "></div>
                      <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-7  "></div>
                    </div>

                    <div className="flex align-middle items-center w-full gap-1">
                      <div className="h-6 bg-gray-300  rounded-full dark:bg-gray-600 w-6  "></div>
                      <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-20  "></div>
                      <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-7  "></div>
                    </div>

                    <div className="flex align-middle items-center w-full gap-1">
                      <div className="h-6 bg-gray-300  rounded-full dark:bg-gray-600 w-6  "></div>
                      <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-20  "></div>
                      <div className="h-4 bg-gray-300  rounded-full dark:bg-gray-600 w-7  "></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
