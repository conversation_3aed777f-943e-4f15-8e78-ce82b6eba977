import { useState, useEffect } from 'react';
import { Dropdown, DropdownHeader, DropdownItem } from 'flowbite-react';

import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthUtils } from 'UI/src/hooks/auth-utils';
import { ProfileEditPage } from '../modules/profile/pages/edit-dialog';

import { Icon, Logo, BackButton } from '../../../';

import { setErrorNotify } from 'UI';
import { setSidebarVisiblity, useAppDispatch, useScreenSize, Button } from 'UI/src';

import { Api, RootState, useAppSelector, UserData } from 'UI/src';

interface AppHeaderProps {
  isDrawerVisible: boolean;
  setIsDrawerVisible: (value: boolean) => void;
}
export const AppHeader = ({ isDrawerVisible, setIsDrawerVisible }: AppHeaderProps) => {
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState<boolean>(false);
  const [trackName, setTrackName] = useState<string>('');

  // Hooks
  const { logout } = useAuthUtils();
  const navigate = useNavigate();
  const location = useLocation();
  const currentUrl: string = location.pathname;
  const screen = useScreenSize();

  // Redux
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);
  const isSidebarVisible = useAppSelector((state: RootState) => state.sidebarVisible.isVisible);
  const dispatch = useAppDispatch();

  const fetchTrackName = async () => {
    if (userData?.trackId) {
      try {
        const response = await Api.get(`lookups/category/single/${userData.trackId}`, {});
        console.log('lookups/category/single', response.data);
        setTrackName(response.data.name);
      } catch (error: any) {
        const message = error?.response?.data?.message || error?.message || 'Something went wrong';

        dispatch(setErrorNotify(message));
      }
    }
  };

  useEffect(() => {
    fetchTrackName();
  }, [userData]);

  const renderRole = (): JSX.Element | undefined => {
    if (Array.isArray(userData?.role) && userData?.role?.some((role: string) => ['super-admin'].includes(role))) {
      return (
        <div className="bg-[#F3E8FFC2] text-[#7E22CE] px-3 py-1 text-xs w-fit rounded-full">
          <p>Super Admin</p>
        </div>
      );
    } else if (Array.isArray(userData?.role) && userData?.role?.some((role: string) => ['admin'].includes(role))) {
      return (
        <div className="bg-[#F3E8FFC2] text-[#7E22CE] px-3 py-1 text-xs w-fit rounded-full">
          <p>Admin</p>
        </div>
      );
    } else if (Array.isArray(userData?.role) && userData?.role?.some((role: string) => ['content-creator'].includes(role))) {
      return (
        <div className="bg-[#FCE7F3C2] text-[#BE185D] px-3 py-1 text-xs w-fit rounded-full">
          <p>Content Creator</p>
        </div>
      );
    } else if (Array.isArray(userData?.role) && userData?.role?.some((role: string) => ['hr'].includes(role))) {
      return (
        <div className="bg-[#E3F2FDC2] text-[#3B82F6] px-3 py-1 text-xs w-fit rounded-full">
          <p>HR</p>
        </div>
      );
    }
  };

  useEffect(() => {
    dispatch(setSidebarVisiblity(isDrawerVisible));
  }, [isDrawerVisible]);

  return (
    <div className={`${currentUrl.includes('pdf') ? 'hidden' : 'block'} print:hidden`}>
      <nav className="bg-white border-b border-[#f4f4f4] px-4 py-2.5 dark:bg-darkBackgroundCard dark:border-[#374151] fixed left-0 right-0 top-0 z-[40]">
        <div className="flex flex-wrap justify-between items-center">
          <div className="flex justify-between items-center gap-4">
            <div className="flex justify-between items-center">
              <Icon
                onClick={() => setIsDrawerVisible(!isDrawerVisible)}
                icon={
                  isDrawerVisible ? 'material-symbols:keyboard-double-arrow-left-rounded' : 'material-symbols:keyboard-double-arrow-right-rounded'
                }
                className={`text-[#8D5BF8] rounded-xl cursor-pointer`}
                width={'30'}
              />

              <div className="flex items-center justify-between cursor-pointer" onClick={() => navigate('/')}>
                <Logo className="h-6 sm:h-7 " />
              </div>
            </div>
            {screen.gt.sm() && <BackButton className={`${isDrawerVisible ? 'pl-[77px]' : ''}`} />}
          </div>

          <div className="flex items-center gap-4">
            {/* <button
              type="button"
              className="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 flex items-center justify-center"
              onClick={switchDarkMode}
            >
              <Icon icon={isDark ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'} width="20px" />
            </button> */}

            <Dropdown
              label
              renderTrigger={() => (
                <div className="w-8 h-8 flex items-center justify-center rounded-full cursor-pointer bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400">
                  <Icon width="20" icon="mdi:user" />
                </div>
              )}
              className="rounded-xl dark:bg-darkBackgroundCard"
            >
              <div className="min-w-56">
                <DropdownHeader>
                  <div className="flex gap-3 items-start">
                    <div className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400">
                      <Icon width="20" icon="mdi:user" />
                    </div>
                    <div className="flex flex-wrap gap-2 items-center pl-3 max-w-[230px]">
                      <span className="block text-base max-w-[200px] break-words">{userData.name}</span>
                      {renderRole()}
                      <p className="text-[#667085] dark:text-gray-300 text-xs">{trackName}</p>
                    </div>
                  </div>
                </DropdownHeader>
                <DropdownItem onClick={() => setCreateDialogVisibility(true)}>
                  <div className="flex ml-1 gap-5">
                    <Icon width="20" icon="lucide:user" />
                    <span>Edit profile </span>
                  </div>
                </DropdownItem>
                <DropdownItem onClick={logout}>
                  <div className="flex ml-1 gap-5">
                    <Icon width="20" icon="mynaui:logout" />
                    <span>Logout</span>
                  </div>
                </DropdownItem>
              </div>
            </Dropdown>
          </div>
        </div>
      </nav>

      {isCreateDialogVisible && (
        <ProfileEditPage
          onClose={() => {
            setCreateDialogVisibility(false);
          }}
        />
      )}
    </div>
  );
};
