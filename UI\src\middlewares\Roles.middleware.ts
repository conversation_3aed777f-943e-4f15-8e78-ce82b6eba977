import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';

// Fetch single role
export const fetchRole = createAsyncThunk(
  'roles/fetchRole',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`roles/single/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch role');
    }
  }
);

interface FetchRoleUsersArgs {
  id: string;
  search?: string;
  pagination?: {
    page: number;
    size: number;
  };
}

interface FetchRoleUsersResponse {
  items: any[];   // replace `any` with `User[]` if you have a User type
  count: number;
}

export const fetchRoleUsers = createAsyncThunk<
  FetchRoleUsersResponse, // return type
  FetchRoleUsersArgs      // argument type
>(
  'roles/fetchRoleUsers',
  async ({ id, search, pagination }, { rejectWithValue }) => {
    try {
      const response = await Api.get(`users/by-role/${id}`, { search, ...pagination });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch role');
    }
  }
);

// Create role
export const createRole = createAsyncThunk(
  'roles/createRole',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('roles/single', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create role');
    }
  }
);

// Update role
export const updateRole = createAsyncThunk(
  'roles/updateRole',
  async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
    try {
      const response = await Api.put(`roles/single/${id}`, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to update role');
    }
  }
);

// Search role permissions
export const searchRolePermissions = createAsyncThunk(
  'roles/searchPermissions',
  async (keyword: string, { rejectWithValue }) => {
    try {
      const result = await Api.get('roles/single/permissions/search', { keyword });
      return result.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to search role permissions');
    }
  }
); 
