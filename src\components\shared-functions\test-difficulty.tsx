// Core
import { EnumText, Icon } from 'src';

// React icons
import { FaUserGraduate, Fa<PERSON>ser, FaStar, FaMedal, FaTrophy } from 'react-icons/fa';
import { LiaCircle } from 'react-icons/lia';
import { LuDiamond } from 'react-icons/lu';
import { PiCube } from 'react-icons/pi';
import { BiPolygon, BiTrash } from 'react-icons/bi';
import { QuestionDifficulty, QuizDifficulty } from 'UI/src';

export const TestSeniorityLevel = ({ seniorityLevel }: { seniorityLevel: number | string }) => {
  const handleTestSeniorityLevel = () => {
    const iconSize = 'text-sm';

    switch (seniorityLevel) {
      case 1:
        return {
          difficultyIcon: <FaUserGraduate className={`${iconSize} text-teal-700`} />, // Intern
          difficultyColor: ' text-teal-700 ',
        };

      case 2:
        // Star Icon fresh level
        return {
          difficultyIcon: <FaUser className={`${iconSize} text-sky-800`} />, // Fresh
          difficultyColor: 'text-sky-800 ',
        };

      case 3:
        // Medal Star junior
        return {
          difficultyIcon: <FaStar className={`${iconSize} text-amber-700`} />, // Junior
          difficultyColor: ' text-amber-700 ',
        };

      case 4:
        // Medal star midlevel
        return {
          difficultyIcon: <FaMedal className={`${iconSize} text-orange-700`} />, // Mid-level
          difficultyColor: 'text-orange-700',
        };

      case 5:
        // Tropy icon for senior with star
        return {
          difficultyIcon: <Icon icon="solar:crown-star-bold" width="16" className={`text-red-800`} />, // Senior
          difficultyColor: 'text-red-800',
        };

      default:
        return { difficultyIcon: null };
    }
  };

  return (
    <span className={`flex items-center py-1 text-sm font-semibold rounded-full capitalize ${handleTestSeniorityLevel()?.difficultyColor}`}>
      <span className="mr-1 flex items-center justify-center">{handleTestSeniorityLevel()?.difficultyIcon}</span>
      {/* <EnumText name={'QuizDifficulty'} value={seniorityLevel} /> */}
      {QuizDifficulty[seniorityLevel as any]}
    </span>
  );
};

export const TestDifficulty = ({ difficulty, difficultyIcon }: { difficulty: number; difficultyIcon?: boolean }) => {
  const handleTestDifficulty = () => {
    const iconSize = 'text-sm';
    switch (difficulty) {
      case 1:
        return {
          difficultyIcon: <LiaCircle className={iconSize} />,
          difficultyColor: 'text-[#4BA665] font-semibold bg-[#EDFDEB]',
        };
      case 2:
        return {
          difficultyIcon: <LuDiamond className={iconSize} />,
          difficultyColor: 'text-[#B16A00] font-semibold bg-[#FFF7E3]',
        };
      case 3:
        return {
          difficultyIcon: <BiPolygon className={iconSize} />,
          difficultyColor: 'text-[#D40101] font-semibold bg-[#FFE6E3]',
        };
      case 4:
        return {
          difficultyIcon: <PiCube className={iconSize} />,
          difficultyColor: 'text-[#B10000] font-semibold bg-[#FFD3CD]',
        };
      default:
        return { difficultyIcon: null };
    }
  };

  return (
    <span
      className={`flex justify-center items-center px-2 py-1 text-xs text-center font-semibold rounded-full capitalize border-[#d1d5db] ${
        !difficultyIcon && 'px-3 bg-white border'
      }  ${handleTestDifficulty()?.difficultyColor}`}
    >
      {difficultyIcon && <span className="mr-1 flex items-center justify-center">{handleTestDifficulty()?.difficultyIcon}</span>}
      {/* <EnumText name={'QuestionDifficulty'} value={difficulty} /> */}
      {QuestionDifficulty[difficulty]}
    </span>
  );
};

export const TestTypeIndicator = ({ type }: { type: string }) => {
  const renderStyle = () => {
    if (type === 'test') {
      return 'text-[#6234C4] bg-[#EDE9FE]';
    } else if (type === 'interview') {
      return 'text-[#2B57D6] bg-[#D8E7FE]';
    } else if (type === 'screening') {
      return 'text-[#DF7AEF] bg-[#FFECFA]';
    } else {
      return '';
    }
  };
  return <div className={`capitalize px-3 py-[1px] ${renderStyle()} text-sm rounded-full`}>{type}</div>;
};

export const AvarageScore = ({ score }: { score?: number }) => {
  const handleAvarageScore = () => {
    if (score && score >= 90) {
      return 'bg-[#ECFDF3] text-green-700 border-[#ABEFC6]';
    } else if (score && score >= 50) {
      return 'bg-[#FDF7EC] text-[#A28202] border-[#EFDCAB]';
    } else if (score && score >= 0) {
      return 'bg-[#FEF3F2] text-red-700 border-red-200';
    }
  };

  return score && score >= 0 ? (
    <div className={`${handleAvarageScore()} rounded-full px-2 text-center py-1 border font-semibold capitalize text-[12px] truncate`}>{score}%</div>
  ) : (
    '—'
  );
};
