import { useEffect, useState } from 'react';
import { Toast } from 'flowbite-react';
import { useAppSelector, useAppDispatch } from 'UI/src';
import { setNotifyMessage, setErrorNotify, setWarningNotify } from 'UI';
import { HiCheck, HiExclamation, HiX } from 'react-icons/hi';

interface NotificationItem {
  id: string;
  message: string;
  type: 'success' | 'error' | 'warning';
}

type NotificationType = 'success' | 'error' | 'warning';

const NOTIFICATION_TIMEOUTS = {
  success: 3000,
  error: 5000,
  warning: 4000,
} as const;

const NOTIFICATION_STYLES = {
  success: 'bg-green-100 text-green-500 dark:bg-green-800 dark:text-green-200',
  error: 'bg-red-100 text-red-500 dark:bg-red-800 dark:text-red-200',
  warning: 'bg-orange-100 text-orange-500 dark:bg-orange-700 dark:text-orange-200',
} as const;

const NOTIFICATION_ICONS = {
  success: HiCheck,
  error: HiX,
  warning: HiExclamation,
} as const;

export const NotificationBridge = () => {
  const dispatch = useAppDispatch();
  const notify = useAppSelector((state) => state.notify);
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);

  const addNotification = (message: string, type: NotificationType) => {
    const newNotification: NotificationItem = {
      id: Date.now().toString(),
      message,
      type,
    };
    setNotifications((prev) => [...prev, newNotification]);

    const timer = setTimeout(() => {
      setNotifications((prev) => prev.filter((n) => n.id !== newNotification.id));
    }, NOTIFICATION_TIMEOUTS[type]);

    // Store timer ID for cleanup if needed
    return timer;
  };

  const removeNotification = (id: string) => {
    setNotifications((prev) => prev.filter((n) => n.id !== id));
  };

  const getToastIcon = (type: NotificationType) => {
    return NOTIFICATION_ICONS[type];
  };

  const getNotificationStyles = (type: NotificationType) => {
    return NOTIFICATION_STYLES[type];
  };

  // Handle success messages
  useEffect(() => {
    if (notify.message) {
      console.log('gg success', notify.message);
      addNotification(notify.message, 'success');
      dispatch(setNotifyMessage(''));
    }
  }, [notify.message, dispatch]);

  // Handle error messages
  useEffect(() => {
    if (notify.error.message) {
      console.log('gg error', notify.error.message);
      addNotification(notify.error.message, 'error');
      dispatch(setErrorNotify(''));
    }
  }, [notify.error.message, dispatch]);

  // Handle warning messages
  useEffect(() => {
    if (notify.warning.message) {
      console.log('gg warning', notify.warning.message);
      addNotification(notify.warning.message, 'warning');
      dispatch(setWarningNotify(''));
    }
  }, [notify.warning.message, dispatch]);

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-[99999] space-y-3 max-w-sm w-full">
      {notifications.map((notification, index) => (
        <div
          key={notification.id}
          className="transform transition-all duration-500 ease-out animate-in slide-in-from-top-4 fade-in zoom-in-95"
          style={{
            animationDelay: `${index * 150}ms`,
            animationFillMode: 'both',
          }}
        >
          <Toast className="w-full shadow-lg border border-gray-200 dark:border-gray-700 backdrop-blur-sm hover:shadow-xl hover:scale-[1.02] transition-all duration-200 group">
            <div
              className={`inline-flex h-8 w-8 shrink-0 items-center justify-center rounded-lg transition-all duration-300 group-hover:scale-110 ${getNotificationStyles(
                notification.type
              )}`}
            >
              {getToastIcon(notification.type)({ className: 'h-4 w-4 transition-transform duration-300' })}
            </div>
            <div className="ml-3 text-sm font-medium text-gray-900 dark:text-gray-100 flex-1 mr-2 transition-all duration-200">
              {notification.message}
            </div>
            <button
              type="button"
              className="ml-auto inline-flex h-8 w-8 rounded-lg bg-transparent p-1.5 text-gray-400 hover:bg-red-100 hover:text-red-600 focus:ring-2 focus:ring-red-300 transition-colors duration-200 dark:text-gray-500 dark:hover:bg-red-900/20 dark:hover:text-red-400 dark:focus:ring-red-600 items-center justify-center"
              onClick={() => removeNotification(notification.id)}
              aria-label={`Close ${notification.type} notification`}
            >
              <span className="sr-only">Close</span>
              <HiX className="h-4 w-4" />
            </button>
          </Toast>
        </div>
      ))}
    </div>
  );
};
