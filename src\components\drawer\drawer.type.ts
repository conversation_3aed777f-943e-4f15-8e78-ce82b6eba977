import type { ReactNode } from 'react';

// Main Drawer Props
export type DrawerProps = {
  children: ReactNode;
  className?: string;
  split?: boolean;
  onClose?: () => void;
};

export type DrawerComponent = DrawerProps & {
  SingleView?: DrawerSingleViewProps;
  Header?: DrawerHeaderProps;
  Body?: DrawerBodyProps & {
    TestDetails: DrawerBodyTestDetailsProps;
  };
  Footer?: DrawerFooterProps & {
    Button: DrawerFooterButtonProps;
  };
};

// Single View Drawer
export interface DrawerSingleViewProps {
  children: ReactNode;
  className?: string;
}

// Question Of Test
export interface QuestionOfTestProps {
  children: ReactNode;
  className: string;
  index: number;
  row: Row;
  mainQuestionsListForm: { questionIds: string[] };
  mainSetFieldValueForm: (valu: string) => void;
  currentPage: number;
  questionsPerPage: number;
  selectedQuestionIds: string[];
  setSelectedQuestionIds: (value: string[]) => void;
  handleGetQuestionGenerateData: (value: string) => void;
  questionDatabase: { _id: string; title: string }[];
  setQuestionDatabase: (value: { _id: string; title: string }[]) => void;
  generatedQuestionsIds: string[];
  refresh: () => void;
  canRemoveQuestion: boolean;
  setAnyQuestionHasEditMode: (valu: {}) => void;
}

// Drawer Filter Section
export interface FilterSectionProps {
  children?: ReactNode;
  className?: string;
  label: string;
  optional: string;
  form: { [key: string]: any; propertyKeyObject: {}; subCategory: string[]; subCategoryFiltration: [] };
  lookups: { _id: string; name?: string }[];
  setFieldValue: (params: { path: string; value: any }) => void;
  propertyKeyObject: string | number;
  filter: {
    options?: {
      value: string;
      name: string;
      lable: string;
    }[];
    key: string;
    enum?: string;
  };
  showSingleClear: boolean;
  handleSingleClear: () => void;
}

// Drawer Header Section
export interface HeaderSectionProps {
  children?: ReactNode;
  className?: string;
  onClose?: () => void;
  headerLabel?: ReactNode;
  onReset: () => void;
  icon: ReactNode;
  selectedQuestionsCount: number;
  resultsFound: string;
}

// Drawer Header
export interface DrawerHeaderProps {
  children?: ReactNode;
  headerChild?: ReactNode;
  className?: string;
  headerLabel?: ReactNode;
  headerSubLabel?: string;
  description?: string;
  onClose?: () => void;
}

// Drawer Body
export interface DrawerBodyProps {
  children: ReactNode;
  className?: string;
}

// Drawer Body TestDetails
export interface DrawerBodyTestDetailsProps {
  className?: string;
  label: string;
  mainButton?: boolean;
  children?: ReactNode;
  enumText?: string | JSX.Element;
  duration?: string;
  totalQuestions?: number;
}

export interface DatePicker {
  children?: ReactNode;
  className?: string;
  startDate: Date;
  dueDate: Date;
  extraTime: number;
  setExtraTime: () => void;
  setTimeSettingsVisible: () => void;
  type: string;
}

// row for Question Of Screening
export interface Row {
  id: string;
  _id: string;
  isEditMode: boolean;
  pendingTitle: string;
  title: string;
  question: QuestionListItem;
  timeTaken: { hours: number; minutes: number; seconds: number };
  answer: string;
  difficulty: string;
  subCategoryName: string;
  topicName: string;
}

// Question List Item data for Question Of Screening
export interface QuestionListItem {
  _id: string;
  pendingTitle: string;
  isEditMode: boolean;
}

// Drawer Body Question Of Screening
export interface DrawerBodyQuestionOfScreeningProps {
  className?: string;
  children?: ReactNode;
  index: number;
  row: Row;
  setQuestionsListData?: (updater: (prev: QuestionListItem[]) => QuestionListItem[]) => void;
  isExpandAllAnswers?: boolean;
  canEditQuestion?: boolean;
  handleSaveTest?: (rowId: string) => void;
}

// Pagination Data for Footer
export interface PaginationData {
  showingText: string;
  count: number;
  size: number;
  currentPage: number;
  onPageChange: (params: { page: number }) => void;
  pagesCount: number;
}

// Drawer Footer
export interface DrawerFooterProps {
  children?: ReactNode;
  className?: string;
  isPaginationActive?: boolean;
  paginationData?: PaginationData | null;
}

// Drawer Footer Button
export interface DrawerFooterButtonProps {
  className?: string;
  label: string;
  mainButton?: boolean;
  onClick?: () => void;
  tertiary?: boolean;
  disabled?: boolean;
  loading?: boolean;
}

// If there are other components (e.g., DrawerContent)
export interface DrawerContentProps {
  children: ReactNode;
  className?: string;
}
