export interface QuestionType {
  _id: string;
  title: string;
  options: {
    id: number;
    label: string;
  }[];
  type: number;
  difficulty: number;
  singleChoiceAnswer: number;
  multiChoiceAnswer: {};
  category: string;
  categoryName: string;
  subCategory: string;
  subCategoryName: string;
  topic: string;
  topicName: string;
  author: string;
  authorName: string;
}
export type QuestionsListType = QuestionType[];




// question Create new Question Req
export type NewQuestionCreateReq={
 title: string
  difficulty: number
  category: string
  subCategory: string
  topic: string
  type: number
  options: QuestionCreateOption[]
  singleChoiceAnswer: number
  multiChoiceAnswer: {}
}
// Question Create Option
export type QuestionCreateOption={
  id: number
  label: string
}



// Question Generate Req
export type QuestionsGenerateReq={
   numOfQuestions: number
  questionsDifficulty: string[]
  category: string[]
  subCategory: string[]
  exclude: string[]
}

// QuestionGenerateRes
export type QuestionGenerationRes={
    _id: string
  title: string
  content: string
  organizationId: string
  difficulty: number
  category: Category
  subCategory: SubCategory
  topic: Topic
  options: string[]
  type: number
  singleChoiceAnswer: number
  multiChoiceAnswer: MultiChoiceAnswer
  author: Author
  createdAt: string
}
export interface Category {
  categoryId: string
  categoryName: string
}
export interface SubCategory {
  subCategoryId: string
  subCategoryName: string
}
export interface Topic {
  topicId: string
  topicName: string
}
export interface MultiChoiceAnswer {}
export interface Author {
  authorId: string
  fullName: string
}


export type questionsListRes={

  _id: string
  title: string
  difficulty: number
  createdAt: string
  category: string | null,
  categoryName: string
  subCategory: string
  subCategoryName: string
  topic: string
  topicName: string
  authorName: string
  authorId: string
  techpassQuiz: boolean

}

export type UpdateQuestionByIdRes={
 title: string
  options: string[]
  type: number
  category: Category
  subCategory: SubCategory
  topic: Topic
  difficulty: number
  singleChoiceAnswer: number
  multiChoiceAnswer: MultiChoiceAnswer
}

export interface QuestionListItem {
  _id: string;
  title: string;
  difficulty: number;
  createdAt: string;
  category: string;
  categoryName: string;
  subCategory: string;
  subCategoryName: string;
  topic: string;
  topicName: string;
  authorName: string;
  authorId: string;
  techpassQuiz: boolean;
}

export type QuestionsList = QuestionListItem[];
