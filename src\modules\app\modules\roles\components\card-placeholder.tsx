import React from 'react';

export const CardPlaceholder = () => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
    <div className="space-y-8 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
      <div className="flex justify-between">
        <div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2.5"></div>
          <div className="w-28 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
      </div>
      <div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"></div>
        <div className="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      </div>
    </div>

    <div className="space-y-8 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
      <div className="flex justify-between">
        <div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2.5"></div>
          <div className="w-28 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
      </div>
      <div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"></div>
        <div className="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      </div>
    </div>

    <div className="space-y-8 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
      <div className="flex justify-between">
        <div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2.5"></div>
          <div className="w-28 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
      </div>
      <div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"></div>
        <div className="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      </div>
    </div>

    <div className="space-y-8 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
      <div className="flex justify-between">
        <div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2.5"></div>
          <div className="w-28 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
      </div>
      <div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"></div>
        <div className="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      </div>
    </div>

    <div className="space-y-8 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
      <div className="flex justify-between">
        <div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2.5"></div>
          <div className="w-28 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
      </div>
      <div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"></div>
        <div className="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      </div>
    </div>

    <div className="space-y-8 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
      <div className="flex justify-between">
        <div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2.5"></div>
          <div className="w-28 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
      </div>
      <div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"></div>
        <div className="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      </div>
    </div>

    <div className="space-y-8 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
      <div className="flex justify-between">
        <div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2.5"></div>
          <div className="w-28 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
      </div>
      <div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"></div>
        <div className="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      </div>
    </div>

    <div className="space-y-8 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
      <div className="flex justify-between">
        <div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2.5"></div>
          <div className="w-28 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
      </div>
      <div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-600 w-24 mb-2.5"></div>
        <div className="w-32 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
      </div>
    </div>
  </div>
);
