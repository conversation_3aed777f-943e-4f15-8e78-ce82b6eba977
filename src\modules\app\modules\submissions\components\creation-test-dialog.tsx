import React, { useEffect, useState, useRef, FC } from 'react';

// UI
import { TextInput, Select, MultiSelect, Checkbox, Icon } from 'src';
import { Dialog, Button } from 'UI';
import { Api, Regex, useValidate, Form, initializeForm, RootState, setFieldValue, useAppDispatch, useAppSelector } from 'UI/src';
import { setErrorNotify, setNotifyMessage } from 'UI';
import { useFormik } from 'formik';

// Types

type BlockDetails = {
  blockIdDetails?: string;
  testIdDetails?: {
    _id: string;
    data: any;
  };
};

type SubmissionsCreationTestDialogProps = {
  blockDetails: BlockDetails;
  setBlockDetails: (details: BlockDetails | null) => void;
  onClose: () => void;
  refresh: (force?: boolean) => void;
  setCreateBlockVisibility: (visible: boolean) => void;
  setCreateTestDialogVisibility: (visible: boolean) => void;
};

export const SubmissionsCreationTestDialog: FC<SubmissionsCreationTestDialogProps> = ({
  blockDetails,
  setBlockDetails,
  onClose,
  refresh,
  setCreateBlockVisibility,
  setCreateTestDialogVisibility,
}) => {
  // Route Params
  const { isRequired, isSelected, isNotSpaces, minLength, maxLength } = useValidate();
  // State
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const [quizUrl, setQuizUrl] = useState<string>('');
  const [submissionId, setSubmissionId] = useState<string>('');
  const [applicants, setApplicants] = useState<any[]>([]);
  const [addIconApplicant, setAddIconApplicant] = useState<boolean>(false);
  const [searchResult, setSearchResult] = useState<string | null>(null);
  const [emailRegex, setEmailRegex] = useState<boolean>(false);
  const subCategoryRef = useRef<any>(null);

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      title: '',
      category: 0,
      subCategory: [],
      numOfQuestions: 10,
      duration: 50,
      difficulty: 0,
      applicantId: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  // Methods
  const handleSearch =
    (endpoint: string, action: (data: any[]) => void, isCustomAdd = false) =>
    async (keyword: string) => {
      try {
        const result = await Api.get(endpoint, { keyword });
        console.log(result.data);
        action(result?.data);
        setEmailRegex(false);
        if (isCustomAdd) {
          if (!result?.data?.length && !!keyword) {
            setAddIconApplicant(true);
            setSearchResult(keyword);
          } else {
            setAddIconApplicant(false);
          }
        }
      } catch (error: any) {
        dispatch(setErrorNotify(error.response.data.message));
      }
    };

  const handleAddNewApplicant = async () => {
    const pattern = Regex.email;
    const inputElem = document.getElementById('applicantId') as HTMLInputElement | null;
    const value = inputElem?.value || '';
    if (!pattern.test(value)) {
      setEmailRegex(true);
      setAddIconApplicant(false);
    } else {
      try {
        const response = await Api.post('applicants/single/custom', {
          email: searchResult,
        });
        dispatch(setFieldValue({ path: 'applicantId', value: response.data }));
        setAddIconApplicant(false);
      } catch (error: any) {
        dispatch(setErrorNotify(error.response.data.message));
      }
    }
  };

  // Generate Test
  const handleGenerateSubmission = async () => {
    if (form.subCategory.length == 0) {
      dispatch(setErrorNotify('Please select 1 to 4 subCategory for your test questions.'));
    } else {
      try {
        setLoading(true);
        const response = await Api.post('submissions/single', {
          customTest: true,
          title: form.title,
          category: form.category,
          subCategory: form.subCategory,
          numOfQuestions: form.numOfQuestions,
          duration: form.duration,
          difficulty: form.difficulty,
          // willSendEmail: form.willSendEmail,
          applicantId: form.applicantId,
        });
        setQuizUrl(response.data.quizUrl);
        setSubmissionId(response.data.submissionId);
      } catch (error: any) {
        dispatch(setErrorNotify(error.response.data.message));
      } finally {
        setLoading(false);
      }
    }
  };

  // Generate Block Test
  const handleGenerateBlockTest = async () => {
    if (form.subCategory.length == 0) {
      dispatch(setErrorNotify('Please select 1 to 4 subCategory for your test questions.'));
    } else {
      try {
        setLoading(true);
        await Api.post('/templates/template/single', {
          blockId: blockDetails.blockIdDetails,
          category: form.category,
          numOfQuestions: form.numOfQuestions,
          duration: form.duration,
          subCategory: form.subCategory,
          difficulty: form.difficulty,
          title: form.subCategory.map((ele: any) => ele).join(),
        });
        refresh(true);
        setCreateTestDialogVisibility(false);
        setBlockDetails(null);
      } catch (error: any) {
        dispatch(setErrorNotify(error.response.data.message));
      } finally {
        setLoading(false);
      }
    }
  };

  // Edit Block Test
  const handleEditBlockTest = async () => {
    if (form.subCategory.length == 0) {
      dispatch(setErrorNotify('Please select 1 to 4 subCategory for your test questions.'));
    } else {
      try {
        setLoading(true);
        await Api.put(`/templates/template/single/${blockDetails.testIdDetails?.data._id}`, {
          blockId: blockDetails.blockIdDetails,
          _id: blockDetails.testIdDetails?._id,
          category: form.category,
          subCategory: form.subCategory,
          numOfQuestions: form.numOfQuestions,
          duration: form.duration,
          difficulty: form.difficulty,
          title: form.subCategory.map((ele: any) => ele),
        });
        refresh(true);
        setCreateTestDialogVisibility(false);
        setCreateBlockVisibility(true);
      } catch (error: any) {
        dispatch(setErrorNotify(error.response.data.message));
      } finally {
        setLoading(false);
      }
    }
  };

  // On Submit
  const onSubmit = () => {
    if (blockDetails?.testIdDetails) {
      // Edit custom test-template without Applicant without Title
      handleEditBlockTest();
    } else if (blockDetails?.blockIdDetails) {
      // New custom test-template without Applicant without Title
      handleGenerateBlockTest();
    } else {
      // New custom submission with Applicant with Title
      handleGenerateSubmission();
    }
  };

  // On Mount
  useEffect(() => {
    if (blockDetails?.testIdDetails) {
      dispatch(initializeForm(blockDetails.testIdDetails.data));
    }
    // setFieldValue('willSendEmail')(false);
  }, [form.applicantId]);

  return (
    <Dialog
      isOpen
      size="lg"
      title={!quizUrl ? (blockDetails?.testIdDetails ? 'Update Test' : 'Create Test') : 'Test Created Successfully!'}
      // className={!!quizUrl && 'text-center'}
      onClose={onClose}
    >
      {/* Creation Form */}
      {!quizUrl && (
        <Form className="space-y-4" onSubmit={onSubmit}>
          {/* Title */}
          {!blockDetails?.blockIdDetails && (
            <TextInput
              name="title"
              label="Test Title"
              placeholder="Enter test title"
              value={form.title}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'title', value }))}
              validators={[isRequired(), isNotSpaces(), minLength(2), maxLength(100)]}
              validatorsScroll={true}
            />
          )}
          <Select
            label="Category"
            name="category"
            required
            value={form.category}
            onChange={(newCategory: any) => {
              subCategoryRef.current?.blur();
              dispatch(setFieldValue({ path: 'category', value: newCategory }));
              dispatch(setFieldValue({ path: 'subCategory', value: [] }));
            }}
            lookup="category"
            creationOptions={{
              url: 'lookups/category/single',
              fieldName: 'name',
              validation: Regex.categorySubcategoryTopic,
            }}
            optionValueKey="_id"
            optionLabelKey="name"
            dropIcon={true}
            validators={[isRequired()]}
          />
          <MultiSelect
            key={form.category}
            ref={subCategoryRef}
            label="Subcategory"
            name="SubCategory"
            value={form.subCategory}
            onChange={(newSubCategory: any) => {
              dispatch(setFieldValue({ path: 'subCategory', value: newSubCategory }));
            }}
            disabled={!form.category}
            lookup="subcategory"
            creationOptions={{
              // it will use the params prop as default paload
              url: 'lookups/subCategory/single',
              fieldName: 'name',
              validation: Regex.categorySubcategoryTopic,
            }}
            params={{ categoryId: form.category }}
            optionValueKey="_id"
            optionLabelKey="name"
            validators={form.category ? [isSelected()] : []}
          />
          <div className="grid grid-cols-2 gap-2">
            <TextInput
              name="numOfQuestions"
              label="Question Count"
              placeholder="Enter number"
              type="number"
              value={form.numOfQuestions}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'numOfQuestions', type: Number, value }))}
              min={10}
              max={100}
              validators={[]}
            />
            <TextInput
              name="duration"
              label="Duration"
              placeholder="Enter duration..."
              type="number"
              value={form.duration <= 1 ? 1 : form.duration}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'duration', type: Number, value }))}
              min={20}
              max={120}
              validators={[]}
            />
          </div>
          <Select
            name="difficulty"
            label="Difficulty"
            lookup="$QuizDifficulty"
            value={form.difficulty}
            onChange={(value: any) => dispatch(setFieldValue({ path: 'difficulty', type: Number, value }))}
            dropIcon={true}
            validators={[isRequired()]}
          />
          {!blockDetails?.blockIdDetails && (
            <>
              <div className="relative w-full">
                <Select
                  name="applicantId"
                  label="Applicant"
                  placeholder="Find applicants by email..."
                  value={form.applicantId}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'applicantId', value }))}
                  // FIXME: (isCustomAdd = true)
                  onSearch={handleSearch('applicants/search', setApplicants, true)}
                  creationOptions={{
                    url: 'applicants/single/custom',
                    fieldName: 'email',
                    validation: Regex.email,
                  }}
                  disabled={loading}
                  lookup={applicants}
                  optionValueKey="_id"
                  optionLabelKey="email"
                  isCustomValue={true}
                  customAddIconApplicant={emailRegex}
                  validators={[]}
                />
                {/* {addIconApplicant && (
                  <div className="absolute right-2 top-[42px] cursor-pointer bg-white dark:bg-[#374151]" onClick={handleAddNewApplicant}>
                    <Icon width={'22'} className="text-green-500" icon="material-symbols:add" />
                  </div>
                )} */}
              </div>

              {/* <TextInput
                name="applicantLimit"
                label="Limit Num Of Applicants"
                type="number"
                placeholder="Enter limit num"
                value={form.applicantLimit}
                onChange={setFieldValue('applicantLimit')}
                disabled={form.applicantId}
                min={0}
              /> */}

              {/* <Checkbox
                name="willSendEmail"
                label="Send link via applicant email"
                value={form.willSendEmail}
                onChange={setFieldValue('willSendEmail')}
                disabled={form.applicantId.length === 0 || loading}
                preventSendingMail={form.applicantId.length === 0 || loading}
              /> */}
            </>
          )}

          {/* Actions */}
          <Button
            type="submit"
            colorType="primary"
            label={blockDetails?.testIdDetails ? 'Update Test' : 'Create Test'}
            className="w-full"
            loading={loading}
            disabled={loading || emailRegex}
          />
        </Form>
      )}

      {/* After Creation */}
      {quizUrl && (
        <div>
          <Icon icon="fluent-emoji:clapping-hands" width="120" />
          <hr className="h-px my-4 bg-gray-200 border-0 dark:bg-gray-700" />
          <div className="space-y-4">
            {/* {form.applicantId && (
              <Button className="w-full" label="View Progress" icon="pajamas:progress" to={`/app/tests/result/view/${submissionId}`} />
            )} */}

            <Button
              className="w-full"
              colorType="secondary"
              label="Copy Link"
              icon="material-symbols:content-copy-outline-rounded"
              onClick={() => {
                navigator.clipboard.writeText(quizUrl);
                dispatch(setNotifyMessage('Link copied'));
              }}
            />
          </div>
        </div>
      )}
    </Dialog>
  );
};
