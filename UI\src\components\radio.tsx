export type RadioState = 'default' | 'hover' | 'pressed' | 'disabled';

interface RadioProps {
  label?: string;
  state?: RadioState;
  checked?: boolean;
  onChange?: (value: string) => void;
  disabled?: boolean;
  className?: string;
  name?: string;
  value?: string;
}

export const Radio: React.FC<RadioProps> = ({
  label = '',

  checked = false,
  onChange,
  disabled = false,
  className = '',
  name,
  value = '',
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('Radio changed:', e.target.checked, e.target.value);
    if (onChange && !disabled) {
      onChange(e.target.value);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <label className="flex items-center cursor-pointer gap-2">
        <input
          type="radio"
          name={name}
          value={value}
          checked={checked}
          onChange={handleChange}
          disabled={disabled}
          className={`w-5 h-5 rounded-full appearance-none border border-[#DEE2E4] bg-white relative
            hover:border-[#743AF5] hover:bg-[#F1E9FE] hover:cursor-pointer
            focus:ring-2 focus:ring-[#F1E9FE]
            checked:after:content-[''] checked:after:w-2 checked:after:h-2 checked:after:bg-[#743AF5] checked:after:rounded-full checked:after:absolute checked:after:top-1/2 checked:after:left-1/2 checked:after:transform checked:after:-translate-x-1/2 checked:after:-translate-y-1/2
            ${disabled ? 'cursor-not-allowed bg-white border-[#DEE2E4]' : ''}
            ${className}`}
          style={
            disabled
              ? {
                  appearance: 'none',
                  width: '20px',
                  height: '20px',
                  borderRadius: '50%',
                  border: 'none',
                  backgroundColor: '#DEE2E4',
                  cursor: 'not-allowed',
                }
              : {}
          }
        />
        <span
          className="text-sm font-medium"
          style={{
            color: disabled ? '#DEE2E4' : checked ? '#743AF5' : '#112B3B',
            cursor: disabled ? 'not-allowed' : 'default',
          }}
        >
          {label}
        </span>
      </label>
    </div>
  );
};
