// Core
import { useEffect, useState } from 'react';
import { Icon, ChartsWavy } from 'src';
import { useParams } from 'react-router-dom';

// Flowbite
import { OrganizationsEngagement, useAppDispatch, Api, UserData, useAppSelector, RootState } from 'UI/src';
import { setErrorNotify } from 'UI';

export const FeatureEngagement = () => {
  const { id } = useParams();
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const orgId = id ?? userData.organizationId;

  const [featureEngagementData, setFeatureEngagementData] = useState<OrganizationsEngagement>();
  const dispatch = useAppDispatch();

  const handleGet = async () => {
    try {
      const response = await Api.get<OrganizationsEngagement>(`organizations/engagement/${orgId}`, {});
      console.log(`organizations/engagement/${orgId}`, response.data);
      setFeatureEngagementData(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  // Calculate usage percentages based on total possible activities per month (12 months)
  const calculateUsagePercentage = (growthData: { count: number }[]) => {
    if (!growthData || !growthData.length) return 0;
    const activatedMonths = growthData.filter((month: any) => month.count > 0).length;
    return Math.round((activatedMonths / 12) * 100);
  };

  // Calculate month-over-month growth percentage
  const calculateGrowth = (growthData: { count: number; month: number }[]) => {
    if (!growthData || growthData.length < 2) return 0;

    // Find the two most recent months with activity
    const activeMonths = growthData.filter((month: any) => month.count > 0).sort((a: any, b: any) => b.month - a.month);

    if (activeMonths.length < 2) return 0;

    const current = activeMonths[0].count;
    const previous = activeMonths[1].count;

    if (previous === 0) return 100; // If previous was 0, show 100% growth
    return Math.round(((current - previous) / previous) * 100);
  };

  // Methods
  const getMonthName = (monthNumber: number, locale: string = 'en-US') => {
    const date = new Date(2023, monthNumber - 1, 1);
    return date.toLocaleString(locale, { month: 'short' });
  };

  // Create the data structure for the chart
  const prepareChartData = (featureData: {
    interviewGrowth: { month: number; count: number }[];
    predefinedAssessmentGrowth: { month: number; count: number }[];
    readyAssessmentsGrowth: { month: number; count: number }[];
  }) => {
    if (!featureData) return [];

    return Array.from({ length: 12 }, (_, i) => {
      const month = i + 1;
      return {
        x: getMonthName(month),
        a: featureData?.interviewGrowth?.find((m: any) => m.month === month)?.count || 0,
        b: featureData?.predefinedAssessmentGrowth?.find((m: any) => m.month === month)?.count || 0,
        c: featureData?.readyAssessmentsGrowth?.find((m: any) => m.month === month)?.count || 0,
      };
    });
  };

  // Generate feature data from API response
  const generateFeatureData = () => {
    if (!featureEngagementData) return [];

    return [
      {
        id: 1,
        type: 1,
        title: 'AI Interviews',
        count: featureEngagementData.interviewCount,
        percentage: calculateUsagePercentage([featureEngagementData.interviewGrowth]),
        gain: calculateGrowth([featureEngagementData.interviewGrowth]),
        growthData: featureEngagementData.interviewGrowth,
      },
      {
        id: 2,
        type: 2,
        title: 'Predefined Assessments',
        count: featureEngagementData.predefinedAssessmentCount,
        percentage: calculateUsagePercentage([featureEngagementData.predefinedAssessmentGrowth]),
        gain: calculateGrowth([featureEngagementData.predefinedAssessmentGrowth]),
        growthData: featureEngagementData.predefinedAssessmentGrowth,
      },
      {
        id: 3,
        type: 3,
        title: 'Custom Assessments',
        count: featureEngagementData.readyAssessmentCount,
        percentage: calculateUsagePercentage([featureEngagementData.readyAssessmentsGrowth]),
        gain: calculateGrowth([featureEngagementData.readyAssessmentsGrowth]),
        growthData: featureEngagementData.readyAssessmentsGrowth,
      },
    ];
  };

  useEffect(() => {
    handleGet();
  }, []);

  // TODO: Markos
  const chartData = prepareChartData(featureEngagementData || ({} as any));
  const featureData = generateFeatureData();

  const colorType = (type: number) => {
    if (type === 1) return 'bg-[#5DC6C0]';
    else if (type === 2) return 'bg-[#6599F7]';
    else if (type === 3) return 'bg-[#AF52DE]';
  };

  const chartLabels = {
    a: 'AI Interviews',
    b: 'Custom Assessments',
    c: 'Predefined Assessments',
  };
  return (
    <div className="p-2">
      <div className="-ml-8">
        {/* TODO: Markos */}
        <ChartsWavy data={chartData as any} dataKeys={chartLabels} colors={{ a: '#5DC6C0', b: '#6599F7', c: '#AF52DE' }} />
      </div>

      <div className="space-y-2">
        {featureData.map((data: any) => {
          return (
            <div key={data.id} className="flex justify-between pt-2  dark:text-white text-sm font-medium">
              <div className="flex items-start gap-2.5">
                {data.type && <p className={`size-3 ${colorType(data.type)} rounded-[2px] mt-1`} />}
                <div className="space-y-1">
                  {data.title && (
                    <div className="flex items-center gap-2">
                      <p>{data.title}</p>
                      <span className="text-xs text-gray-500">({data.count})</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2 text-gray-500 text-xs">
                {data.percentage !== undefined && <p>{data.percentage}% Usage</p>}
                {data.percentage < 50 && <Icon icon="mingcute:arrow-down-line" className="text-[#ef4444]" width="14" />}
              </div>

              {/* <div className="flex items-center gap-3">
                <div className="flex gap-0.5">
                  {data.starRating && (
                    <>
                      <Rating>
                        <Rating.Star />
                      </Rating>
                      <p>{data.starRating}</p>
                    </>
                  )}
                </div>
                <div className={`flex items-center gap-0.5 ${data.gain >= 0 ? 'text-[#24c081]' : 'text-[#ef4444]'}`}>
                  {data.gain >= 0 ? <Icon icon="mingcute:arrow-up-line" /> : <Icon icon="mingcute:arrow-down-line" />}
                  <p>{Math.abs(data.gain)}%</p>
                </div>
              </div> */}
            </div>
          );
        })}
      </div>
    </div>
  );
};
