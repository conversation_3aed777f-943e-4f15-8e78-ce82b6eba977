// React
import { useEffect, useState } from 'react';

// Flowbite
import { Label, Checkbox } from 'flowbite-react';

// Core
import { Card, Radio, Icon, Textarea, ScrollableTabs } from 'src';
import { AiAvatarModels, InterviewLanguages, RootState, setFieldValue, useAppDispatch, useAppSelector } from 'UI/src';

interface SetupAssignAssessment {
  formData: any;
  disableButtons: any;
}

export const SetupAssignAssessment = ({ formData, disableButtons }: SetupAssignAssessment) => {
  // Form
  const dispatch = useAppDispatch();
  const form = useAppSelector((state: RootState) => state.form.data);

  // States
  const [activeTab, setActiveTab] = useState(0);

  // tabs
  const tabs: any = [
    { title: 'All', data: 'All' },
    { title: 'System', data: 'System' },
    { title: 'Customized', data: 'Customized' },
  ];

  useEffect(() => {
    disableButtons.setDisableNextButton(!form.avatarName || !form.avatarLang);
  }, [form]);

  return (
    <div className="border border-[#DEE2E4] rounded-xl overflow-hidden divide-y divide-[#DEE2E4]">
      <div className="space-y-4">
        <p className="thepassHthree p-4 border-b border-[#DEE2E4] bg-[#F9F8FA]">Setup Interview Preferences</p>

        <div className="px-4">
          <p className="thepassHtwo text-[#1B1F3B]">Select Avatar</p>
        </div>

        <div className="px-4">
          <ScrollableTabs
            data={tabs}
            titleClassName='thepassHfour'
            selectedTab={{
              activeTab: activeTab,
              setActiveTab: setActiveTab,
            }}
            // nav={{ routePrefix: '' }}
          />
        </div>

        <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-4 px-4 pb-4">
          {AiAvatarModels.map((interviewer: { value: string; iconPath: string }, index: number) => (
            <div key={interviewer.value}>
              <Label
                htmlFor={interviewer?.value}
                className={`flex items-center px-4 py-2 rounded-lg shadow-md shadow-[#743AF51A] cursor-pointer ${
                  form.avatarName === interviewer.value && 'bg-[#F5F2FC] border-[#9566F8]'
                }`}
              >
                <Radio
                  key={interviewer?.value}
                  name={interviewer?.value}
                  selectionValue={interviewer?.value}
                  value={form.avatarName}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'avatarName', value }))}
                  // lookup="$AiAvatarModel"
                  className="mb-6"
                />
                <img src={`/assets/models/${interviewer.iconPath}`} className="size-12 rounded-full" />
                <div className="pl-4">
                  <p className={`text-[#181D27] thepassBtwo capitalize ${form.avatarName !== interviewer.value && 'dark:text-white'}`}>
                    {interviewer.value}
                  </p>
                  <p className={`text-[#535862] thepassBthree ${form.avatarName !== interviewer.value && 'dark:text-white'}`}>
                    {form.avatarDescription || 'HR Representative '}
                  </p>
                </div>
              </Label>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-4 p-4">
        <p className="dark:text-white font-semibold">Select Language</p>
        {/* <p className="text-[#667085] text-sm">Choose the language in which the interview should be conducted.</p> */}

        <div className="flex flex-wrap gap-4">
          {InterviewLanguages.map((language: { value: string; icon: string }, index: number) => (
            <Label
              key={language.value}
              htmlFor={language?.value}
              className={`w-full sm:w-auto flex-grow h-[50px] flex items-center gap-2 px-2 border border-[#DEE2E4] rounded-lg cursor-pointer ${
                form.avatarLang === language.value && 'bg-[#F5F2FC] border-[#9566F8]'
              }`}
            >
              <div className="pl-2">
                <Radio
                  key={language?.value}
                  name={language?.value}
                  selectionValue={language?.value}
                  value={form.avatarLang}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'avatarLang', value }))}
                  lookup={'$InterviewLanguage'}
                />
              </div>
              <Icon icon={language.icon} width="30" />
              <p className={`text-[#181D27] text-sm font-semibold capitalize ${form.avatarLang !== language.value && 'dark:text-white'}`}>
                {language.value}
              </p>
            </Label>
          ))}
        </div>
      </div>

      <div className="space-y-4 p-4">
        <div className="space-y-1">
          <p className="dark:text-white font-semibold">
            Interview Instructions <span className="thepassHfour text-[#868D9C] font-medium">Optional</span>
          </p>
          {/* <p className="text-[#667085] text-sm">Add any specific instructions for the interviewer to conduct...</p> */}
        </div>

        <Textarea
          name="notes"
          placeholder="Add any specific instructions for the interviewer to conduct..."
          value={form.notes}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'notes', value }))}
          rows="4"
          validators={[]}
        />
      </div>

      <div className="space-y-4 p-4">
        <div className="space-y-1">
          <p className="dark:text-white font-semibold">Recording Options</p>
          <p className="text-[#667085] text-sm">Configure recording settings for this interview.</p>
        </div>

        <div className="flex items-center">
          <Checkbox
            name="recordInterview"
            checked={form.recordInterview}
            onChange={(e) => dispatch(setFieldValue({ path: 'recordInterview', value: e.target.checked }))}
          />
          <Label htmlFor="recordInterview" className="ml-2 text-[#181D27] dark:text-white">
            Record Interview Session
          </Label>
        </div>
      </div>
    </div>
  );
};
