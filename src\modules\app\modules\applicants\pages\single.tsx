// React
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// UI
import { StaticData, useFetchList, useScreenSize, Placeholder, ApplicantAssessmentCard } from 'UI/src';
import { Icon, ToggleFilter, NoDataFound, ScrollableTabs, NoDataMatches } from 'src';

// Components
import { ApplicantData } from '../components/applicant-data';
import { PhoneScreening } from '../components/phone-screening';
import { TestCardPlaceholder } from '../components/test-card-placeholder';

// Flowbite
import { Pagination, Spinner } from 'flowbite-react';

export const ApplicantProgressSingle = () => {
  // State
  const [isShowPhoneScreening, setShowPhoneScreening] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  // Hooks
  const { id } = useParams();
  const screen = useScreenSize();

  const TabsItemComponent = (propertyKeyObject: string, endpoint: string, title: string) => {
    // Tabs Component State
    const [backupCount, setBackupCount] = useState(0);

    const { ready, loading, setLoading, list, count, filters, search, pagination, refresh, handleDates } = useFetchList(endpoint, {
      search: '',
      pagination: {
        page: 1,
        size: 10,
      },
      filters: {
        dueDate: {
          label: 'Due Date',
          enum: 'SubmissionDueDate',
        },
        status: {
          label: 'Status',
          enum: 'SubmissionStatus',
        },
        grade: {
          label: 'Score Range',
          enum: 'grade',
        },
        Warnings: {
          label: 'Warnings',
          enum: 'SubmissionWarning',
        },
      },
      id: id,
    });

    // Pagination
    const { page, size } = pagination;
    const pagesCount = Math.max(Math.ceil(count / size), 1);
    const showingText = `${count ? page * size - size + 1 : count} - ${page * size > count ? count : page * size}`;
    const isPaginationActive = !!pagination.update;

    // Import Placeholder component at the top of the file
    const getPlaceholderProps = () => {
      let title = '';
      let imagePath = '';

      if (propertyKeyObject === 'submissions') {
        imagePath = '/UI/src/assets/placeholder/TestImagePlaceholder.svg';
        title = list?.length === 0 ? 'No tests assigned yet' : '';
      } else if (propertyKeyObject === 'interviews') {
        imagePath = '/UI/src/assets/placeholder/NoInterview.svg';
        title = 'No interviews assigned yet';
      } else if (propertyKeyObject === 'screening') {
        imagePath = '/UI/src/assets/placeholder/TestImagePlaceholder.svg';
        title = 'No screening assigned yet';
      } else if (propertyKeyObject === 'warnings') {
        imagePath = '/UI/src/assets/placeholder/no warnings.svg';
        title = 'No Assessments has warnings';
      }

      return { title, imagePath };
    };

    const placeholderProps = getPlaceholderProps();

    const handleFilterCountNumber = () => {
      let filterCount = 0;
      if (filters?.length > 0) {
        filters.map((subFilter) =>
          subFilter?.options.map((subFilterOption: { label: string; value: number | string | boolean }) => {
            // Count regular options with truthy value
            if (subFilterOption.value) filterCount++;

            // Check if the option is a date picker (e.g., "Pick a Date")
            if (subFilterOption.label === 'Pick a Date') {
              if (handleDates.startDate.value) filterCount++;
              if (handleDates.endDate.value) filterCount++;
            }
          })
        );
      }
      //  else if (drawerFilter?.filterCountNumber > 0) {
      //   filterCount = drawerFilter?.filterCountNumber; //@TODO: drawerFilter is not defined
      // }

      return filterCount;
    };

    useEffect(() => {
      if (backupCount === 0) {
        setBackupCount(count);
      }
    }, [count]);

    return {
      ready,
      loading,
      setLoading,
      count,
      backupCount,
      // pagination,
      refresh,
      title,
      component: (
        <>
          {list?.length ? (
            list?.map((data: { _id: string; type: string }) => (
              <ApplicantAssessmentCard
                type={
                  data?.type === 'test'
                    ? 'submissions'
                    : data?.type === 'interview'
                    ? 'interviews'
                    : data?.type === 'screening'
                    ? 'screening'
                    : propertyKeyObject
                }
                test={data}
                key={data?._id}
              />
            ))
          ) : (
            <div className="flex justify-center align-middle   min-h-[calc(40vh-1rem)] items-center ">
              <div className=" w-2/4 space-y-2  ">
                {/* No data created || No results found */}
                {backupCount > 0 ? (
                  <NoDataMatches message="No results found" />
                ) : (
                  <Placeholder title={placeholderProps.title} image={placeholderProps.imagePath} />
                )}
              </div>
            </div>
          )}
        </>
      ),
      pagination: isPaginationActive && count > size && (
        <nav
          // className="flex flex-row justify-between items-center space-y-0 px-4 pt-1 pb-2 bg-white dark:bg-darkGrayBackground bottom-0 sticky z-20"
          className="flex justify-center items-center px-4 my-1 sticky bottom-0 z-20"
          aria-label="Table navigation"
        >
          {/* <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
            Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
            <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
          </span> */}
          {count > size && (
            <Pagination
              theme={StaticData.paginationTheme}
              currentPage={page}
              onPageChange={(page) => pagination.update({ page })}
              showIcons
              totalPages={pagesCount}
              layout={screen.gt.md() ? 'pagination' : 'navigation'}
              previousLabel={screen.gt.sm() ? 'Previous' : ''}
              nextLabel={screen.gt.sm() ? 'Next' : ''}
            />
          )}
        </nav>
      ),
    };
  };

  const tabs = [
    // TabsItemComponent('screening', 'submissions/phone-screening-tests', 'screening'),
    TabsItemComponent('submissions', 'submissions/list', 'tests'),
    TabsItemComponent('interviews', 'ai-interview/list', 'interviews'),
  ];

  // Use these styles to make black overlay visible and not scrollable
  // Make the scroll in list pages only be smooth
  // useEffect(() => {
  //   if (isShowPhoneScreening) {
  //     document.querySelector('html')?.classList.add('overflow-x-hidden');
  //   } else {
  //     document.querySelector('html')?.classList.remove('overflow-x-hidden');
  //   }
  // }, [isShowPhoneScreening]);

  return (
    <>
      <div className="relative">
        <ApplicantData />

        <div className="my-2">
          <ScrollableTabs
            data={tabs.map(({ backupCount, ...rest }: any) => rest)}
            titleClassName="thepassHfour"
            selectedTab={{
              activeTab: activeTab,
              setActiveTab: setActiveTab,
            }}
            nav={{
              routePrefix: `/app/applicants/progress/${id}`,
            }}
          />
        </div>

        {tabs[activeTab]?.ready ? (
          <>
            {tabs[activeTab]?.component}

            {tabs[activeTab]?.pagination}

            {tabs[activeTab]?.loading && (
              <div className="absolute z-50 left-0 right-0 bottom-0 top-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
                <Spinner size="lg" color="purple" />
              </div>
            )}
          </>
        ) : (
          <TestCardPlaceholder />
        )}
      </div>

      {isShowPhoneScreening && <PhoneScreening isShowPhoneScreening={isShowPhoneScreening} setShowPhoneScreening={setShowPhoneScreening} />}
    </>
  );
};
