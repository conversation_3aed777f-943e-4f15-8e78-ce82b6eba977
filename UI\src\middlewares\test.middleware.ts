import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api, setErrorNotify, type QuestionListItem, type RootState } from '../..';

export const handleGet = createAsyncThunk<{ count: number; items: QuestionListItem[] }, void, { state: RootState }>(
  'test/handleGet',
  async (_, { getState, dispatch }) => {
    const state = getState() as RootState;
    const { submission } = state.submission;
    const { pagination, filterUnanswered, filterBookmark } = state.test;
    try {
      const response = await Api.get(
        `stages/list/?submissionId=${submission?._id}&page=${pagination?.page}&size=${pagination?.size}&filterUnanswered=${filterUnanswered}&filterBookmark=${filterBookmark}`
      );
      return (await response.data) as any;
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  }
);
