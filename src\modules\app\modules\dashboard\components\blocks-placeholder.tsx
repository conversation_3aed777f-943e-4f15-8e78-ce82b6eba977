import React from 'react';
import { DashboardCard } from './dashbaord-card';

export default function BlocksPlaceholder() {
  return (
    <DashboardCard>
      <div className="flex flex-col gap-4">
        <div className="w-32 h-3 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
        <div className="flex flex-col sm:flex-row gap-3">
          {[...Array(3)].map((ele, index) => {
            return <div key={index} className="bg-gray-300 dark:bg-gray-600 p-1.5 rounded-md w-full sm:w-40 h-4 animate-pulse"></div>;
          })}
        </div>
      </div>

      <div className="mt-5">
        <div>
          <div className="w-32 h-3 bg-gray-300 rounded-full dark:bg-gray-600 mb-4 animate-pulse"></div>
          <div className="hidden sm:grid sm:grid-cols-6 gap-8 text-secondaryGray dark:text-white text-sm mb-3">
            <div className="sm:col-span-3 w-20 h-2 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
            <div className="w-20 h-2 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
            <div className="w-20 h-2 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
            <div className="w-20 h-2 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
          </div>

          <div className="gird sm:hidden text-secondaryGray dark:text-white text-sm mb-3">
            <div className="w-20 h-2 bg-gray-300 rounded-full dark:bg-gray-600 animate-pulse"></div>
          </div>

          <div className="flex flex-col gap-6">
            {[...Array(5)].map((ele, index) => (
              <div key={index} className="grid grid-cols-2 items-center sm:grid-cols-6 gap-8">
                <div className="sm:col-span-3 sm:w-48 h-6 bg-gray-300 rounded-full dark:bg-gray-600  animate-pulse"></div>
                <div className="w-6 h-6 bg-gray-300 rounded-full dark:bg-gray-600 sm:hidden animate-pulse"></div>
                <div className="w-10 h-2 bg-gray-300 rounded-full dark:bg-gray-600 sm:block hidden animate-pulse"></div>
                <div className="w-10 h-2 bg-gray-300 rounded-full dark:bg-gray-600 sm:block hidden animate-pulse"></div>
                <div className="w-10 h-2 bg-gray-300 rounded-full dark:bg-gray-600 sm:block hidden animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </DashboardCard>
  );
}
