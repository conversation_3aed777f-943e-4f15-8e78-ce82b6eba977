// vite.config.ts
import react from "file:///D:/Projects/Frontend-V2/node_modules/@vitejs/plugin-react/dist/index.mjs";
import path from "path";
import { analyzer } from "file:///D:/Projects/Frontend-V2/node_modules/vite-bundle-analyzer/dist/index.mjs";
import tailwindcss from "@tailwindcss/vite";
var __vite_injected_original_dirname = "D:\\Projects\\Frontend-V2";
var vite_config_default = {
  plugins: [react(), analyzer(), tailwindcss()],
  optimizeDeps: {
    include: ["date-fns-tz"]
  },
  resolve: {
    alias: {
      "@": path.resolve(__vite_injected_original_dirname, "./src"),
      src: path.resolve(__vite_injected_original_dirname, "./src"),
      UI: path.resolve(__vite_injected_original_dirname, "./UI"),
      images: path.resolve(__vite_injected_original_dirname, "./src/images")
    }
  },
  preview: {
    allowedHosts: ["techpass-test-frontend"]
  },
  server: {
    host: "0.0.0.0",
    port: 3e3,
    strictPort: true,
    allowedHosts: ["techpass-test-frontend", "localhost"],
    proxy: {
      "/api": {
        target: process.env.VITE_API_BASE_URL,
        changeOrigin: true,
        rewrite: (path2) => path2.replace(/^\/api/, "")
      }
    }
  },
  build: {
    chunkSizeWarningLimit: 4600
  }
};
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
