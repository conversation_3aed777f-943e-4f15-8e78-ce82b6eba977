// React
import { useState } from 'react';
import { useParams } from 'react-router-dom';

// Core
import { Jumbotron, ScrollableTabs } from 'src';

// Components
import { ProfileDetails } from '../components/profile/profile-details';
import { OverviewProfile } from '../components/profile/overview';
import { UsersProfile } from '../components/profile/users';
import { PlanAndBillingsProfile } from '../components/profile/plan-and-billings';
import { AvatarsGenerated } from '../components/profile/avatar/avatars-list-generated';
import { AvatarsRequests } from '../components/profile/avatar/avatars-requests';

export const OrganizationsProfile = () => {
  // State
  const [activeTab, setActiveTab] = useState(0);

  // Hooks
  const { id } = useParams();
  
  // Data
  const tabs = [
    {
      title: 'overview',
      component: (
        <OverviewProfile
          selectedTab={{
            setActiveTab: setActiveTab,
          }}
        />
      ),
    },
    {
      title: 'users',
      component: <UsersProfile />,
    },
    {
      title: 'plan & billings',
      component: <PlanAndBillingsProfile />,
    },
    {
      title: 'avatars',
      component: (
        <div className="space-y-8">
          <AvatarsRequests />
          <AvatarsGenerated showEmpty={false} />
        </div>
      ),
    },
    // {
    //   title: 'activity logs',
    //   component: (
    //     <ActivityProfile
    //       selectedTab={{
    //         activeTab: activeTab,
    //         setActiveTab: setActiveTab,
    //       }}
    //     />
    //   ),
    // },
  ];

  return (
    <div className="space-y-2">
      <Jumbotron />

      <ProfileDetails />

      <ScrollableTabs
        data={tabs}
        titleClassName='thepassHfour'
        selectedTab={{
          activeTab: activeTab,
          setActiveTab: setActiveTab,
        }}
        nav={{
          routePrefix: `/app/organizations/profile/${id}`
        }}
      />

      {tabs[activeTab]?.component}
    </div>
  );
};
