// React
import { useRef, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useParams } from 'react-router-dom';

// Core
import { Icon } from '../';

interface ScrollableTabsProps {
  data: { title: string; backupCount?: number; component: React.ReactNode }[];
  selectedTab: {
    activeTab: number;
    setActiveTab: (index: number) => void;
  };
  fullWidth?: boolean;
  nav?: {
    routePrefix: string;
    routerParam?: string;
  };
  titleClassName?: string;
}

export const ScrollableTabs = ({ data, selectedTab, fullWidth, titleClassName, nav }: ScrollableTabsProps) => {
  //Props
  const { routePrefix, routerParam = 'tab' } = nav || {};
  //Hooks
  const navigate = useNavigate();

  const routerParamValue = useParams()[routerParam];
  // States
  const [isAtStart, setAtStart] = useState(true);
  const [isAtEnd, setAtEnd] = useState(false);

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);

  // Handle scroll
  const handleScroll = (direction: string) => {
    containerRef.current!.scrollBy({ left: direction === 'left' ? -200 : 200, behavior: 'smooth' });
  };

  const handleUpdateTab = (index: number) => {
    selectedTab?.setActiveTab(index);
    if (!!nav) {
      const { title } = data[index];
      navigate(`${routePrefix}/${title}`);
    }
  };

  // Check scroll position
  const checkScroll = () => {
    if (containerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = containerRef.current;
      setAtStart(scrollLeft === 0);
      setAtEnd(Math.round(scrollLeft + clientWidth) >= scrollWidth);
    }
  };

  // Set up event listeners
  useEffect(() => {
    if (containerRef.current) {
      checkScroll();
      containerRef.current.addEventListener('scroll', checkScroll);
    }

    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener('scroll', checkScroll);
      }
    };
  }, [isAtStart, isAtEnd]);

  useEffect(() => {
    const tabIndex = data.findIndex((item) => item.title === routerParamValue);
    tabIndex > 0 ? handleUpdateTab(tabIndex) : handleUpdateTab(0);
  }, [routerParamValue]);

  // Main Tabs OR Descriptions Titles
  const handleTitleClassname = (index: number) => {
    if (!!selectedTab) {
      const classNameWhenSelectedTab = 'px-2 xsmd:px-3 py-1 xsmd:py-2 cursor-pointer';
      if (selectedTab?.activeTab === index) return `border-b-2 border-b-[#a169fb] ${classNameWhenSelectedTab}`;
      else return `custom-tab-border ${classNameWhenSelectedTab}`;
    } else return 'text-sm dark:bg-gray-600 dark:text-gray-300 text-gray-700 bg-white dark:bg-[#3A3A3] rounded-3xl px-3 py-1';
  };

  return (
    <div className="w-full flex items-center relative overflow-hidden">
      {/* Left Arrow */}
      <button
        className={`px-2 py-2 rounded-full text-gray-700 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600 transition ${
          isAtStart && 'hidden'
        }`}
        onClick={() => handleScroll('left')}
        disabled={isAtStart}
      >
        <Icon icon="ep:arrow-left-bold" />
      </button>

      {/* Scrollable Container */}
      {data?.length > 0 && (
        <div
          className={`flex items-center gap-2 whitespace-nowrap overflow-x-auto scroll-smooth scrollbar-hidden ${!!selectedTab && 'justify-around'} ${
            fullWidth && 'flex-1'
          }`}
          ref={containerRef}
        >
          {data?.map((singleData, index) => (
            <div
              key={singleData?.title}
              onClick={() => !!selectedTab && handleUpdateTab(index)}
              className={`flex flex-col xsmd:flex-row justify-center items-center gap-3 thepassBone ${handleTitleClassname(index)}`}
            >
              {singleData?.backupCount !== undefined && (
                <span className={`text-[19px] ${singleData.backupCount >= 0 && 'font-semibold'}`}>{singleData?.backupCount}</span>
              )}

              {singleData?.title && (
                <span
                  className={`thepassHfour ${titleClassName} capitalize ${selectedTab?.activeTab === index ? 'text-[#8D5BF8]' : 'text-[#4A5568]'}`}
                >
                  {singleData?.title}
                </span>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Right Arrow */}
      <button
        className={`px-2 py-2 rounded-full text-gray-700 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600 transition ${isAtEnd && 'hidden'}`}
        onClick={() => handleScroll('right')}
        disabled={isAtEnd}
      >
        <Icon icon="ep:arrow-right-bold" />
      </button>
    </div>
  );
};
