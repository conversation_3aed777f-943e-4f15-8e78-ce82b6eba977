import React from 'react';
import { useScreenSize } from 'UI/src';

export const CardPlaceholder = () => {
  const screen = useScreenSize();

  return (
    <div className="min-w-[85vw] lg:min-w-[86vw] lg:p-0 xl:min-w-[84vw] md:min-w-[96vw] md:px-5 xl:px-0">
      {/* <div className={`w-full sm:w-[20%] p-6 rounded-lg shadow-sm mb-4 h-10 animate-pulse flex justify-between items-center`}>
      </div> */}
      <div className="mt-5 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4">
        <div className="space-y-6 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
          <div className="w-36 h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
          <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
        </div>

        <div className="space-y-6 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
          <div className="w-36 h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
          <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
        </div>

        <div className="space-y-6 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
          <div className="w-36 h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
          <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
        </div>

        <div className="space-y-6 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
          <div className="w-36 h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
          <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
        </div>

        {/* 
        <div className="space-y-6 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
          <div className="w-36 h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
          <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
        </div>

        <div className="space-y-6 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
          <div className="w-36 h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
          <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
        </div>

        <div className="space-y-6 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
          <div className="w-36 h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
          <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
        </div>

        <div className="space-y-6 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
          <div className="w-36 h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
          <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
          <div>
            <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
            <div className="w-44 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
          </div>
        </div> */}
      </div>
    </div>
  );
};
