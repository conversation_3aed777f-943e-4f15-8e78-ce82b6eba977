// React
import React, { useContext, useEffect, useState, FC } from 'react';

import { PaginationType, StepperPagination } from './pagination';

// Core
import { CustomIcon } from 'src';

// Flowbite
import { Spinner } from 'flowbite-react';
import { Api, fetchSubmission, RootState, setErrorNotify, setSubmission, useAppDispatch, useAppSelector, ToggleSwitch } from 'UI/src';
import { AnswerValue } from '../stepper/answer';
import { Submission } from '../onboarding/index';

// types

export type QuestionListItem = {
  _id: string;
  index: number;
  answer: AnswerValue;
  bookmarkFlag?: boolean;
};

type BookmarkProps = {
  filterUnanswered: boolean;
  setFilterUnanswered: (value: boolean) => void;
  filterBookmark: boolean;
  setFilterBookmark: (value: boolean) => void;
  questionList: QuestionListItem[];
  handleOnMove: (index: number, action: string) => void;
  count: number;
  pagination: PaginationType;
  setPagination: (value: PaginationType | ((prev: PaginationType) => PaginationType)) => void;
};

export type SubmissionContextType = {
  applicantId?: string;
  setLoading?: (loading: boolean) => void;
  setSubmission?: (submission: Submission) => void;
  submission: Submission;
  handleGetSubmission: (id?: string) => Promise<void>;
  handleServerError: (error: string) => void;
};

export const Bookmark: FC<BookmarkProps> = ({
  filterUnanswered,
  setFilterUnanswered,
  filterBookmark,
  setFilterBookmark,
  questionList,
  handleOnMove,
  count,
  pagination,
  setPagination,
}) => {
  const dispatch = useAppDispatch();
  const { submission } = useAppSelector((state: RootState) => state.submission);

  const handleServerError = (error: any) => {
    if (error.response?.data.message.includes('submission-locked')) {
      dispatch(setSubmission({ locked: true }));
    } else {
      dispatch(setErrorNotify(error.response.data.message));
    }
  };

  // Themes
  const theme = {
    toggle: {
      base: 'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out ',
      //  + 'before:absolute before:top-1/2 before:-translate-y-1/2 before:left-1 before:h-3 before:w-3 before:rounded-full before:bg-white ' + // left white dot
      // 'after:absolute after:top-1/2 after:-translate-y-1/2 after:right-1 after:h-3 after:w-3 after:rounded-full after:bg-white', // right white dot
      checked: {
        on: 'bg-blue-600', // background when ON
        off: 'bg-gray-400', // background when OFF
      },
      toggle: {
        base: 'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out', // 👈 white knob
        checked: {
          on: 'translate-x-5', // move right
          off: 'translate-x-0', // move left
        },
      },
    },
  };

  // State
  const [testRemainingInMilliSeconds, setTestRemainingInMilliSeconds] = useState<number | null>(null);
  const [timingView, setTimingView] = useState<{ textExceedsOneHour: boolean; hours: number; minutes: number; seconds: number }>({
    textExceedsOneHour: false,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  // Methods
  const navigateToQuestion = (e: React.MouseEvent<HTMLDivElement>, i: number) => {
    if (e.currentTarget == e.target) {
      handleOnMove(i, 'next');
    }
  };

  const handleBookmarkStyles = (answer: AnswerValue) => {
    if (typeof answer === 'object' && answer !== null) {
      let counter = 0;
      Object.keys(answer).map((key) => {
        if ((answer as Record<number, boolean>)[Number(key)]) counter++;
        return null;
      });
      if (counter >= 2) {
        return 'text-white bg-[#9061F9]';
      }
    } else if (answer !== 0 && answer !== '') {
      return 'text-white bg-[#9061F9]';
    }
  };

  const questionListPlaceholder = () => {
    if (filterBookmark && filterUnanswered) {
      return (
        <div className="flex col-span-12 flex-col items-center gap-4 mt-16">
          <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M23.6885 0.931968C24.0152 1.04475 24.335 1.17697 24.6481 1.32864L28.3902 3.17197C28.8911 3.41851 29.4419 3.54672 30.0002 3.54672C30.5585 3.54672 31.1093 3.41851 31.6102 3.17197L35.3523 1.32864C36.2974 0.863379 37.3248 0.588846 38.376 0.520714C39.4272 0.452582 40.4816 0.592185 41.4788 0.931551C42.476 1.27092 43.3967 1.8034 44.1881 2.49859C44.9796 3.19378 45.6263 4.03806 46.0914 4.98322L46.3043 5.45572L46.4881 5.9428L47.8298 9.88905C48.1943 10.9624 49.0373 11.8024 50.1077 12.167L54.0568 13.5086C55.1371 13.8762 56.1265 14.47 56.9589 15.2503C57.7914 16.0307 58.4478 16.9797 58.8843 18.034C59.3207 19.0883 59.5272 20.2236 59.4899 21.364C59.4526 22.5045 59.1725 23.6239 58.6681 24.6474L56.8277 28.3895C56.5811 28.8904 56.4529 29.4412 56.4529 29.9995C56.4529 30.5577 56.5811 31.1086 56.8277 31.6095L58.6681 35.3516C59.172 36.375 59.4517 37.494 59.4887 38.6342C59.5258 39.7743 59.3192 40.9092 58.8827 41.9631C58.4463 43.017 57.7901 43.9657 56.9579 44.7459C56.1257 45.5261 55.1367 46.1198 54.0568 46.4874L50.1077 47.8291C49.5794 48.0093 49.0996 48.3084 48.7051 48.7033C48.3107 49.0982 48.0122 49.5785 47.8327 50.107L46.4881 54.0561C46.1205 55.136 45.5268 56.125 44.7466 56.9572C43.9664 57.7894 43.0177 58.4456 41.9638 58.882C40.9099 59.3185 39.775 59.5251 38.6349 59.488C37.4948 59.451 36.3757 59.1712 35.3523 58.6674L31.6102 56.827C31.1093 56.5804 30.5585 56.4522 30.0002 56.4522C29.4419 56.4522 28.8911 56.5804 28.3902 56.827L24.6481 58.6674C23.6247 59.1712 22.5056 59.451 21.3655 59.488C20.2254 59.5251 19.0905 59.3185 18.0366 58.882C16.9826 58.4456 16.0339 57.7894 15.2537 56.9572C14.4736 56.125 13.8799 55.136 13.5123 54.0561L12.1706 50.107C11.9901 49.5784 11.6905 49.0983 11.2951 48.7039C10.8996 48.3094 10.4188 48.0111 9.88976 47.832L5.94351 46.4874C4.86339 46.1201 3.87405 45.5266 3.04154 44.7466C2.20904 43.9665 1.55251 43.0179 1.1158 41.9639C0.679087 40.9099 0.472239 39.7749 0.509062 38.6347C0.545886 37.4944 0.825536 36.3751 1.32934 35.3516L3.17267 31.6095C3.41921 31.1086 3.54742 30.5577 3.54742 29.9995C3.54742 29.4412 3.41921 28.8904 3.17267 28.3895L1.32934 24.6474C0.825536 23.6238 0.545886 22.5045 0.509062 21.3643C0.472239 20.224 0.679087 19.089 1.1158 18.035C1.55251 16.9811 2.20904 16.0324 3.04154 15.2524C3.87405 14.4723 4.86339 13.8788 5.94351 13.5116L9.88976 12.1699C10.4189 11.9898 10.8995 11.6904 11.2945 11.2949C11.6895 10.8994 11.9882 10.4184 12.1677 9.88905L13.5093 5.9428C13.8485 4.94528 14.3809 4.02435 15.076 3.23262C15.7712 2.44089 16.6155 1.79389 17.5608 1.32856C18.5061 0.86323 19.5338 0.588701 20.5852 0.520653C21.6366 0.452605 22.6911 0.592372 23.6885 0.931968ZM17.651 7.34864L16.3093 11.2978C15.9137 12.461 15.2561 13.5178 14.3873 14.3866C13.5185 15.2554 12.4617 15.913 11.2985 16.3086L7.35226 17.6503C6.86111 17.8172 6.41124 18.0871 6.0327 18.4418C5.65417 18.7965 5.35568 19.2278 5.15718 19.7071C4.95869 20.1864 4.86475 20.7025 4.88165 21.2209C4.89854 21.7394 5.02589 22.2483 5.25517 22.7136L7.09851 26.4557C7.64145 27.5581 7.92381 28.7706 7.92381 29.9995C7.92381 31.2283 7.64145 32.4408 7.09851 33.5432L5.25517 37.2824C5.02589 37.7477 4.89854 38.2566 4.88165 38.7751C4.86475 39.2935 4.95869 39.8096 5.15718 40.2889C5.35568 40.7682 5.65417 41.1996 6.0327 41.5543C6.41124 41.9089 6.86111 42.1788 7.35226 42.3457L11.2985 43.6874C12.4617 44.083 13.5185 44.7406 14.3873 45.6094C15.2561 46.4782 15.9137 47.535 16.3093 48.6982L17.651 52.6474C17.8171 53.1391 18.0865 53.5896 18.4411 53.9687C18.7956 54.3478 19.2271 54.6466 19.7067 54.8452C20.1862 55.0439 20.7027 55.1376 21.2214 55.1202C21.7401 55.1029 22.2492 54.9747 22.7143 54.7445L26.4564 52.9011C27.5589 52.3582 28.7713 52.0758 30.0002 52.0758C31.229 52.0758 32.4415 52.3582 33.5439 52.9011L37.2831 54.7445C37.427 54.8125 37.5728 54.8718 37.7206 54.9224C38.6357 55.2339 39.6371 55.1693 40.5045 54.7427C41.372 54.316 42.0345 53.5624 42.3464 52.6474L43.6881 48.6982C44.0837 47.535 44.7413 46.4782 45.6101 45.6094C46.4789 44.7406 47.5357 44.083 48.6989 43.6874L52.6481 42.3457C52.7939 42.2952 52.9398 42.2349 53.0856 42.1649C53.5152 41.9533 53.8989 41.6592 54.2149 41.2993C54.5308 40.9394 54.7727 40.5208 54.9268 40.0674C55.0809 39.614 55.1442 39.1347 55.113 38.6568C55.0819 38.179 54.9569 37.7119 54.7452 37.2824L52.9018 33.5403C52.3594 32.4383 52.0773 31.2263 52.0773 29.998C52.0773 28.7697 52.3594 27.5578 52.9018 26.4557L54.7452 22.7136C54.9745 22.2483 55.1018 21.7394 55.1187 21.2209C55.1356 20.7025 55.0417 20.1864 54.8432 19.7071C54.6447 19.2278 54.3462 18.7965 53.9676 18.4418C53.5891 18.0871 53.1392 17.8172 52.6481 17.6503L48.6989 16.3086C47.5357 15.913 46.4789 15.2554 45.6101 14.3866C44.7413 13.5178 44.0837 12.461 43.6881 11.2978L42.3464 7.35155L42.2618 7.12989L42.1656 6.91405L41.9848 6.5903C41.5056 5.82494 40.7595 5.26461 39.8909 5.01782C39.0223 4.77104 38.0931 4.85537 37.2831 5.25447L33.541 7.0978C32.439 7.64028 31.227 7.92239 29.9987 7.92239C28.7704 7.92239 27.5585 7.64028 26.4564 7.0978L22.7143 5.25447C22.2492 5.02569 21.7405 4.89875 21.2224 4.88211C20.7043 4.86548 20.1885 4.95953 19.7096 5.15799C19.2307 5.35645 18.7996 5.65477 18.4452 6.03302C18.0907 6.41128 17.8209 6.86079 17.6539 7.35155M24.3127 36.9703L40.121 21.162C40.5113 20.7706 41.0354 20.5415 41.5877 20.5207C42.1401 20.4999 42.6798 20.689 43.0985 21.05C43.5171 21.411 43.7835 21.9171 43.8442 22.4665C43.9049 23.0159 43.7553 23.5679 43.4256 24.0115L43.2127 24.2566L25.7127 41.7565C25.317 42.1522 24.7847 42.3811 24.2254 42.3963C23.6661 42.4115 23.1222 42.2118 22.7056 41.8382L22.4868 41.6107L15.1952 32.8607C14.8426 32.4365 14.6633 31.8947 14.6932 31.3439C14.7231 30.7931 14.96 30.2739 15.3565 29.8903C15.753 29.5068 16.2797 29.2872 16.8312 29.2755C17.3827 29.2638 17.9183 29.461 18.3306 29.8274L18.5552 30.0607L24.3127 36.9703Z"
              fill="#9CA3AF"
            />
          </svg>
          <div className="text-sm text-center font-medium leading-4 text-[#6B7280] space-y-3">
            <p>No unanswered questions have been bookmarked</p>
          </div>
        </div>
      );
    }
    if (filterBookmark && !filterUnanswered) {
      return (
        <div className="flex col-span-12 flex-col items-center gap-4 mt-16">
          <svg width="65" height="65" viewBox="0 0 65 65" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.75" y="0.75" width="63.5" height="63.5" rx="7.25" fill="none" />
            <rect x="0.75" y="0.75" width="63.5" height="63.5" rx="7.25" stroke="#9CA3AF" strokeWidth="1.5" />
            <path
              d="M23.75 16.25C22.1183 16.25 21.25 17.7933 21.25 19.07V45.7533C21.25 48.0067 23.6983 49.6 25.7267 48.2833L32.3017 44.015C32.36 43.9749 32.4292 43.9534 32.5 43.9534C32.5708 43.9534 32.64 43.9749 32.6983 44.015L39.2733 48.2833C41.3017 49.6 43.75 48.0083 43.75 45.7533V19.07C43.75 17.7933 42.8817 16.25 41.25 16.25H23.75Z"
              fill="#9CA3AF"
            />
          </svg>

          <p className="text-sm font-medium leading-4 text-[#6B7280]">No bookmarked questions are added.</p>
        </div>
      );
    }
    if (!filterBookmark && filterUnanswered) {
      return (
        <div className="flex col-span-12 flex-col items-center gap-4 mt-16 mb-10">
          <CustomIcon definedIcon="questions" width="60" height="60" className="text-[#6B7280]" />
          <p className="text-sm font-medium leading-4  text-[#6B7280]">All questions have been answered</p>
        </div>
      );
    }
  };

  const testTimeEndLocally = () => {
    if ((submission?.exceededTime ?? 0) > 0) return (testRemainingInMilliSeconds ?? 0) + (submission?.exceededTime ?? 0) * 60 * 1000 < 0;
    else return (testRemainingInMilliSeconds ?? 0) < 0;
  };

  // On mount
  useEffect(() => {
    const startedAtInSeconds = submission.startedAt ? new Date(submission.startedAt).getTime() / 1000 : 0;
    const testDurationInSeconds = submission.quiz?.duration ? submission.quiz.duration * 60 : 0;
    const testEndInMilliseconds = (startedAtInSeconds + testDurationInSeconds) * 1000;

    const updateRemainingTime = () => setTestRemainingInMilliSeconds(testEndInMilliseconds - new Date().getTime());
    updateRemainingTime();

    const intervalId = setInterval(updateRemainingTime, 1000);
    return () => clearInterval(intervalId);
  }, []);

  useEffect(() => {
    setTimingView(() => {
      if (testTimeEndLocally()) {
        return {
          textExceedsOneHour: false,
          hours: 0,
          minutes: 0,
          seconds: 0,
        };
      } else if ((testRemainingInMilliSeconds ?? 0) < 0) {
        const exceededTimeRemainingInMilliSeconds = (testRemainingInMilliSeconds ?? 0) + (submission?.exceededTime ?? 0) * 60 * 1000;
        return {
          textExceedsOneHour: Boolean(Math.trunc((submission?.exceededTime ?? 0) / 60)),
          hours: Math.abs(Math.trunc(exceededTimeRemainingInMilliSeconds / (1000 * 60 * 60))),
          minutes: Math.abs(Math.trunc((exceededTimeRemainingInMilliSeconds / (1000 * 60)) % 60)),
          seconds: Math.abs(Math.trunc((exceededTimeRemainingInMilliSeconds / 1000) % 60)),
        };
      } else {
        return {
          textExceedsOneHour: Boolean(Math.trunc((submission?.quiz?.duration ?? 0) / 60)),
          hours: Math.abs(Math.trunc((testRemainingInMilliSeconds ?? 0) / (1000 * 60 * 60))),
          minutes: Math.abs(Math.trunc(((testRemainingInMilliSeconds ?? 0) / (1000 * 60)) % 60)),
          seconds: Math.abs(Math.trunc(((testRemainingInMilliSeconds ?? 0) / 1000) % 60)),
        };
      }
    });

    /* When local counting ends */
    if (testTimeEndLocally()) {
      /* Check if screening question */
      if (submission.stage?.question?.type === 3) {
        /* New function because asynchronous inside useEffect */
        const handleSubmitAnswer = async () => {
          try {
            const payload = {
              submissionId: submission._id,
              stageId: submission.stage?._id,
              answer: submission.stage?.answer,
              phoneScreening: true,
              index: submission.stage?.index,
            };
            await Api.post('submissions/progress/submit', payload);
          } catch (error) {
            handleServerError(error instanceof Error ? error.message : String(error));
          }
        };
        handleSubmitAnswer();
      }

      dispatch(fetchSubmission(submission._id));
    }
  }, [testRemainingInMilliSeconds]);

  const timingStatus = () => {
    if ((testRemainingInMilliSeconds ?? 0) < 0 && (submission?.exceededTime ?? 0) > 0) {
      // Exceeded Red
      return {
        type: 3,
        text: 'exceeded',
        textStyles: 'text-[#798296] font-semibold',
        timeStyles: 'text-[#C83333] ml-auto',
        styles: 'bg-[#FFF3F3] dark:bg-[#C727164D]',
        icon: (
          <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M17.3327 8.66536C17.3327 13.2677 13.6017 16.9987 8.99935 16.9987C4.39697 16.9987 0.666016 13.2677 0.666016 8.66536C0.666016 4.06299 4.39697 0.332031 8.99935 0.332031C13.6017 0.332031 17.3327 4.06299 17.3327 8.66536ZM8.99935 13.457C9.34452 13.457 9.62435 13.1772 9.62435 12.832V7.83203C9.62435 7.48686 9.34452 7.20703 8.99935 7.20703C8.65418 7.20703 8.37435 7.48686 8.37435 7.83203V12.832C8.37435 13.1772 8.65418 13.457 8.99935 13.457ZM8.99935 4.4987C9.4596 4.4987 9.83268 4.8718 9.83268 5.33203C9.83268 5.79226 9.4596 6.16536 8.99935 6.16536C8.5391 6.16536 8.16602 5.79226 8.16602 5.33203C8.16602 4.8718 8.5391 4.4987 8.99935 4.4987Z"
              fill="#C72716"
            />
          </svg>
        ),
      };
    } else {
      if ((testRemainingInMilliSeconds ?? 0) - 10 * 60 * 1000 > 0) {
        // Safe zoon Gray
        return {
          type: 1,
          text: 'remaining',
          textStyles: 'text-[#798296] dark:text-white font-medium',
          timeStyles: 'text-[#394240] dark:text-white',
          styles: 'bg-[#F5F6F8] dark:bg-darkGrayBackground',
          icon: (
            <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
              <rect width="30" height="30" rx="15" fill="#F5F1FF" />
              <path
                d="M15 25.5C12.3478 25.5 9.8043 24.4464 7.92893 22.5711C6.05357 20.6957 5 18.1522 5 15.5C5 12.8478 6.05357 10.3043 7.92893 8.42893C9.8043 6.55357 12.3478 5.5 15 5.5C17.6522 5.5 20.1957 6.55357 22.0711 8.42893C23.9464 10.3043 25 12.8478 25 15.5C25 18.1522 23.9464 20.6957 22.0711 22.5711C20.1957 24.4464 17.6522 25.5 15 25.5ZM15 23.5C17.1217 23.5 19.1566 22.6571 20.6569 21.1569C22.1571 19.6566 23 17.6217 23 15.5C23 13.3783 22.1571 11.3434 20.6569 9.84315C19.1566 8.34285 17.1217 7.5 15 7.5C12.8783 7.5 10.8434 8.34285 9.34315 9.84315C7.84285 11.3434 7 13.3783 7 15.5C7 17.6217 7.84285 19.6566 9.34315 21.1569C10.8434 22.6571 12.8783 23.5 15 23.5ZM14 15.91V9.5H16V15.09L19.95 19.04L18.54 20.45L14 15.91Z"
                fill="#AC86FF"
              />
            </svg>
          ),
        };
      } else {
        // Warning Orange
        return {
          type: 2,
          text: 'remaining',
          textStyles: 'text-[#667085] font-semibold',
          styles: 'bg-[#F6F0E6] dark:bg-[#E88F264D]',
          timeStyles: 'text-[#E88F26] ml-auto',
          icon: (
            <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M17.3327 8.66536C17.3327 13.2677 13.6017 16.9987 8.99935 16.9987C4.39697 16.9987 0.666016 13.2677 0.666016 8.66536C0.666016 4.06299 4.39697 0.332031 8.99935 0.332031C13.6017 0.332031 17.3327 4.06299 17.3327 8.66536ZM8.99935 13.457C9.34452 13.457 9.62435 13.1772 9.62435 12.832V7.83203C9.62435 7.48686 9.34452 7.20703 8.99935 7.20703C8.65418 7.20703 8.37435 7.48686 8.37435 7.83203V12.832C8.37435 13.1772 8.65418 13.457 8.99935 13.457ZM8.99935 4.4987C9.4596 4.4987 9.83268 4.8718 9.83268 5.33203C9.83268 5.79226 9.4596 6.16536 8.99935 6.16536C8.5391 6.16536 8.16602 5.79226 8.16602 5.33203C8.16602 4.8718 8.5391 4.4987 8.99935 4.4987Z"
                fill="#E88F26"
              />
            </svg>
          ),
        };
      }
    }
  };

  return (
    <>
      <div className="max-w-[335px] min-w-[335px] flex flex-col align-middle items-center space-y-4 bg-white p-4 dark:bg-[#1f2937] rounded-xl border border-[#DEE2E4] overflow-y-auto">
        <div className={`w-full rounded-lg p-3 space-y-2 ${timingStatus()?.styles}`}>
          <div className="flex justify-center items-center flex-wrap">
            {/* <div className="flex gap-2 items-center">
              {timingStatus()?.icon}
              <p className={`text-sm font-medium text-[#667085] leading-5 capitalize ${timingStatus()?.textStyles}`}>time {timingStatus()?.text}</p>
            </div> */}

            <div className="flex justify-between">
              <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="30" height="30" rx="15" fill="#F5F1FF" />
                <path
                  d="M15 25.5C12.3478 25.5 9.8043 24.4464 7.92893 22.5711C6.05357 20.6957 5 18.1522 5 15.5C5 12.8478 6.05357 10.3043 7.92893 8.42893C9.8043 6.55357 12.3478 5.5 15 5.5C17.6522 5.5 20.1957 6.55357 22.0711 8.42893C23.9464 10.3043 25 12.8478 25 15.5C25 18.1522 23.9464 20.6957 22.0711 22.5711C20.1957 24.4464 17.6522 25.5 15 25.5ZM15 23.5C17.1217 23.5 19.1566 22.6571 20.6569 21.1569C22.1571 19.6566 23 17.6217 23 15.5C23 13.3783 22.1571 11.3434 20.6569 9.84315C19.1566 8.34285 17.1217 7.5 15 7.5C12.8783 7.5 10.8434 8.34285 9.34315 9.84315C7.84285 11.3434 7 13.3783 7 15.5C7 17.6217 7.84285 19.6566 9.34315 21.1569C10.8434 22.6571 12.8783 23.5 15 23.5ZM14 15.91V9.5H16V15.09L19.95 19.04L18.54 20.45L14 15.91Z"
                  fill="#AC86FF"
                />
              </svg>

              <div className={`text-xl font-semibold ${timingStatus()?.timeStyles}`}>
                {timingView?.textExceedsOneHour && `${timingView?.hours < 10 ? `0${timingView?.hours}` : timingView?.hours}h : `}
                {timingView?.minutes < 10 ? `0${timingView?.minutes}` : timingView?.minutes}m :{' '}
                {timingView?.seconds < 10 ? `0${timingView?.seconds}` : timingView?.seconds}s
              </div>
            </div>
          </div>

          {(timingStatus()?.type === 2 || timingStatus()?.type === 3) && (
            <p className="text-sm font-medium text-[#798296] text-center">
              {timingStatus()?.type === 2
                ? (submission.exceededTime ?? 0) > 0
                  ? 'Time is almost up! You can continue after time ends, but extra time may impact your performance evaluation.'
                  : 'Time is almost up! Review your answers now, when time runs out, your test will be submitted automatically'
                : timingStatus()?.type === 3 &&
                  'You are now using extra time. The test remains open, but your performance evaluation may be affected.'}
            </p>
          )}
        </div>

        {/* Questions */}
        <div className="w-full space-y-3">
          <p className="text-black dark:text-white items-center justify-center flex font-medium text-base leading-6">Questions</p>
          <div className="flex justify-between">
            <div className="flex gap-3 text-sm font-medium">
              <span className="text-[#798296] font-medium text-sm">Unanswered</span>
              <ToggleSwitch checked={filterUnanswered} onChange={setFilterUnanswered} color="purple" />
            </div>

            <div className="flex gap-3 text-sm font-medium">
              <span className="text-[#798296] font-medium text-sm">Marked</span>
              <ToggleSwitch checked={filterBookmark} onChange={setFilterBookmark} color="purple" />
            </div>
          </div>
        </div>

        <div className="bg-[#F5F6F8] w-full py-4 rounded-lg">
          {/* Questions icons guide */}
          <div className="flex justify-between items-center align-middle mx-4">
            <div className="flex items-center text-center align-middle gap-2">
              <span className="h-[10px] w-[10px] bg-[#7E3AF2] block rounded-full"></span>
              <span className="text-[12px] font-medium text-[#798296]">Answered</span>
            </div>

            {/* <div className="flex items-center text-center align-middle gap-2">
              <span className="h-[10px] w-[10px] bg-transparent border border-[#9061f9] block rounded-full"></span>
              <span className="text-[12px] font-medium text-[#798296]">Unanswered</span>
            </div> */}

            <div className="flex items-center text-center align-middle gap-2">
              <span className="h-[10px] w-[10px] bg-[#11ABE6] block rounded-full"></span>
              <span className="text-[12px] font-medium text-[#798296]">Marked</span>
            </div>
          </div>

          {/* Questions order items */}
          {questionList?.length > 0 ? (
            <div className="grid grid-cols-5 px-3 gap-5 pt-4 justify-center items-center">
              {questionList?.map((item) => (
                <div
                  key={item?._id}
                  onClick={(e) => navigateToQuestion(e, item.index)}
                  className={`${
                    submission.stage?.index === item.index ? 'bg-[#743AF5] text-white' : ''
                  } hover:bg-purple-300 hover:text-white transition duration-500 w-[35px] h-[35px] flex items-center justify-center p-[10px] dark:text-white border border-[#c4aefa] cursor-pointer rounded-[20px] relative ${handleBookmarkStyles(
                    item?.answer
                  )}`}
                >
                  {item.index}
                  {item.bookmarkFlag && <span className="h-[10px] w-[10px] bg-[#11ABE6] block rounded-full -top-[0.25px] -right-[0.25px] absolute" />}
                </div>
              ))}
            </div>
          ) : (
            questionListPlaceholder()
          )}
        </div>

        <StepperPagination count={count} pagination={pagination} setPagination={setPagination} />
      </div>

      {testTimeEndLocally() && (
        <div className="flex items-center justify-center fixed top-0 right-0 bottom-0 left-0 z-10 bg-black/50">
          <Spinner />
        </div>
      )}
    </>
  );
};
