import React from 'react';

export const ProgressCardPlaceholder = () => (
  <div className="space-y-4">
    <div className="flex flex-col gap-4">
      <div className="grid lg:grid-cols-2 gap-3">
        <div className="animate-pulse max-w overflow-hidden max-w p-6 border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 flex flex-col gap-7">
          <div className="w-36 h-4 my-1 bg-gray-300 rounded-full dark:bg-gray-600"></div>
          <div className="grid grid-cols-3 sm:grid-cols-7 sm:gap-12">
            <div className="w-[60px] h-[60px] bg-gray-300 dark:bg-gray-600 rounded-full flex justify-center items-center">
              <div className="w-12 h-12 rounded-full bg-[#f9f9f9] dark:bg-[#171923] flex justify-center items-center">
                <div className="w-6 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
              </div>
            </div>
            <div className="col-span-2 sm:col-span-6 self-center">
              <div className="flex items-baseline gap-1 mb-1">
                <div className="h-2 bg-gray-300 rounded-full dark:bg-gray-600 w-16"></div>
                <div className="col-span-2 w-24 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
              </div>
              <div className="flex items-baseline gap-1 mb-1">
                <div className="h-2 bg-gray-300 rounded-full dark:bg-gray-600 w-16"></div>
                <div className="col-span-2 w-24 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
              </div>
            </div>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 sm:gap-5">
            <div className="flex flex-col">
              <div className="flex  gap-2">
                <div className="w-[22px] h-[22px] bg-gray-200 rounded-lg dark:bg-gray-700"></div>
                <div className="flex items-center gap-1">
                  <div className="h-2 bg-gray-300 rounded-full dark:bg-gray-600 w-16"></div>
                  <div className="col-span-2 w-24 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
                </div>
              </div>
            </div>
            <div className="flex sm:ml-4 gap-2">
              <div className="flex  gap-2">
                <div className="w-[22px] h-[22px] bg-gray-200 rounded-lg dark:bg-gray-700"></div>
                <div className="flex items-center gap-1">
                  <div className="h-2 bg-gray-300 rounded-full dark:bg-gray-600 w-16"></div>
                  <div className="col-span-2 w-24 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="animate-pulse max-w p-6 border border-gray-200 rounded-lg shadow-sm dark:border-gray-700 flex flex-col gap-4">
          <div className="w-36 h-4 my-1 bg-gray-300 rounded-full dark:bg-gray-600"></div>
          <div className="flex flex-col gap-2">
            <div className="grid grid-cols-3 gap-3 sm:gap-0">
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
              <div className="col-span-2 w-42 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            </div>
            <div className="grid grid-cols-3 gap-3 sm:gap-0">
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
              <div className="col-span-2 w-42 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            </div>
            <div className="grid grid-cols-3 gap-3 sm:gap-0">
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
              <div className="col-span-2 w-42 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            </div>
            <div className="grid grid-cols-3 gap-3 sm:gap-0">
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
              <div className="col-span-2 w-42 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex w-full flex-col flex-1 gap-3 space-y-3 border border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700">
        <div className="w-36 h-4 my-1 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <div className="grid grid-cols-1 lg:grid-cols-4 md:grid-cols-2 gap-3">
          <div className="p-4 flex gap-3 border border-gray-200 dark:border-gray-700 rounded-xl">
            <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
            <div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
              <div className="w-6 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            </div>
          </div>
          <div className="p-4 flex gap-3 border border-gray-200 dark:border-gray-700 rounded-xl">
            <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
            <div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
              <div className="w-6 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            </div>
          </div>
          <div className="p-4 flex gap-3 border border-gray-200 dark:border-gray-700 rounded-xl">
            <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
            <div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
              <div className="w-6 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            </div>
          </div>
          <div className="p-4 flex gap-3 border border-gray-200 dark:border-gray-700 rounded-xl">
            <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
            <div>
              <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div>
              <div className="w-6 h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-between items-baseline space-y-6 border border-gray-200 rounded-xl shadow animate-pulse px-6 dark:border-gray-700 h-20">
        <div className="w-36 h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
      </div>
      <div className="flex justify-between items-baseline space-y-6 border border-gray-200 rounded-xl shadow animate-pulse px-6 dark:border-gray-700 h-20">
        <div className="w-36 h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <div className="w-6 h-6 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
      </div>
    </div>
  </div>
);
