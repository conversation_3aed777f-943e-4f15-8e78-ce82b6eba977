// Core
import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';

import { ChartsDonut } from 'src';
import { OrganizationsUsers, useAppDispatch, Api, setErrorNotify } from 'UI/src';

export const UsersCard = () => {
  // Hooks
  const { id } = useParams();

  // states
  const dispatch = useAppDispatch();
  const [users, setUsers] = useState<OrganizationsUsers>();

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get<OrganizationsUsers>(`organizations/users/${id}`, {});
      console.log(`organizations/users/${id}`, response.data);
      setUsers(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };
  const data =
    users?.userStatistics?.map((item: { role: string; value: number }) => {
      return {
        name: item.role,
        value: item.value,
        count: item.value,
        payload: item,
        color: item.role === 'Admin' ? '#C893FD' : item.role === 'Content Creator' ? '#FEC4F8' : item.role === 'HR' ? '#BDD6FF' : '#=',
      };
    }) || [];

  const rightData = () =>
    data?.map((item: { name: string; value: number; color: string }) => (
      <div key={item.name} className="flex items-center space-y-0.5 dark:text-white">
        <div className="size-3 mr-3 mt-0.5 rounded-sm" style={{ backgroundColor: item.color }}></div>
        <span className="w-32 mr-2">{item.name}</span>
        <span className="font-medium">{item.value}%</span>
      </div>
    ));

  useEffect(() => {
    handleGet();
  }, []);

  // TODO: Markos
  return (
    <ChartsDonut
      data={data as any}
      rightData={rightData}
      rightDataStyles=""
      innerRadius={45}
      outerRadius={60}
      width="80%"
      height={150}
      centeredTextOfChart={() => ''}
      bottomData={() => []}
    />
  );
};
