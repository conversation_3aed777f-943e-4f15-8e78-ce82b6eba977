import React, { ChangeEvent, FC, useEffect } from 'react';

// UI
import { Card, Icon, SubscribeCard, ToggleFilter } from 'src';
// Components
import { Jumbotron } from 'src';
import { ShowFullInfo } from '../components/show-full-info';
import { SubmissionsCreationBlockDialog } from '../components/creation-block';
import { SubmissionsCreationDialog } from '../components/creation-dialog';
import { SubmissionsCreationTestDialog } from '../components/creation-test-dialog';
import { CategoryCard } from '../components/category-card';
import { TestNotExist } from '../components/test-not-exist';
import { CardPlaceholder } from '../components/card/card-placeholder';
import { AiIntreviewDialog } from '../components/ai-dialog';
import { useAppDispatch } from 'UI/src';
import { setErrorNotify } from 'UI';
import { Api, RootState, useAppSelector, UserData, useFetchList } from 'UI/src';
import {
  setSubmissionsBankShowFullInfo,
  setSubmissionsBankCreateBlockVisible,
  setSubmissionsBankCreateDialogVisible,
  setSubmissionsBankCreateTestDialogVisible,
  setSubmissionsBankTestNotExist,
  setSubmissionsBankShowInfo,
  setSubmissionsBankShowSpecialAccessButton,
  setSubmissionsBankShowSubscribe,
  setSubmissionsBankAiDialog,
  setSubmissionsBankBlockIndex,
  setSubmissionsBankSubBlockIndex,
  setSubmissionsBankBack,
  setSubmissionsBankTestId,
  setSubmissionsBankBlockDetails,
  updateSubmissionsBankBlockDetails,
  showSubmissionsBankMoreInfo,
  closeSubmissionsBankCreationDialog,
  resetSubmissionsBankBlockDetails,
} from 'UI/src/slices/submissionsBank/submissionsBank.slice';

import { QuizType } from 'UI';
import { SingleBlock, BlockDetails } from './bank.types';

export const SubmissionsBankPage: FC = () => {
  // UI Hooks
  const dispatch = useAppDispatch();

  // Redux State
  const {
    isShowFullInfo,
    isCreateBlockVisible,
    isCreateDialogVisible,
    isCreateTestDialogVisible,
    isTestNotExist,
    blockIndex,
    subBlockIndex,
    testId,
    back,
    showInfo,
    blockDetails,
    showSpecialAccessButton,
    showSubscribe,
    aiDialog,
  } = useAppSelector((state: RootState) => state.submissionsBank);

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);
  // const isPermittedSuperAdmin = userData?.role?.some((role) => ['super-admin', 'admin'].includes(role));

  // Methods
  const { ready, loading, count, list, refresh, search, pagination, filters } = useFetchList('/blocks/list', {
    search: '',
    filters: {
      category: {
        label: 'Category',
        lookup: 'category',
      },
      subCategory: {
        label: 'Sub Category',
        lookup: 'subcategory',
        parentLookup: { key: 'category', fieldName: 'categoryId' },
      },
      difficulty: {
        label: 'Difficulty',
        enum: 'QuizDifficulty',
      },
    },
  });

  const handlePost = async (test: QuizType) => {
    try {
      dispatch(setSubmissionsBankTestId(test._id));
      dispatch(setSubmissionsBankCreateDialogVisible(true));
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
      dispatch(setSubmissionsBankTestNotExist(true));
    }
  };

  const showMoreInfo = (blockIndexValue: number, subBlockIndexValue: number) => {
    dispatch(showSubmissionsBankMoreInfo({ blockIndex: blockIndexValue, subBlockIndex: subBlockIndexValue }));
  };

  const closeSubmissionsCreationDialog = () => {
    dispatch(closeSubmissionsBankCreationDialog());
  };

  const closeSubmissionsCreationTestDialog = () => {
    if (blockDetails?.testIdDetails) {
      dispatch(setSubmissionsBankCreateTestDialogVisible(false));
      dispatch(setSubmissionsBankCreateBlockVisible(true));
    } else {
      dispatch(resetSubmissionsBankBlockDetails());
      dispatch(setSubmissionsBankCreateTestDialogVisible(false));
    }
  };

  const creationSubmissionsDialog = (test: any, backValue: boolean) => {
    dispatch(setSubmissionsBankShowFullInfo(false));
    dispatch(setSubmissionsBankBack(backValue));
    handlePost(test);
  };

  const backButton = () => {
    dispatch(setSubmissionsBankShowFullInfo(true));
    dispatch(setSubmissionsBankCreateDialogVisible(false));
  };

  const handleEditBlock = (singleBlock: SingleBlock) => {
    dispatch(
      setSubmissionsBankBlockDetails({
        blockIdDetails: singleBlock._id,
        titleDetails: singleBlock.title,
        testIdDetails: '',
      })
    );
    dispatch(setSubmissionsBankCreateBlockVisible(true));
  };

  const handleAddBlockTests = (singleBlock: SingleBlock) => {
    dispatch(updateSubmissionsBankBlockDetails({ blockIdDetails: singleBlock._id }));
    dispatch(setSubmissionsBankCreateTestDialogVisible(true));
  };

  const handleEditBlockTest = async (blockId: string, testIdValue: string) => {
    dispatch(setSubmissionsBankCreateBlockVisible(false));
    try {
      const response = await Api.get(`/templates/template/single/${testIdValue}`, {
        blockId: blockId,
      });
      dispatch(updateSubmissionsBankBlockDetails({ testIdDetails: response.data }));
    } catch (error: any) {
      dispatch(setErrorNotify(error));
    }
    dispatch(setSubmissionsBankCreateTestDialogVisible(true));
  };

  // On Mount
  useEffect(() => {
    if (userData?.role && Array.isArray(userData?.role)) {
      const hasAccess = userData?.role.some((role: any) => ['super-admin', 'admin', 'hr'].includes(typeof role === 'string' ? role : role.name));
      dispatch(setSubmissionsBankShowSpecialAccessButton(hasAccess));
    }
  }, [userData?.role, dispatch]);

  return (
    <>
      <div>
        <div className="space-y-4">
          {/* Header Only In Large Screen */}
          <Jumbotron />
          {/* <div className="sm:flex gap-3 items-center justify-between space-y-2">
          <TabsButtons />
        </div> */}
        </div>

        <div className="flex justify-end gap-4 my-4">
          {/* Search bar */}
          <div className="flex flex-row items-center space-x-3 space-y-0 justify-between shadow-sm">
            <div className="relative w-full mb-4 sm:mb-0">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search tests..."
                className="bg-gray-white border border-gray-200 text-gray-800 text-[13.5px] rounded-lg  block w-full lg:w-[270px] pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0 focus:border-gray-300"
                value={search.value}
                onInput={(e: ChangeEvent<HTMLInputElement>) => search.update(e.target.value)}
              />
            </div>
          </div>

          <ToggleFilter
            filters={filters}
            resultsFound={count}
            handleDates={{ startDate: { value: null, update: () => {} }, endDate: { value: null, update: () => {} } }}
            drawerInsideDrawer={false}
            tempException={false}
          />
        </div>

        {!ready && <CardPlaceholder />}
        {/* blocks list */}
        {list && (list as SingleBlock[]).length > 0 && (
          <div className="grid grid-cols-1 grid-rows-[320px] sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4 mt-2">
            {(list as SingleBlock[])
              .filter((singleBlock) => singleBlock.quiz && singleBlock.quiz.length > 0)
              .map((singleBlock, blockIndexValue) => {
                if (!singleBlock || !singleBlock.quizzes) return null;
                return (
                  <CategoryCard
                    key={singleBlock.subCategory}
                    subCategoryName={singleBlock.subCategoryName}
                    quizzes={singleBlock.quiz}
                    blockIndex={blockIndexValue}
                    subBlockIndex={subBlockIndex}
                    showMoreInfo={showMoreInfo}
                    creationSubmissionsDialog={creationSubmissionsDialog as any}
                    refresh={refresh}
                    setCreateBlockVisibility={(visible: boolean) => dispatch(setSubmissionsBankCreateBlockVisible(visible))}
                    setCreateTestDialogVisibility={(visible: boolean) => dispatch(setSubmissionsBankCreateTestDialogVisible(visible))}
                    index={subBlockIndex}
                  />
                );
              })}
          </div>
        )}

        {/* Information Lamp Icon */}
        <div className="fixed bottom-[10%] right-8 w-full cursor-pointer z-10">
          {showInfo && (
            <div className="text-[#374151] absolute top-[-155px] right-0 rounded-lg bg-[#F2ECFF] dark:bg-[#d0bcfd] p-4">
              <p className="mb-2 font-medium">Applicant Category</p>
              <div className="text-xs flex flex-col gap-1">
                <p>Intern: 0 years of experience.</p>
                <p>Fresh: 0 -1 years of experience.</p>
                <p>Junior : 1-3 years of experience.</p>
                <p>Mid-Level : 3-5 years of experience.</p>
                <p>Senior : 5+ years of experience.</p>
              </div>
              <div className="absolute top-full right-[14px] w-0 h-0 border-[10px] border-solid border-transparent border-t-[#F2ECFF] dark:border-t-[#d0bcfd] border-r-[10px] "></div>
            </div>
          )}
          <div
            onClick={() => dispatch(setSubmissionsBankShowInfo(!showInfo))}
            className="w-[50px] h-[50px] rounded-[50%] absolute top-[50%] right-0 bg-[#F2ECFF] dark:bg-[#d0bcfd]"
          >
            <Icon
              icon={showInfo ? 'iconamoon:close-fill' : 'heroicons-outline:light-bulb'}
              width="30"
              className="text-center mt-[22%] text-primaryPurple"
            />
          </div>
        </div>

        {/* Card Full View */}
        {isShowFullInfo && (
          <ShowFullInfo
            singleDetails={(list as SingleBlock[])[blockIndex]}
            creationSubmissionsDialog={creationSubmissionsDialog as any}
            onClose={() => dispatch(setSubmissionsBankShowFullInfo(false))}
          />
        )}

        {/* Creation Block */}
        {isCreateBlockVisible && (
          <SubmissionsCreationBlockDialog
            refresh={refresh}
            blockDetails={blockDetails}
            setBlockDetails={(details: any) => dispatch(setSubmissionsBankBlockDetails(details))}
            loading={loading}
            setCreateBlockVisibility={(visible: boolean) => dispatch(setSubmissionsBankCreateBlockVisible(visible))}
            setCreateTestDialogVisibility={(visible: boolean) => dispatch(setSubmissionsBankCreateTestDialogVisible(visible))}
            handleEditBlockTest={handleEditBlockTest as any}
          />
        )}

        {/* Creation Dialog */}
        {isCreateDialogVisible && (
          <SubmissionsCreationDialog testId={testId || ''} back={back} backButton={backButton} onClose={closeSubmissionsCreationDialog} />
        )}

        {/* Creation New Test Dialog */}
        {isCreateTestDialogVisible && (
          <SubmissionsCreationTestDialog
            blockDetails={blockDetails as any}
            setBlockDetails={(details: any) => dispatch(setSubmissionsBankBlockDetails(details))}
            onClose={closeSubmissionsCreationTestDialog}
            setCreateBlockVisibility={(visible: boolean) => dispatch(setSubmissionsBankCreateBlockVisible(visible))}
            setCreateTestDialogVisibility={(visible: boolean) => dispatch(setSubmissionsBankCreateTestDialogVisible(visible))}
            refresh={refresh}
          />
        )}

        {/* Ai Interview */}
        {aiDialog && <AiIntreviewDialog onClose={() => dispatch(setSubmissionsBankAiDialog(false))} onCreate={() => {}} />}

        {/* Test Not Available */}
        {isTestNotExist && <TestNotExist onClose={() => dispatch(setSubmissionsBankTestNotExist(false))} />}

        {/* Subscription */}
        {showSubscribe && <SubscribeCard onClose={() => dispatch(setSubmissionsBankShowSubscribe(false))} onSubscribe={() => {}} />}
      </div>

      {/* Creation Block */}
      {/* {isCreateBlockVisible && (
        <SubmissionsCreationBlockDialog
          refresh={refresh}
          blockDetails={blockDetails}
          setBlockDetails={setBlockDetails}
          loading={loading}
          setCreateBlockVisibility={setCreateBlockVisibility}
          setCreateTestDialogVisibility={setCreateTestDialogVisibility}
          handleEditBlockTest={handleEditBlockTest}
        />
      )} */}
    </>
  );
};
