import { combineReducers, configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer, type PersistConfig } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import appSlice from '../slices/app/app.slice';
import authSlice from '../slices/auth/auth.slice';
import notifySlice from '../slices/notify/notify.slice';
import sidebarVisibleSlice from '../slices/sidebarVisible/sidebarVisible.slice';
import viewOnlySlice from '../slices/viewOnly/viewOnly.slice';
import confirmDialogSlice from '../slices/confirmDialog/confirmDialog.slice';
import formSlice from '../slices/form/form.slice';
import submissionAiSlice from '../slices/submissionAi/submissionAi.slice';
import submissionSlice from '../slices/submission/submission.slice';
import applicantsSlice from '../slices/applicants/applicants.slice';
import categoryManagementSlice from '../slices/categoryManagement/categoryManagement.slice';
import drawerSlice from '../slices/drawer/drawer.slice';
import submissionsBankSlice from '../slices/submissionsBank/submissionsBank.slice';
import quizzesSingleSlice from '../slices/quizzesSingle/quizzesSingle.slice';
import interviewSlice from '../slices/interview/interview.slice';
import testSlice from '../slices/test/test.slice';
import apiMiddleware from './api.middleware';
import assessmentReportSlice from '../slices/assessmentReport/assessmentReport.slice';
import featureEndedSlice from '../slices/featureEnded/featureEnded.slice';
import { encryptTransform } from 'redux-persist-transform-encrypt';
import cookieStorage from './cookie.store';

import { type TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

const rootReducer = combineReducers({
  app: appSlice,
  auth: authSlice,
  notify: notifySlice,
  sidebarVisible: sidebarVisibleSlice,
  viewOnly: viewOnlySlice,
  confirmDialog: confirmDialogSlice,
  form: formSlice,
  submissionAi: submissionAiSlice,
  submission: submissionSlice,
  applicants: applicantsSlice,
  categoryManagement: categoryManagementSlice,
  drawer: drawerSlice,
  submissionsBank: submissionsBankSlice,
  quizzesSingle: quizzesSingleSlice,
  interview: interviewSlice,
  test: testSlice,
  assessmentReport: assessmentReportSlice,
  featureEnded: featureEndedSlice,
});

const persistConfig: PersistConfig<ReturnType<typeof rootReducer>> = {
  key: 'root',
  storage,
  /* This is for saving the states in the Local Storage */
  whitelist: [
    'app',
    'auth',
    'mode',
    'notify',
    'sidebarVisible',
    'viewOnly',
    'confirmDialog',
    'form',
    'submissionAi',
    'submission',
    'applicants',
    'categoryManagement',
    'drawer',
    'submissionsBank',
    'quizzesSingle',
    'interview',
    'test',
  ],
  transforms: [
    encryptTransform(
      {
        secretKey: 'my-super-secret-key',
        onError: function (error: unknown) {
          console.error('Encryption error:', error);
        },
      },
      {}
    ),
  ],
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }).concat(apiMiddleware),
});

export const persistor = persistStore(store);
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
export const CookieStorage = cookieStorage;
