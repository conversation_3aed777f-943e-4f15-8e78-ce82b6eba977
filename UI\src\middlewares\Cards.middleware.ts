import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../services/api';

// Card type interface
export interface CardData {
  name: string;
  number: string;
  month: string;
  year: string;
  cvc: string;
  type: string;
  default: boolean;
}

// Add card
export const addCard = createAsyncThunk('cards/addCard', async (payload: CardData, { rejectWithValue }) => {
  try {
    const response = await Api.post('cards/single', payload);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to add card');
  }
});

// Update card
export const updateCard = createAsyncThunk('cards/updateCard', async ({ id, payload }: { id: string; payload: CardData }, { rejectWithValue }) => {
  try {
    const response = await Api.put(`cards/single/${id}`, payload);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to update card');
  }
});

// Delete card
export const deleteCard = createAsyncThunk('cards/deleteCard', async (id: string, { rejectWithValue }) => {
  try {
    const response = await Api.delete(`cards/single/${id}`);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to delete card');
  }
});

// Get all cards
export const fetchCards = createAsyncThunk('cards/fetchCards', async (_, { rejectWithValue }) => {
  try {
    const response = await Api.get('cards/list');
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to fetch cards');
  }
});

// Get single card by ID
export const fetchCardById = createAsyncThunk('cards/fetchCardById', async (id: string, { rejectWithValue }) => {
  try {
    const response = await Api.get(`cards/single/${id}`);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to fetch card');
  }
});

// Set default card
export const setDefaultCard = createAsyncThunk('cards/setDefaultCard', async (id: string, { rejectWithValue }) => {
  try {
    const response = await Api.put(`cards/default/${id}`);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to set default card');
  }
});
