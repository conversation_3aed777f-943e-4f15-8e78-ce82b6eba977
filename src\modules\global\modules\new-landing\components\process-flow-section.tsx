// Core
import { CustomIcon } from 'src';
import addNote1 from 'images/landing/select.png';
import addNote2 from 'images/landing/test applicant.png';
import addNote3 from 'images/landing/report.png';
import addNote4 from 'images/landing/interview.png';
import addNote5 from 'images/landing/insights.png';
export const ProcessFlowSection = () => {
  const data: { id: number; src: string; title: string; description: string }[] = [
    {
      id: 1,
      src: addNote1,
      title: 'Select Role Category',
      description: 'Pick your role category to personalize your recruitment experience',
    },
    {
      id: 2,
      src: addNote2,
      title: 'Test Your Applicant',
      description: 'Easily send pre-built or custom tests matched to the role to identify top talent.  ',
    },
    {
      id: 3,
      src: addNote3,
      title: 'Get Test Report',
      description: 'Turn test data into actionable insights for faster evaluation',
    },
    {
      id: 4,
      src: addNote4,
      title: 'Interview Who’s Worth It',
      description: 'Save time by meeting only the best-fit applicants ',
    },
    {
      id: 5,
      src: addNote5,
      title: 'Get Full Insights',
      description: 'See the full picture with reports and performance trends',
    },
  ];

  return (
    <div id="how-It-Works" className="w-full flex flex-col justify-center items-center sm:mt-3 py-1 sm:py-7 ">
      {/* <div className="w-fit h-8 text-nowrap bg-[#ddd7ff]/30 rounded-lg flex items-center justify-start mt-8 px-4 py-2 mb-7">
        <h2 className="text-sm text-[#8d5bf8] dark:text-white  font-medium tracking-wider uppercase">FROM Apply TO hire IN MINUTES </h2>
      </div> */}
      <div className="flex flex-col items-center text-center justify-center space-y-4 px-10">
        <p className="font-semibold text-[16px] md:text-[55px]">Remove the Burden of Manual Work</p>
        <div className="w-11/12">
          <p className=" text-[14px] sm:text-[22px] text-text-500 font-normal text-center mb-14">
            Let AI handle the heavy lifting while you focus on building great teams.
          </p>
        </div>
      </div>

      {/* tablet and desktop screens  */}
      <div className="hidden sm:grid grid-cols-1 sm:grid-cols-3 md:grid-cols-5 gap-8 max-w-full mx-auto mb-7 px-4 md:px-8 lg:px-12 mt-12">
        {data.map((item, index) => (
          <div key={item.id} className="w-full ">
            <div
              className={`flex flex-col justify-center gap-3 max-w-[155px] sm:max-w-none ${
                index % 2 === 0
                  ? 'items-start text-left mr-auto sm:items-center sm:text-center sm:mx-0'
                  : 'items-end text-right ml-auto sm:items-center sm:text-center sm:mx-0'
              }`}
            >
              <div className="relative">
                <img width={80} src={item.src} alt="Notes" className="mb-2 sm:scale-100 md:scale-75 lg:scale-125" />
                {item.id !== data.length &&
                  (index % 2 === 1 ? (
                    <>
                      <div className={`absolute top-[50px] hidden sm:block md:hidden lg:block left-[80px] transform scale-x-[-1] rotate-180`}>
                        <CustomIcon definedIcon="curvedArrow" />
                      </div>

                      <div className="hidden md:block lg:hidden absolute top-[30px] left-[80px]  ">
                        <svg width="73" height="30" viewBox="0 0 73 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M0.821592 1.25781C0.679207 1.02121 0.755586 0.713976 0.992189 0.571592C1.22879 0.429207 1.53602 0.505587 1.67841 0.742189L1.25 1L0.821592 1.25781ZM72.5 1L72.3262 6.77089L67.4154 3.73496L72.5 1ZM1.25 1L1.67841 0.742189C2.19252 1.59649 2.70941 2.43265 3.22902 3.25044L2.80701 3.51859L2.38499 3.78673C1.8609 2.96189 1.33975 2.11884 0.821592 1.25781L1.25 1ZM6.11122 8.42053L6.51758 8.1292C7.6987 9.77664 8.89234 11.3305 10.0977 12.7885L9.71237 13.1071L9.32702 13.4257C8.10594 11.9487 6.89833 10.3765 5.70487 8.71186L6.11122 8.42053ZM13.666 17.5033L14.0223 17.1525C15.4499 18.6027 16.8915 19.9197 18.346 21.1L18.0309 21.4882L17.7159 21.8765C16.23 20.6706 14.7609 19.3283 13.3097 17.854L13.666 17.5033ZM22.8638 24.8821L23.1193 24.4523C24.8527 25.4827 26.6012 26.3205 28.3629 26.9609L28.1921 27.4308L28.0213 27.9007C26.1959 27.2372 24.391 26.3716 22.6083 25.3119L22.8638 24.8821ZM33.9258 28.8202L33.9864 28.3239C34.9453 28.441 35.9082 28.5 36.875 28.5V29V29.5C35.8674 29.5 34.864 29.4385 33.8652 29.3165L33.9258 28.8202ZM36.875 29V28.5C37.8418 28.5 38.8047 28.441 39.7636 28.3239L39.8242 28.8202L39.8848 29.3165C38.886 29.4385 37.8826 29.5 36.875 29.5V29ZM45.5579 27.4308L45.3871 26.9609C47.1488 26.3205 48.8973 25.4827 50.6307 24.4523L50.8862 24.8821L51.1417 25.3119C49.359 26.3716 47.5541 27.2372 45.7287 27.9007L45.5579 27.4308ZM55.7191 21.4882L55.404 21.1C56.8585 19.9197 58.3001 18.6027 59.7277 17.1525L60.084 17.5033L60.4403 17.854C58.9891 19.3283 57.52 20.6706 56.0341 21.8765L55.7191 21.4882ZM64.0376 13.1071L63.6523 12.7885C64.8577 11.3305 66.0513 9.77664 67.2324 8.12919L67.6388 8.42053L68.0451 8.71186C66.8517 10.3765 65.6441 11.9487 64.423 13.4257L64.0376 13.1071ZM0.821592 1.25781C0.679207 1.02121 0.755586 0.713976 0.992189 0.571592C1.22879 0.429207 1.53602 0.505587 1.67841 0.742189L1.25 1L0.821592 1.25781ZM72.5 1L72.3262 6.77089L67.4154 3.73496L72.5 1ZM1.25 1L1.67841 0.742189C2.19252 1.59649 2.70941 2.43265 3.22902 3.25044L2.80701 3.51859L2.38499 3.78673C1.8609 2.96189 1.33975 2.11884 0.821592 1.25781L1.25 1ZM6.11122 8.42053L6.51758 8.1292C7.6987 9.77664 8.89234 11.3305 10.0977 12.7885L9.71237 13.1071L9.32702 13.4257C8.10594 11.9487 6.89833 10.3765 5.70487 8.71186L6.11122 8.42053ZM13.666 17.5033L14.0223 17.1525C15.4499 18.6027 16.8915 19.9197 18.346 21.1L18.0309 21.4882L17.7159 21.8765C16.23 20.6706 14.7609 19.3283 13.3097 17.854L13.666 17.5033ZM22.8638 24.8821L23.1193 24.4523C24.8527 25.4827 26.6012 26.3205 28.3629 26.9609L28.1921 27.4308L28.0213 27.9007C26.1959 27.2372 24.391 26.3716 22.6083 25.3119L22.8638 24.8821ZM33.9258 28.8202L33.9864 28.3239C34.9453 28.441 35.9082 28.5 36.875 28.5V29V29.5C35.8674 29.5 34.864 29.4385 33.8652 29.3165L33.9258 28.8202ZM36.875 29V28.5C37.8418 28.5 38.8047 28.441 39.7636 28.3239L39.8242 28.8202L39.8848 29.3165C38.886 29.4385 37.8826 29.5 36.875 29.5V29ZM45.5579 27.4308L45.3871 26.9609C47.1488 26.3205 48.8973 25.4827 50.6307 24.4523L50.8862 24.8821L51.1417 25.3119C49.359 26.3716 47.5541 27.2372 45.7287 27.9007L45.5579 27.4308ZM55.7191 21.4882L55.404 21.1C56.8585 19.9197 58.3001 18.6027 59.7277 17.1525L60.084 17.5033L60.4403 17.854C58.9891 19.3283 57.52 20.6706 56.0341 21.8765L55.7191 21.4882ZM64.0376 13.1071L63.6523 12.7885C64.8577 11.3305 66.0513 9.77664 67.2324 8.12919L67.6388 8.42053L68.0451 8.71186C66.8517 10.3765 65.6441 11.9487 64.423 13.4257L64.0376 13.1071Z"
                            fill="#1B1F3B"
                          />
                        </svg>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className={`absolute top-[-10px] hidden sm:block md:hidden lg:block left-[80px] transform`}>
                        <CustomIcon definedIcon="curvedArrow" />
                      </div>
                      <div className="hidden md:block lg:hidden absolute top-[10px] left-[70px]">
                        <svg width="77" height="30" viewBox="0 0 77 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M0.578018 28.7318C0.429895 28.9649 0.498746 29.2739 0.7318 29.422C0.964854 29.5701 1.27386 29.5013 1.42198 29.2682L1 29L0.578018 28.7318ZM76.25 29L75.9324 23.2352L71.0988 26.3927L76.25 29ZM1 29L1.42198 29.2682C1.97445 28.399 2.52997 27.5485 3.08846 26.717L2.67341 26.4382L2.25835 26.1594C1.69501 26.9981 1.13487 27.8556 0.578018 28.7318L1 29ZM6.22102 21.4649L6.61919 21.7673C7.88517 20.1006 9.16478 18.5301 10.4572 17.0584L10.0815 16.7285L9.70575 16.3985C8.39664 17.8893 7.10209 19.4783 5.82285 21.1625L6.22102 21.4649ZM14.3028 12.315L14.6479 12.6768C16.1659 11.229 17.6991 9.91605 19.2462 8.74138L18.9438 8.34316L18.6414 7.94495C17.0621 9.14414 15.5005 10.4818 13.9577 11.9531L14.3028 12.315ZM24.05 4.99345L24.2923 5.43081C26.1078 4.425 27.9394 3.60802 29.7852 2.98475L29.6253 2.51103L29.4653 2.03731C27.5584 2.68121 25.6719 3.52333 23.8077 4.55608L24.05 4.99345ZM35.5758 1.17232L35.6321 1.66914C36.6257 1.55668 37.6234 1.5 38.625 1.5V1V0.5C37.5856 0.5 36.5503 0.55882 35.5196 0.675489L35.5758 1.17232ZM38.625 1V1.5C39.6266 1.5 40.6243 1.55668 41.6179 1.66914L41.6742 1.17232L41.7304 0.675489C40.6997 0.55882 39.6644 0.5 38.625 0.5V1ZM47.6247 2.51103L47.4648 2.98475C49.3106 3.60802 51.1422 4.42501 52.9577 5.43082L53.2 4.99345L53.4423 4.55609C51.5781 3.52333 49.6916 2.68121 47.7847 2.03731L47.6247 2.51103ZM58.3062 8.34316L58.0038 8.74138C59.5509 9.91605 61.0841 11.229 62.6021 12.6768L62.9472 12.315L63.2923 11.9531C61.7495 10.4818 60.1879 9.14414 58.6086 7.94495L58.3062 8.34316ZM67.1685 16.7284L66.7928 17.0584C68.0852 18.5301 69.3648 20.1006 70.6308 21.7673L71.029 21.4649L71.4271 21.1625C70.1479 19.4783 68.8534 17.8893 67.5443 16.3985L67.1685 16.7284ZM0.578018 28.7318C0.429895 28.9649 0.498746 29.2739 0.7318 29.422C0.964854 29.5701 1.27386 29.5013 1.42198 29.2682L1 29L0.578018 28.7318ZM76.25 29L75.9324 23.2352L71.0988 26.3927L76.25 29ZM1 29L1.42198 29.2682C1.97445 28.399 2.52997 27.5485 3.08846 26.717L2.67341 26.4382L2.25835 26.1594C1.69501 26.9981 1.13487 27.8556 0.578018 28.7318L1 29ZM6.22102 21.4649L6.61919 21.7673C7.88517 20.1006 9.16478 18.5301 10.4572 17.0584L10.0815 16.7285L9.70575 16.3985C8.39664 17.8893 7.10209 19.4783 5.82285 21.1625L6.22102 21.4649ZM14.3028 12.315L14.6479 12.6768C16.1659 11.229 17.6991 9.91605 19.2462 8.74138L18.9438 8.34316L18.6414 7.94495C17.0621 9.14414 15.5005 10.4818 13.9577 11.9531L14.3028 12.315ZM24.05 4.99345L24.2923 5.43081C26.1078 4.425 27.9394 3.60802 29.7852 2.98475L29.6253 2.51103L29.4653 2.03731C27.5584 2.68121 25.6719 3.52333 23.8077 4.55608L24.05 4.99345ZM35.5758 1.17232L35.6321 1.66914C36.6257 1.55668 37.6234 1.5 38.625 1.5V1V0.5C37.5856 0.5 36.5503 0.55882 35.5196 0.675489L35.5758 1.17232ZM38.625 1V1.5C39.6266 1.5 40.6243 1.55668 41.6179 1.66914L41.6742 1.17232L41.7304 0.675489C40.6997 0.55882 39.6644 0.5 38.625 0.5V1ZM47.6247 2.51103L47.4648 2.98475C49.3106 3.60802 51.1422 4.42501 52.9577 5.43082L53.2 4.99345L53.4423 4.55609C51.5781 3.52333 49.6916 2.68121 47.7847 2.03731L47.6247 2.51103ZM58.3062 8.34316L58.0038 8.74138C59.5509 9.91605 61.0841 11.229 62.6021 12.6768L62.9472 12.315L63.2923 11.9531C61.7495 10.4818 60.1879 9.14414 58.6086 7.94495L58.3062 8.34316ZM67.1685 16.7284L66.7928 17.0584C68.0852 18.5301 69.3648 20.1006 70.6308 21.7673L71.029 21.4649L71.4271 21.1625C70.1479 19.4783 68.8534 17.8893 67.5443 16.3985L67.1685 16.7284Z"
                            fill="#1B1F3B"
                          />
                        </svg>
                      </div>
                    </>
                  ))}
              </div>

              <h3 className="text-base font-semibold">{item.title}</h3>
              <p className="text-sm  text-[#4E5E82] max-w-[238px] text-center leading-relaxed">{item.description}</p>

              {item.id !== data.length && (
                <div className="sm:hidden mt-3 w-full flex justify-center">
                  <svg
                    className={`${index % 2 === 0 ? '' : 'scale-x-[-1]'}`}
                    width="63"
                    height="54"
                    viewBox="0 0 63 54"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M1.02727 0.107498C0.751531 0.0925358 0.515873 0.303934 0.50091 0.579671C0.485947 0.855408 0.697346 1.09107 0.973083 1.10603L1.00018 0.606764L1.02727 0.107498ZM60.9029 53.6319L62.7824 48.1729L57.115 49.2746L60.9029 53.6319ZM1.00018 0.606764L0.973083 1.10603C1.87904 1.15519 2.77886 1.21857 3.67229 1.29625L3.7156 0.798127L3.75891 0.300005C2.85452 0.221377 1.94389 0.157237 1.02727 0.107498L1.00018 0.606764ZM9.11888 1.45414L9.04127 1.94808C10.8401 2.2307 12.6092 2.57561 14.3462 2.98346L14.4605 2.4967L14.5748 2.00993C12.8128 1.59622 11.0193 1.24658 9.19648 0.960195L9.11888 1.45414ZM19.7049 3.95031L19.5518 4.42631C21.2776 4.9812 22.9668 5.6041 24.6168 6.29577L24.8101 5.83464L25.0034 5.37351C23.3263 4.67053 21.6103 4.03776 19.8579 3.47431L19.7049 3.95031ZM29.7292 8.16143L29.495 8.60321C31.0863 9.4467 32.635 10.3618 34.1384 11.3494L34.4129 10.9315L34.6874 10.5136C33.1576 9.50864 31.5819 8.5776 29.9633 7.71966L29.7292 8.16143ZM38.8133 14.1328L38.5002 14.5226C39.1994 15.0843 39.8869 15.664 40.5623 16.2619L40.8938 15.8876L41.2252 15.5132C40.5378 14.9047 39.8381 14.3147 39.1264 13.743L38.8133 14.1328ZM40.8938 15.8876L40.5623 16.2619C41.2378 16.8599 41.8967 17.4719 42.5391 18.0978L42.888 17.7397L43.2369 17.3815C42.5831 16.7445 41.9126 16.1216 41.2252 15.5132L40.8938 15.8876ZM46.5996 21.7191L46.2181 22.0423C47.3809 23.4147 48.4772 24.8408 49.5076 26.3181L49.9177 26.032L50.3278 25.746C49.2797 24.2434 48.1644 22.7925 46.9811 21.3959L46.5996 21.7191ZM52.8242 30.6324L52.39 30.8802C53.2767 32.4341 54.1 34.0352 54.8602 35.6809L55.3142 35.4712L55.7681 35.2615C54.9961 33.5904 54.1598 31.9639 53.2585 30.3846L52.8242 30.6324ZM57.3935 40.5004L56.9242 40.6729C57.5398 42.3476 58.0968 44.0617 58.5957 45.813L59.0766 45.676L59.5574 45.539C59.052 43.7645 58.4872 42.0266 57.8628 40.3279L57.3935 40.5004ZM1.02727 0.107498C0.751531 0.0925358 0.515873 0.303934 0.50091 0.579671C0.485947 0.855408 0.697346 1.09107 0.973083 1.10603L1.00018 0.606764L1.02727 0.107498ZM60.9029 53.6319L62.7824 48.1729L57.115 49.2746L60.9029 53.6319ZM1.00018 0.606764L0.973083 1.10603C1.87904 1.15519 2.77886 1.21857 3.67229 1.29625L3.7156 0.798127L3.75891 0.300005C2.85452 0.221377 1.94389 0.157237 1.02727 0.107498L1.00018 0.606764ZM9.11888 1.45414L9.04127 1.94808C10.8401 2.2307 12.6092 2.57561 14.3462 2.98346L14.4605 2.4967L14.5748 2.00993C12.8128 1.59622 11.0193 1.24658 9.19648 0.960195L9.11888 1.45414ZM19.7049 3.95031L19.5518 4.42631C21.2776 4.9812 22.9668 5.6041 24.6168 6.29577L24.8101 5.83464L25.0034 5.37351C23.3263 4.67053 21.6103 4.03776 19.8579 3.47431L19.7049 3.95031ZM29.7292 8.16143L29.495 8.60321C31.0863 9.4467 32.635 10.3618 34.1384 11.3494L34.4129 10.9315L34.6874 10.5136C33.1576 9.50864 31.5819 8.5776 29.9633 7.71966L29.7292 8.16143ZM38.8133 14.1328L38.5002 14.5226C39.1994 15.0843 39.8869 15.664 40.5623 16.2619L40.8938 15.8876L41.2252 15.5132C40.5378 14.9047 39.8381 14.3147 39.1264 13.743L38.8133 14.1328ZM40.8938 15.8876L40.5623 16.2619C41.2378 16.8599 41.8967 17.4719 42.5391 18.0978L42.888 17.7397L43.2369 17.3815C42.5831 16.7445 41.9126 16.1216 41.2252 15.5132L40.8938 15.8876ZM46.5996 21.7191L46.2181 22.0423C47.3809 23.4147 48.4772 24.8408 49.5076 26.3181L49.9177 26.032L50.3278 25.746C49.2797 24.2434 48.1644 22.7925 46.9811 21.3959L46.5996 21.7191ZM52.8242 30.6324L52.39 30.8802C53.2767 32.4341 54.1 34.0352 54.8602 35.6809L55.3142 35.4712L55.7681 35.2615C54.9961 33.5904 54.1598 31.9639 53.2585 30.3846L52.8242 30.6324ZM57.3935 40.5004L56.9242 40.6729C57.5398 42.3476 58.0968 44.0617 58.5957 45.813L59.0766 45.676L59.5574 45.539C59.052 43.7645 58.4872 42.0266 57.8628 40.3279L57.3935 40.5004Z"
                      fill="#1B1F3B"
                    />
                  </svg>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Mobile  */}
      <div className="sm:hidden w-full grid grid-cols-2 gap-4 px-9 ">
        {data.map((item, index) => (
          <div key={item.id} className={`w-full relative flex ${item.id === 2 || item.id === 4 ? ' pt-[146px] ' : '-mt-8.5'}`}>
            <div className={`flex flex-col  items-center text-center gap-3 max-w-[155px]`}>
              <div className="relative">
                <img width={80} src={item.src} alt="Notes" className="mb-2" />
              </div>

              <h3 className="text-base font-semibold">{item.title}</h3>
              <p className="text-sm text-[#4E5E82] max-w-[238px] text-center leading-relaxed">{item.description}</p>

              {item.id == 1 || item.id == 3 ? (
                <div className={`mt-1 absolute z-50 top-28 left-40 w-full flex`}>
                  <svg
                    // className={`${item.id === 2 || item.id === 4 ? 'scale-x-[-1]' : index % 2 === 0 ? '' : 'scale-x-[-1]'}`}
                    width="63"
                    height="54"
                    viewBox="0 0 63 54"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M1.02727 0.107498C0.751531 0.0925358 0.515873 0.303934 0.50091 0.579671C0.485947 0.855408 0.697346 1.09107 0.973083 1.10603L1.00018 0.606764L1.02727 0.107498ZM60.9029 53.6319L62.7824 48.1729L57.115 49.2746L60.9029 53.6319ZM1.00018 0.606764L0.973083 1.10603C1.87904 1.15519 2.77886 1.21857 3.67229 1.29625L3.7156 0.798127L3.75891 0.300005C2.85452 0.221377 1.94389 0.157237 1.02727 0.107498L1.00018 0.606764ZM9.11888 1.45414L9.04127 1.94808C10.8401 2.2307 12.6092 2.57561 14.3462 2.98346L14.4605 2.4967L14.5748 2.00993C12.8128 1.59622 11.0193 1.24658 9.19648 0.960195L9.11888 1.45414ZM19.7049 3.95031L19.5518 4.42631C21.2776 4.9812 22.9668 5.6041 24.6168 6.29577L24.8101 5.83464L25.0034 5.37351C23.3263 4.67053 21.6103 4.03776 19.8579 3.47431L19.7049 3.95031ZM29.7292 8.16143L29.495 8.60321C31.0863 9.4467 32.635 10.3618 34.1384 11.3494L34.4129 10.9315L34.6874 10.5136C33.1576 9.50864 31.5819 8.5776 29.9633 7.71966L29.7292 8.16143ZM38.8133 14.1328L38.5002 14.5226C39.1994 15.0843 39.8869 15.664 40.5623 16.2619L40.8938 15.8876L41.2252 15.5132C40.5378 14.9047 39.8381 14.3147 39.1264 13.743L38.8133 14.1328ZM40.8938 15.8876L40.5623 16.2619C41.2378 16.8599 41.8967 17.4719 42.5391 18.0978L42.888 17.7397L43.2369 17.3815C42.5831 16.7445 41.9126 16.1216 41.2252 15.5132L40.8938 15.8876ZM46.5996 21.7191L46.2181 22.0423C47.3809 23.4147 48.4772 24.8408 49.5076 26.3181L49.9177 26.032L50.3278 25.746C49.2797 24.2434 48.1644 22.7925 46.9811 21.3959L46.5996 21.7191ZM52.8242 30.6324L52.39 30.8802C53.2767 32.4341 54.1 34.0352 54.8602 35.6809L55.3142 35.4712L55.7681 35.2615C54.9961 33.5904 54.1598 31.9639 53.2585 30.3846L52.8242 30.6324ZM57.3935 40.5004L56.9242 40.6729C57.5398 42.3476 58.0968 44.0617 58.5957 45.813L59.0766 45.676L59.5574 45.539C59.052 43.7645 58.4872 42.0266 57.8628 40.3279L57.3935 40.5004ZM1.02727 0.107498C0.751531 0.0925358 0.515873 0.303934 0.50091 0.579671C0.485947 0.855408 0.697346 1.09107 0.973083 1.10603L1.00018 0.606764L1.02727 0.107498ZM60.9029 53.6319L62.7824 48.1729L57.115 49.2746L60.9029 53.6319ZM1.00018 0.606764L0.973083 1.10603C1.87904 1.15519 2.77886 1.21857 3.67229 1.29625L3.7156 0.798127L3.75891 0.300005C2.85452 0.221377 1.94389 0.157237 1.02727 0.107498L1.00018 0.606764ZM9.11888 1.45414L9.04127 1.94808C10.8401 2.2307 12.6092 2.57561 14.3462 2.98346L14.4605 2.4967L14.5748 2.00993C12.8128 1.59622 11.0193 1.24658 9.19648 0.960195L9.11888 1.45414ZM19.7049 3.95031L19.5518 4.42631C21.2776 4.9812 22.9668 5.6041 24.6168 6.29577L24.8101 5.83464L25.0034 5.37351C23.3263 4.67053 21.6103 4.03776 19.8579 3.47431L19.7049 3.95031ZM29.7292 8.16143L29.495 8.60321C31.0863 9.4467 32.635 10.3618 34.1384 11.3494L34.4129 10.9315L34.6874 10.5136C33.1576 9.50864 31.5819 8.5776 29.9633 7.71966L29.7292 8.16143ZM38.8133 14.1328L38.5002 14.5226C39.1994 15.0843 39.8869 15.664 40.5623 16.2619L40.8938 15.8876L41.2252 15.5132C40.5378 14.9047 39.8381 14.3147 39.1264 13.743L38.8133 14.1328ZM40.8938 15.8876L40.5623 16.2619C41.2378 16.8599 41.8967 17.4719 42.5391 18.0978L42.888 17.7397L43.2369 17.3815C42.5831 16.7445 41.9126 16.1216 41.2252 15.5132L40.8938 15.8876ZM46.5996 21.7191L46.2181 22.0423C47.3809 23.4147 48.4772 24.8408 49.5076 26.3181L49.9177 26.032L50.3278 25.746C49.2797 24.2434 48.1644 22.7925 46.9811 21.3959L46.5996 21.7191ZM52.8242 30.6324L52.39 30.8802C53.2767 32.4341 54.1 34.0352 54.8602 35.6809L55.3142 35.4712L55.7681 35.2615C54.9961 33.5904 54.1598 31.9639 53.2585 30.3846L52.8242 30.6324ZM57.3935 40.5004L56.9242 40.6729C57.5398 42.3476 58.0968 44.0617 58.5957 45.813L59.0766 45.676L59.5574 45.539C59.052 43.7645 58.4872 42.0266 57.8628 40.3279L57.3935 40.5004Z"
                      fill="#1B1F3B"
                    />
                  </svg>
                </div>
              ) : (
                <div className={`mt-1 absolute z-50 top-60 -left-17 w-full flex`}>
                  <svg width="62" height="56" viewBox="0 0 62 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M60.9426 0.758301C61.2169 0.726383 61.4651 0.922865 61.497 1.19716C61.529 1.47145 61.3325 1.71968 61.0582 1.7516L61.0004 1.25495L60.9426 0.758301ZM2.62389 55.9558L0.771104 50.4877L6.43304 51.6172L2.62389 55.9558ZM61.0004 1.25495L61.0582 1.7516C60.1668 1.85532 59.2815 1.97276 58.4025 2.10398L58.3287 1.60946L58.2548 1.11494C59.1447 0.982108 60.0407 0.863252 60.9426 0.758301L61.0004 1.25495ZM53.0284 2.58414L53.1357 3.07249C51.3784 3.45865 49.65 3.90387 47.9527 4.4087L47.8102 3.92945L47.6676 3.4502C49.3887 2.93829 51.1406 2.48703 52.921 2.09579L53.0284 2.58414ZM42.7079 5.66233L42.887 6.12913C41.2142 6.77113 39.5763 7.47635 37.976 8.24537L37.7594 7.7947L37.5428 7.34403C39.1682 6.56301 40.831 5.84707 42.5287 5.19552L42.7079 5.66233ZM33.0056 10.3314L33.2596 10.762C31.7248 11.6673 30.2303 12.6381 28.7787 13.675L28.488 13.2681L28.1974 12.8613C29.673 11.8072 31.1919 10.8206 32.7516 9.90069L33.0056 10.3314ZM24.2461 16.5907L24.5715 16.9703C23.897 17.5486 23.2335 18.1433 22.5814 18.7543L22.2395 18.3895L21.8976 18.0246C22.5605 17.4035 23.235 16.799 23.9207 16.2111L24.2461 16.5907ZM22.2395 18.3895L22.5814 18.7543C21.9292 19.3654 21.2927 19.9889 20.6719 20.6245L20.3142 20.2751L19.9565 19.9257C20.5877 19.2796 21.2347 18.6458 21.8976 18.0246L22.2395 18.3895ZM16.7231 24.2923L17.1103 24.6087C15.9813 25.9899 14.9155 27.4182 13.9124 28.891L13.4992 28.6096L13.0859 28.3281C14.1052 26.8315 15.1884 25.3799 16.336 23.9758L16.7231 24.2923ZM10.6592 33.1886L11.0948 33.434C10.2235 34.981 9.41339 36.5696 8.6641 38.1972L8.20992 37.9882L7.75573 37.7791C8.51616 36.1272 9.33858 34.5144 10.2235 32.9432L10.6592 33.1886ZM6.14934 42.9672L6.61833 43.1405C6.00434 44.8014 5.4478 46.4973 4.94834 48.2257L4.46799 48.0869L3.98764 47.9481C4.49368 46.1969 5.05776 44.478 5.68036 42.7938L6.14934 42.9672ZM60.9426 0.758301C61.2169 0.726383 61.4651 0.922865 61.497 1.19716C61.529 1.47145 61.3325 1.71968 61.0582 1.7516L61.0004 1.25495L60.9426 0.758301ZM2.62389 55.9558L0.771104 50.4877L6.43304 51.6172L2.62389 55.9558ZM61.0004 1.25495L61.0582 1.7516C60.1668 1.85532 59.2815 1.97276 58.4025 2.10398L58.3287 1.60946L58.2548 1.11494C59.1447 0.982108 60.0407 0.863252 60.9426 0.758301L61.0004 1.25495ZM53.0284 2.58414L53.1357 3.07249C51.3784 3.45865 49.65 3.90387 47.9527 4.4087L47.8102 3.92945L47.6676 3.4502C49.3887 2.93829 51.1406 2.48703 52.921 2.09579L53.0284 2.58414ZM42.7079 5.66233L42.887 6.12913C41.2142 6.77113 39.5763 7.47635 37.976 8.24537L37.7594 7.7947L37.5428 7.34403C39.1682 6.56301 40.831 5.84707 42.5287 5.19552L42.7079 5.66233ZM33.0056 10.3314L33.2596 10.762C31.7248 11.6673 30.2303 12.6381 28.7787 13.675L28.488 13.2681L28.1974 12.8613C29.673 11.8072 31.1919 10.8206 32.7516 9.90069L33.0056 10.3314ZM24.2461 16.5907L24.5715 16.9703C23.897 17.5486 23.2335 18.1433 22.5814 18.7543L22.2395 18.3895L21.8976 18.0246C22.5605 17.4035 23.235 16.799 23.9207 16.2111L24.2461 16.5907ZM22.2395 18.3895L22.5814 18.7543C21.9292 19.3654 21.2927 19.9889 20.6719 20.6245L20.3142 20.2751L19.9565 19.9257C20.5877 19.2796 21.2347 18.6458 21.8976 18.0246L22.2395 18.3895ZM16.7231 24.2923L17.1103 24.6087C15.9813 25.9899 14.9155 27.4182 13.9124 28.891L13.4992 28.6096L13.0859 28.3281C14.1052 26.8315 15.1884 25.3799 16.336 23.9758L16.7231 24.2923ZM10.6592 33.1886L11.0948 33.434C10.2235 34.981 9.41339 36.5696 8.6641 38.1972L8.20992 37.9882L7.75573 37.7791C8.51616 36.1272 9.33858 34.5144 10.2235 32.9432L10.6592 33.1886ZM6.14934 42.9672L6.61833 43.1405C6.00434 44.8014 5.4478 46.4973 4.94834 48.2257L4.46799 48.0869L3.98764 47.9481C4.49368 46.1969 5.05776 44.478 5.68036 42.7938L6.14934 42.9672Z"
                      fill="#1B1F3B"
                    />
                  </svg>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
