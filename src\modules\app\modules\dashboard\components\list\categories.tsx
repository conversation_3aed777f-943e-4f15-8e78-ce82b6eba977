import React from 'react';

import { Icon } from 'src';

interface CategoriesProps {
  category: string;
  icon: string;
  number: number;
  color: string;
}

export default function Categories({ category, icon, number, color }: CategoriesProps) {
  return (
    <div className="border p-4 border-[#F4F4F4] bg-[#FFF] dark:bg-darkBackgroundCard rounded-lg dark:text-white dark:border-gray-600 shadow-[0px_4px_14px_0px_rgba(195,195,195,0.08)]">
      <div className="rounded-lg flex flex-col gap-3">
        <Icon icon={icon} width="40" className={`self-start`} style={{ color: color }} />
        <p className="text-sm sm:text-xl text-darkBackgroundCard dark:text-white font-medium">Total {category}</p>
        <p className="text-2xl font-medium">{number}</p>
      </div>
    </div>
  );
}
