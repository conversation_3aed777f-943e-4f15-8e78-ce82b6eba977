// Flowbite
import { Progress } from 'flowbite-react';
import { Logo } from 'src';

export const StepperAiProgress = ({ isLoaded, loadingProgression }: any) => {
  // Themes
  const customTheme = {
    bar: 'space-x-2 rounded-full text-center font-medium leading-none text-cyan-300 text-white',
  };

  return (
    <div className={'w-full h-full flex flex-col justify-center items-center py-[100px] absolute top-0 left-0'}>
      <div>
        <Logo className="h-[200px] animate-pulse bg-transparent" icon />
      </div>
      <div className="w-[370px] self-center mt-10">
        <Progress theme={customTheme} size="lg" color="purple" labelProgress progressLabelPosition="inside" progress={loadingProgression} />
      </div>
    </div>
  );
};
