// React
import React from 'react';

// Core
import { Icon } from 'src';

interface StageProps {
  stage: any[];
  selectedStage: {
    activeStage: number;
  };
}

export const Stage = ({ stage, selectedStage }: StageProps) => {
  const handleSyles = (index: number) => {
    if (selectedStage?.activeStage > index) return 'bg-[#743AF5] text-white';
    else if (selectedStage?.activeStage === index) return 'bg-white text-[#743AF5] border border-[#743AF5]';
    else if (selectedStage?.activeStage < index) return 'bg-[#D8D8D8] text-white';
  };

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:flex lg:flex-row lg:items-center gap-x-4 gap-y-2 sm:gap-y-6 lg:gap-4 items-start">
      {stage.map((singleProgress: { label: string }, index: number, array) => (
        <React.Fragment key={singleProgress?.label}>
          <div key={singleProgress?.label} className="flex items-center gap-4">
            <p
              className={`size-8 text-[13px] flex justify-center items-center rounded-full font-semibold sm:size-10 sm:text-base ${handleSyles(
                index
              )}`}
            >
              {selectedStage?.activeStage > index ? (
                <Icon icon="material-symbols:fitbit-check-small-rounded" width="40" />
              ) : (
                <span className="thepassHthree">{index + 1}</span>
              )}
            </p>
            <p className={`thepassHfour ${selectedStage?.activeStage >= index ? 'text-[#1B1F3B]' : 'text-[#A3A7B0]'}`}>{singleProgress?.label}</p>
          </div>
          {index !== array.length - 1 && <p className="w-8 h-0.5 border border-[#CFD3D8] border-dashed hidden lg:block" />}
        </React.Fragment>
      ))}
    </div>
  );
};
