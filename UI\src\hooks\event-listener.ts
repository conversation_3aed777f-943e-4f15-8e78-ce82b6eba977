import { useRef, useEffect } from 'react';

export const useEventListener = (type: string, handler: (event: Event) => void, el: EventTarget = window) => {
  const savedHandler = useRef<(event: Event) => void | null>(null);

  useEffect(() => {
    (savedHandler.current as any) = handler;
  }, [handler]);

  useEffect(() => {
    const listener = (e: Event) => {
      if (savedHandler.current) {
        savedHandler.current(e);
      }
    };

    el.addEventListener(type, listener);

    return () => {
      el.removeEventListener(type, listener);
    };
  }, [type, el]);
};
