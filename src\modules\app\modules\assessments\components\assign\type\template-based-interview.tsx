// React
import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

import { useFetchList, RootState, setFieldValue, useAppDispatch, useAppSelector, UserData } from 'UI/src';

// Core
import {
  TestDifficulty,
  TestSeniorityLevel,
  Table,
  CategoryFieldColumn,
  SubcategoryFieldColumn,
  DurationFieldColumn,
  NumberOfQuestionFieldColumn,
  NameFieldColumn,
} from 'src';

// Components

interface TemplateBasedInterviewProps {
  formData: {
    quizId: string;
    category: any[];
    categoryName: string[];
    subCategory: any[];
    subCategoryName: string[];
    difficulty: number;
    estimationTime: number;
    skips: number;
    numberOfQuestions: number;
    setFieldValue: any; // ?
  };
  disableButtons: any;
  assessmentTemplateData: {
    assessmentTemplate: NonNullable<string | number | null | undefined> | null | undefined;
    setSelectedAssessmentTemplate: React.Dispatch<React.SetStateAction<NonNullable<string | number | null | undefined> | null | undefined>>;
  };
}

export const TemplateBasedInterview = ({ formData, disableButtons, assessmentTemplateData }: TemplateBasedInterviewProps) => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Permissions
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');
  const isAdmin = Array.isArray(userData?.role) && userData?.role.includes('admin');
  const isContentCreator = Array.isArray(userData?.role) && userData?.role.includes('content-creator');

  // State
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);

  // Hooks
  const navigate = useNavigate();
  const { type } = useParams();
  const { assessmentTemplate, setSelectedAssessmentTemplate } = assessmentTemplateData;

  const dispatch = useAppDispatch();

  // Hooks
  const initialFilters = {
    // ...(userData.trackId
    //   ? {}
    //   : {
    category: {
      label: 'Category',
      lookup: 'category',
    },
    // }),

    seniorityLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },

    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
  };
  const { ready, loading, list, count, filters, setFilters, search, pagination } = useFetchList('templates/list', {
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
    ...(type === 'interview'
      ? {
          props: {
            type: 'interview',
          },
        }
      : {}),
  });

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  useEffect(() => {
    const selectedTest: any = list.find((test: any) => test._id === assessmentTemplate);
    dispatch(setFieldValue({ path: 'quizId', value: selectedTest?._id }));
    dispatch(setFieldValue({ path: 'category', value: selectedTest?.category }));
    dispatch(setFieldValue({ path: 'categoryName', value: selectedTest?.categoryName }));
    dispatch(setFieldValue({ path: 'subCategory', value: selectedTest?.subCategory }));
    dispatch(setFieldValue({ path: 'subCategoryName', value: selectedTest?.subCategoryName }));
    dispatch(setFieldValue({ path: 'difficulty', value: selectedTest?.difficulty }));
    dispatch(setFieldValue({ path: 'estimationTime', value: selectedTest?.duration }));
    dispatch(setFieldValue({ path: 'skips', value: 0 }));
    dispatch(setFieldValue({ path: 'numberOfQuestions', value: selectedTest?.numOfQuestions }));
  }, [assessmentTemplate]);

  useEffect(() => {
    disableButtons.setDisableNextButton(!assessmentTemplate);
  }, [assessmentTemplate]);
  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Templates"
        // title={`${type ? type.charAt(0).toUpperCase() + type.slice(1) : ''} Templates`}
        searchPlaceholder="Search for template name..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          applicantName: (
            _: unknown,
            row: {
              _id: string;
              title: string;
            }
          ) => (
            <NameFieldColumn
              id={row?._id}
              name={row?.title}
              showMoreMap={showMoreMap}
              onClick={() => (isSuperAdmin || isAdmin || isContentCreator) && navigate(`/app/assessment-templates/${type}/view/${row?._id}`)}
            />
          ),
          category: (_: unknown, row: { categoryName: string[] }) => <CategoryFieldColumn categoryNameArray={row?.categoryName} />,
          subCategory: (_: unknown, row: { subCategoryName: string[] }) => <SubcategoryFieldColumn subCategoryName={row?.subCategoryName} />,
          difficulty: (_: unknown, row: { difficulty: number }) => {
            return (
              <div className="w-fit">
                <TestDifficulty difficulty={row?.difficulty} difficultyIcon />
              </div>
            );
          },
          seniortyLevel: (_: unknown, row: { seniorityLevel: number }) => {
            return (
              <div className="w-fit">
                <TestSeniorityLevel seniorityLevel={row?.seniorityLevel} />
              </div>
            );
          },
          numberOfQuestion: (_: unknown, row: { numOfQuestions: number }) => <NumberOfQuestionFieldColumn numOfQuestions={row?.numOfQuestions} />,
          duration: (_: unknown, row: { duration: number }) => <DurationFieldColumn duration={row?.duration} />,
        }}
        columns={[
          {
            key: 'applicantName',
            label: 'Name',
            primary: true,
            width: '22%',
          },
          {
            key: 'category',
            label: 'Category',
            primary: true,
            width: '20%',
            cardFooterProp: true,
          },
          {
            key: 'seniortyLevel',
            label: 'Seniorty',
            // primary: true,
            width: '15%',
            inline: true,
          },
          {
            key: 'difficulty',
            label: 'Difficulty',
            // primary: true,
            width: '15%',
            inline: true,
          },
          {
            key: 'duration',
            label: 'Duration',
            // label: type == 'interview' ? 'Estimation Time' : 'Duration',
            // primary: true,
            width: '10%',
          },
          {
            key: 'numberOfQuestion',
            label: 'No.Question',
            // primary: true,
            width: '13%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_: unknown, row: { _id: string }) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/assessment-templates/${type}/view/${row?._id}`,
                },
                ...(isSuperAdmin || isAdmin || isContentCreator
                  ? [
                      {
                        label: 'Update',
                        customIcon: 'edit',
                        iconWidth: '22',
                        iconHeight: '22',
                        color: 'text-black dark:text-white',
                        path: `/app/assessment-templates/${type}/edit/${row?._id}`,
                      },
                    ]
                  : []),
              ];
            },
          },
        ]}
        groups={[
          {
            name: 'group1',
            keys: [
              ['seniortyLevel', 'difficulty'],
              ['numberOfQuestion', 'duration'],
            ],
          },
        ]}
        singleSelectedRow={{ selectedIdSingle: assessmentTemplate, setSelectedIdSingle: setSelectedAssessmentTemplate }}
        placeholder={{
          title: 'No assessment created yet',
          subTitle: 'Start by creating a assessment to evaluate applicants skills.',
          image: '/UI/src/assets/placeholder/TestImagePlaceholder.svg',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        hideJumbotron
        isScrollableTabsExists
      />
    </>
  );
};
