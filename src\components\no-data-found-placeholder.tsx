import { Icon, CustomIcon } from '../';
import { CustomIconType } from '../../UI/src/types/CustomIcon.type';

interface NoDataFoundProps {
  noDataFound?: {
    imageSrc?: string;
    messageHeader?: string;
    icon?: string;
    message?: string;
    customIcon?: CustomIconType;
    description?: string;
  };
  size?: string;
  textIconSize?: string;
  textMessageSize?: string;
  margin?: string;
  spacing?: string;
  width?: string;
  height?: string;
}

export const NoDataFound = ({
  noDataFound,
  size = 'w-3/5',
  textIconSize = 'text-[4rem]',
  textMessageSize = 'text-[16px]',
  margin = 'm-0',
  spacing = 'space-y-1',
  width = '30',
  height = '30',
}: NoDataFoundProps) => {
  const { imageSrc, messageHeader, icon, message, customIcon, description } = noDataFound || {};

  return (
    <>
      <div className={`flex flex-col ${margin}  m-0 items-center text-center ${spacing} justify-center h-full `}>
        {imageSrc && <img src={imageSrc} alt="No Data" className={`${size} mt-2`} />}
        {icon && <Icon icon={icon} className={`${textIconSize} dark:text-gray-500 text-gray-400`} />}
        {customIcon && (
          <CustomIcon
            definedIcon={customIcon.definedIcon}
            width={`${width}`}
            height={`${height}`}
            className={`${textIconSize} mt-5 mb-2 dark:text-gray-500 text-gray-400 `}
          />
        )}
        {messageHeader && <h2 className=" dark:text-white pt-0 mt-0 text-gray-500 font-semibold ">{messageHeader}</h2>}
        {message && <p className={`${textMessageSize} text-gray-400 mt-2 font-light`}>{message}</p>}
        {description && <p className="sm:w-[80%] lg:w-[60%] text-[#566577] text-base font-normal">{description}</p>}
      </div>
    </>
  );
};
