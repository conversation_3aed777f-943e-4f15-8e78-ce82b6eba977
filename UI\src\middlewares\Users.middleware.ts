import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';
import type { UsersListItem } from '../types/User.type';

// Fetch single user
export const fetchUser = createAsyncThunk(
  'users/fetchUser',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get<UsersListItem>(`users/single/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch user');
    }
  }
);

// Create user
export const createUser = createAsyncThunk(
  'users/createUser',
  async (userData: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('users/single', userData);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create user');
    }
  }
);

// Update user
export const updateUserMiddleware = createAsyncThunk(
  'users/updateUser',
  async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
    try {
      const response = await Api.put(`users/single/${id}`, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to update user');
    }
  }
);

// Search users
export const searchUsers = createAsyncThunk(
  'users/searchUsers',
  async ({ endpoint, keyword }: { endpoint: string; keyword: string }, { rejectWithValue }) => {
    try {
      const result = await Api.get(endpoint, { keyword });
      return result.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to search users');
    }
  }
); 
