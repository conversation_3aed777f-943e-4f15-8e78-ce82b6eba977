// React
import { useEffect } from 'react';
import { FileText, MessagesSquare } from 'lucide-react';

// Core
import { Icon } from 'src';
import { setFieldValue, useAppDispatch } from 'UI/src';

export const TypeQuickAssignPage = ({ formData, disableButtons, stage }: any) => {
  // Destructuring
  const { form } = formData || {};

  // Redux
  const dispatch = useAppDispatch();

  const data = [
    {
      type: 'test',
      label: 'Test',
      description: 'Set up structured tests and evaluations',
      features: [
        'Automated scoring',
        'Fair comparisons',
        'Expert-curated and AI-generated questions',
        'Public or private test links',
        'Detailed performance & evaluation reports',
        'Cheating detection (tab switching, IP change alerts)',
        'PDF evaluation summary report',
      ],
      lucideIcon: <FileText className="size-8" strokeWidth={1} />,
    },
    {
      type: 'interview',
      label: 'Interview',
      description: 'Set up structured interviews and evaluations',
      features: [
        'Guided or interactive AI conversations',
        'Fair comparisons',
        'Expert questions or custom interview creation',
        'AI-powered scoring and evaluation',
        'Interview video recordings (one per question)',
        'Real-time avatars (default or custom) for realistic experience',
        'Full analytics: response transcripts, scores, and category breakdowns',
        'PDF and Excel reports per applicant',
      ],
      lucideIcon: <MessagesSquare className="size-8" strokeWidth={1} />,
    },
  ];

  useEffect(() => {
    if (!form.type) {
      dispatch(setFieldValue({ path: 'type' as any, value: 'test' }));
    }
  }, []);

  useEffect(() => {
    disableButtons.setDisableNextButton(!form.type);
  }, [form.type]);

  const isDisabled = stage !== 0;

  return (
    <div className="flex flex-wrap justify-center gap-6">
      <div className={`w-fit flex gap-2 p-1 rounded-full border border-gray-200 ${isDisabled ? 'pointer-events-none opacity-50' : ''}`}>
        {data.map((singleData) => (
          <div
            key={singleData?.type}
            className={`px-4 py-2 font-semibold rounded-full cursor-pointer ${
              form.type === singleData?.type ? 'bg-[#743AF5] text-white' : 'text-[#A1A1AA]'
            }`}
            onClick={() => !isDisabled && dispatch(setFieldValue({ path: 'type' as any, value: singleData?.type }))}
          >
            {singleData?.label}
          </div>
        ))}
      </div>
    </div>
  );
};
