// React
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
// Date format
import { formatDistanceToNow } from 'date-fns';

// Core
import { Jumbotron, Button, CustomIcon, ScrollableTabs, NoDataFound, CategoryFieldColumn, SubcategoryFieldColumn } from 'src';
import { AssessmentCompCard } from 'UI/src/components/assessment-comp-card';

// Components
import { CandidateProfileCard, CandidateProfileCardData } from '../components/single/candiadte-card';
import { GeneratedLinks } from '../components/single/generated-links';
import { AssignedApplicants } from '../components/single/assigned-applicants';
import { ReviewDrawer } from '../../assessments/components/create/review-drawer';
import { api, QuestionDifficulty, setErrorNotify } from 'UI';
import { QuizType, Api, useAppDispatch } from 'UI/src';

interface ApiDataTypes {
  title: string;
  difficulty: any;
  duration: number;
  numOfQuestions: number;
  authorName: string;
  createdAt: string;
  updatedAt: string;
  categoryName: string[];
  subCategoryName: string[];
  seniorityLevel: any;
}

export const AssessmentReport = () => {
  // State
  const [activeTab, setActiveTab] = useState(0);
  const [isShowReviewDrawer, setIsShowReviewDrawer] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedQuestionsID, setSelectedQuestionsID] = useState({});
  const [anyQuestionHasEditMode, setAnyQuestionHasEditMode] = useState({});
  const [apiData, setApiData] = useState<ApiDataTypes>();

  // Hooks
  const dispatch = useAppDispatch();
  const { type, id, quizId } = useParams();

  const tabs = [
    { title: 'generated links', data: <GeneratedLinks /> },
    { title: 'assigned applicants', data: <AssignedApplicants /> },
  ];

  // const handelGet = async () => {
  //   let assessment;
  //   switch (type) {
  //     case 'interview':
  //       assessment = 'quizzes/single';
  //       break;
  //     case 'screening':
  //       assessment = 'quizzes/single/phoneScreening';
  //       break;
  //     case 'test':
  //       assessment = 'quizzes/single';
  //       break;
  //     default:
  //       break;
  //   }

  //   try {
  //     setLoading(true);
  //     const response = await Api.get(`${assessment}/${id}`);
  //     // Handle different response structures
  //     const data = Array.isArray(response.data) ? response.data[0] : response.data;
  //   } catch (error) {
  //     dispatch(setErrorNotify(error?.response?.data?.message));
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  const handleGetData = async () => {
    try {
      setLoading(true);
      const response = await Api.get<QuizType>(`templates/single/${quizId}`, {});
      console.log('templates/single', response.data);
      setApiData(response.data);
      setSelectedQuestionsID(Object.fromEntries(response.data.questionIds.map((questionId) => [questionId, true])));
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setLoading(false);
    }
  };

  // Set edit mode based on id presence
  useEffect(() => {
    if (quizId) {
      // handelGet();
      handleGetData();
    }
  }, [quizId]);

  return (
    <>
      <div className="space-y-4">
        {/* Header Section */}
        <Jumbotron />

        {/* Assessment Info Card */}
        <AssessmentCompCard
          createdByName={apiData?.authorName || ''}
          // createdByDate={apiData?.createdAt || ''}
          // updatedDate={apiData?.updatedAt || ''}
          name={apiData?.title || ''}
          seniority={apiData?.seniorityLevel || ''}
          difficulty={apiData?.difficulty || ''}
          questionsNumber={apiData?.numOfQuestions || 0}
          duration={apiData?.duration || 0}
          categoryName={apiData?.categoryName?.[0] || ''}
          subCategories={apiData?.subCategoryName || []}
          onReviewQuestionsClick={() => setIsShowReviewDrawer(true)}
        />

        <ScrollableTabs
          data={tabs as any}
          selectedTab={{
            activeTab: activeTab,
            setActiveTab: setActiveTab,
          }}
          nav={{
            routePrefix: `/app/assessment-report/view/${type}/${quizId}`,
          }}
        />

        <div className="mt-5">{tabs[activeTab].data}</div>
      </div>

      {isShowReviewDrawer && (
        <ReviewDrawer
          onClose={() => setIsShowReviewDrawer(false)}
          selectedQuestionsID={selectedQuestionsID}
          setSelectedQuestionsID={setSelectedQuestionsID}
          anyQuestionHasEditMode={anyQuestionHasEditMode}
          setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
          canRemoveQuestion={false}
        />
      )}
    </>
  );
};
