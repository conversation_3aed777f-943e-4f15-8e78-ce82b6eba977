// Core
import { useEffect, useState } from 'react';
import { useAppDispatch, Api } from 'UI/src';
import { ChartsWavy } from 'src';
import { setErrorNotify } from 'UI';

export const OrganizationsGrowth = () => {
  const dispatch = useAppDispatch();
  // state
  const [growthData, setGrowthData] = useState<any[]>([]);

  // Methods
  const getMonthName = (monthNumber: number, locale = 'en-US') => {
    // Adjust month number to start from 1 (January) instead of 0
    const date = new Date(2023, monthNumber - 1, 1);
    return date.toLocaleString(locale, { month: 'short' });
  };

  const handleGet = async () => {
    try {
      const response = await Api.get('superAdmin/organizations/growth');
      setGrowthData(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  const formatChartData = () => {
    if (growthData) {
      // First, create a placeholder entry
      const result = [];

      // Then add entries for all months (1-12)
      for (let monthNumber = 1; monthNumber <= 12; monthNumber++) {
        // Find the data for this month, or use a default value
        const monthData = growthData.find((item) => item.month === monthNumber) || { month: monthNumber, count: 0 };

        result.push({
          x: getMonthName(monthData.month),
          a: monthData.count,
        });
      }

      return result;
    }
    return [];
  };

  const chartLabels = {
    a: 'count',
    b: 'string',
    c: 'string',
  };
  // effect
  useEffect(() => {
    handleGet();
  }, []);

  const chartdata = formatChartData();
  return (
    <div className="p-2">
      <div className="-ml-8">
        <ChartsWavy data={chartdata as any} dataKeys={chartLabels} colors={{ a: '#8884d8', b: '#82ca9d', c: '#ffc658' }} />
      </div>
    </div>
  );
};
