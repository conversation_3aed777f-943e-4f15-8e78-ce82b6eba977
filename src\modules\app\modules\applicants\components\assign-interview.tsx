// React
import { useState, useRef, useEffect } from 'react';

// Core
import { Radio, MultiSelect, Icon, Button, Select, TextInput, Drawer } from 'src';
import { AiAvatarModels, useValidate } from 'UI/src';

import { Form, Api, Regex } from 'UI/src';

// Flowbite
import { Label } from 'flowbite-react';

// Components
import { TimeSettingsDialog } from './assign-time-settings';
import { TestCreatedSucessfully } from './test-created-sucessfully';
import { RootState, useAppDispatch, useAppSelector } from 'UI/src';
import { setErrorNotify, setNotifyMessage } from 'UI';
import { initializeForm, setFieldValue } from 'UI/src/slices/form/form.slice';
import { Applicant } from './applicant-data';
import { useFormik } from 'formik';

interface AssignIntreviewProps {
  onClose: () => void;
  onCreate: () => void;
  applicantDetails: Applicant | null;
  refreshMainTable: () => void;
}

export const AssignIntreview: React.FC<AssignIntreviewProps> = ({ onClose, onCreate, applicantDetails, refreshMainTable = () => {} }) => {
  // Reference
  const subCategoryRef = useRef<HTMLInputElement>(null);

  // Hooks
  const dispatch = useAppDispatch();
  const { isRequired, isNumber, isValidateMaxAndMinNumber } = useValidate();

  // State
  const [isTestCreatedSucessfullyVisible, setTestCreatedSucessfullyVisibilty] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);
  const [interviewQuiz, setInterviewQuiz] = useState<string>('');
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [dueDate, setDueDate] = useState<Date>(() => {
    const result = new Date(startDate);
    result.setDate(result.getDate() + 1);
    return result;
  });
  const [extraTime, setExtraTime] = useState<number>(0);
  const [isTimeSettingsVisible, setTimeSettingsVisible] = useState<boolean>(false);
  const type: string = 'interview';

  const initialPayload = {
    technology: '',
    numberOfQuestions: '',
    yearsOfExperience: '',
    estimationTime: '',
    type: 1,
    skips: '',
    applicantId: '',
    notes: '',
    dueDate: '',
    startDate: '',
    category: applicantDetails?.track ?? null,
    subCategory: [],
    modelType: 'gpt-4.1-mini',
    avatarName: AiAvatarModels[0]?.value,
    avatarLang: '',
  };
  const formik = useFormik({
    initialValues: initialPayload,
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  const form = useAppSelector((state: RootState) => state.form.data);

  const handleCreate = async () => {
    if (form.numberOfQuestions > 30) {
      dispatch(setErrorNotify("Max questions can't exceed 30"));
    } else if (Number(form.skips) >= Number(form.numberOfQuestions)) {
      dispatch(setErrorNotify("Max skips can't exceed or equal to number of questions"));
    } else if (!form?.startDate) {
      dispatch(setErrorNotify('Please select start date'));
    } else if (!form?.dueDate) {
      dispatch(setErrorNotify('Please select due date'));
    } else {
      try {
        setLoading(true);
        const { applicantId, ...payload } = form;
        if (payload.notes == '') delete payload.notes;
        // @TODO:Handle multi applicant assignment
        if (form.applicantId) payload.applicantId = [form.applicantId];

        // Include recordInterview flag
        payload.recordInterview = !!form.recordInterview;

        const result = await Api.post('ai-interview/single', payload);
        setInterviewQuiz(result.data.quizUrl);
        setTestCreatedSucessfullyVisibilty(true);
        onCreate();
      } catch (error: any) {
        dispatch(setErrorNotify(error.response.data.message));
      } finally {
        setLoading(false);
      }
    }
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(interviewQuiz);
    dispatch(setNotifyMessage('Link copied'));
  };

  useEffect(() => {
    if (applicantDetails) {
      dispatch(setFieldValue({ path: 'applicantId', value: applicantDetails?._id }));
      dispatch(setFieldValue({ path: 'category', value: applicantDetails?.track }));
    }
  }, []);

  useEffect(() => {
    dispatch(setFieldValue({ path: 'startDate', value: startDate }));
    dispatch(setFieldValue({ path: 'dueDate', value: dueDate }));
  }, [startDate, dueDate]);

  return (
    <Drawer split onClose={onClose} className="justify-between">
      <Drawer.SingleView>
        <Drawer.Header
          headerLabel={applicantDetails?.email ? 'Assign Interview' : 'Generate Interview Link'}
          headerSubLabel={applicantDetails?.email}
          onClose={onClose}
          className="border-b border-[#E5E7EB] pb-2"
        />

        {/* @TODO: Fix this */}
        <Drawer.Body.DatePicker
          startDate={startDate}
          dueDate={dueDate}
          extraTime={extraTime}
          setExtraTime={setExtraTime as () => void}
          setTimeSettingsVisible={setTimeSettingsVisible as () => void}
          type={type}
        />

        {/* Creation Form */}
        <div className="h-full overflow-auto">
          <Form className="grid sm:grid-cols-2 gap-4 my-2" onSubmit={() => {}}>
            <Select
              validators={[]}
              label="Interview Type"
              name="type"
              value={form.type}
              disabled={loading}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'type', value }))}
              lookup="$InterviewType"
              dropIcon
              requiredLabel
              labelTooltip={
                <div className="space-y-2">
                  <p>Interactive Interview: Live interview experience with real-time questions.</p>
                  <p>Ready Questions: Static set of questions for self-paced completion.</p>
                </div>
              }
            />

            <Select
              validators={[]}
              label="Interview Model"
              name="model"
              value={form.modelType}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'modelType', value, type: String }))}
              lookup="$InterviewModel"
              dropIcon={true}
            />

            {!applicantDetails?.track && (
              <Select
                validators={[]}
                label="Category"
                name="category"
                value={form.category}
                disabled={loading}
                onChange={(newCategory: string) => {
                  subCategoryRef.current?.blur();
                  dispatch(setFieldValue({ path: 'category', value: newCategory }));
                  dispatch(setFieldValue({ path: 'subCategory', value: null }));
                }}
                lookup="category"
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon
                requiredLabel
                creationOptions={{
                  url: 'lookups/category/single',
                  fieldName: 'name',
                  validation: Regex.categorySubcategoryTopic,
                }}
              />
            )}

            <MultiSelect
              validators={[]}
              key={form.category}
              label="Subcategory"
              requiredLabel
              name="subCategory"
              placeholder="Search for subcategory"
              value={Array.isArray(form.subCategory) ? form.subCategory : []}
              onChange={(newSubCategory: string[]) => dispatch(setFieldValue({ path: 'subCategory', value: newSubCategory }))}
              disabled={!form.category || loading}
              disabledMessage="Please select category first"
              lookup="subcategory"
              params={{ categoryId: form.category }}
              creationOptions={{
                url: 'lookups/subCategory/single',
                fieldName: 'name',
                validation: Regex.categorySubcategoryTopic,
              }}
              optionValueKey="_id"
              optionLabelKey="name"
            />

            <Select
              validators={[]}
              disabled={loading}
              name="difficulty"
              label="Difficulty"
              lookup="$QuizDifficulty"
              value={form.difficulty}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'difficulty', value }))}
              dropIcon
              requiredLabel
            />

            <TextInput
              disabled={loading}
              name="numberOfQuestions"
              label="Number of Questions"
              placeholder="Number of questions"
              value={form.numberOfQuestions}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'numberOfQuestions', value }))}
              validators={[isNumber(), isRequired()]}
              requiredLabel
            />

            <TextInput
              disabled={loading}
              name="estimationTime"
              label="Estimation Time"
              placeholder="Estimation time"
              labelTooltip="Expected time for the interview in minutes."
              value={form.estimationTime}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'estimationTime', value }))}
              validators={[isNumber(), isRequired(), isValidateMaxAndMinNumber('min', 10), isValidateMaxAndMinNumber('max', 240)]}
              requiredLabel
              type="number"
              min={10}
            />

            <TextInput
              disabled={loading}
              name="skips"
              label="Max Skips"
              labelTooltip="Maximum skips allowed without affecting the score."
              placeholder="Max Skips"
              value={form.skips}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'skips', value }))}
              validators={[isNumber(), isRequired()]}
              requiredLabel
            />

            <TextInput
              validators={[]}
              label="Notes"
              name="notes"
              placeholder="e.g., Focus on advanced JavaScript topics"
              labelTooltip="Provide specific details or instructions for the interview."
              value={form.notes}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'notes', value }))}
              disabled={loading}
            />

            <Select
              name="avatarLang"
              label="Language"
              lookup={[
                { value: 'English', label: 'English' },
                { value: 'Arabic', label: 'Arabic' },
                { value: 'Turkish', label: 'Turkish' },
              ]}
              value={form.avatarLang}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'avatarLang', value }))}
              dropIcon={true}
              validators={[isRequired()]}
            />

            <div className="space-y-2">
              <Label className="text-inputLabel dark:text-inputDarkLabel">Avatar Model</Label>
              <div className="grid grid-cols-2 space-y-2 ml-1">
                {AiAvatarModels.map((model: { value: string; iconPath: string }) => (
                  <div className="flex items-center">
                    <Radio
                      label=""
                      key={model?.value}
                      name={model?.value}
                      selectionValue={model?.value}
                      value={form.avatarName}
                      onChange={(value: any) => dispatch(setFieldValue({ path: 'avatarName', value }))}
                      className="cursor-pointer"
                    />
                    <img
                      src={`/assets/models/${model?.iconPath}`}
                      alt="Icon"
                      className="size-10 rounded-full cursor-pointer"
                      onChange={() => dispatch(setFieldValue({ path: 'avatarName', value: model?.value }))}
                    />
                    <Label htmlFor={model.value} className="ml-2 text-inputLabel dark:text-inputDarkLabel capitalize cursor-pointer">
                      {model?.value}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </Form>
        </div>

        <Drawer.Footer isPaginationActive={false} paginationData={null}>
          <div className="grid grid-cols-6 w-full gap-4 items-end">
            <Button className="col-span-2" tertiary label="Cancel" onClick={onClose} />
            <Button
              loading={loading}
              disabled={
                loading ||
                !form.type ||
                !form.category ||
                !form.subCategory ||
                !form.difficulty ||
                !form.numberOfQuestions ||
                !form.estimationTime ||
                !form.skips ||
                !form.avatarLang
              }
              className="col-span-4"
              onClick={handleCreate}
              label={applicantDetails?.email ? 'Assign Interview' : 'Generate Link'}
            />
          </div>
        </Drawer.Footer>
      </Drawer.SingleView>

      {/* Interview Ceated Sucessfully */}
      {isTestCreatedSucessfullyVisible && (
        <TestCreatedSucessfully
          assignment={applicantDetails?.email ? true : false}
          defaultType="Interview"
          quizUrl={interviewQuiz}
          onClose={() => {
            applicantDetails?.email && refreshMainTable();
            onClose();
          }}
        />
      )}

      {isTimeSettingsVisible && (
        <TimeSettingsDialog
          onClose={() => setTimeSettingsVisible(false)}
          startDate={startDate}
          setStartDate={setStartDate}
          dueDate={dueDate}
          setDueDate={setDueDate}
          type={type}
        />
      )}
    </Drawer>
  );
};
