import React from 'react';

interface CheckboxProps {
  label?: string;
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
  name?: string;
  id?: string;
}

export const CheckBox: React.FC<CheckboxProps> = ({ label = '', checked = false, onChange, disabled = false, className = '', name, id }) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onChange && !disabled) {
      onChange(e.target.checked);
    }
  };

  return (
    <div className="flex items-center gap-2">
      <input
        id={String(id)}
        type="checkbox"
        name={name}
        checked={checked}
        onChange={handleChange}
        disabled={disabled}
        className={`w-5 h-5 rounded border border-[#DEE2E4] bg-white appearance-none
          hover:bg-[#F1E9FE] hover:border-[#743AF5] hover:cursor-pointer
          focus:ring-2 focus:ring-[#F1E9FE]
          checked:bg-[#743AF5] checked:border-[#743AF5]
          ${disabled ? 'cursor-not-allowed bg-white border-[#DEE2E4] text-gray-400' : ''}
          ${className}`}
      />
      {label && <label className={`text-sm text-[#112B3B] font-medium ${disabled ? 'text-[#DEE2E4] cursor-not-allowed' : ''}`}>{label}</label>}
    </div>
  );
};
