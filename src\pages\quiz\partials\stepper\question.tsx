// React
import { FC, useContext, useEffect, useState } from 'react';

// MD editor
import MDEditor from '@uiw/react-md-editor';

// Flowbite
import { Tooltip } from 'flowbite-react';

// UI
import { Icon } from 'src';

import { RootState, useAppDispatch, useAppSelector, Api } from 'UI/src';
import { setErrorNotify } from 'UI';
import { Submission } from '../onboarding/index';

// types

type SubmissionContextType = {
  submission: Submission;
};

type QuestionProps = {
  handleGetBookmark: () => void;
};

export const Question: FC<QuestionProps> = ({ handleGetBookmark }) => {
  // Context
  const { submission } = useAppSelector((state: RootState) => state.submission);

  if (!submission.stage) return null;

  // Hooks
  const dispatch = useAppDispatch();

  // State
  const [isBookMarked, setIsBookMarked] = useState(false);

  const handleGet = async () => {
    try {
      const response = await Api.get(`stages/single/${submission.stage!._id}`);
      setIsBookMarked(response.data);
    } catch (error) {
      dispatch(setErrorNotify(error instanceof Error ? error.message : String(error)));
    }
  };

  const addtobookmark = async () => {
    try {
      await Api.put(`stages/single/${submission.stage!._id}`, {});
      setIsBookMarked(!isBookMarked);
      handleGetBookmark();
    } catch (error) {
      dispatch(setErrorNotify(error instanceof Error ? error.message : String(error)));
    }
  };

  // On Mount
  useEffect(() => {
    handleGet();
  }, [submission.stage]);

  return (
    <div className="space-y-4">
      <div className="flex justify-center gap-2">
        {Array.from({ length: submission?.quiz?.questionIds?.length }).map((_, index) => (
          <div key={index} className={`w-2 h-1.5 rounded-2xl ${index < submission?.stage?.index ? 'bg-[#7E3AF2]' : 'bg-[#9B89F585]'}`} />
        ))}
      </div>

      <div className="flex justify-between items-start gap-4 py-2 my-1 rounded-lg select-none">
        <MDEditor.Markdown
          className="!bg-white text-lg !text-[#1B1F3B] font-medium dark:bg-transparent dark:border-gray-500 dark:text-gray-300"
          source={
            submission.stage.question && 'title' in submission.stage.question
              ? String((submission.stage.question as { title?: unknown }).title ?? '')
              : ''
          }
          disableCopy
        />

        <div className="flex items-center cursor-pointer">
          <Tooltip content="Bookmark" placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
            <div onClick={addtobookmark}>
              <Icon
                width="20"
                icon="bxs:bookmark"
                className={`p-1.5 rounded-lg border ${
                  isBookMarked
                    ? 'border-[#E0F3FB] dark:border-[#38383a] text-[#11ABE6] dark:text-[#E88F26]'
                    : 'border-[#eee] dark:border-[#949494] text-[#e4e5e9] dark:text-[#e4e5e9]'
                }`}
              />
            </div>
          </Tooltip>
        </div>
      </div>
    </div>
  );
};
