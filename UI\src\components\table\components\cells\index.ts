// cells-map.ts
import { <PERSON>ou<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>py<PERSON><PERSON>, <PERSON><PERSON>ell } from './cells';
import { TableAction } from './actions'

const tableCell = {
  text: { cell: TextCell, props: ['value'] as const },
  money: { cell: AmountCell, props: ['value'] as const },
  date: { cell: DateCell, props: ['value'] as const },
  statusBadge: { cell: StatusCell, props: ['status'] as const },
  copy: { cell: CopyCell, props: ['text'] as const },
  actions: { cell: TableAction, props: ['actions', 'row'] }
};

export default tableCell;