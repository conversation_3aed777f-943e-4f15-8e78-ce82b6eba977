// Core
import { useState, useEffect } from 'react';
import { Icon } from 'src';
import { useParams } from 'react-router-dom';
import { format, isValid } from 'date-fns';
import { OrganizationsPlanOverview, RootState, useAppSelector, UserData, Api, Button, Dialog } from 'UI/src';
import { useAppDispatch } from 'UI/src';
import { setErrorNotify } from 'UI';

export const CurrentPlanSummary = () => {
  // Hooks
  const { id } = useParams();

  // states
  const [plansData, setPlansData] = useState<OrganizationsPlanOverview>();
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const dispatch = useAppDispatch();

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const orgId = id ?? userData.organizationId;

  // Methods
  const handleGet = async () => {
    try {
      console.log(orgId);
      const response = await Api.get<OrganizationsPlanOverview>(`organizations/plan/overview/${orgId}`);
      console.log(`organizations/plan/overview/${orgId}`, response);
      setPlansData(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  const formatDate = (customDate: Date) => {
    const date = new Date(customDate);
    if (!isValid(date)) {
      return 'Invalid date';
    }
    // Display as DD/MM/YYYY to match the design
    return format(date, 'dd/MM/yyyy');
  };

  const handleDeactivateClick = () => {
    setShowDeactivateModal(true);
  };

  const handleConfirmDeactivate = () => {
    // TODO: Implement deactivation logic here
    console.log('Deactivating plan...');
    setShowDeactivateModal(false);
  };

  const handleCancelDeactivate = () => {
    setShowDeactivateModal(false);
  };

  // Effects

  useEffect(() => {
    handleGet();
  }, []);

  return (
    <>
      <div className="space-y-4 p-4 rounded-2xl border border-gray-200 dark:border-gray-600 shadow-md bg-white dark:bg-transparent">
        {plansData && Object.keys(plansData).length > 0 && plansData.name ? (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <p className="text-lg font-semibold text-[#2C2C2C] dark:text-white">Plan Details</p>
              <Button
                label="Deactivate"
                colorType="tertiary"
                onClick={handleDeactivateClick}
                icon={
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 64 64">
                    <path
                      fill="#be1e2d"
                      d="M62.961 33.825c-.142 16.255-13.434 29.31-29.693 29.17l-3.102-.028C13.907 62.821.856 49.528.996 33.275l.026-3.104C1.167 13.916 14.461.861 30.716 1.001l3.103.029c16.255.143 29.31 13.438 29.18 29.692l-.031 3.104"
                    />
                    <path
                      fill="#fff"
                      d="M57.677 34.11a6.57 6.57 0 0 1-6.625 6.507l-38.26-.341a6.563 6.563 0 0 1-6.504-6.625l.008-.94a6.565 6.565 0 0 1 6.623-6.507l38.26.34a6.564 6.564 0 0 1 6.505 6.625z"
                    />
                  </svg>
                }
              />
            </div>
            <div className="space-y-6">
              <div className="flex items-start justify-between gap-8">
                <div className="space-y-1">
                  <p className="text-[#566577] dark:text-gray-400 text-sm font-medium">Plan name</p>
                  <p className="text-[#2C2C2C] dark:text-white text-sm font-semibold">{plansData.name}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#566577] dark:text-gray-400 text-sm font-medium">Billing cycle</p>
                  {/* <p className="text-[#2C2C2C] dark:text-white text-sm font-semibold">{plansData.billingCycle}</p> */}
                  <p className="text-[#2C2C2C] dark:text-white text-sm font-semibold">Monthly</p>
                </div>
              </div>

              <div className="flex items-start justify-between gap-8">
                <div className="space-y-1">
                  <p className="text-[#566577] dark:text-gray-400 text-sm font-medium">Plan cost</p>
                  <p className="text-[#2C2C2C] dark:text-white text-sm font-semibold">{plansData.price}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-[#566577] dark:text-gray-400 text-sm font-medium">Expiry date</p>
                  <p className="text-[#2C2C2C] dark:text-white text-sm font-semibold">{formatDate(plansData.endDate)}</p>
                </div>
              </div>
            </div>

            {/** Usage section is currently not used */}
            {/*
          <div className="mt-6 space-y-4">
            <div className="flex justify-between items-center">
              <p className="text-[#566577] dark:text-gray-400 text-sm font-medium uppercase">Usage</p>
              <p className="text-[#2C2C2C] dark:text-white font-semibold text-sm">
                {plansData.features && plansData.usage && <>{plansData.percentageUsageOverall}% Used</>}
              </p>
            </div>
            {plansData.usageWarning && <p className="text-[#D71111] font-semibold">{plansData.usageWarning}</p>}
          </div>
          */}

            {/** Progress usage bar is disabled */}
            {false && plansData?.features && plansData?.usage && (
              <div className="w-full h-6 bg-[#EAEAEA] rounded-lg overflow-hidden">
                <div className="h-full bg-[#986CFA]" style={{ width: `${plansData?.percentageUsageOverall}%` }} />
              </div>
            )}

            <div className="mt-4 border-t pt-3 flex justify-between text-sm">
              <p className="font-semibold text-[#2C2C2C] dark:text-white">Extra Credit</p>
              <p className="text-gray-500 dark:text-gray-400">No available extra credit</p>
            </div>
          </div>
        ) : (
          <div className={`h-[219px] flex flex-col items-center justify-center text-center`}>
            <Icon icon="iconoir:warning-circle" className="dark:text-gray-500 text-gray-400" width="48" />
            <p className={`text-gray-400 mt-2`}>No current plan summary found</p>
          </div>
        )}
      </div>

      {/* Deactivate Confirmation Dialog */}
      <Dialog isOpen={showDeactivateModal} onClose={handleCancelDeactivate} title="" subtitle="" size="md">
        <div className="text-center space-y-4">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">Deactivate Plan</h3>
          <p className="text-gray-600 dark:text-gray-300 text-base leading-relaxed">
            Are you sure you want to deactivate this plan?
            <br />
            This action cannot be undone.
          </p>

          <div className="flex gap-3 pt-4">
            <Button label="Cancel" colorType="secondary" onClick={handleCancelDeactivate} className="flex-1" variant="lg" />
            <Button label="Deactivate" colorType="destructive" onClick={handleConfirmDeactivate} className="flex-1" variant="lg" />
          </div>
        </div>
      </Dialog>
    </>
  );
};
