// React
import { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { RootState, useAppSelector, UserData } from 'UI/src';

type childrenType = {
  children: ReactNode;
};

export const ProtectedRoute = ({ children }: childrenType) => {
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);
  if (!userData?.access_token) return <Navigate to={'/'} replace />;
  return children;
};
