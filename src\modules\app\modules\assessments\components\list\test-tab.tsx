// React
import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { CopyPlus } from 'lucide-react';

import { RootState, useAppSelector, UserData, useFetchList, useScreenSize, useAppDispatch, Tags, Api, showConfirm, hideConfirm } from 'UI/src';
import { setNotifyMessage, setErrorNotify, useUserPermissions, UserPermissions } from 'UI';

// Components
import { CreateTemplateDialog } from '../create/create-template-dialog';

// Core
import {
  Table,
  NameFieldColumn,
  CategoryFieldColumn,
  SubcategoryFieldColumn,
  NumberOfQuestionFieldColumn,
  DurationFieldColumn,
  Jumbotron,
  CustomIcon,
  Icon,
} from 'src';
import { Button } from 'UI/src';

// Flowbite
import { Dropdown, DropdownItem } from 'flowbite-react';

interface onLoadingChangeProps {
  onLoadingChange: (value: boolean) => void;
  createTemplateDialog?: any;
}

export const TestTab = ({ onLoadingChange, createTemplateDialog }: onLoadingChangeProps) => {
  const dispatch = useAppDispatch();
  const { hasPermission } = useUserPermissions();

  // Destructuring
  const { isCreateTemplateVisible, setCreateTemplateVisibilty } = createTemplateDialog || {};

  // Context
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Permissions
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');
  const isAdmin = Array.isArray(userData?.role) && userData?.role.includes('admin');
  const isContentCreator = Array.isArray(userData?.role) && userData?.role.includes('content-creator');
  const isHr = Array.isArray(userData?.role) && userData?.role.includes('hr');

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState<boolean | undefined>();
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);

  // Hooks
  const navigate = useNavigate();
  const screen = useScreenSize();
  const { type } = useParams();
  const initialFilters = {
    // ...(userData.trackId
    //   ? {}
    //   : {
    category: {
      label: 'Category',
      lookup: 'category',
    },
    // }),
    seniorityLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },

    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },
  };
  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh, handleDates } = useFetchList(
    'templates/list?type=test',
    {
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
      // type: 'test',
    }
  );

  const filterFeedData = Object.keys(initialFilters).map((key) => (key === 'difficulty' ? initialFilters.difficulty.enum : key));

  /*
  const { notify } = useNotify();
  // Methods
  const handleDeleteAssessment = async (id: string) => {
    showConfirm(ConfirmText('delete'), {
      confirmText: 'Yes',
      cancelText: 'No',
      onConfirm: async () => {
        try {
          await Api.delete(`quizzes/single/${id}`);
          refresh();
          hideConfirm();
          dispatch(setNotifyMessage('Deleted successfully!'));
        } catch (error: any) {
          dispatch(setErrorNotify(error.response?.data?.message));
        }
      },
    });
  };
  const ConfirmText = (action = 'delete', templateName: string = '') => {
    let icon = 'hugeicons:archive-02';
    let iconBg = 'bg-[#ddd1f8]';
    let iconInnerBg = 'bg-[#cab6f5]';
    let iconColor = 'text-[#9061F9]';
    let message = `Are you sure you want to delete ${templateName ? '"' + templateName + '"' : 'this template'}?`;
    if (action === 'duplicate') {
      icon = 'lucide:copy-plus';
      iconBg = 'bg-[#ddd1f8]';
      iconInnerBg = 'bg-[#cab6f5]';
      iconColor = 'text-[#9061F9]';
      message = 'Are you sure you want to duplicate this template?';
    }
    return (
      <div className="text-center">
        <div className={`flex mx-auto p-4 mb-7 ${iconBg} w-24 h-24 rounded-full`}>
          <div className={`flex mx-auto mb-7 ${iconInnerBg} w-16 h-16 justify-center rounded-full`}>
            <Icon icon={icon} className={iconColor} width="40" />
          </div>
        </div>
        <p>{message}</p>
      </div>
    );
  };
  */
  const ConfirmText = (action = 'delete', templateName: string = '') => {
    let icon = 'hugeicons:archive-02';
    let iconBg = 'bg-[#ddd1f8]';
    let iconInnerBg = 'bg-[#cab6f5]';
    let iconColor = 'text-[#9061F9]';
    let message = `Are you sure you want to delete ${templateName ? '"' + templateName + '"' : 'this template'}?`;
    if (action === 'duplicate') {
      icon = 'lucide:copy-plus';
      iconBg = 'bg-[#ddd1f8]';
      iconInnerBg = 'bg-[#cab6f5]';
      iconColor = 'text-[#9061F9]';
      message = 'Are you sure you want to duplicate this template?';
    }
    console.log(templateName);
    return (
      <div className="text-center">
        <div className={`flex mx-auto p-4 mb-7 ${iconBg} w-24 h-24 rounded-full`}>
          <div className={`flex mx-auto mb-7 ${iconInnerBg} w-16 h-16 justify-center rounded-full`}>
            <Icon icon={icon} className={iconColor} width="40" />
          </div>
        </div>
        <p>{message}</p>
      </div>
    );
  };

  const handleDeleteAssessment = async (id: string, title?: string) => {
    dispatch(
      showConfirm({
        message: ConfirmText('delete', title || ''),
        options: {
          confirmLabel: 'Delete',
          cancelLabel: 'Cancel',
          onConfirm: async () => {
            try {
              await Api.delete(`templates/single/${id}`);
              refresh();
              dispatch(setNotifyMessage('Deleted successfully!'));
            } catch (error: any) {
              dispatch(setErrorNotify(error.response?.data?.message));
            } finally {
              dispatch(hideConfirm());
            }
          },
        },
      })
    );
  };
  const dublicateRowAssessment = async (id: string) => {
    /* FIXME: Add confirmDialog */
    // showConfirm(ConfirmText('duplicate'), {
    //   confirmText: 'Yes',
    //   cancelText: 'No',
    //   onConfirm: async () => {
    try {
      await Api.get(`templates/duplicate/${id}`);
      refresh();
      // hideConfirm();
      dispatch(setNotifyMessage('Successfully duplicated!'));
    } catch (error: any) {
      dispatch(setErrorNotify(error.response?.data?.message));
    }
    //   },
    // });
  };

  useEffect(() => {
    if (backupList.length === 0 && list.length > 0) {
      setBackupList(list);
    }
  }, [list, backupList.length]);

  // Notify parent component about loading state changes
  useEffect(() => {
    if (onLoadingChange) {
      onLoadingChange(loading || !ready);
    }
  }, [loading, ready, onLoadingChange]);

  const actions = {
    label: 'Create Template',
    dropdownlist: [
      {
        label: 'Manual Creation',
        customIcon: 'magicStick',
        onClick: () => navigate(`/app/assessment-templates/${type}/create`),
      },
      {
        label: 'AI Creation',
        customIcon: 'mind',
        onClick: () => setCreateTemplateVisibilty('AI'),
      },
    ],
  };

  return (
    <>
      <div className="space-y-2">
        <Table
          addButtonPermission={false}
          ready={ready}
          loading={loading}
          title="Templates"
          searchPlaceholder="Search for template name..."
          count={count}
          search={search}
          filters={filters}
          setFilters={setFilters}
          pagination={pagination}
          rows={list}
          backupRows={backupList}
          slots={{
            applicantName: (_: unknown, row: { _id: string; title: string }) => (
              <NameFieldColumn
                id={row?._id}
                name={row?.title}
                showMoreMap={showMoreMap as any}
                onClick={() => (isSuperAdmin || isAdmin || isContentCreator) && navigate(`/app/assessment-templates/${type}/view/${row?._id}`)}
              />
            ),
            category: (_: unknown, row: { categoryName: string[] }) => <CategoryFieldColumn categoryNameArray={row?.categoryName} />,
            subCategory: (_: unknown, row: { subCategoryName: string[] }) => <SubcategoryFieldColumn subCategoryName={row?.subCategoryName} />,
            difficulty: (_: unknown, row: { difficulty: number }) => {
              const getDifficultyText = (level: number): string => {
                switch (level) {
                  case 1:
                    return 'easy';
                  case 2:
                    return 'medium';
                  case 3:
                    return 'hard';
                  default:
                    return 'easy';
                }
              };

              const getDifficultyColor = (level: number): string => {
                switch (level) {
                  case 1:
                    return 'bg-[#EEFFF1] text-[#056816]';
                  case 2:
                    return 'bg-[#FFFCDF] text-[#BA8500]';
                  case 3:
                    return 'bg-[#FFECE9] text-[#A80000]';
                  default:
                    return 'bg-[#EEFFF1] text-[#056816]';
                }
              };

              return (
                <div className="w-fit">
                  <Tags type={getDifficultyText(row?.difficulty)} color={getDifficultyColor(row?.difficulty)} />
                </div>
              );
            },
            seniortyLevel: (_: unknown, row: { seniorityLevel: number }) => {
              const getSeniorityLevelText = (level: number): string => {
                switch (level) {
                  case 1:
                    return 'intern';
                  case 2:
                    return 'fresh';
                  case 3:
                    return 'junior';
                  case 4:
                    return 'mid-level';
                  case 5:
                    return 'senior';
                  default:
                    return 'intern';
                }
              };

              return (
                <div className="w-fit">
                  <Tags type={getSeniorityLevelText(row?.seniorityLevel)} color="bg-transparent" />
                </div>
              );
            },
            numberOfQuestion: (_: unknown, row: { numOfQuestions: number }) => <NumberOfQuestionFieldColumn numOfQuestions={row?.numOfQuestions} />,
            duration: (_: unknown, row: { duration: number }) => <DurationFieldColumn duration={row?.duration} />,
          }}
          columns={[
            {
              key: 'applicantName',
              label: 'Name',
              primary: true,
              width: '22%',
            },
            // {
            //   key: 'description',
            //   label: 'Description',
            //   primary: true,
            //   width: '23%',
            // },
            {
              key: 'category',
              label: 'Category',
              primary: true,
              width: '20%',
            },
            // {
            //   key: 'subCategory',
            //   label: 'Sub Category',
            //   primary: true,
            //   width: '20%',
            // },
            {
              key: 'seniortyLevel',
              label: 'Seniorty Level',
              // primary: true,
              width: '15%',
            },
            {
              key: 'difficulty',
              label: 'Difficulty',
              // primary: true,
              width: '15%',
            },
            {
              key: 'numberOfQuestion',
              label: 'No.Question',
              // primary: true,
              width: '8%',
              inline: true,
            },
            {
              key: 'duration',
              label: 'Duration',
              // primary: true,
              width: '8%',
              inline: true,
            },
            {
              key: 'actions',
              label: 'Actions',
              width: '20%',
              buttons(_: unknown, row: { _id: string }) {
                return [
                  {
                    label: 'View',
                    customIcon: 'eye',
                    iconWidth: '22',
                    iconHeight: '22',
                    color: 'text-black dark:text-white',
                    path: `/app/assessment-templates/${type}/view/${row?._id}`,
                  },
                  ...((isSuperAdmin || isAdmin || isContentCreator) && hasPermission(UserPermissions.UPDATE_ASSESSMENT)
                    ? [
                        {
                          label: 'Update',
                          customIcon: 'edit',
                          iconWidth: '22',
                          iconHeight: '22',
                          color: 'text-black dark:text-white',
                          path: `/app/assessment-templates/${type}/edit/${row?._id}`,
                        },
                      ]
                    : []),
                  ...(hasPermission(UserPermissions.ASSIGN_ASSESSMENT)
                    ? [
                        {
                          label: 'Assign',
                          customIcon: 'assign',
                          iconWidth: '22',
                          iconHeight: '22',
                          color: 'text-black dark:text-white',
                          path: `/app/assessment-templates/${type}/assign/${row?._id}`,
                        },
                      ]
                    : []),
                  ...(hasPermission(UserPermissions.ARCHIVE_ASSESSMENT)
                    ? [
                        {
                          label: 'Archive',
                          customIcon: 'archive',
                          iconWidth: '22',
                          iconHeight: '22',
                          color: 'text-gray-700 font-normal text-[14px] dark:text-white',
                          // path: `/app/assessment-templates/${type}/assign/${row?._id}`,
                          onClick: () => handleDeleteAssessment(row?._id, (row as any)?.title),
                        },
                      ]
                    : []),
                  ...(hasPermission(UserPermissions.CREATE_ASSESSMENT)
                    ? [
                        {
                          label: 'Duplicate',
                          lucideIcon: 'CopyPlus',
                          iconWidth: '22',
                          iconHeight: '22',
                          color: 'text-gray-700 font-normal text-[14px] dark:text-white',
                          // path: `/app/assessment-templates/${type}/assign/${row?._id}`,
                          onClick: () => dublicateRowAssessment(row?._id),
                        },
                      ]
                    : []),
                ];
              },
            },
          ]}
          placeholder={{
            title: 'No assessment created yet',
            subTitle: 'Start by creating a assessment to evaluate applicant`s skills.',
            image: '/UI/src/assets/placeholder/TestImagePlaceholder.svg',
          }}
          noDataFoundIconWidth="60"
          noDataFoundIconHeight="60"
          showMoreMap={showMoreMap}
          setShowMoreMap={setShowMoreMap}
        />
      </div>

      {isCreateTemplateVisible && (
        <CreateTemplateDialog isCreateTemplateVisible={isCreateTemplateVisible} onClose={() => setCreateTemplateVisibilty(false)} refresh={refresh} />
      )}
    </>
  );
};
