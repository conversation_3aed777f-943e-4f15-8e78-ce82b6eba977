import { forwardRef, useEffect, useState } from 'react';
import { Label, TextInput as Input, ListGroup } from 'flowbite-react';
import ClickAwayListener from 'react-click-away-listener';
import { asField } from '../../../../../components/form/hocs/field';
import { Icon, EnumText } from 'src';
import { QuestionDifficulty, useLookups } from 'UI/src';

// Types

type OptionType = {
  [key: string]: any;
  subCategory?: { subCategoryName?: string };
  topic?: { topicName?: string };
  difficulty?: number;
};

type SelectQuestionsProps = {
  name: string;
  label?: string;
  placeholder?: string;
  value?: string;
  disabled?: boolean;
  onChange: (value: any) => void;
  onSearch?: (keyword: string) => void;
  filterOnly?: boolean;
  optionLabelKey?: string;
  optionValueKey?: string;
  dropIcon?: boolean;
  isCustomValue?: boolean;
  multiSelect?: boolean;
  singelSelect?: boolean;
  lookup?: string;
  params?: any;
  cached?: boolean;
  errorMessage?: string;
  validatorsScroll?: boolean;
  customAddIconApplicant?: boolean;
};

export const SelectQuestions = asField(
  forwardRef<HTMLInputElement, SelectQuestionsProps>(
    (
      {
        name,
        label = '',
        //  placeholder = 'Choose question ...',
        placeholder = '',
        value = '',
        disabled,
        onChange,
        onSearch,
        filterOnly,
        optionLabelKey,
        optionValueKey,
        dropIcon,
        isCustomValue = false,
        multiSelect,
        singelSelect,
        lookup,
        params,
        cached = false,
        errorMessage,
        validatorsScroll,
        customAddIconApplicant,
      },
      ref
    ) => {
      const [keyword, setKeyword] = useState<string>('');
      const [listVisibility, setListVisibility] = useState<boolean>(false);
      const { lookups, loading } = useLookups(lookup, { cached, params });
      const lookupsArr = lookups as any[];
      const customTheme = {
        field: {
          input: {
            colors: {
              gray: 'block w-full border disabled:cursor-not-allowed disabled:opacity-50 border-gray-300 bg-gray-50 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 p-2.5 text-sm rounded-lg focus:ring-0 focus:border-gray-300',
              failure:
                'border-red-500 bg-white-500 text-gray-900 dark:text-white placeholder-gray-400 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-[#374151] dark:focus:border-red-500 dark:focus:ring-red-500',
            },
          },
        },
      };

      const filteredLookup = (): OptionType[] => {
        if (onSearch) {
          return lookupsArr;
        }
        return lookupsArr.filter((option: any) => option[optionLabelKey as string]?.toLowerCase().includes(keyword?.toLowerCase()));
      };
      const isListVisible = (): boolean => {
        return listVisibility && filteredLookup().length > 0;
      };

      // Actions
      const updateInput = (updatedValue = value) => {
        const selected = lookupsArr.find((option: any) => option[optionValueKey as string] === updatedValue);
        if (selected) {
          setKeyword(selected?.[optionLabelKey as string]);
        } else if (!value && keyword && !isCustomValue) {
          setKeyword('');
        }
      };
      const handleSelect = (selectedValue: any) => () => {
        onChange(selectedValue);
        setListVisibility(false);
        setKeyword('');
      };
      const handleReset = () => {
        onChange('');
        setKeyword('');
        setListVisibility(true);
        if (onSearch) {
          onSearch('');
        }
      };
      const handleClickAway = () => {
        setListVisibility(false);
        updateInput();
        setKeyword('');
      };
      const handleUpdateKeyword = (e: React.ChangeEvent<HTMLInputElement>) => {
        const keywordValue = e.target.value;
        setKeyword(keywordValue);
        if (onSearch) {
          if (multiSelect) {
            onSearch(keywordValue);
          } else {
            onChange('');
            onSearch(keywordValue);
          }
        }
      };
      const handleHelperText = () => {
        if (errorMessage) return errorMessage;
        else if (customAddIconApplicant) return 'This field pattern is invalid';
        else return undefined;
      };
      // Lifecycle
      useEffect(() => {
        if (value) {
          updateInput();
        }
      }, [value, lookups]);

      return (
        <div className="relative">
          <div className="mb-2 block">
            <Label htmlFor={name} value={label} />
          </div>
          <ClickAwayListener onClickAway={handleClickAway}>
            <div>
              <div className="relative">
                <Input
                  ref={ref}
                  theme={customTheme}
                  id={name}
                  placeholder={placeholder}
                  value={keyword}
                  onInput={handleUpdateKeyword}
                  autoComplete="off"
                  onFocus={(e) => {
                    if (singelSelect) {
                      handleReset();
                    } else {
                      setListVisibility(true);
                      handleUpdateKeyword(e as any);
                      setKeyword('');
                    }
                  }}
                  disabled={disabled}
                  color={errorMessage || customAddIconApplicant ? 'failure' : 'gray'}
                  helperText={handleHelperText()}
                  {...({
                    scrolltoerror:
                      validatorsScroll && errorMessage && document.getElementById(name)?.scrollIntoView({ behavior: 'smooth', block: 'end' }),
                  } as any)}
                />
                {(!!value || dropIcon) && !disabled && !filterOnly && (
                  <button className="absolute right-3 top-3" onClick={handleReset} type="button">
                    <Icon icon={!dropIcon ? 'ic:baseline-clear' : 'charm:chevron-down'} />
                  </button>
                )}
              </div>
              {isListVisible() && !loading && (
                <ListGroup className="absolute left-0 right-0 z-10 overflow-y-auto max-h-[200px]">
                  <ListGroup.Item active className="hidden lg:block">
                    <div className="w-full text-left lg:grid grid-cols-11 space-x-4 py-2 cursor-auto">
                      <p className="truncate px-1 col-span-3">Question</p>
                      <p className="truncate px-1 col-span-3">Sub Category</p>
                      <p className="truncate px-1 col-span-3">Topic</p>
                      <p className="truncate px-1 col-span-2">Difficulty</p>
                    </div>
                  </ListGroup.Item>
                  {filteredLookup().map((option: any) => (
                    <ListGroup.Item key={option[optionValueKey as string]} onClick={handleSelect(option[optionValueKey as string])}>
                      <div className="w-full text-left lg:grid grid-cols-11 space-x-4">
                        <p className="truncate px-1 col-span-3">{option[optionLabelKey as string]}</p>
                        <p className="truncate px-1 col-span-3 hidden lg:block">{option.subCategory?.subCategoryName}</p>
                        <p className="truncate px-1 col-span-3 hidden lg:block">{option.topic?.topicName}</p>
                        <p className="truncate px-1 col-span-2 hidden lg:block">
                          {/* <EnumText name={'QuestionDifficulty'} value={option?.difficulty ?? 0} /> */}
                          {QuestionDifficulty[option?.difficulty ?? 0]}
                        </p>
                      </div>
                    </ListGroup.Item>
                  ))}
                </ListGroup>
              )}
            </div>
          </ClickAwayListener>
        </div>
      );
    }
  )
);
