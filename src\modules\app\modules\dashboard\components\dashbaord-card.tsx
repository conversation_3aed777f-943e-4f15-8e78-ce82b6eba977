import React from 'react';

interface DashboardCardProps {
  children?: any;
  className?: string;
}

export const DashboardCard = ({ children, className, ...props }: DashboardCardProps) => (
  <div
    className={`max-w p-4 bg-white border border-gray-200 rounded-lg dark:bg-darkBackgroundCard dark:border-[#374151] shadow-[0px_4px_14px_0px_rgba(195,195,195,0.08)] ${className}`}
    {...props}
  >
    {children}
  </div>
);
