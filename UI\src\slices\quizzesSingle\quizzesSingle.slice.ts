import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { type RootState } from '../../store';

interface QuestionDB {
  _id: string;
  [key: string]: any;
}

interface IsAnyQuestionHasEditMode {
  [key: string]: boolean;
}

interface QuizzesSingleState {
  // Selection mode states
  isManualSelection: boolean;
  isAutomateSelection: boolean;
  
  // Data states
  questionDatabase: QuestionDB[];
  questionsList: QuestionDB[];
  selectedQuestionIds: string[];
  
  // UI states
  currentPage: number;
  isAnyQuestionHasEditMode: IsAnyQuestionHasEditMode;
}

const initialState: QuizzesSingleState = {
  // Selection mode states
  isManualSelection: false,
  isAutomateSelection: false,
  
  // Data states
  questionDatabase: [],
  questionsList: [],
  selectedQuestionIds: [],
  
  // UI states
  currentPage: 1,
  isAnyQuestionHasEditMode: {},
};

const quizzesSingleSlice = createSlice({
  name: 'quizzesSingle',
  initialState,
  reducers: {
    // Selection mode actions
    setQuizzesManualSelection: (state, action: PayloadAction<boolean>) => {
      state.isManualSelection = action.payload;
    },
    setQuizzesAutomateSelection: (state, action: PayloadAction<boolean>) => {
      state.isAutomateSelection = action.payload;
    },
    
    // Data actions
    setQuizzesQuestionDatabase: (state, action: PayloadAction<QuestionDB[]>) => {
      state.questionDatabase = action.payload;
    },
    setQuizzesQuestionsList: (state, action: PayloadAction<QuestionDB[]>) => {
      state.questionsList = action.payload;
    },
    setQuizzesSelectedQuestionIds: (state, action: PayloadAction<string[]>) => {
      state.selectedQuestionIds = action.payload;
    },
    addQuizzesSelectedQuestionId: (state, action: PayloadAction<string>) => {
      if (!state.selectedQuestionIds.includes(action.payload)) {
        state.selectedQuestionIds.push(action.payload);
      }
    },
    removeQuizzesSelectedQuestionId: (state, action: PayloadAction<string>) => {
      state.selectedQuestionIds = state.selectedQuestionIds.filter(id => id !== action.payload);
    },
    
    // UI actions
    setQuizzesCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setQuizzesAnyQuestionHasEditMode: (state, action: PayloadAction<IsAnyQuestionHasEditMode>) => {
      state.isAnyQuestionHasEditMode = action.payload;
    },
    updateQuizzesQuestionEditMode: (state, action: PayloadAction<{ questionId: string; editMode: boolean }>) => {
      state.isAnyQuestionHasEditMode[action.payload.questionId] = action.payload.editMode;
    },
    
    // Combined actions
    resetQuizzesSelectionStates: (state) => {
      state.isManualSelection = false;
      state.isAutomateSelection = false;
    },
    clearQuizzesQuestionData: (state) => {
      state.questionDatabase = [];
      state.questionsList = [];
      state.selectedQuestionIds = [];
      state.currentPage = 1;
    },
    
    // Reset actions
    resetQuizzesSingleState: (state) => {
      return initialState;
    },
  },
});

export const {
  setQuizzesManualSelection,
  setQuizzesAutomateSelection,
  setQuizzesQuestionDatabase,
  setQuizzesQuestionsList,
  setQuizzesSelectedQuestionIds,
  addQuizzesSelectedQuestionId,
  removeQuizzesSelectedQuestionId,
  setQuizzesCurrentPage,
  setQuizzesAnyQuestionHasEditMode,
  updateQuizzesQuestionEditMode,
  resetQuizzesSelectionStates,
  clearQuizzesQuestionData,
  resetQuizzesSingleState,
} = quizzesSingleSlice.actions;

export const quizzesSingleState = (state: RootState) => state.quizzesSingle;
export default quizzesSingleSlice.reducer;
