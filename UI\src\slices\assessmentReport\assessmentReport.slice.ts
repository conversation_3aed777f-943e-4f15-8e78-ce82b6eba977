// React
import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

export const assessmentReportSlice = createSlice({
  name: 'assessmentReport',
  initialState: { backupListTest: 0, backupListInterview: 0 },
  reducers: {
    setBackupListTest: (state, action: PayloadAction<number>) => {
      state.backupListTest = action.payload;
    },
    setBackupListInterview: (state, action: PayloadAction<number>) => {
      state.backupListInterview = action.payload;
    },
  },
});

export default assessmentReportSlice.reducer;
export const { setBackupListTest, setBackupListInterview } = assessmentReportSlice.actions;
