// Core
import { useEffect, useState } from 'react';
import { Button } from 'UI';
import { Dropdown, DropdownItem } from 'flowbite-react';

import { addDays, format, isValid } from 'date-fns';

import {
  OrganizationsPlanOverview,
  Api,
  useFetchList,
  RootState,
  useAppSelector,
  UserData,
  UsersListItem,
  BillingCycle,
  useValidate,
  showConfirm,
  hideConfirm,
  setErrorNotify,
  setNotifyMessage,
  useAppDispatch,
  initializeForm,
  setFieldValue,
  addCard,
  updateCard,
  deleteCard,
  fetchCards,
  fetchCardById,
  setDefaultCard,
} from 'UI/src';

// Core
import {
  Jumbotron,
  Table,
  SubscriptionPlanPeriod,
  FormatDateFieldColumn,
  NameFieldColumn,
  PaymentStatus,
  CurrencySymbol,
  BasePlans,
  CustomIcon,
  Icon,
  TextInput,
  Checkbox,
} from 'src';
import { Dialog } from 'UI/src';

// Components
import { AddQuotaDialog } from '../components/add-quota-dialog';

const formatFeatureName = (name: string) => {
  const spacedName = name.replace(/([A-Z])/g, ' $1').trim();
  return `${spacedName.charAt(0).toUpperCase()}${spacedName.slice(1)} Credits`;
};

const formatDate = (customDate: any) => {
  const date = new Date(customDate || Date.now());

  if (!isValid(date)) {
    return 'Invalid date';
  }

  return format(date, 'dd MMMM , yyyy');
};

const ConfirmText = (action = '', card: any = null) => {
  let icon = 'material-symbols:question-mark-rounded';

  let iconBg = 'bg-[#ddd1f8]';

  let iconInnerBg = 'bg-[#cab6f5]';

  let iconColor = 'text-[#9061F9]';

  let message = 'Change Default Card';

  let submessage = 'Are you sure you want to make this card as your default payment method? ';

  if (action === 'remove card') {
    icon = 'lucide:trash-2';

    iconBg = 'bg-[#FEE2E2]';

    iconInnerBg = 'bg-[#FECACA]';

    iconColor = 'text-[#DC2626]';

    message = 'Delete Payment Method';

    submessage = 'Are you sure you want to delete this payment method? This action cannot be undone.';
  }

  return (
    <div className="text-center">
      <div className={`flex mx-auto p-4 mb-7 ${iconBg} w-24 h-24 rounded-full`}>
        <div className={`flex mx-auto mb-7 ${iconInnerBg} w-16 h-16 justify-center rounded-full`}>
          <Icon icon={icon} className={iconColor} width="40" />
        </div>
      </div>

      <p className="text-xl font-semibold mb-2">{message}</p>

      <p className="text-[#626262] font-normal text-base">{submessage}</p>

      {card && (
        <div className="w-full border rounded-lg flex items-center gap-3 px-4 py-3 mb-2 mt-4">
          {card.type === 'visa' && <img src={'/src/images/payment/visa.svg'} alt="visa" className="w-10 h-7" />}

          {card.type === 'mastercard' && <img src={'/src/images/payment/mastercard.svg'} alt="mastercard" className="w-10 h-7" />}

          <div className="flex flex-col">
            <span className="font-medium text-base">{card.number}</span>

            <span className="text-sm text-gray-500">Exp {card.exp}</span>
          </div>
        </div>
      )}

      {action === 'remove card' && card?.isDefault && (
        <div className="w-full bg-[#FFF8E8] border border-[#DDD1A8] rounded-lg p-3 mt-4">
          <div className="flex items-center gap-2">
            <span className="text-[#896C31] text-sm font-semibold ">
              Warning :{' '}
              <span className="ml-0.5 text-sm font-medium">
                This is your default payment method. If you delete it, another card will be set as default automatically.
              </span>
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export const PlansListPage = () => {
  // State
  const [showMoreMap, setShowMoreMap] = useState<Record<string, boolean>>({});
  const [backupList, setBackupList] = useState<any[]>([]);
  const [isPlansVisible, setPlansVisibilty] = useState(false);
  const [planData, setPlanData] = useState<OrganizationsPlanOverview>();
  const [creditsData, setCreditsData] = useState<any[]>([]);
  const [isAddQuotaVisible, setAddQuotaVisibility] = useState(false);
  const [isRenewalDialogOpen, setRenewalDialogOpen] = useState(false);
  const [renewalType, setRenewalType] = useState('automatic');
  const [isPaymentMehodsVisible, setPaymentMehodsVisibility] = useState(false);

  const [isAddCardDialogOpen, setAddCardDialogOpen] = useState(false);

  const [isDefaultCard, setIsDefaultCard] = useState(false);

  // Show all credits dialog
  const [isCreditsDialogOpen, setCreditsDialogOpen] = useState(false);

  const [selectedCardForEdit, setSelectedCardForEdit] = useState<any>(null);
  const [isLoadingCardData, setIsLoadingCardData] = useState(false);

  // Local cards state for form operations
  const [localCards, setLocalCards] = useState<any[]>([]);

  // Additional states for payment form
  const [saveCardInfo, setSaveCardInfo] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('monthly');

  const dispatch = useAppDispatch();

  // Form for add card dialog - using Redux form state
  const form = useAppSelector((state: RootState) => state.form.data);

  // Validation hooks
  const { isRequired, validateRegex, minLength, maxLength } = useValidate();

  useEffect(() => {
    if (isAddCardDialogOpen && selectedCardForEdit) {
      // Fetch card data from API using _id
      const fetchCardData = async () => {
        setIsLoadingCardData(true);
        try {
          const cardData = await dispatch(fetchCardById(selectedCardForEdit._id || selectedCardForEdit.id)).unwrap();

          // Format the data for the form
          const formattedDate = `${cardData.month} / ${cardData.year}`;

          dispatch(setFieldValue({ path: 'visaCardNumber', value: '************' + cardData.number.slice(-3) }));
          dispatch(setFieldValue({ path: 'visaCardDate', value: formattedDate }));
          dispatch(setFieldValue({ path: 'visaCardCVV', value: '' }));
          dispatch(setFieldValue({ path: 'visaCardName', value: cardData.name }));
          setIsDefaultCard(cardData.default);
        } catch (error: any) {
          dispatch(setErrorNotify(error || 'Failed to fetch card data'));
          // Fallback to local data if API fails
          dispatch(setFieldValue({ path: 'visaCardNumber', value: '************' + selectedCardForEdit.number.slice(-3) }));
          dispatch(setFieldValue({ path: 'visaCardDate', value: selectedCardForEdit.exp }));
          dispatch(setFieldValue({ path: 'visaCardCVV', value: '' }));
          dispatch(setFieldValue({ path: 'visaCardName', value: '' }));
          setIsDefaultCard(selectedCardForEdit.isDefault);
        } finally {
          setIsLoadingCardData(false);
        }
      };

      fetchCardData();
    } else if (isAddCardDialogOpen && !selectedCardForEdit) {
      dispatch(setFieldValue({ path: 'visaCardNumber', value: '' }));
      dispatch(setFieldValue({ path: 'visaCardDate', value: '' }));
      dispatch(setFieldValue({ path: 'visaCardCVV', value: '' }));
      dispatch(setFieldValue({ path: 'visaCardName', value: '' }));
      setIsDefaultCard(false);
    }
  }, [isAddCardDialogOpen, selectedCardForEdit, dispatch]);

  // Initialize form when component mounts
  useEffect(() => {
    dispatch(
      initializeForm({
        visaCardNumber: '',
        visaCardDate: '',
        visaCardCVV: '',
        visaCardName: '',
        coupon: '',
      })
    );
  }, [dispatch]);

  // Fetch cards when component mounts
  useEffect(() => {
    const fetchCardsData = async () => {
      try {
        const result = await dispatch(fetchCards()).unwrap();
        if (result && result.items) {
          const formattedCards = result.items.map((card: any) => ({
            _id: card._id,
            id: card._id,
            name: card.name,
            number: `**** **** **** ${card.number?.slice(-3) || '***'}`,
            exp: `${card.month} / ${card.year}`,
            month: card.month,
            year: card.year,
            type: card.type,
            brand: card.type,
            default: card.default,
            isDefault: card.default,
            isExpired: false,
            isExpiringSoon: false,
          }));
          setLocalCards(formattedCards);
        }
      } catch (error: any) {
        console.error('Failed to fetch cards:', error);
      }
    };

    fetchCardsData();
  }, [dispatch]);

  // User Data
  const userData: UsersListItem = useAppSelector((state: RootState) => state.auth.user);

  useEffect(() => {
    if (planData && planData.planSummary) {
      const generatedCredits = Object.entries(planData.planSummary).map(([key, value]: any) => ({
        title: formatFeatureName(key),
        used: value.used,
        total: value.limit,
        usedPercentage: value.percentageUsed,
        progressColor: 'bg-[#E8B431BD]',
      }));
      setCreditsData(generatedCredits);
    }
  }, [planData]);

  const handleGetPlanData = async () => {
    setLoading(true);
    try {
      const response = await Api.get<OrganizationsPlanOverview>(`organizations/plan/overview/${userData?.organizationId}`, {});
      console.log(`organizations/plan/overview/${userData?.organizationId}`, response.data);
      setPlanData(response?.data);
      setRenewalType(response?.data?.autoRenewal ? 'automatic' : 'manual');
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setLoading(false);
    }
  };

  const handlePaymentMethod = async (action: string, index: number) => {
    dispatch(
      showConfirm({
        message: ConfirmText(action, localCards[index]),
        options: {
          confirmLabel: action === 'remove card' ? 'Delete' : 'Change',
          cancelLabel: 'Cancel',
          className: 'z-[999999999999999999]', // Add high z-index
          onConfirm: async () => {
            try {
              if (action === 'make default') {
                // Call API to set default card
                const card = localCards[index];
                if (card && (card._id || card.id)) {
                  await dispatch(setDefaultCard(card._id || card.id)).unwrap();
                }

                setLocalCards((prevCards) =>
                  prevCards.map((card, idx) => ({
                    ...card,
                    default: idx === index,
                    isDefault: idx === index,
                  }))
                );
                dispatch(setNotifyMessage('✅ Default card changed successfully!'));
              } else if (action === 'remove card') {
                // Call API to delete card
                const card = localCards[index];
                if (card && (card._id || card.id)) {
                  await dispatch(deleteCard(card._id || card.id)).unwrap();
                }

                // Fetch updated cards list from API
                try {
                  const result = await dispatch(fetchCards()).unwrap();
                  if (result && result.items) {
                    const formattedCards = result.items.map((card: any) => ({
                      _id: card._id,
                      id: card._id,
                      name: card.name,
                      number: `**** **** **** ${card.number?.slice(-3) || '***'}`,
                      exp: `${card.month} / ${card.year}`,
                      month: card.month,
                      year: card.year,
                      type: card.type,
                      brand: card.type,
                      default: card.default,
                      isDefault: card.default,
                      isExpired: false,
                      isExpiringSoon: false,
                    }));
                    setLocalCards(formattedCards);
                  }
                } catch (error: any) {
                  console.error('Failed to fetch updated cards:', error);
                  // Fallback to local update if API fails
                  let updatedCards = localCards.filter((_, idx) => idx !== index);
                  if (localCards[index].default && updatedCards.length > 0) {
                    updatedCards = updatedCards.map((card, idx) => ({
                      ...card,
                      default: idx === 0,
                      isDefault: idx === 0,
                    }));
                  }
                  setLocalCards(updatedCards);
                }

                dispatch(setNotifyMessage('✅ Card removed successfully!'));
              }
            } catch (error: any) {
              dispatch(setErrorNotify(error || `Failed to ${action}`));
            } finally {
              // Close confirm dialog after a short delay to show the notification
              setTimeout(() => {
                dispatch(hideConfirm());
              }, 500);
            }
          },
          size: 'lg',
          danger: action === 'remove card',
          zIndex: 999999999999999999, // Add z-index for confirm dialog
        },
      })
    );
  };

  const dropdownData = (card: any, index: number) => {
    return (
      <Dropdown
        label=""
        className="z-[999999999999999999]"
        dismissOnClick={false}
        renderTrigger={() => (
          <button className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100">
            <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
              <circle cx="4" cy="10" r="1.5" fill="#888" />

              <circle cx="10" cy="10" r="1.5" fill="#888" />

              <circle cx="16" cy="10" r="1.5" fill="#888" />
            </svg>
          </button>
        )}
      >
        {!card.default && (
          <DropdownItem
            className="flex items-center gap-2"
            onClick={() => {
              handlePaymentMethod('make default', index);
            }}
          >
            <CustomIcon definedIcon="nikeWithCircle" />
            Make Default
          </DropdownItem>
        )}

        <DropdownItem
          className="flex items-center gap-2"
          onClick={() => {
            setSelectedCardForEdit({ ...card, index });

            setAddCardDialogOpen(true);
          }}
        >
          <CustomIcon definedIcon="edit" />
          Edit Card
        </DropdownItem>

        <DropdownItem
          className="flex items-center gap-2 text-red-600"
          onClick={() => {
            handlePaymentMethod('remove card', index);
          }}
        >
          <CustomIcon definedIcon="trash" />
          Remove Card
        </DropdownItem>
      </Dropdown>
    );
  };

  // Permissions
  const isPermittedAdmin = Array.isArray(userData?.role) && userData?.role.includes('admin');
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');

  const initialFilters = {
    type: {
      label: 'Plan',
      enum: 'PlanType',
    },
  };
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters } = useFetchList('/subscription/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
    id: userData?.organizationId,
  });

  // Cards fetch list
  const {
    ready: cardsReady,
    loading: cardsLoading,
    setLoading: setCardsLoading,
    list: cards,
    count: cardsCount,
    search: cardsSearch,
    pagination: cardsPagination,
    filters: cardsFilters,
    setFilters: setCardsFilters,
  } = useFetchList('/cards/list', {
    search: '',
    pagination: {
      page: 1,
      size: 30,
    },
    filters: {},
    id: userData?.organizationId,
  });

  useEffect(() => {
    if (list.length > 0 && backupList.length === 0) {
      setBackupList(list);
    }
  }, [list, backupList.length]);

  // Update local cards when cards from API change
  useEffect(() => {
    setLocalCards(cards);
  }, [cards]);

  useEffect(() => {
    if (userData?.organizationId) {
      handleGetPlanData();
    }
  }, [userData?.organizationId]);

  const limitedCredits = creditsData.filter((data: { total: string }) => typeof data.total === 'number');

  const handleUpdateType = async () => {
    try {
      const payload = {
        enabled: renewalType === 'automatic' ? true : false,
      };
      const response = await Api.put(`subscription/auto-renewal/activate/${planData?._id}`, payload);
    } catch (error) {
      // notify.error(error.response.data.message);
    }
  };

  const handleAddCoupon = () => {
    // TODO: Implement coupon logic
    console.log('Coupon applied:', form?.coupon);
  };

  const handleDiscountedAmount = () => {
    // TODO: Implement discount calculation
    return planData?.price || 0;
  };

  const formatCardNumber = (cardNumber: string) => {
    // Extract last 3 digits from the card number
    const lastThreeDigits = cardNumber.replace(/\D/g, '').slice(-3);
    return `**** **** **** ${lastThreeDigits}`;
  };

  // Minimal dialog component matching the image
  const RenewalTypeDialog = () => {
    // For demo, always show dialog
    const [show, setShow] = useState(true);

    const dispatch = useAppDispatch();

    if (!show) return null;

    return (
      <div className=" inset-0 flex items-center justify-center  bg-opacity-10 z-50">
        <div className="bg-white rounded-xl w-full p-0 flex flex-col">
          <div className="flex-1 flex flex-col items-center">
            <div className="text-lg font-semibold mb-4">Renewal Type</div>
            <div className="flex flex-col gap-3 w-full">
              {/* Manual Option */}
              <div
                className={`flex items-center gap-4 px-6 py-3 rounded-2xl border transition cursor-pointer ${
                  renewalType === 'manual' ? 'border-primaryPurple bg-white' : 'border-[#E5E7EB] bg-white'
                }`}
                onClick={() => setRenewalType('manual')}
              >
                <span
                  className={`w-6 h-6 flex items-center justify-center rounded-full border-2 ${
                    renewalType === 'manual' ? 'border-primaryPurple' : 'border-[#E5E7EB]'
                  }`}
                  style={{ background: '#fff' }}
                >
                  {renewalType === 'manual' && <span className="w-3 h-3 bg-primaryPurple rounded-full block" />}
                </span>
                <span className="text-lg font-medium">Manual</span>
              </div>
              {/* Automatic Option */}
              <div
                className={`flex items-center gap-4 px-6 py-3 rounded-2xl border transition cursor-pointer ${
                  renewalType === 'automatic' ? 'border-primaryPurple bg-[#f6f0ff]' : 'border-[#E5E7EB] bg-white'
                }`}
                onClick={() => setRenewalType('automatic')}
              >
                <span
                  className={`w-6 h-6 flex items-center justify-center rounded-full border-2 ${
                    renewalType === 'automatic' ? 'border-primaryPurple' : 'border-[#E5E7EB]'
                  }`}
                  style={{ background: renewalType === 'automatic' ? '#a259ff22' : '#fff' }}
                >
                  {renewalType === 'automatic' && <span className="w-3 h-3 bg-primaryPurple rounded-full block" />}
                </span>
                <span className="text-lg font-medium">Automatic</span>
              </div>
            </div>

            <Button
              label="Done"
              colorType="primary"
              className=" mt-6 w-32 h-10 sm:w-auto "
              onClick={() => {
                handleUpdateType();
                setRenewalDialogOpen(false);
                dispatch(setNotifyMessage(`Renewal type updated successfully to ${renewalType === 'manual' ? 'Manual' : 'Automatic'}`));
              }}
            />
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="space-y-4">
        <Jumbotron />

        {!isSuperAdmin && planData?.name ? (
          <>
            {/* Current Plan and Payment Methods in the same row */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Current Plan Section */}
              <div className="border border-[#DEE2E4] rounded-2xl p-6 w-full">
                <div className="flex items-start justify-between mb-6 pb-4 border-b border-gray-200">
                  <h2 className="text-xl font-semibold text-gray-900">Current Plan</h2>
                  <Button
                    label="Manage Plan"
                    colorType="tertiary"
                    onClick={() => setPlansVisibilty(true)}
                    icon={
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M10.1824 1.66406H9.81569C9.37366 1.66406 8.94974 1.83966 8.63718 2.15222C8.32462 2.46478 8.14902 2.8887 8.14902 3.33073V3.48073C8.14872 3.773 8.07157 4.06005 7.92531 4.31309C7.77904 4.56613 7.56881 4.77626 7.31569 4.9224L6.95736 5.13073C6.70399 5.27701 6.41659 5.35402 6.12402 5.35402C5.83146 5.35402 5.54406 5.27701 5.29069 5.13073L5.16569 5.06406C4.78325 4.84345 4.32889 4.7836 3.90236 4.89765C3.47583 5.01171 3.11198 5.29034 2.89069 5.6724L2.70736 5.98906C2.48674 6.37151 2.42689 6.82587 2.54095 7.25239C2.655 7.67892 2.93364 8.04277 3.31569 8.26406L3.44069 8.3474C3.69259 8.49282 3.90204 8.70164 4.04823 8.95309C4.19443 9.20454 4.27227 9.48987 4.27403 9.78073V10.2057C4.27519 10.4994 4.19873 10.7882 4.05239 11.0428C3.90606 11.2974 3.69503 11.5089 3.44069 11.6557L3.31569 11.7307C2.93364 11.952 2.655 12.3159 2.54095 12.7424C2.42689 13.1689 2.48674 13.6233 2.70736 14.0057L2.89069 14.3224C3.11198 14.7044 3.47583 14.9831 3.90236 15.0971C4.32889 15.2112 4.78325 15.1513 5.16569 14.9307L5.29069 14.8641C5.54406 14.7178 5.83146 14.6408 6.12402 14.6408C6.41659 14.6408 6.70399 14.7178 6.95736 14.8641L7.31569 15.0724C7.56881 15.2185 7.77904 15.4287 7.92531 15.6817C8.07157 15.9347 8.14872 16.2218 8.14902 16.5141V16.6641C8.14902 17.1061 8.32462 17.53 8.63718 17.8426C8.94974 18.1551 9.37366 18.3307 9.81569 18.3307H10.1824C10.6244 18.3307 11.0483 18.1551 11.3609 17.8426C11.6734 17.53 11.849 17.1061 11.849 16.6641V16.5141C11.8493 16.2218 11.9265 15.9347 12.0727 15.6817C12.219 15.4287 12.4292 15.2185 12.6824 15.0724L13.0407 14.8641C13.2941 14.7178 13.5815 14.6408 13.874 14.6408C14.1666 14.6408 14.454 14.7178 14.7074 14.8641L14.8324 14.9307C15.2148 15.1513 15.6692 15.2112 16.0957 15.0971C16.5222 14.9831 16.8861 14.7044 17.1074 14.3224L17.2907 13.9974C17.5113 13.6149 17.5712 13.1606 17.4571 12.7341C17.343 12.3075 17.0644 11.9437 16.6824 11.7224L16.5574 11.6557C16.303 11.5089 16.092 11.2974 15.9457 11.0428C15.7993 10.7882 15.7229 10.4994 15.724 10.2057V9.78906C15.7229 9.49538 15.7993 9.2066 15.9457 8.95197C16.092 8.69734 16.303 8.4859 16.5574 8.33906L16.6824 8.26406C17.0644 8.04277 17.343 7.67892 17.4571 7.25239C17.5712 6.82587 17.5113 6.37151 17.2907 5.98906L17.1074 5.6724C16.8861 5.29034 16.5222 5.01171 16.0957 4.89765C15.6692 4.7836 15.2148 4.84345 14.8324 5.06406L14.7074 5.13073C14.454 5.27701 14.1666 5.35402 13.874 5.35402C13.5815 5.35402 13.2941 5.27701 13.0407 5.13073L12.6824 4.9224C12.4292 4.77626 12.219 4.56613 12.0727 4.31309C11.9265 4.06005 11.8493 3.773 11.849 3.48073V3.33073C11.849 2.8887 11.6734 2.46478 11.3609 2.15222C11.0483 1.83966 10.6244 1.66406 10.1824 1.66406Z"
                          stroke="#743AF5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                        <path
                          d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z"
                          stroke="#743AF5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    }
                  />
                </div>

                {/* Plan Details */}
                <div className="space-y-4 mb-6">
                  <div className="flex flex-wrap  justify-between  gap-8">
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Plan name</p>
                      <p className="text-base font-medium text-gray-900">{planData?.name || 'Free'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Plan cost</p>
                      <p className="text-base font-medium text-gray-900">
                        {planData?.price ? (
                          <>
                            <CurrencySymbol currency={planData?.currency} />
                            {planData?.price}
                          </>
                        ) : (
                          'Free'
                        )}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Expiry date</p>
                      <p className="text-base font-medium text-gray-900">
                        {planData?.endDate ? format(new Date(planData?.endDate), 'dd/MM/yyyy') : 'No expiry'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 mb-1">Billing cycle</p>
                      <p className="text-base w-fit font-medium text-gray-900">
                        <SubscriptionPlanPeriod plan={planData?.billingCycle || 0} />
                      </p>
                    </div>
                  </div>
                </div>

                {/* Extra Credit Section */}
                <div className="pt-3">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="thepassBone  text-[#1B1F3B]">Extra Credit</h3>
                    <button className="text-primaryPurple text-sm font-medium hover:underline" onClick={() => setCreditsDialogOpen(true)}>
                      View All
                    </button>
                  </div>

                  {/* Credit Cards */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    {limitedCredits.slice(0, 3).map((data: { title: string; usedPercentage: number; total: number; used: number }, index: number) => (
                      <div key={index} className="bg-white rounded-lg p-4 border border-gray-200">
                        {/* Title and Value Row */}
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-medium text-gray-900">{data.title}</span>
                          <div className="flex items-center gap-2">
                            <span className="text-base font-semibold text-gray-900">{data.total.toLocaleString()}</span>
                            <span className="text-xs text-gray-500">({data.usedPercentage}% used)</span>
                          </div>
                        </div>

                        {/* Expiry Date Row */}
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">Expiry Date</span>
                          <span className="text-sm font-medium text-gray-900">
                            {planData?.endDate ? format(new Date(planData?.endDate), 'dd/MM/yyyy') : 'No expiry'}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Payment Methods Section */}
              <div className="border border-[#DEE2E4] rounded-2xl p-6 w-full min-w-0 space-y-4 flex flex-col justify-between">
                <div className="flex items-center justify-between pb-4 border-b border-gray-200">
                  <div className="flex items-center gap-2">
                    <CustomIcon definedIcon="visa" />
                    <h2 className="text-base font-medium flex items-center gap-2">Payment Methods</h2>
                  </div>
                  <Button
                    label="Add card"
                    icon={
                      <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="10" cy="10" r="9.5" fill="white" stroke="#743AF5" />
                        <path
                          d="M10 10H6.5M10 6.5V10V6.5ZM10 10V13.5V10ZM10 10H13.5H10Z"
                          stroke="#743AF5"
                          stroke-width="1.5"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                        />
                      </svg>
                    }
                    className="h-10"
                    customIcon="plusWithPurple"
                    colorType="tertiary"
                    onClick={() => setAddCardDialogOpen(true)}
                  />
                </div>

                {/* Subheader: Cards and view more */}
                <div className="flex items-center justify-between mt-3">
                  <span className="thepassBone text-[#1B1F3B]">Cards</span>
                  {localCards.length > 1 && (
                    <button
                      type="button"
                      className="text-primaryPurple text-sm font-medium hover:underline"
                      onClick={() => setPaymentMehodsVisibility(true)}
                    >
                      View {localCards.length} more
                    </button>
                  )}
                </div>

                {/* Cards Array */}

                {(() => {
                  if (cardsLoading) {
                    return (
                      <div className="space-y-3">
                        <div className="flex items-center justify-center py-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primaryPurple"></div>
                        </div>
                      </div>
                    );
                  }

                  if (localCards.length === 0) {
                    return (
                      <div className="text-center py-8 text-gray-500">
                        <p>No cards found</p>
                      </div>
                    );
                  }

                  const visibleCards = localCards.slice(0, 1);

                  const moreCardsCount = localCards.length - 1;

                  return (
                    <div className="space-y-3">
                      {visibleCards.map((card, index) => {
                        const realIndex = localCards.findIndex((c) => c.id === card.id);

                        return (
                          <div
                            key={card.id}
                            className={`flex items-center justify-between border rounded-xl px-4 py-3 relative mb-2 ${
                              card.default ? 'border-purple-400 bg-[#F4EBFF]' : 'border-gray-200 bg-white'
                            }`}
                          >
                            <div className={`flex items-center gap-3 ${card.isExpired ? 'opacity-70' : ''}`}>
                              <img src={'/src/images/payment/visa.svg'} alt="visa" className="w-10 h-7" />

                              <div>
                                <div className="font-medium text-base flex items-center gap-4">
                                  <h3 className="text-[#868D9C]">{formatCardNumber(card.number)}</h3>

                                  {card.default && (
                                    <span className="bg-[#F5F6F8] border border-[#F5F6F8] text-[#743AF5] px-4 py-1 rounded-full text-[13px] font-medium">
                                      Default
                                    </span>
                                  )}
                                </div>

                                <div className="flex items-center gap-5 text-sm text-gray-500">
                                  <span>Exp {card.exp}</span>

                                  {card.isExpired && (
                                    <span className="flex items-center text-[12px] text-[#B42318] gap-1">
                                      <CustomIcon definedIcon="warningLarge" />
                                      Expired
                                    </span>
                                  )}

                                  {card.isExpiringSoon && !card.isExpired && (
                                    <span className="flex items-center text-[12px] text-[#E8B431] gap-1">
                                      <CustomIcon definedIcon="warningLarge" />
                                      Expires soon
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>

                            <div className="flex items-center gap-2">{dropdownData(card, index)}</div>
                          </div>
                        );
                      })}

                      {/* More cards indicator removed; handled by subheader 'View more' */}

                      {/* Add card button removed; now placed in header next to title */}
                    </div>
                  );
                })()}

                {/* Auto Renewal Toggle */}
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-base font-medium text-gray-900">Auto Renewal</h3>
                      <p className="text-sm text-gray-500">Automatically renews subscription on billing date</p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="sr-only peer"
                        checked={renewalType === 'automatic'}
                        onChange={(e) => {
                          const newRenewalType = e.target.checked ? 'automatic' : 'manual';
                          setRenewalType(newRenewalType);
                          handleUpdateType();
                          dispatch(setNotifyMessage(`Auto renewal ${e.target.checked ? 'enabled' : 'disabled'} successfully`));
                        }}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primaryPurple"></div>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <Table
              addButtonPermission
              ready={ready}
              loading={loading}
              title="Billing History"
              searchPlaceholder="Search for billing..."
              count={count}
              search={search}
              filters={filters}
              setFilters={setFilters}
              pagination={pagination}
              rows={list}
              backupRows={backupList}
              slots={{
                invoice: (_: string, row: { _id: string; paymentId: string }) => (
                  <NameFieldColumn id={row?._id} name={row?.paymentId} showMoreMap={showMoreMap} />
                ),
                date: (_: string, row: { createdAt: Date }) => <FormatDateFieldColumn date={row?.createdAt} />,
                status: (value: number) => (
                  <div className="w-fit">
                    <PaymentStatus status={value} />
                  </div>
                ),
                type: (_: string, row: any) => (
                  <div className="flex flex-col gap-1">
                    {row?.couponCode ? (
                      <>
                        <div className="flex items-center text-sm gap-2 capitalize">
                          <span className="w-2 h-2 block bg-yellow-300 rounded-full"></span>
                          <span className="text-[#535862]">Coupon</span>
                        </div>
                        <span className="text-[#8DA0C0] text-[12px] px-3">{row?.couponCode}</span>
                      </>
                    ) : (
                      <div className="flex items-center text-sm gap-2 capitalize">
                        <span className="w-2 h-2 block bg-[#A379FC] rounded-full"></span>
                        <span className="text-[#535862]">Norm</span>
                      </div>
                    )}
                  </div>
                ),
                amount: (_: string, row: { currency: string; price: number }) => (
                  <p>
                    {row?.currency && <CurrencySymbol currency={row?.currency} />}
                    {row?.price}
                  </p>
                ),
                plan: (_: string, row: { planName: string; billingCycle: number; name: string }) => (
                  <div className="space-y-2">
                    <p className="text-black">{row?.name} Plan</p>
                    <p className="text-sm">{BillingCycle[row?.billingCycle]} Subscription</p>
                  </div>
                ),
              }}
              columns={[
                { key: 'invoice', label: 'Invoice', primary: true, width: '25%' },
                { key: 'plan', label: 'Purchase', width: '15%' },
                { key: 'amount', label: 'Amount', width: '10%' },
                { key: 'type', label: 'Type', width: '10%' },
                { key: 'status', label: 'Status', primary: true, width: '15%' },
                { key: 'date', label: 'Created At', primary: true, width: '15%' },
              ]}
              // noDataFound={{
              //   message: 'No billing records yet',
              //   customIcon: 'dollarInsideZigzagBorder',
              //   description:
              //     !planData &&
              //     "Since you're currently on a free plan, there's no billing activity to display. Once you upgrade or make a payment, your invoice history will appear here for easy access.",
              // }}
              placeholder={{
                title: 'No billings created yet',
                subTitle: 'Start by adding a billing plan to manage payments and subscriptions.',
                image: '/UI/src/assets/placeholder/NoBillings.svg',
              }}
              noDataFoundIconWidth="60"
              noDataFoundIconHeight="60"
              showMoreMap={showMoreMap}
              setShowMoreMap={setShowMoreMap}
              hideJumbotron
              isScrollableTabsExists
            />

            {isPlansVisible && (
              <Dialog isOpen size="7xl" onClose={() => setPlansVisibilty(false)} zIndex={999999999999999999}>
                <div className="overflow-y-scroll max-h-[720px]">
                  <BasePlans pageType="plans" />
                </div>
              </Dialog>
            )}
          </>
        ) : (
          <BasePlans pageType="plans" />
        )}
      </div>

      {isAddQuotaVisible && <AddQuotaDialog onClose={() => setAddQuotaVisibility(false)} />}

      {/* Credits Dialog */}
      {isCreditsDialogOpen && (
        <Dialog isOpen size="xl" title="View Extra Credits" onClose={() => setCreditsDialogOpen(false)} zIndex={999999999999999999}>
          <div className="p-6 max-h-[70vh] overflow-y-auto">
            {creditsData.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-gray-500 text-lg font-medium mb-2">No available extra credit</div>
                <div className="text-gray-400 text-sm">You don't have any extra credits at the moment</div>
              </div>
            ) : (
              <div className="grid grid-cols-1  lg:grid-cols-1 gap-4">
                {creditsData.map((data: any, index: number) => (
                  <div key={index} className="bg-white rounded-lg p-4 border border-gray-200">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-gray-900">{data.title}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-base font-semibold text-gray-900">{Number(data.total).toLocaleString()}</span>
                        <span className="text-xs text-gray-500">({Math.round(Number(data.usedPercentage))}% used)</span>
                      </div>
                    </div>
                    <div className="space-y-1 flex justify-between">
                      <p className="text-xs text-gray-500">Expiry date</p>
                      <p className="text-sm font-semibold text-gray-900">
                        {planData?.endDate ? format(new Date(planData?.endDate), 'dd/MM/yyyy') : 'No expiry'}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Cancel Button */}
            <div className="flex justify-center mt-6">
              <Button
                label="Cancel"
                colorType="tertiary"
                variant="md"
                onClick={() => setCreditsDialogOpen(false)}
                className="px-8 py-3 w-full text-lg"
              />
            </div>
          </div>
        </Dialog>
      )}

      {isRenewalDialogOpen && (
        <Dialog isOpen size="md" onClose={() => setRenewalDialogOpen(false)} zIndex={999999999999999999}>
          <RenewalTypeDialog />
        </Dialog>
      )}

      {/* Add Card Dialog */}
      {isAddCardDialogOpen && (
        <div className="z-[999999999999999999]">
          <Dialog
            isOpen
            size="xl"
            title={selectedCardForEdit ? 'Edit Payment Method' : 'Add Payment Method'}
            onClose={() => {
              setAddCardDialogOpen(false);
              setSelectedCardForEdit(null);
            }}
            zIndex={999999999999999999}
          >
            {isLoadingCardData && (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primaryPurple"></div>
                <span className="ml-2 text-gray-600">Loading card data...</span>
              </div>
            )}

            <form
              onSubmit={async (e: React.FormEvent) => {
                e.preventDefault();

                if (isLoadingCardData) return;

                if (selectedCardForEdit) {
                  // Update card - Send to API with proper format
                  try {
                    const cardData = {
                      name: form.visaCardName,
                      number: form.visaCardNumber.replace(/\s+/g, ''),
                      month: form.visaCardDate?.split(' / ')[0],
                      year: form.visaCardDate?.split(' / ')[1],
                      cvc: form.visaCardCVV,
                      type: form.visaCardNumber.startsWith('4') ? 'visa' : 'mastercard',
                      default: isDefaultCard,
                    };

                    // Send to API using middleware
                    const result = await dispatch(updateCard({ id: selectedCardForEdit._id || selectedCardForEdit.id, payload: cardData })).unwrap();

                    // Update local state for UI with data from API response
                    const updatedCard = {
                      ...selectedCardForEdit,
                      _id: result._id || selectedCardForEdit._id,
                      id: result._id || selectedCardForEdit.id,
                      name: result.name || form.visaCardName,
                      number: `**** **** **** ${result.number?.slice(-3) || form.visaCardNumber.replace(/\s/g, '').slice(-3)}`,
                      exp: `${result.month} / ${result.year}`,
                      month: result.month,
                      year: result.year,
                      type: result.type || (form.visaCardNumber.startsWith('4') ? 'visa' : 'mastercard'),
                      brand: result.type || (form.visaCardNumber.startsWith('4') ? 'visa' : 'mastercard'),
                      default: result.default || isDefaultCard,
                      isDefault: result.default || isDefaultCard,
                      isExpired: false,
                      isExpiringSoon: false,
                    };

                    setLocalCards((prevCards) => {
                      let updatedCards = [...prevCards];
                      if (isDefaultCard) {
                        updatedCards = updatedCards.map((card, idx) => ({ ...card, default: false, isDefault: false }));
                      }
                      updatedCards[selectedCardForEdit.index] = updatedCard;
                      return updatedCards;
                    });

                    dispatch(setNotifyMessage('✅ Card updated successfully!'));

                    // Close dialog after a short delay to show the notification
                    setTimeout(() => {
                      setSelectedCardForEdit(null);
                      setAddCardDialogOpen(false);
                    }, 500);
                  } catch (error: any) {
                    dispatch(setErrorNotify(error || 'Failed to update card'));
                  }
                } else {
                  // Add new card - Send to API with proper format
                  try {
                    const cardData = {
                      name: form.visaCardName,
                      number: form.visaCardNumber.replace(/\s+/g, ''),
                      month: form.visaCardDate?.split(' / ')[0],
                      year: form.visaCardDate?.split(' / ')[1],
                      cvc: form.visaCardCVV,
                      type: form.visaCardNumber.startsWith('4') ? 'visa' : 'mastercard',
                      default: isDefaultCard,
                    };

                    // Send to API using middleware
                    const result = await dispatch(addCard(cardData)).unwrap();

                    // Add to local state for UI with data from API response
                    const newCard = {
                      _id: result._id,
                      id: result._id,
                      name: result.name || form.visaCardName,
                      number: `**** **** **** ${result.number?.slice(-3) || form.visaCardNumber.replace(/\s/g, '').slice(-3)}`,
                      exp: `${result.month} / ${result.year}`,
                      month: result.month,
                      year: result.year,
                      type: result.type || (form.visaCardNumber.startsWith('4') ? 'visa' : 'mastercard'),
                      brand: result.type || (form.visaCardNumber.startsWith('4') ? 'visa' : 'mastercard'),
                      default: result.default || isDefaultCard,
                      isDefault: result.default || isDefaultCard,
                      isExpired: false,
                      isExpiringSoon: false,
                    };

                    setLocalCards((prevCards) => {
                      let updatedCards = [...prevCards];
                      if (isDefaultCard) {
                        updatedCards = updatedCards.map((card) => ({ ...card, default: false, isDefault: false }));
                      }
                      updatedCards.push(newCard);
                      return updatedCards;
                    });

                    dispatch(setNotifyMessage('✅ Card added successfully!'));

                    // Close dialog after a short delay to show the notification
                    setTimeout(() => {
                      setAddCardDialogOpen(false);
                    }, 500);
                  } catch (error: any) {
                    dispatch(setErrorNotify(error || 'Failed to add card'));
                  }
                }

                // Reset form
                dispatch(setFieldValue({ path: 'visaCardNumber', value: '' }));
                dispatch(setFieldValue({ path: 'visaCardDate', value: '' }));
                dispatch(setFieldValue({ path: 'visaCardCVV', value: '' }));
                dispatch(setFieldValue({ path: 'visaCardName', value: '' }));
                dispatch(setFieldValue({ path: 'coupon', value: '' }));
                setIsDefaultCard(false);
                setSaveCardInfo(false);
              }}
            >
              <div className="space-y-3">
                {/* Name on Card */}
                <TextInput
                  type="text"
                  name="name"
                  label="Name on Card"
                  placeholder="Enter"
                  value={form?.visaCardName}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'visaCardName', value }))}
                  validators={[isRequired()]}
                  disabled={isLoadingCardData}
                />

                {/* Card Number */}
                <TextInput
                  name="number"
                  label="Card Number"
                  type="text"
                  placeholder="Enter"
                  value={form?.visaCardNumber}
                  onChange={(value: string) => {
                    // Remove non-digits
                    const cleaned = value.replace(/\D/g, '');

                    // Format with spaces after every 4 digits
                    let formatted = '';
                    for (let i = 0; i < cleaned.length; i++) {
                      if (i > 0 && i % 4 === 0) {
                        formatted += ' ';
                      }
                      formatted += cleaned[i];
                    }

                    // Limit to 16 digits (19 chars with spaces)
                    formatted = formatted.substring(0, 19);
                    dispatch(setFieldValue({ path: 'visaCardNumber', value: formatted }));
                  }}
                  validators={[
                    isRequired(),
                    (value: string) => {
                      // Check if it has 16 digits (ignoring spaces)
                      const digitsOnly = value ? value.replace(/\s/g, '') : '';
                      return digitsOnly.length === 16 ? null : 'Card number must be 16 digits';
                    },
                  ]}
                  disabled={isLoadingCardData}
                />

                {/* Expiry & CVV */}
                <div className="grid grid-cols-2 gap-4">
                  <TextInput
                    name="visaCardDate"
                    label="MM/YY"
                    type="text"
                    placeholder="Enter"
                    value={form?.visaCardDate}
                    onChange={(value: string) => {
                      // Remove non-digits
                      const cleaned = value.replace(/\D/g, '');
                      let formatted = '';

                      if (cleaned.length > 0) {
                        // For the first digit of month, only allow 0 or 1
                        if (cleaned.length === 1 && parseInt(cleaned[0]) > 1) {
                          formatted = '0' + cleaned[0];
                        } else {
                          // Add first 2 digits (month)
                          formatted = cleaned.substring(0, 2);

                          // If month > 12, set to 12
                          if (parseInt(formatted) > 12) {
                            formatted = '12';
                          }
                        }

                        // Add " / " and year digits if available
                        if (cleaned.length > 2) {
                          formatted += ' / ' + cleaned.substring(2, 4);
                        }
                      }

                      dispatch(setFieldValue({ path: 'visaCardDate', value: formatted }));
                    }}
                    validators={[
                      isRequired(),
                      (value: string) => {
                        if (!value) return null;

                        // Check if year is valid (not in the past)
                        const match = value.match(/^(0[1-9]|1[0-2])\s\/\s([0-9]{2})$/);
                        if (match) {
                          const currentYear = new Date().getFullYear() % 100; // Get last 2 digits
                          const month = parseInt(match[1]);
                          const year = parseInt(match[2]);

                          if (year < currentYear || (year === currentYear && month < new Date().getMonth() + 1)) {
                            return 'Expiration date cannot be in the past';
                          }
                        }
                        return null;
                      },
                    ]}
                    disabled={isLoadingCardData}
                  />

                  <TextInput
                    name="cvc"
                    type="text"
                    label="CVV"
                    placeholder="Enter"
                    value={form?.visaCardCVV}
                    onChange={(value: string) => {
                      // Remove non-digits and limit to 3 characters
                      const cleaned = value.replace(/\D/g, '').substring(0, 3);
                      dispatch(setFieldValue({ path: 'visaCardCVV', value: cleaned }));
                    }}
                    validators={[isRequired(), minLength(3), maxLength(3)]}
                    disabled={isLoadingCardData}
                  />
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4 pt-6">
                  <Button
                    label="Back"
                    colorType="tertiary"
                    className="w-1/3"
                    onClick={() => {
                      setAddCardDialogOpen(false);
                      setSelectedCardForEdit(null);
                    }}
                    disabled={isLoadingCardData}
                  />
                  <Button
                    type="submit"
                    label={selectedCardForEdit ? 'Update' : 'Add'}
                    colorType="primary"
                    className="w-2/3"
                    disabled={isLoadingCardData}
                  />
                </div>
              </div>
            </form>
          </Dialog>
        </div>
      )}

      {/* Drawer */}

      {isPaymentMehodsVisible && (
        <Dialog isOpen title="My Cards" size="2xl" onClose={() => setPaymentMehodsVisibility(false)} zIndex={999999999999999999}>
          <div className="space-y-6">
            {cardsLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primaryPurple"></div>
              </div>
            ) : localCards.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>No cards found</p>
              </div>
            ) : (
              localCards.map((card, index) => (
                <div
                  key={card.id}
                  className={`flex items-center justify-between border rounded-xl px-4 py-3  relative mb-2

                    ${card.isDefault ? 'border-purple-400 bg-[#F4EBFF]' : 'border-gray-200 bg-white'}`}
                >
                  <div className={`flex items-center gap-3 ${card.isExpired ? 'opacity-70' : ''}`}>
                    {card.type === 'visa' && <img src={'/src/images/payment/visa.svg'} alt="visa" className="w-10 h-7" />}

                    {card.type === 'mastercard' && <img src={'/src/images/payment/mastercard.svg'} alt="mastercard" className="w-10 h-7" />}

                    <div>
                      <div className="font-medium text-base flex items-center gap-4">
                        {formatCardNumber(card.number)}

                        {card.isDefault && (
                          <span className="bg-[#ECFDF3] border border-[#ABEFC6] text-[#067647] px-3 py-1 rounded-full text-[13px] font-medium flex items-center gap-1">
                            <svg width="14" height="14" viewBox="0 0 12 12" fill="none">
                              <g>
                                <path
                                  d="M10.8993 5.0017C11.1277 6.12235 10.9649 7.28742 10.4383 8.3026C9.91157 9.31779 9.05278 10.1217 8.00509 10.5804C6.9574 11.039 5.78414 11.1246 4.68098 10.8229C3.57782 10.5212 2.61142 9.8504 1.94297 8.92241C1.27451 7.99443 0.94439 6.86532 1.00766 5.7234C1.07093 4.58147 1.52377 3.49575 2.29065 2.64729C3.05754 1.79883 4.09212 1.23892 5.22186 1.06093C6.3516 0.882942 7.50822 1.09763 8.49883 1.6692"
                                  stroke="#067647"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                />

                                <path d="M4.5 5.5L6 7L11 2" stroke="#067647" strokeLinecap="round" strokeLinejoin="round" />
                              </g>
                            </svg>
                            Default
                          </span>
                        )}
                      </div>

                      <div className="flex items-center gap-5 text-sm text-gray-500">
                        <span>Exp {card.exp}</span>

                        {card.isExpired && (
                          <span className="flex items-center text-[12px] text-[#B42318] gap-1">
                            <CustomIcon definedIcon="warningLarge" />
                            Expired
                          </span>
                        )}

                        {card.isExpiringSoon && !card.isExpired && (
                          <span className="flex items-center text-[12px] text-[#E8B431] gap-1">
                            <CustomIcon definedIcon="warningLarge" />
                            Expires soon
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    {/* Flowbite Dropdown */}

                    {dropdownData(card, index)}
                  </div>
                </div>
              ))
            )}
          </div>
        </Dialog>
      )}
    </>
  );
};
