import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';
import type { QuestionType } from '../types/Question.type';

// Fetch single question
export const fetchQuestion = createAsyncThunk(
  'questions/fetchQuestion',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get<QuestionType>(`questions/single/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch question');
    }
  }
);

// Create question
export const createQuestion = createAsyncThunk(
  'questions/createQuestion',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('questions/single', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create question');
    }
  }
);

// Update question
export const updateQuestion = createAsyncThunk(
  'questions/updateQuestion',
  async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
    try {
      const response = await Api.put(`questions/single/${id}`, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to update question');
    }
  }
);

// Delete question
export const deleteQuestion = createAsyncThunk(
  'questions/deleteQuestion',
  async (id: string, { rejectWithValue }) => {
    try {
      await Api.delete(`questions/single/${id}`, {});
      return id;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to delete question');
    }
  }
);

// Delete multiple questions
export const deleteMultipleQuestions = createAsyncThunk(
  'questions/deleteMultipleQuestions',
  async (ids: string[], { rejectWithValue }) => {
    try {
      await Api.delete('questions/multi', { ids });
      return ids;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to delete questions');
    }
  }
);

// Generate questions
export const generateQuestions = createAsyncThunk(
  'questions/generateQuestions',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('questions/generate', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to generate questions');
    }
  }
);

// Get questions total
export const getQuestionsTotal = createAsyncThunk(
  'questions/getQuestionsTotal',
  async (params: any, { rejectWithValue }) => {
    try {
      const response = await Api.get('questions/total', params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to get questions total');
    }
  }
); 
