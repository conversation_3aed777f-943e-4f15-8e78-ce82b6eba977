import { ReactNode, type FC } from 'react';
import React from 'react';
import { EnumText, Icon } from 'src';
import { QuizDifficulty, useScreenSize } from 'UI/src';
import { Tooltip } from 'flowbite-react';
import { FaUserGraduate, FaUser, FaStar, FaMedal } from 'react-icons/fa';

type TestData = {
  _id: string; // Added to match the Quiz type
  title?: string;
  difficulty: number;
  numOfQuestions: number;
  duration: number;
};

type BlockDetails = {
  blockIdDetails: string;
  titleDetails: string;
};

type CategoryCardDataProps = {
  test: TestData;
  creationSubmissionsDialog: (test: TestData, back?: () => void) => void;
  back?: () => void;
  // New props for edit mode
  blockDetails?: BlockDetails | null;
  isEditMode?: boolean;
  handleEditBlockTest?: (test: TestData) => void;
  handleDeleteBlockTest?: (blockId: string, testId: string) => void;
  setCreateBlockVisibility?: (visible: boolean) => void;
  setCreateTestDialogVisibility?: (visible: boolean) => void;
};

export const CategoryCardData: FC<CategoryCardDataProps> = ({
  test,
  creationSubmissionsDialog,
  back,
  isEditMode,
  blockDetails,
  handleEditBlockTest,
  handleDeleteBlockTest,
  setCreateBlockVisibility,
  setCreateTestDialogVisibility,
}) => {
  const text = test.title ?? 'Quiz';
  const screen = useScreenSize();

  // Define icon and color for difficulty levels
  let difficultyIcon: ReactNode = null;
  let difficultyColor = 'font-semibold';

  let iconSize = 'text-xs';
  switch (test.difficulty) {
    case 1:
      difficultyIcon = <FaUserGraduate className={`${iconSize} ${difficultyColor}`} />; // Intern
      break;

    // Star Icon fresh level
    case 2:
      difficultyIcon = <FaUser className={`${iconSize} ${difficultyColor}`} />; // Fresh
      break;
    // Medal Star junior
    case 3:
      difficultyIcon = <FaStar className={`${iconSize} ${difficultyColor}`} />; // Junior
      break;
    case 4:
      difficultyIcon = <FaMedal className={`${iconSize} ${difficultyColor}`} />; // Mid-level
      break;

    // Tropy icon for senior with star
    case 5:
      difficultyIcon = <Icon icon="solar:crown-star-bold" className={`${iconSize}  ${difficultyColor}`} />; // Senior
      // difficultyColor = 'text-red-800';
      break;
    default:
      difficultyIcon = null;
  }
  return (
    <div className="relative flex flex-col p-2 bg-white dark:bg-darkBackgroundCard space-y-1">
      <div onClick={() => creationSubmissionsDialog(test, back)} className="cursor-pointer">
        <h3 className="text-base font-semibold text-darkBlueText dark:text-grayTextOnDarkMood hover:underline truncate">{text}</h3>
        {screen.gt.lg() && (
          <Tooltip content={text} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit text-sm">
            <div className="w-full h-full absolute left-0 top-0"></div>
          </Tooltip>
        )}
      </div>
      <div className="flex items-center text-[#798296] dark:text-[#838398] text-sm  gap-2 mb-2">
        <span>
          <span className="font-semibold"> {test.numOfQuestions} </span> {test.numOfQuestions > 1 ? 'Questions' : 'Question'}
        </span>
        <span className="text-gray-400 dark:text-gray-300">•</span> {/* Separator */}
        <span>
          <span className="font-semibold"> {test.duration} </span>min
        </span>
        <span className="text-gray-400 dark:text-gray-300">•</span> {/* Separator */}
        <div className={`flex items-center text-sm gap-1`}>
          {difficultyIcon}
          {/* <EnumText name={'QuizDifficulty'} value={test.difficulty} /> */}
          {QuizDifficulty[test.difficulty]}
        </div>
      </div>
      <div className="pt-1">
        <hr className="border-gray-200 dark:border-gray-700" />
      </div>
    </div>
  );
};
