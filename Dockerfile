# Use Node.js 20 base image
FROM node:20.11.1

# Set working directory to root of the app
WORKDIR /usr/src/app

# Copy root-level package files
COPY package*.json ./
RUN npm install --legacy-peer-deps

# Install dependencies for the UI submodule
WORKDIR /usr/src/app/UI
COPY UI/package*.json ./
RUN npm install --legacy-peer-deps

# Copy the entire app source
WORKDIR /usr/src/app
COPY . .

# Expose port 3000 for dev server
EXPOSE 3000

# Start Vite dev server on port 3000
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3000"]
