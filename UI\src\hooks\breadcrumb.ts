import { useNavigate, useLocation } from 'react-router-dom';
import { breadCrumbRoutesMap } from '../constants/breadcrumbRoutes';

export const useBreadcrumb = () => {
  interface RouteData {
    title?: string;
    subtitle?: string;
    infoIcon?: boolean;
    infoText?: string;
    icon?: React.ReactNode;
    label?: string;
  }

  // Composables
  const navigate = useNavigate();
  const location = useLocation();

  // Simple breadcrumb mapping based on pathname
  const getBreadcrumbData = (pathname: string): RouteData => {
    const data = breadCrumbRoutesMap[pathname];
    if (data) return data;

    // Fallback: generate label from path segment
    const segments = pathname.split('/').filter(Boolean);
    const lastSegment = segments[segments.length - 1];

    if (lastSegment) {
      // Convert kebab-case or camelCase to Title Case
      const label = lastSegment
        .replace(/[-_]/g, ' ')
        .replace(/([a-z])([A-Z])/g, '$1 $2')
        .split(' ')
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
        .join(' ');

      return { label };
    }

    return { label: 'Unknown' };
  };

  // Create routes array based on current pathname
  const createRoutes = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const routes = [];

    // Build cumulative paths
    let currentPath = '';
    for (const segment of pathSegments) {
      currentPath += `/${segment}`;
      const data = getBreadcrumbData(currentPath);

      // Only add routes that have meaningful labels
      if (data.label && data.label !== 'Unknown') {
        routes.push({
          pathname: currentPath,
          data,
        });
      }
    }

    return routes;
  };

  const routes = createRoutes();

  // Methods
  const handleRouteClick = (e: React.MouseEvent<HTMLLIElement>, path: string) => {
    e.preventDefault();
    navigate(path);
  };

  const currentRoute = routes[routes.length - 1];

  return {
    routes,
    currentRoute,
    handleRouteClick,
  };
};
