// React
import { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { createPortal } from 'react-dom';

// Object Path
import { get } from 'object-path';

// Flowbite
import { Tooltip, Dropdown, Spinner as FlowbiteSpinner } from 'flowbite-react';

// UI
import { Icon, CustomIcon } from 'src';

// to convert lucide to PascalCase
function toPascalCase(str: string) {
  return str
    .replace(/[-_]+/g, ' ')
    .replace(/\s+(\w)/g, (_, c) => c.toUpperCase())
    .replace(/^\w/, (c) => c.toUpperCase())
    .replace(/\s/g, '');
}

//composables
import CheckFeatureManagement from 'src/composables/feature-management';

// Lucide Icons
import * as LucideIcons from 'lucide-react';

export const TableAction = ({ buttons, row, onCheckSubscription }: { buttons: any; row: any; onCheckSubscription: any }) => {
  const navigate = useNavigate();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [buttonPosition, setButtonPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLSpanElement>(null);
  // to convert lucide to PascalCase
  function toPascalCase(str: string) {
    return str
      .replace(/[-_]+/g, ' ')
      .replace(/\s+(\w)/g, (_, c) => c.toUpperCase())
      .replace(/^\w/, (c) => c.toUpperCase())
      .replace(/\s/g, '');
  }

  const { checkFeature } = CheckFeatureManagement();
  const value = get(row, 'actions');

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Calculate button position when dropdown opens
  const handleDropdownToggle = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const newPosition = {
        top: rect.bottom + window.scrollY,
        left: rect.right - 160, // 160px is the width of dropdown
      };
      setButtonPosition(newPosition);
    }

    const newState = !isDropdownOpen;
    setIsDropdownOpen(newState);
  };
  const handleBtnClick = (button: any) => {
    if (button.permission) {
      const hasFeature = checkFeature(button.permission);
      if (!hasFeature) {
        onCheckSubscription(true);
        return;
      }
    }

    // Handle buttons with path (navigation)
    if (button.path) {
      navigate(button.path);
      return;
    }

    // Handle buttons with onClick function
    if (button && typeof button.onClick === 'function') {
      button.onClick();
    } else if (button && button.onClick) {
      console.warn('Button onClick is not a function:', button.onClick);
      console.warn('Full button object:', button);
    } else {
      console.warn('Button has no onClick property and no path:', button);
    }
  };

  const allButtons = buttons(value, row);
  const firstButton = allButtons[0];
  const remainingButtons = allButtons.slice(1);

  // Debug: Log the buttons data

  // Check if buttons have onClick
  remainingButtons.forEach((button: any, index: number) => {});

  const renderButton = ({ label, color, loading, ...button }: any, index: number) => {
    const element = () => {
      if (button.path) {
        return (
          <Link to={button.path}>
            {button.icon && <Icon className={`cursor-pointer ${color} ${button.className}`} key={button.icon} width={'20'} {...button} />}
            {button.customIcon && (
              <CustomIcon
                definedIcon={button.customIcon}
                className={`cursor-pointer ${color} ${button.className}`}
                width={button.iconWidth || '20'}
                height={button.iconHeight || '20'}
                onClick={() => handleBtnClick(button)} // Pass as callback instead of executing
              />
            )}
            {button.lucideIcon &&
              LucideIcons[toPascalCase(button.lucideIcon) as keyof typeof LucideIcons] &&
              (() => {
                const LucideIconComponent: any = LucideIcons[toPascalCase(button.lucideIcon) as keyof typeof LucideIcons];
                return (
                  <LucideIconComponent
                    className={`cursor-pointer ${color} ${button.className}`}
                    width={button.iconWidth || '20'}
                    height={button.iconHeight || '20'}
                    onClick={() => handleBtnClick(button)}
                  />
                );
              })()}
          </Link>
        );
      } else if (button.dropDown) {
        return (
          <div className="relative" key={button.icon}>
            <Dropdown
              label=""
              dismissOnClick={false}
              className="sm:ml-11 lg:ml-5"
              inline
              renderTrigger={() => (
                <span>
                  {button.icon && <Icon className={`cursor-pointer ${color}`} key={index} width={'20'} {...button} />}
                  {button.customIcon && (
                    <CustomIcon
                      definedIcon={button.customIcon}
                      className={`cursor-pointer ${color} ${button.className}`}
                      width={button.iconWidth || '20'}
                      height={button.iconHeight || '20'}
                    />
                  )}
                </span>
              )}
            >
              {button.dropDown.map(({ label, color, ...subButton }: any, index: number) => (
                <div className="min-w-40" key={subButton.icon}>
                  <Dropdown.Item onClick={() => handleBtnClick(subButton)}>
                    <div className="flex gap-2 cursor-pointer w-fit">
                      {subButton.icon && <Icon className={`cursor-pointer ${color}`} key={index} width={'20'} {...subButton} />}
                      {subButton.customIcon && (
                        <CustomIcon
                          definedIcon={subButton.customIcon}
                          className={`cursor-pointer ${color} ${button.className}`}
                          width={button.iconWidth || '20'}
                          height={button.iconHeight || '20'}
                        />
                      )}
                      <p className="text-nowrap">{label}</p>
                      {subButton.element}
                    </div>
                  </Dropdown.Item>
                  {button.dropDown.length - 1 > index && <hr className="dark:border-gray-500" />}
                </div>
              ))}
            </Dropdown>
          </div>
        );
      } else {
        return (
          <div key={index}>
            {button.icon && <Icon className={`cursor-pointer ${color}`} width={'20'} {...button} />}
            {button.customIcon && (
              <CustomIcon
                definedIcon={button.customIcon}
                className={`cursor-pointer ${color} ${button.className}`}
                width={button.iconWidth || '20'}
                height={button.iconHeight || '20'}
                onClick={() => handleBtnClick(button)} // Pass as callback instead of executing
              />
            )}
            {button.lucideIcon &&
              LucideIcons[toPascalCase(button.lucideIcon) as keyof typeof LucideIcons] &&
              (() => {
                const LucideIconComponent: any = LucideIcons[toPascalCase(button.lucideIcon) as keyof typeof LucideIcons];
                return (
                  <LucideIconComponent
                    className={`cursor-pointer ${color} ${button.className}`}
                    width={button.iconWidth || '20'}
                    height={button.iconHeight || '20'}
                    onClick={() => handleBtnClick(button)}
                  />
                );
              })()}
          </div>
        );
      }
    };
    const btnLbl = label ? (
      <Tooltip key={index} content={label} placement="bottom" arrow={false} className="text-xs bg-gray-700 dark:bg-gray-200 dark:text-gray-900">
        {element()}
      </Tooltip>
    ) : (
      element()
    );
    return !loading ? btnLbl : <FlowbiteSpinner key={index} size="sm" />;
  };

  const buttonsData = (
    <div className="flex gap-3 items-center relative z-[9999]">
      {/* First button outside */}
      {firstButton && renderButton(firstButton, 0)}

      {/* Custom dropdown with 3 dots - only show if there are remaining buttons */}
      {remainingButtons.length > 0 && (
        <div className="relative z-[9999] overflow-visible" style={{ position: 'relative', zIndex: 99999 }}>
          <span
            ref={buttonRef}
            className="cursor-pointer p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded inline-flex items-center justify-center"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleDropdownToggle();
            }}
          >
            <svg className="cursor-pointer text-gray-600 dark:text-gray-400 w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <circle cx="12" cy="12" r="1" />
              <circle cx="19" cy="12" r="1" />
              <circle cx="5" cy="12" r="1" />
            </svg>
          </span>

          {isDropdownOpen &&
            (() => {
              return createPortal(
                <div
                  ref={dropdownRef}
                  className="fixed bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-[999999] min-w-40"
                  style={{
                    top: buttonPosition.top + 5,
                    left: buttonPosition.left,
                    zIndex: 999999,
                  }}
                >
                  {remainingButtons.length > 0 ? (
                    remainingButtons.map((button: any, index: number) => (
                      <div key={button.icon || index}>
                        {Array.isArray(button.dropDown) && button.dropDown.length > 0 ? (
                          button.dropDown.map((subButton: any, subIndex: number) => (
                            <div key={subButton.icon || subIndex}>
                              <div
                                className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex gap-2 items-center"
                                onClick={() => {
                                  handleBtnClick(subButton);
                                  setIsDropdownOpen(false);
                                }}
                              >
                                {subButton.icon && <Icon className={`cursor-pointer ${subButton.color}`} width={'20'} {...subButton} />}
                                {subButton.customIcon && (
                                  <CustomIcon
                                    definedIcon={subButton.customIcon}
                                    className={`cursor-pointer ${subButton.color} ${subButton.className}`}
                                    width={subButton.iconWidth || '20'}
                                    height={subButton.iconHeight || '20'}
                                  />
                                )}
                                {subButton.lucideIcon &&
                                  LucideIcons[toPascalCase(subButton.lucideIcon) as keyof typeof LucideIcons] &&
                                  (() => {
                                    const LucideIconComponent: any = LucideIcons[toPascalCase(subButton.lucideIcon) as keyof typeof LucideIcons];
                                    return (
                                      <LucideIconComponent
                                        className={`cursor-pointer ${subButton.color} ${subButton.className}`}
                                        width={subButton.iconWidth || '20'}
                                        height={subButton.iconHeight || '20'}
                                      />
                                    );
                                  })()}
                                <p className="text-nowrap">{subButton.label}</p>
                                {subButton.element}
                              </div>
                              {/* divider between sub-items */}
                              {button.dropDown.length - 1 > subIndex && <hr className="dark:border-gray-500" />}
                            </div>
                          ))
                        ) : (
                          <div
                            className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex gap-2 items-center"
                            onClick={() => {
                              handleBtnClick(button);
                              setIsDropdownOpen(false);
                            }}
                          >
                            {button.icon && <Icon className={`cursor-pointer ${button.color}`} width={'20'} {...button} />}
                            {button.customIcon && (
                              <CustomIcon
                                definedIcon={button.customIcon}
                                className={`cursor-pointer ${button.color} ${button.className}`}
                                width={button.iconWidth || '20'}
                                height={button.iconHeight || '20'}
                              />
                            )}
                            {button.lucideIcon &&
                              LucideIcons[toPascalCase(button.lucideIcon) as keyof typeof LucideIcons] &&
                              (() => {
                                const LucideIconComponent: any = LucideIcons[toPascalCase(button.lucideIcon) as keyof typeof LucideIcons];
                                return (
                                  <LucideIconComponent
                                    className={`cursor-pointer ${button.color} ${button.className}`}
                                    width={button.iconWidth || '20'}
                                    height={button.iconHeight || '20'}
                                  />
                                );
                              })()}
                            <p className="text-nowrap">{button.label}</p>
                            {button.element}
                          </div>
                        )}
                        {remainingButtons.length - 1 > index && <hr className="dark:border-gray-500" />}
                      </div>
                    ))
                  ) : (
                    <div className="px-4 py-3 text-gray-500 dark:text-gray-400 text-sm">No additional actions available</div>
                  )}
                </div>,
                document.body
              );
            })()}
        </div>
      )}
    </div>
  );
  return buttonsData;
};
