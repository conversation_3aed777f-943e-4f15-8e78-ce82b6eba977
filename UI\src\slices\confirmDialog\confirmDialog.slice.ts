import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import type { RootState } from '../../..';

interface confirmDialogState {
  message: React.ReactNode;
  options: {
    onConfirm?: () => void;
    onClose?: () => void;
    confirmLabel?: string;
    cancelLabel?: string;
    [key: string]: any;
  } | null;
}
const initialState: confirmDialogState = {
  message: null,
  options: null,
};

const confirmDialogSlice = createSlice({
  name: 'confirmDialog',
  initialState,
  reducers: {
    showConfirm: (state, { payload }: PayloadAction<confirmDialogState>) => {
      state.message = payload.message;
      state.options = payload.options;
    },
    hideConfirm: (state) => {
      state.options?.onClose?.();
      state.message = null;
      state.options = null;
    },
  },
});

export default confirmDialogSlice.reducer;
export const { hideConfirm, showConfirm } = confirmDialogSlice.actions;
export const confirmDialogState = (state: RootState) => state.confirmDialog.options;
