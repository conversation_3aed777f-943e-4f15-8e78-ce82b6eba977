import React, { FC, useEffect, useState } from 'react';
import { TextInput, Icon } from 'src';
import { Dialog, Button } from 'UI';

// Components
import { CategoryCardData } from './card/category-card-data';
import { Api, useValidate, hideConfirm, Form, showConfirm, useAppDispatch, initializeForm, useAppSelector, RootState, setFieldValue } from 'UI/src';
import { setErrorNotify, setNotifyMessage } from 'UI';
import { useFormik } from 'formik';

type BlockDetails = {
  blockIdDetails: string;
  titleDetails: string;
};

type TestDetails = {
  _id: string;
  title?: string;
  difficulty: number;
  numOfQuestions: number;
  duration: number;
};

type SubmissionsCreationBlockDialogProps = {
  refresh: (force?: boolean) => void;
  blockDetails: BlockDetails | null;
  setBlockDetails: (details: BlockDetails | null) => void;
  loading: boolean;
  setCreateBlockVisibility: (visible: boolean) => void;
  setCreateTestDialogVisibility: (visible: boolean) => void;
  handleEditBlockTest: (test: TestDetails) => void;
};

export const SubmissionsCreationBlockDialog: FC<SubmissionsCreationBlockDialogProps> = ({
  refresh,
  blockDetails,
  setBlockDetails,
  loading,
  setCreateBlockVisibility,
  setCreateTestDialogVisibility,
  handleEditBlockTest,
}) => {
  // UI Hooks
  const dispatch = useAppDispatch();
  const { isRequired, isNotSpaces } = useValidate();

  // State
  const [testDetails, setTestDetails] = useState<TestDetails[] | undefined>();

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);

  const formik = useFormik({
    initialValues: {
      title: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });
  const handleGet = async () => {
    if (!blockDetails?.blockIdDetails) return;
    try {
      const response = await Api.get(`/templates/template/list/${blockDetails.blockIdDetails}`, {});
      setTestDetails(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    }
  };

  const handleInsert = async () => {
    try {
      await Api.post(`/templates/block`, {
        title: form.title,
      });
      refresh(true);
      setCreateBlockVisibility(false);
      dispatch(setNotifyMessage('Category created successfully!'));
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    } finally {
      setBlockDetails(null);
    }
  };

  const handleUpdate = async () => {
    if (!blockDetails?.blockIdDetails) return;
    try {
      await Api.put(`/templates/block/${blockDetails.blockIdDetails}`, {
        title: form.title,
      });
      refresh(true);
      setCreateBlockVisibility(false);
      dispatch(setNotifyMessage('Category updated successfully!'));
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    } finally {
      setBlockDetails(null);
    }
  };

  const ConfirmTextBlock = () => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="hugeicons:archive-02" className="text-red-600" width="40" />
          </div>
        </div>
        <p>Once confirmed, This Category will be archived permanently!</p>
      </div>
    );
  };
  const handleDeleteBlock = async () => {
    setCreateBlockVisibility(false);
    // Use onClose function in provider
    dispatch(
      showConfirm({
        message: ConfirmTextBlock(),
        options: {
          onConfirm: async () => {
            if (!blockDetails?.blockIdDetails) return;
            try {
              await Api.delete(`/templates/block/${blockDetails.blockIdDetails}`, {});
            } catch (error: any) {
              dispatch(setErrorNotify(error.response.data.message));
            } finally {
              refresh(true);
              dispatch(hideConfirm());
              setBlockDetails(null);
            }
          },
          onClose: () => setCreateBlockVisibility(true),
        },
      })
    );
  };

  const ConfirmTextBlockTest = () => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="hugeicons:archive-02" className="text-red-600" width="40" />
          </div>
        </div>
        <p>Once confirmed, This Test will be archived permanently!</p>
      </div>
    );
  };
  const handleDeleteBlockTest = async (blockId: string, testId: string) => {
    setCreateBlockVisibility(false);
    // Use onClose function in provider
    dispatch(
      showConfirm({
        message: ConfirmTextBlockTest(),
        options: {
          onConfirm: async () => {
            try {
              await Api.delete(`/templates/template/single/${testId}`, {
                blockId: blockId,
              });
            } catch (error: any) {
              dispatch(setErrorNotify(error));
            } finally {
              refresh(true);
              dispatch(hideConfirm());
              setCreateBlockVisibility(true);
            }
          },
          onClose: () => setCreateBlockVisibility(true),
        },
      })
    );
  };

  const onClose = () => {
    setCreateBlockVisibility(false);
    setBlockDetails(null);
  };

  useEffect(() => {
    if (blockDetails?.blockIdDetails) {
      handleGet();
      dispatch(setFieldValue({ path: 'title', value: blockDetails.titleDetails }));
    }
  }, []);

  return (
    <Dialog isOpen size="lg" title={blockDetails?.blockIdDetails ? 'Update Template' : 'Create New Template'} onClose={onClose}>
      <Form className="space-y-4" onSubmit={blockDetails?.blockIdDetails ? handleUpdate : handleInsert}>
        <TextInput
          name="title"
          label="Template Title"
          placeholder="Enter name"
          value={form.title}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'title', value }))}
          validators={[isRequired(), isNotSpaces()]}
          validatorsScroll={true}
        />

        {blockDetails?.blockIdDetails &&
          testDetails?.map((singleTest) => {
            return (
              <CategoryCardData
                key={singleTest._id}
                test={singleTest}
                blockDetails={blockDetails}
                isEditMode={true}
                handleEditBlockTest={handleEditBlockTest}
                handleDeleteBlockTest={handleDeleteBlockTest}
                setCreateBlockVisibility={setCreateBlockVisibility}
                setCreateTestDialogVisibility={setCreateTestDialogVisibility}
                creationSubmissionsDialog={() => {}} // Dummy function to satisfy prop type
              />
            );
          })}

        {/* Buttons */}
        <div className="flex gap-2">
          {blockDetails?.blockIdDetails && (
            <Button colorType="destructive" label="Delete" loading={loading} disabled={loading} onClick={handleDeleteBlock} />
          )}
          <Button
            type="submit"
            label={blockDetails?.blockIdDetails ? 'Update' : 'Create Template'}
            colorType="primary"
            className="w-full"
            loading={loading}
            disabled={loading}
          />
        </div>
      </Form>
    </Dialog>
  );
};
