import { createSlice } from '@reduxjs/toolkit';
import { sendManualReply } from '../../middlewares/interview.middleware';
import type { RootState } from '../../store';

interface startAndResultType {
  currentQuestionIndex: number;
  availableSkips: number;
  chat: [];
  transcript: any;
  currentQuestion: any;
  processStatus: any;
}

interface InterviewState {
  interviewValues: {
    isShowApplicantForm: boolean;
    isRecording: boolean;
    isFinished: boolean;
    recordingMode: boolean;
    isPaused: boolean;
    isChatVisible: boolean;
    loadingProgression: number;
    recordingTime: number;
    textAnswer: string;
    start: startAndResultType;
    result: startAndResultType;
  };
  previousInterviewValues: { valus: InterviewState[]; count: number };
}

const initialState: InterviewState = {
  interviewValues: {
    isShowApplicantForm: false,
    isRecording: false,
    isFinished: false,
    recordingMode: false,
    isPaused: false,
    isChatVisible: false,
    loadingProgression: 0,
    recordingTime: 0,
    textAnswer: '',
    start: {
      currentQuestionIndex: 0,
      availableSkips: 0,
      chat: [],
      transcript: undefined,
      currentQuestion: undefined,
      processStatus: undefined,
    },
    result: {
      currentQuestionIndex: 0,
      availableSkips: 0,
      chat: [],
      transcript: undefined,
      currentQuestion: undefined,
      processStatus: undefined,
    },
  },
  previousInterviewValues: { valus: [], count: 0 },
};

const savePrevious = (state: InterviewState) => {
  state.previousInterviewValues.count++;
  localStorage.setItem('previousInterviewValues', JSON.stringify(state.previousInterviewValues));
};

const interviewSlice = createSlice({
  name: 'interview',
  initialState,
  reducers: {
    setIsRecording: (state, { payload }) => {
      state.interviewValues.isRecording = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.isRecording = payload;
      savePrevious(state);
    },
    setIsFinished: (state, { payload }) => {
      state.interviewValues.isFinished = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.isFinished = payload;
      savePrevious(state);
    },
    setRecordingMode: (state, { payload }) => {
      state.interviewValues.recordingMode = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.recordingMode = payload;
      savePrevious(state);
    },
    setIsPaused: (state, { payload }) => {
      state.interviewValues.isPaused = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.isPaused = payload;
      savePrevious(state);
    },
    setIsChatVisible: (state, { payload }) => {
      state.interviewValues.isChatVisible = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.isChatVisible = payload;
      savePrevious(state);
    },
    setStart: (state, { payload }) => {
      state.interviewValues.start = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.start = payload;
      savePrevious(state);
    },
    setResult: (state, { payload }) => {
      state.interviewValues.result = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.result = payload;
      savePrevious(state);
    },
    setLoadingProgression: (state, { payload }) => {
      state.interviewValues.loadingProgression = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.loadingProgression = payload;
      savePrevious(state);
    },
    setTextAnswer: (state, { payload }) => {
      state.interviewValues.textAnswer = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.textAnswer = payload;
      savePrevious(state);
    },
    setRecordingTime: (state, { payload }) => {
      state.interviewValues.recordingTime = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.recordingTime = payload;
      savePrevious(state);
    },
    setShowApplicantForm: (state, { payload }) => {
      state.interviewValues.isShowApplicantForm = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.isShowApplicantForm = payload;
      savePrevious(state);
    },
    getPreviousInterviewValues: (state) => {
      const backStepsCount = state.previousInterviewValues.count--;
      state.interviewValues = state.previousInterviewValues.valus[backStepsCount].interviewValues;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(sendManualReply.fulfilled, (state, { payload }) => {
      state.interviewValues.result = payload;
      state.interviewValues.textAnswer = '';
      state.interviewValues.recordingMode = false;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.result = payload;
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.textAnswer = '';
      state.previousInterviewValues.valus[state.previousInterviewValues.valus.length - 1].interviewValues.recordingMode = false;
      savePrevious(state);
    });
  },
});

export const interviewState = (state: RootState) => state.interview.interviewValues;
export const {
  setTextAnswer,
  setLoadingProgression,
  setIsChatVisible,
  setIsFinished,
  setIsPaused,
  setIsRecording,
  setRecordingMode,
  setResult,
  setStart,
  setRecordingTime,
  setShowApplicantForm,
} = interviewSlice.actions;
export default interviewSlice.reducer;
