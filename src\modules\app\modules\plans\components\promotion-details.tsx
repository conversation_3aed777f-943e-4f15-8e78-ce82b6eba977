// React
import { useEffect, useState } from 'react';

import { RootState, useAppSelector, UserData, useFetchList, useScreenSize, useValidate } from 'UI/src';

// Core
import { Icon, TestSeniorityLevel, Table, CustomIcon, TestDifficulty, AvarageScore } from 'src';

// Components

// Flowbite

export const PromotionDetails = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Permissions
  const isPermitted = Array.isArray(userData?.role) && userData?.role.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');

  // State
  const [selectAll, setSelectAll] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [dueDate, setDueDate] = useState(() => {
    const result = new Date(startDate);
    result.setDate(result.getDate() + 1);
    return result;
  });
  const [showNote, setShowNote] = useState(true);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);

  // Hooks
  const screen = useScreenSize();
  const { isRequired, validateRegex, minLength, maxLength, countryCodeNumberValid } = useValidate();
  const initialFilters = {};
  // TODO: Markos
  const filterFeedData = Object.keys(initialFilters).map((key) => (key === 'difficulty' ? (initialFilters as any).difficulty?.enum || key : key));
  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh } = useFetchList('applicants/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  });

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  // TODO: table needs to be responsive
  const headerData = ['Sale Name', 'Duration', 'Users', 'Actions'];

  const fakeData = [
    {
      id: Math.random(),
      users: 3,
      name: 'Eid Sale',
      duration: '3 Days',
      engagement: 2,
      lastActiveStatus: 1,
      date: 'Apr 2, 2025 12:00AM - Apr 3 ,2025 3:00PM ',
      actions: (
        <div>
          <div className="flex align-middle items-center gap-8">
            <CustomIcon definedIcon="copy" stroke="#535862" width="20" height="20" />
            <CustomIcon definedIcon="download" stroke="#535862" width="20" height="20" />
            <CustomIcon definedIcon="trash" stroke="#535862" width="20" height="20" />
          </div>
        </div>
      ),
    },
    {
      id: Math.random(),
      name: 'Ramadan Sale',
      duration: '3 Days',
      engagement: 2,
      lastActiveStatus: 1,
      date: 'Apr 2, 2025 12:00AM - Apr 3 ,2025 3:00PM ',
      users: 0,
      actions: (
        <div>
          <div className="flex align-middle items-center gap-8">
            <CustomIcon definedIcon="copy" stroke="#535862" width="20" height="20" />
            <CustomIcon definedIcon="download" stroke="#535862" width="20" height="20" />
            <CustomIcon definedIcon="trash" stroke="#535862" width="20" height="20" />
          </div>
        </div>
      ),
    },
    {
      id: Math.random(),
      name: 'Easter Sale',
      duration: '3 Days',
      lastActiveStatus: 1,
      engagement: 2,
      date: 'Apr 2, 2025 12:00AM - Apr 3 ,2025 3:00PM ',
      users: 1,
      actions: (
        <div>
          <div className="flex align-middle items-center gap-8">
            <CustomIcon definedIcon="copy" width="20" stroke="#535862" height="20" />
            <CustomIcon definedIcon="download" stroke="#535862" width="20" height="20" />
            <CustomIcon definedIcon="trash" stroke="#535862" width="20" height="20" />
          </div>
        </div>
      ),
    },
  ];

  const handleEnagement = (engagement: number) => {
    if (engagement === 1) {
      return {
        text: 'active',
        color: 'text-green-700',
        background: 'bg-green-100',
        border: 'border-green-300',
      };
    }
  };

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Promotion List"
        searchPlaceholder="Search for promotions..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        // filterFeedData={filterFeedData}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={fakeData}
        backupRows={backupList}
        slots={{
          saleName: (_: any, row: any) => {
            return <div className="whitespace-nowrap text-[14px] font-normal text-[#535862] dark:text-white">{row?.name}</div>;
          },
          duration: (_: any, row: any) => {
            return (
              <div className="flex flex-col gap-2">
                <div className="flex gap-3 items-center">
                  <span className="text-sm font-normal text-[#535862]">{row?.duration}</span>
                  <div
                    className={`${handleEnagement(row?.lastActiveStatus)?.background || ''} ${handleEnagement(row?.lastActiveStatus)?.border || ''} ${
                      handleEnagement(row?.lastActiveStatus)?.color || ''
                    } rounded-full w-fit px-2 py-0.5 border capitalize text-sm`}
                  >
                    {handleEnagement(row?.lastActiveStatus)?.text || ''}
                  </div>
                </div>
                <div>
                  <p className="text-sm font-normal text-[#535862]">{row?.date}</p>
                </div>
              </div>
            );
          },
          users: (_: any, row: any) => {
            return <div className="capitalize font-semibold text-base text-[#667085] truncate">{row?.users}</div>;
          },
          averageScore: (_: any, row: any) => {
            return (
              <div className="w-fit">
                <AvarageScore score={row?.averageScore} />
              </div>
            );
          },
          actinos: (_: any, row: any) => {
            return <div className="capitalize font-semibold text-base text-[#667085] truncate">{row?.actions}</div>;
          },
        }}
        columns={[
          {
            key: 'saleName',
            label: 'Sale Name',
            primary: true,
            width: '22%',
          },
          {
            key: 'duration',
            label: 'Duration',
            // primary: true,
            width: '15%',
          },
          {
            key: 'users',
            label: 'Users',
            // primary: true,
            width: '10%',
          },

          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_: any, row: any) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/assessment-status/assessment-report/screening/${row?._id}`,
                },
                {
                  label: 'New User',
                  customIcon: 'newUser',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  // path: `/app/applicants/progress/`,
                },
              ];
            },
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
        noDataFound={{
          customIcon: 'applicant',
          message: 'No assessment created yet',
        }}
        placeholder={{
          title: 'No Promotion created yet',
          // subTitle: 'Start by adding a billing plan to manage payments and subscriptions.',
          image: '/UI/src/assets/placeholder/NoBillings.svg',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // addButtonLabel=""
        // onClickAdd={() => {}}
        // actions={[]}
        hideJumbotron
        isScrollableTabsExists
        addButtonPermission
      />
    </>
  );
};
