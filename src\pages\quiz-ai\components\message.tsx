// React
import { memo } from 'react';

// Core
import { AiAvatarModels } from 'UI/src';

// Hooks
import { useTypingEffect } from 'UI/src/hooks/use-typing-effect';
import { RootState, useAppSelector } from 'UI/src';

const Message = ({ message, isLastMessage, hideIcon }: any) => {
  const { loading, submissionAi } = useAppSelector((state: RootState) => state.submissionAi);

  // Debug logging to track re-renders
  const selectedAiModel: any = AiAvatarModels?.find((value) => value?.value === submissionAi?.interview?.avatarName);

  const displayedText = useTypingEffect(isLastMessage && !loading ? message.text : message.text);

  return (
    <div className={`flex items-start gap-2 mr-1 break-all overflow-auto ${message.type === 1 ? '' : 'ml-auto max-w-64 rounded-xl p-2'}`}>
      {!hideIcon && message.type === 1 && (
        <img src={`/assets/models/${selectedAiModel?.iconPath}`} className="size-8 min-w-8 object-cover rounded-full" alt={selectedAiModel?.name} />
      )}

      <div
        className={`grid h-full px-3 py-2 rounded-xl text-[#1B1F3B] thepassBthree ${
          !hideIcon && (message.type === 1 ? 'bg-[#F9F8FA] rounded-tl-none border border-[#DEE2E4]' : 'bg-[#F1E9FE] rounded-tr-none')
        }`}
      >
        {displayedText}
      </div>

      {!hideIcon && message.type !== 1 && (
        <div className="thepassBone text-[#743AF5] flex justify-center items-center rounded-full size-10 bg-[#F1E9FE]">
          {submissionAi?.applicant?.name?.charAt(0)}
        </div>
      )}
    </div>
  );
};

export default memo(Message);
