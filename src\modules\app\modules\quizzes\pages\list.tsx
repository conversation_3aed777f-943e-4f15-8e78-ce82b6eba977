// React
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFetchList, useScreenSize, hideConfirm, RootState, showConfirm, useAppSelector, UserData, QuizDifficulty } from 'UI/src';

// React icons
import { FaUserGraduate, FaUser, FaStar, FaMedal, FaTrophy } from 'react-icons/fa';

// Core
import { Table, Icon, EnumText, SubscribeDialog, DurationFieldColumn } from 'src';

// Components
import { SubmissionsCreationDialog } from '../../submissions/components/creation-dialog';

// Flowbite
import { Tooltip } from 'flowbite-react';
import { Api, useAppDispatch } from 'UI/src';
import { setNotifyMessage, setErrorNotify } from 'UI';

// Types

type ShowMoreMap = { [key: string]: boolean };
type QuizRow = {
  _id: string;
  title: string;
  categoryName?: string;
  difficulty: number;
  numOfQuestions: number;
  duration: number;
  authorName: string;
  [key: string]: any;
};

export const QuizzesListPage = () => {
  // Hooks
  const screen = useScreenSize();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  // State
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState<boolean>(false);
  const [needSubscription, setNeedSubscription] = useState<boolean>(false);
  const [back, setBack] = useState<boolean>(false);
  const [testId, setTestId] = useState<string | null>(null);
  const [showMoreMap, setShowMoreMap] = useState<ShowMoreMap>({});
  const [backupList, setBackupList] = useState<QuizRow[]>([]);

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);
  const isSuperAdmin =
    Array.isArray(userData?.role) && userData?.role.some((role: any) => ['super-admin'].includes(typeof role === 'string' ? role : role.name));

  // Permissions
  const isPermitted =
    Array.isArray(userData?.role) &&
    userData?.role.some((role: any) => ['super-admin', 'admin', 'content-creator'].includes(typeof role === 'string' ? role : role.name));
  const isPermittedAllAdmins =
    Array.isArray(userData?.role) &&
    userData?.role.some((role: any) => ['super-admin', 'admin'].includes(typeof role === 'string' ? role : role.name));
  const isPermittedContentCreator =
    Array.isArray(userData?.role) && userData?.role.some((role: any) => ['content-creator'].includes(typeof role === 'string' ? role : role.name));
  const isPermittedAuthor = (rowAuthorName: string) => {
    return userData.name === rowAuthorName;
  };

  // Hooks
  const { ready, loading, setLoading, list, count, search, pagination, filters, refresh } = useFetchList('templates/list?type=test', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: {
      difficulty: {
        label: 'Difficulty',
        enum: 'QuizDifficulty',
      },
      // ...(userData.trackId
      //   ? {}
      //   : {
      category: {
        label: 'Category',
        lookup: 'category',
      },
      // }),
      subCategory: {
        label: 'Sub Category',
        lookup: 'subcategory',
        parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: null },
      },
      topic: {
        label: 'Topic',
        lookup: 'topic',
        parentLookup: { key: 'subCategory', fieldName: 'subcategoryId' },
      },
      // scope: {
      //   label: 'Scope',
      //   enum: 'Scope',
      // },
    },
  });

  const closeSubmissionsCreationDialog = () => {
    setCreateDialogVisibility(false);
    setTestId(null);
  };

  const backButton = () => {
    // setShowFullInfo(true);
    setCreateDialogVisibility(false);
  };

  const ConfirmText = (value?: number) => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="hugeicons:archive-02" className="text-[#9061F9]" width="40" />
          </div>
        </div>
        {value ? (
          <p>
            Once confirmed, {value} test{value > 1 && 's'} will be archived permanently!
          </p>
        ) : (
          <p>Once confirmed, This test will be archived permanently!</p>
        )}
      </div>
    );
  };

  // Delete Quizz
  const handleDelete = async (row: QuizRow) => {
    dispatch(
      showConfirm({
        message: ConfirmText(),
        options: {
          onConfirm: async () => {
            try {
              await Api.delete(`/templates/single/${row._id}`);
              dispatch(hideConfirm());
              refresh();
              dispatch(setNotifyMessage('Quiz deleted successfully!'));
            } catch (error: any) {
              dispatch(hideConfirm());
              dispatch(setErrorNotify(error.response.data.message));
            }
          },
        },
      })
    );
  };

  // Delete all selected ids
  const handleArchiveSelectedIds = async () => {
    if (selectedIds.length) {
      dispatch(
        showConfirm({
          message: ConfirmText(selectedIds.length),
          options: {
            onConfirm: async () => {
              try {
                setLoading(true);
                await Api.delete('quizzes/multi', { ids: selectedIds });
                setSelectedIds([]);
                refresh();
                dispatch(setNotifyMessage('Quizzes deleted successfully!'));
              } catch (error: any) {
                dispatch(setErrorNotify(error.response.data.message));
              } finally {
                dispatch(hideConfirm());
                setLoading(false);
              }
            },
          },
        })
      );
    }
  };

  // Assign test
  const handleAssignTest = (row: QuizRow) => {
    setTestId(row._id);
    setCreateDialogVisibility(true);
  };

  useEffect(() => {
    if (backupList.length === 0 && list.length > 0) {
      setBackupList(list as QuizRow[]);
    }
  }, [list, backupList.length]);

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Tests List"
        searchPlaceholder={screen.customScreen ? 'Search by name or author' : 'Name or author'}
        addButtonLabel={isPermitted ? 'Create Test' : ''}
        // Assing Test secondary action button
        // actions={[
        //   ...(isPermittedAllAdmins
        //     ? [{ label: 'Assign Test', icon: 'material-symbols:assignment-add-outline', onClick: () => setCreateDialogVisibility(true) }]
        //     : []),
        // ]}
        addButtonPath={true && '/app/tests/create'}
        addButtonPermission={isPermitted}
        onClickAdd={() => {
          if (true) {
          } else {
            setNeedSubscription(true);
          }
        }}
        rows={list as QuizRow[]}
        backupRows={backupList}
        count={count}
        search={search}
        pagination={pagination}
        filters={filters}
        slots={{
          title: (_: any, row: QuizRow) => {
            const element = row.title;
            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div
                    className={`text-[#101828] font-medium text-[14px] capitalize dark:text-grayTextOnDarkMood lg:truncate ${
                      !showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'
                    }`}
                  >
                    {element}
                  </div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
          numOfQuestions: (_: any, row: QuizRow) => {
            return (
              <span className="flex items-center space-x-1 font-medium text-[14px]">
                <span className="text-[#667085] font-medium text-sm dark:text-grayTextOnDarkMood">{row.numOfQuestions}</span>
                <span className="ml-1 text-[#667085] font-medium text-sm dark:text-grayTextOnDarkMood">Questions</span>
              </span>
            );
          },
          duration: (_: any, row: QuizRow) => <DurationFieldColumn duration={row?.duration} />,
          categoryName: (_: any, row: QuizRow) => {
            const element = row.categoryName;
            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div
                    className={`rounded-full py-1 text-[#667085] dark:text-grayTextOnDarkMood text-[15px] font-medium capitalize lg:truncate ${
                      !showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'
                    }`}
                  >
                    {element}
                  </div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
          difficulty: (_: any, row: QuizRow) => {
            let difficultyIcon;
            let difficultyColor;
            let iconSize = 'text-sm';
            switch (row.difficulty) {
              case 1:
                difficultyIcon = <FaUserGraduate className={`${iconSize} text-teal-700`} />; // Intern
                difficultyColor = ' text-teal-700 ';
                break;
              case 2:
                difficultyIcon = <FaUser className={`${iconSize} text-sky-800`} />; // Fresh
                difficultyColor = 'text-sky-800 ';
                break;
              case 3:
                difficultyIcon = <FaStar className={`${iconSize} text-amber-700`} />; // Junior
                difficultyColor = ' text-amber-700 ';
                break;
              case 4:
                difficultyIcon = <FaMedal className={`${iconSize} text-orange-700`} />; // Mid-level
                difficultyColor = 'text-orange-700';
                break;
              case 5:
                difficultyIcon = <Icon icon="solar:crown-star-bold" width="16" className={`text-red-800`} />; // Senior
                difficultyColor = 'text-red-800';
                break;
              default:
                difficultyIcon = null;
            }
            return (
              <span className={`inline-flex items-center  py-1 text-sm font-medium rounded-full capitalize ${difficultyColor}`}>
                <span className="mr-1 flex items-center justify-center">{difficultyIcon}</span>
                {/* <EnumText name={'QuizDifficulty'} value={row.difficulty} /> */}
                {QuizDifficulty[row.difficulty]}
              </span>
            );
          },
          authorName: (_: any, row: QuizRow) => {
            const element = row.authorName;
            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div className="text-gray-400 font-normal capitalize dark:text-gray-400 lg:truncate">{element}</div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'title',
            label: 'Test Name',
            primary: true,
            width: '18%',
          },
          ...(!userData.trackId
            ? [
                {
                  key: 'categoryName',
                  label: 'Category',
                  // lookup: 'category',
                  width: '14%',
                  primary: true,
                },
              ]
            : []),
          {
            key: 'difficulty',
            label: 'Difficulty',
            width: '12%',
            primary: true,
            // enum: 'QuizDifficulty',
          },
          {
            key: 'numOfQuestions',
            label: 'Total Questions',
            width: '12%',
          },
          {
            key: 'duration',
            label: 'Duration',
            width: '12%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_: any, row: QuizRow) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/tests/view/${row._id}`,
                },
                ...(isPermittedAllAdmins
                  ? [
                      {
                        label: 'Assign Test',
                        color: 'text-black dark:text-white',
                        customIcon: 'assign',
                        iconWidth: '22',
                        iconHeight: '22',
                        onClick() {
                          handleAssignTest(row);
                        },
                      },
                    ]
                  : []),
                ,
                isPermittedAuthor(row.authorName) && {
                  label: 'Archive',
                  iconWidth: '18',
                  color: 'text-black dark:text-white',
                  customIcon: 'archive',
                  onClick() {
                    handleDelete(row);
                  },
                },
              ];
            },
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}
        // noDataFound={{
        //   icon: 'healthicons:i-exam-multiple-choice-outline',
        //   message: 'No tests created yet',
        // }}
        placeholder={{
          title: 'No tests created yet',
          subTitle: 'Start by creating a test to evaluate applicant`s skills.',
          image: '/UI/src/assets/placeholder/TestImagePlaceholder.svg',
        }}
        showMoreMap={undefined}
        setShowMoreMap={setShowMoreMap}
      />
      {isCreateDialogVisible && (
        <SubmissionsCreationDialog testId={testId || ''} back={back} backButton={backButton} onClose={closeSubmissionsCreationDialog} />
      )}
      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}
    </>
  );
};
