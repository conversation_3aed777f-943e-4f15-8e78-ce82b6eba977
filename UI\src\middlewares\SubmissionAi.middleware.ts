import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';

export const fetchSubmissionAi = createAsyncThunk('submissionAi/fetch', async (id: string, { rejectWithValue }) => {
  try {
    const response = await Api.get(`ai-interview/single/details/${id}`, {});
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to fetch submission');
  }
});

export const logInterviewEvent = createAsyncThunk('submissionAi/logEvent', async (payload: Record<string, any>, { rejectWithValue }) => {
  try {
    const response = await Api.post('logs/create', payload);
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to log event');
  }
});
