// React
import { useEffect, useState } from 'react';
import { Navigate, useNavigate, useParams } from 'react-router-dom';
// Flowbite
import { Datepic<PERSON>, Tooltip } from 'flowbite-react';

import { RootState, SubscriptionStatus, useAppSelector, UserData } from 'UI/src';

// Core
import { Icon, EnumText, Table, NameFieldColumn, EmailFieldColumn, FormatDateFieldColumn } from 'src';
import { setNotifyMessage } from 'UI';
import { useAppDispatch, useFetchList, useScreenSize, BillingCycle, Tags } from 'UI/src';

export const TransactionsList = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);
  const dispatch = useAppDispatch();

  // Permissions
  const isPermitted = Array.isArray(userData?.role) && userData?.role.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);

  // Hooks
  const navigate = useNavigate();
  const screen = useScreenSize();
  const { id } = useParams();

  const { ready, loading, count, list, search, filters, setFilters, pagination } = useFetchList(`/subscription/list`, {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: {
      status: {
        label: 'Status',
        enum: 'SubscriptionStatus',
      },
    },
  });

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  return (
    <>
      {/* Export button */}
      {/* <div className="flex justify-end">
        <button className="inline-flex items-center justify-center h-10 gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
          <Icon icon="ion:filter" width="22" />
          <span className="hidden sm:block">Export</span>
        </button>
      </div> */}

      <Table
        ready={ready}
        loading={loading}
        title="Transactions List"
        searchPlaceholder="Search by org name or email..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        columns={[
          {
            key: 'paymentId',
            label: 'Invoice',
            width: '16%',
            primary: true,
          },
          {
            key: 'organizationName',
            label: 'Name',
            width: '16%',
            primary: true,
          },
          {
            key: 'organizationEmail',
            label: 'Email',
            width: '16%',
            primary: true,
            className: 'w-full',
          },
          {
            key: 'planName',
            label: 'Purchase',
            // primary: true,
            width: '15%',
            inline: true,
          },
          {
            key: 'type',
            label: 'Type',
            // primary: true,
            width: '10%',
            inline: true,
          },
          {
            key: 'amount',
            label: 'Amount',
            width: '13%',
            inline: true,
          },

          {
            key: 'status',
            label: 'Status',
            width: '10%',
            primary: true,
            inline: true,
          },

          {
            key: 'createdAt',
            label: 'Date',
            width: '14%',
            inline: true,
          },
          // {
          //   key: 'actions',
          //   label: 'Actions',
          //   width: '5%',
          //   buttons: () => [
          //     {
          //       label: 'Download',
          //       customIcon: 'cloudArrowDown',
          //       color: 'text-black dark:text-white',
          //       onClick: () => navigate(`/app/organizations/profile/${id}`),
          //     },
          //   ],
          // },
        ]}
        slots={{
          organizationName: (_: string, row: { _id: string }) => <NameFieldColumn id={row?._id} name={_} showMoreMap={showMoreMap} />,
          organizationEmail: (_: string, row: { _id: string }) => <EmailFieldColumn id={row?._id} email={_} showMoreMap={showMoreMap} />,
          paymentId: (value: string) => {
            return (
              <div className="flex items-center gap-2">
                <p className="text-[#1f2937] dark:text-white font-medium truncate">{value || '—'}</p>
                <Tooltip
                  content="Copy Invoice ID"
                  placement="bottom"
                  arrow={false}
                  className="text-xs bg-gray-700 dark:bg-gray-200 dark:text-gray-900"
                >
                  <span
                    onClick={() => {
                      navigator.clipboard.writeText(value);
                      dispatch(setNotifyMessage('Invoice ID copied'));
                    }}
                    className="cursor-pointer"
                  >
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M20 8H10C8.89543 8 8 8.89543 8 10V20C8 21.1046 8.89543 22 10 22H20C21.1046 22 22 21.1046 22 20V10C22 8.89543 21.1046 8 20 8Z"
                        stroke="#A47BFA"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M4 16C2.9 16 2 15.1 2 14V4C2 2.9 2.9 2 4 2H14C15.1 2 16 2.9 16 4"
                        stroke="#A47BFA"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                </Tooltip>
              </div>
            );
          },
          planName: (_: string, row: any) => {
            return (
              <div className="space-y-2">
                <p className="text-black">{row?.name} Plan</p>
                <p className="text-sm">{BillingCycle[row?.billingCycle]} Subscription</p>
              </div>
            );
          },
          status: (value: number) => {
            const getStatusColor = (type: number) => {
              if (type === 2) {
                // Green
                return 'bg-[#EEFFF1] text-[#056816] ';
              }
              if (type === 4) {
                // Red
                return 'bg-[#FFECE9] text-[#A80000] ';
              }
              if (type === 1) {
                // Yellow
                return 'bg-[#FFFCDF] text-[#BA8500] ';
              }
              if (type === 3) {
                // Blue
                return 'bg-[#F3F4F6] text-[#374151]';
              }
              if (type === 5) {
                return 'bg-[#f3f4f6] text-[#1d4ed8]';
              }
              return 'bg-gray-100 text-gray-700 border border-gray-300';
            };

            return value ? <Tags type={SubscriptionStatus[value]} color={getStatusColor(value)} /> : '—';
          },
          amount: (_: string, row: any) => {
            return (
              <p className="dark:text-white">
                {row?.discountedAmount ? (
                  <>
                    <span className="icon-saudi_riyal"></span> {row?.discountedAmount} {row?.currency}
                  </>
                ) : (
                  '—'
                )}
              </p>
            );
          },
          type: (_: string, row: any) => {
            return (
              <div className="flex flex-col gap-1">
                {row?.couponCode ? (
                  <>
                    <div className="flex items-center text-sm gap-2 capitalize">
                      <span className="w-2 h-2 block bg-yellow-300 rounded-full"></span>
                      <span className="text-[#535862]">Coupon</span>
                    </div>
                    <span className="text-[#8DA0C0] text-[12px] px-3">{row?.couponCode}</span>
                  </>
                ) : (
                  <div className="flex items-center text-sm gap-2 capitalize">
                    <span className="w-2 h-2 block bg-[#A379FC] rounded-full"></span>
                    <span className="text-[#535862]">Norm</span>
                  </div>
                )}
              </div>
            );
          },
          createdAt: (_: string, row: { createdAt: Date }) => <FormatDateFieldColumn date={row?.createdAt} />,
        }}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}

        placeholder={{
          title: 'No subscription plans created',
          subTitle: 'Create your first subscription plan to start offering paid services.',
          image: '/UI/src/assets/placeholder/NoPlans.svg',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        // addButtonLabel=""
        // onClickAdd={() => {}}
        // actions={[]}
        hideJumbotron
        isScrollableTabsExists
        addButtonPermission
      />
    </>
  );
};
