import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api, setErrorNotify, type RootState, type CurrentUserRole} from '../..';

export const getUserRole = createAsyncThunk<CurrentUserRole, void, { state: RootState }>(
  'test/handleGetUserRole',
  async (_, { getState, dispatch }) => {
    const state = getState() as RootState;
    const { user } = state.auth;
    try {
      const { data } = await Api.get(
        `/roles/single/${user.roleId}`
      );
      return (await data) as any;
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  }
);
