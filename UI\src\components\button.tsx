// import React, { useState, type MouseEvent } from 'react';
import React, { type MouseEvent } from 'react';
import { Link } from 'react-router-dom';

import { Spinner } from 'flowbite-react';
import { classNames } from '../utils/tailwind-classes';
// import { CustomIcon } from './custom-icons';
import { CustomIcon } from '../../../src/components/custom-icons';
import type { CustomIconType } from '../../src/types/CustomIcon.type';
// import { SubscribeCard } from '../../../src';
// FIXME: Fix import
// import CheckFeatureManagement from './feature-management';

// Define the variant types for button sizes
export type ButtonVariant = 'xs' | 'sm' | 'md' | 'lg';
export type ButtonColorType = 'primary' | 'secondary' | 'tertiary' | 'destructive' | 'success' | 'magic';
export type ButtonState = 'default' | 'hover' | 'pressed' | 'disabled';
type PermissionType = string | Record<string, any> | null;

// Composables
// import { SubscribeCard } from '../../../src/components/subscribe-card';

interface ButtonProps {
  variant?: ButtonVariant;
  colorType?: ButtonColorType;
  label?: string | React.ReactNode;
  state?: ButtonState;
  icon?: React.ReactNode;
  iconRight?: React.ReactNode;
  onClick?: (event?: MouseEvent<HTMLElement>) => void;
  disabled?: boolean;
  iconWidth?: string;
  iconHeight?: string;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  customIcon?: CustomIconType | string;
  loading?: boolean;
  to?: string;
  permission?: PermissionType;

  children?: React.ReactNode;
}

// Styles for variants (sizes)
const buttonVariantStyles: Record<ButtonVariant, string> = {
  xs: ' h-[30px] px-3 py-2 text-xs gap-1.5 rounded-lg',
  sm: ' h-[37px] px-4 py-2.5 text-sm gap-1.5 rounded-lg',
  md: ' h-[40px] px-5 py-3 text-base gap-2.5 rounded-lg',
  lg: ' h-[51px] px-6 py-4 text-lg gap-2.5 rounded-lg',
};

// Styles for colorType (per state)
const buttonColorTypeStyles: Record<ButtonColorType, Record<ButtonState, string> | string> = {
  primary: 'bg-buttonDefault cursor-pointer text-white hover:bg-buttonHover text-white active:bg-buttonPressed text-white',
  secondary:
    'bg-white text-buttonDefault border border-buttonDefault hover:bg-secondaryBgHover cursor-pointer border border-secondaryHover shadow-[0_0_15.9px_0_#D8D8D8] active:bg-secondaryPressed border border-buttonDefault ',

  tertiary:
    'bg-tertiaryDefault cursor-pointer text-black border border-tertiaryBorder hover:bg-white hover:shadow-[0_0_15.9px_0_#D8D8D8] active:bg-white active:border border-tertiaryBorder ',
  destructive:
    'bg-destructiveDefault cursor-pointer text-white border border-destructiveDefaultBorder hover:bg-destructiveHover hover:text-destructiveDefault hover:shadow-[0_4px_15px_rgba(141,141,141,0.5)] active:bg-destructivePressed active:border border-destructiveDefaultBorder',
  success: 'bg-[#009217] border-green-600 hover:bg-green-700 text-white',
  magic:
    'px-4 py-3 max-sm:py-2 rounded-lg font-medium text-sm text-white bg-gradient-to-r from-[#8484E1] via-[#5F19D5] to-[#4897FF] transition-all duration-300 hover:from-indigo-500 hover:via-purple-700 hover:to-blue-600 focus:outline-none focus:ring-2 focus:ring-purple-800 text-nowrap',
};

// Styles for labels
const buttonLabelStyles: Record<ButtonVariant, string> = {
  xs: 'text-xs font-semibold',
  sm: 'text-sm font-medium',
  md: 'text-sm font-semibold',
  lg: 'text-base font-semibold',
};

const Button: React.FC<ButtonProps> = ({
  variant = 'md',
  colorType = 'primary',
  label = '',
  state = 'default',
  loading = false,
  icon,
  iconRight,
  onClick,
  disabled = false,
  className = '',
  type = 'button',
  children,
  customIcon,
  iconWidth,
  iconHeight,
  to = '',
  permission = null,
}) => {
  // const [needSubscription, setNeedSubscription] = useState<boolean>(false);

  // FIXME: Fix import
  // const { checkFeature } = CheckFeatureManagement();

  const isDisabled = Boolean(disabled || state === 'disabled');
  const currentState: ButtonState = isDisabled ? 'disabled' : state;
  const hasOnlyIcon = !!icon && !label && !children;

  const baseClasses = classNames(
    'font-medium rounded-lg transition-colors duration-200 flex items-center justify-center leading-none',
    hasOnlyIcon ? 'p-2' : 'gap-3'
  );

  const variantClass = hasOnlyIcon ? '' : buttonVariantStyles[variant] || buttonVariantStyles['md'];
  const colorTypeConfig = buttonColorTypeStyles[colorType];
  const disabledClassByColorType: Record<ButtonColorType, string> = {
    primary: 'bg-buttonDisabled text-gray-500 border border-buttonDisabled cursor-not-allowed',
    secondary: 'bg-buttonDisabled text-secondaryTextDisabled border border-secondaryBorderDisabled cursor-not-allowed',
    tertiary: 'bg-buttonDisabled text-gray-500 border border-buttonDisabled cursor-not-allowed',
    destructive: 'bg-buttonDisabled text-gray-500 border border-buttonDisabled cursor-not-allowed',
    success: 'bg-buttonDisabled text-gray-500 border border-buttonDisabled cursor-not-allowed',
    magic: 'bg-buttonDisabled text-gray-500 border border-buttonDisabled cursor-not-allowed',
  };
  const colorTypeClass =
    typeof colorTypeConfig === 'string'
      ? currentState === 'disabled'
        ? disabledClassByColorType[colorType]
        : colorTypeConfig
      : colorTypeConfig[currentState];
  const mergedClassName = isDisabled
    ? classNames(baseClasses, variantClass, className, colorTypeClass)
    : classNames(baseClasses, variantClass, colorTypeClass, className);

  if (to) {
    if (isDisabled) {
      return (
        <span aria-disabled="true" className={mergedClassName}>
          {icon}
          {label && <span className={buttonLabelStyles[variant]}>{label}</span>}
          {customIcon && typeof customIcon !== 'string' && (
            <div className={label ? 'mr-2 self-center' : ''}>
              <CustomIcon
                definedIcon={customIcon.definedIcon}
                width={customIcon.width || iconWidth || '18'}
                height={customIcon.height || iconHeight || '18'}
                className={customIcon.className || 'flex items-center justify-center'}
                stroke={customIcon.stroke!}
                onClick={undefined}
              />
            </div>
          )}
          {loading && <Spinner size="sm" aria-label="Info spinner example" className="me-3" light />}
          {children}
        </span>
      );
    }

    return (
      <Link to={to} className={mergedClassName}>
        {icon}
        {label && <span className={buttonLabelStyles[variant]}>{label}</span>}
        {customIcon && typeof customIcon !== 'string' && (
          <div className={label ? 'mr-2 self-center' : ''}>
            <CustomIcon
              definedIcon={customIcon.definedIcon}
              width={customIcon.width || iconWidth || '18'}
              height={customIcon.height || iconHeight || '18'}
              className={customIcon.className || 'flex items-center justify-center'}
              stroke={customIcon.stroke!}
              onClick={customIcon.onClick}
            />
          </div>
        )}
        {loading && <Spinner size="sm" aria-label="Info spinner example" className="me-3" light />}
        {children}
      </Link>
    );
  }

  return (
    <>
      <button
        type={type}
        className={mergedClassName}
        // onClick={(e: MouseEvent<HTMLElement>) => {
        //   if (permission) {
        //     const hasFeature = checkFeature(permission as any);
        //     if (!hasFeature) {
        //       setNeedSubscription(true);
        //       return;
        //     }
        //   }
        //   onClick?.(e);
        // }}
        onClick={isDisabled ? undefined : onClick}
        disabled={isDisabled}
        // FIXME: Fix import
        // onClick={isDisabled || (permission && !checkFeature(permission as any)) ? undefined : onClick}
        // disabled={isDisabled || !!(permission && !checkFeature(permission as any))}
      >
        {customIcon && typeof customIcon !== 'string' && (
          <div className={label ? 'mr-1 self-center' : ''}>
            <CustomIcon
              definedIcon={customIcon.definedIcon}
              width={customIcon.width || iconWidth || '18'}
              height={customIcon.height || iconHeight || '18'}
              className={customIcon.className || 'flex items-center justify-center'}
              stroke={customIcon.stroke!}
              onClick={isDisabled ? undefined : customIcon.onClick}
            />
          </div>
        )}
        {icon}
        {label && <span className={buttonLabelStyles[variant]}>{label}</span>}
        {iconRight}

        {loading && <Spinner size="sm" aria-label="Info spinner example" className="me-3" light />}
        {children}
      </button>
      {/* {needSubscription && (
        <SubscribeCard key={permission as string} onClose={() => setNeedSubscription(false)} onSubscribe={() => setNeedSubscription(true)} />
      )} */}
    </>
  );
};

export default Button;
