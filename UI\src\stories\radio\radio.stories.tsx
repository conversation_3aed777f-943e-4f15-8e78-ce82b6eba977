import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Radio } from '../../components/radio';

const meta: Meta<typeof Radio> = {
  title: 'Components/Radio',
  component: Radio,
  tags: ['autodocs'],
  argTypes: {
    state: {
      control: 'select',
      options: ['default', 'hover', 'pressed', 'disabled'],
    },
    onChange: { action: 'changed' },
  },
};

export default meta;
type Story = StoryObj<typeof Radio>;

export const Default: Story = {
  args: {
    label: 'Default Radio',
    checked: false,
    state: 'default',
    disabled: false,
  },
};

export const Checked: Story = {
  args: {
    label: 'Checked Radio',
    checked: true,
    state: 'default',
    disabled: false,
  },
};

export const Hover: Story = {
  args: {
    label: 'Hover Radio',
    checked: false,
    state: 'hover',
    disabled: false,
  },
};

export const Pressed: Story = {
  args: {
    label: 'Pressed Radio',
    checked: true,
    state: 'pressed',
    disabled: false,
  },
};

export const Disabled: Story = {
  args: {
    label: 'Disabled Radio',
    checked: true,
    state: 'disabled',
    disabled: true,
  },
};
