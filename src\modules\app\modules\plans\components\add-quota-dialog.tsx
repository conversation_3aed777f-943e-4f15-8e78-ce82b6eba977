import { useFormik } from 'formik';
import { useEffect } from 'react';
import { Di<PERSON>, Button } from 'UI';
import { initializeForm, RootState, setFieldValue, useAppDispatch, useAppSelector } from 'UI/src';

export const AddQuotaDialog = ({ onClose }: { onClose: () => void }) => {
  const svg = {
    warning: (
      <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 5.80469V9.74219" stroke="#D65C62" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
        <path
          d="M15.8072 6.44284V11.5728C15.8072 12.4128 15.3572 13.1928 14.6297 13.6203L10.1747 16.1928C9.44721 16.6128 8.54719 16.6128 7.81219 16.1928L3.35719 13.6203C2.62969 13.2003 2.17969 12.4203 2.17969 11.5728V6.44284C2.17969 5.60284 2.62969 4.82281 3.35719 4.39531L7.81219 1.82281C8.53969 1.40281 9.43971 1.40281 10.1747 1.82281L14.6297 4.39531C15.3572 4.82281 15.8072 5.59534 15.8072 6.44284Z"
          stroke="#D65C62"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path d="M9 12.1445V12.2195" stroke="#D65C62" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ),
    subtract: (
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M14 0.5C21.4558 0.5 27.5 6.54416 27.5 14C27.5 21.4558 21.4558 27.5 14 27.5C6.54416 27.5 0.5 21.4558 0.5 14C0.5 6.54416 6.54416 0.5 14 0.5Z"
          stroke="#7166F0"
        />
        <path d="M9.33594 14H18.6693" stroke="#7166F0" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ),
    add: (
      <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M14 0.5C21.4558 0.5 27.5 6.54416 27.5 14C27.5 21.4558 21.4558 27.5 14 27.5C6.54416 27.5 0.5 21.4558 0.5 14C0.5 6.54416 6.54416 0.5 14 0.5Z"
          stroke="#7166F0"
        />
        <path d="M14.0026 9.33398V18.6673M9.33594 14.0007H18.6693" stroke="#7166F0" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      </svg>
    ),
  };

  const prices = {
    applicants: 20,
    customAvatars: 3,
  };

  const dispatch = useAppDispatch();
  const form = useAppSelector((state: RootState) => state.form.data);

  const formik = useFormik({
    initialValues: {
      applicants: 1,
      customAvatars: 1,
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });
  return (
    <Dialog
      size="xl"
      isOpen
      onClose={onClose}
      title="Add Feature Quota"
      subtitle={<p className="text-[#535862] text-sm font-normal">Control feature availability by assigning usage caps</p>}
      zIndex={999999999999999999}
    >
      <div className="space-y-4">
        <div>
          <p className="text-[#181D27] text-base font-semibold mb-2">Your Plan Features</p>
          <div>
            <div className="grid grid-cols-5 select-none">
              <div className="col-span-3 flex items-center gap-2">
                {svg.warning} <p className="text-base text-[#100A55] font-normal">Applicants</p> <p className="text-[#6B7280] text-xs">2 Remaining</p>
              </div>

              <div className="flex">
                <span
                  className="cursor-pointer"
                  onClick={() => form.applicants > 1 && dispatch(setFieldValue({ path: 'applicants', value: form.applicants - 1 }))}
                >
                  {svg.subtract}
                </span>{' '}
                <span className="w-8 text-center text-base font-semibold">{form.applicants}</span>{' '}
                <span className="cursor-pointer" onClick={() => dispatch(setFieldValue({ path: 'applicants', value: form.applicants + 1 }))}>
                  {svg.add}
                </span>
              </div>

              <p className="text-[#667085] text-xl text-right font-medium">{20 * form.applicants} $</p>
            </div>
          </div>
        </div>

        <div>
          <p className="text-[#181D27] text-base font-semibold mb-2">More Features</p>
          <div>
            <div className="grid grid-cols-5 select-none">
              <div className="col-span-3 flex items-center gap-2">
                <p className="text-base text-[#100A55] font-normal">Custom Avatars</p>
              </div>

              <div className="flex">
                <span
                  className="cursor-pointer"
                  onClick={() => form?.customAvatars > 1 && dispatch(setFieldValue({ path: 'customAvatars', value: form?.customAvatars - 1 }))}
                >
                  {svg.subtract}
                </span>{' '}
                <span className="w-8 text-center text-base font-semibold">{form?.customAvatars}</span>{' '}
                <span className="cursor-pointer" onClick={() => dispatch(setFieldValue({ path: 'customAvatars', value: form?.customAvatars + 1 }))}>
                  {svg.add}
                </span>
              </div>

              <p className="text-[#667085] text-xl text-right font-medium">{3 * form?.customAvatars} $</p>
            </div>
          </div>
        </div>

        <hr />

        <div className="flex justify-between items-center text-2xl font-semibold">
          <h2>Total</h2>
          <h2>{Object.entries(form)?.reduce((acc, [key, value]) => acc + value * (prices as any)[key], 0)} $</h2>
        </div>

        <Button label="Complete Purchase" colorType="primary" className="w-full " onClick={onClose} />
      </div>
    </Dialog>
  );
};
