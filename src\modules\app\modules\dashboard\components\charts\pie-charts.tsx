import React from 'react';

// Recharts
import { <PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';

// UI
import { useScreenSize } from 'UI/src';

type statisticsType = {
  submissionsStatistic: {
    inProgressCount: number;
    submittedCount: number;
    notStartedCount: number;
  };
};

function PieCharts({ statistics }: { statistics: statisticsType }) {
  const screen = useScreenSize();

  let data;
  let COLORS;
  let noSubmissions;
  if (
    statistics.submissionsStatistic.inProgressCount === 0 &&
    statistics.submissionsStatistic.submittedCount === 0 &&
    statistics.submissionsStatistic.notStartedCount === 0
  ) {
    noSubmissions = true;
  } else {
    noSubmissions = false;
  }
  if (noSubmissions) {
    data = [{ name: 'No Submissions', value: 1 }];
    COLORS = ['gray'];
  } else {
    data = [
      { name: 'Not Started', value: statistics.submissionsStatistic.notStartedCount },
      { name: 'In Progress', value: statistics.submissionsStatistic.inProgressCount },
      { name: 'Submitted', value: statistics.submissionsStatistic.submittedCount },
    ];
    COLORS = ['#58A8DC', '#FFA500', '#24C081'];
  }
  return (
    <PieChart width={190} height={screen.lt.sm() ? 130 : 190}>
      <Pie
        data={data}
        cx="50%"
        cy="50%"
        labelLine={false}
        startAngle={0}
        endAngle={360}
        innerRadius={screen.lt.sm() ? 30 : 70}
        outerRadius={screen.lt.sm() ? 50 : 90}
        dataKey="value"
        paddingAngle={0}
        stroke="none"
      >
        {data.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
        ))}
      </Pie>
    </PieChart>
  );
}

export default PieCharts;
