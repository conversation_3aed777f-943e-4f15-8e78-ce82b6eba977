import React from 'react';

import { Radio as FlowbiteRadio, Label, Tooltip } from 'flowbite-react';
import { Icon } from 'src';
import { StaticData } from 'UI/src/';

interface RadioProps {
  name: string;
  selectionValue: string | number;
  value: string | number;
  label?: string;
  labelTooltip?: string;
  labelTooltipStyles?: string;
  onChange: (value: string) => void;
  fullWidth?: boolean;
  pointer?: boolean;
  isCustomLabel?: boolean;
  applicantTestView?: boolean;
  customSize?: string;
  className?: string;
  disabled?: boolean;
  theme?: any;
  lookup?: string;
  required?: boolean;
  outlineDesign?: boolean;
}

export const Radio = ({
  name,
  selectionValue,
  value,
  label,
  labelTooltip,
  labelTooltipStyles,
  onChange,
  fullWidth,
  pointer,
  isCustomLabel,
  applicantTestView,
  customSize = 'sm',
  disabled,
  theme,
  outlineDesign = false,
  ...props
}: RadioProps) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  // Custom theme for the radio button
  const radioTheme = {
    root: {
      base: 'h-5 w-5 border border-gray-300 text-primaryPurple ',
    },
  };

  return (
    <div
      className={`flex items-center gap-2 ${applicantTestView ? 'pl-4 pr-1 border border-[#DEDEDE] rounded-lg' : ''} ${
        applicantTestView && (selectionValue === value ? 'bg-[#F8FAFC] border !border-[#743AF5]' : '')
      }`}
    >
      <FlowbiteRadio
        id={name}
        name={name}
        value={selectionValue}
        checked={selectionValue === value}
        onChange={(value) => (!disabled ? handleChange(value) : undefined)}
        theme={radioTheme}
        {...props}
      />
      <Label htmlFor={name} className={`${fullWidth && 'w-full'} ${pointer && 'cursor-pointer'} overflow-hidden break-words`}>
        {isCustomLabel ? (
          <div className={`${applicantTestView ? '' : 'pl-4 pr-1 border border-[#DEDEDE] rounded-lg'} py-5 thepassBthree`}>
            {/* NB: applicantTestView is for submission of applicant only will be black and white */}
            <span className={`${applicantTestView ? 'text-[#2A3348]' : 'text-inputSubLabel'} inputsLabel`}> {label} </span>
          </div>
        ) : (
          <span className={`${applicantTestView ? 'text-[#374151] dark:text-white' : 'text-inputSubLabel'} text-${customSize} inputsLabel`}>
            {' '}
            {label}{' '}
          </span>
        )}
      </Label>
      {labelTooltip && (
        <Tooltip theme={StaticData.customTooltipTheme} content={labelTooltip} style="auto" className={`border border-white ${labelTooltipStyles}`}>
          <Icon icon="solar:info-circle-outline" className="text-gray-600 dark:text-gray-200" width="18" />
        </Tooltip>
      )}
    </div>
  );
};
