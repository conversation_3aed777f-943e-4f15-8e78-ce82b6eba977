// React
import { useState } from 'react';

// Core
import { Dialog, Button } from 'UI';
import { CustomIcon } from 'src';

// Rsuite
import { DatePicker, DateRangePicker } from 'rsuite';
import { useAppDispatch, useScreenSize } from 'UI/src';
import { setErrorNotify } from 'UI';

interface TimeSettingsDialogProps {
  onClose: () => void;
  startDate: Date;
  setStartDate: (date: Date) => void;
  dueDate: Date;
  setDueDate: (date: Date) => void;
  type: string;
}

export const TimeSettingsDialog = ({ onClose, startDate, setStartDate, dueDate, setDueDate, type }: TimeSettingsDialogProps) => {
  const [localStartDate, setLocalStartDate] = useState(startDate);
  const [localDueDate, setLocalDueDate] = useState(dueDate);

  const dispatch = useAppDispatch();
  const screen = useScreenSize();

  const { beforeToday } = DateRangePicker;

  const handleSubmit = () => {
    if (localStartDate < localDueDate) {
      setStartDate(localStartDate);
      setDueDate(localDueDate);
      onClose();
    } else {
      dispatch(setErrorNotify("Start date can't be greater than due date"));
    }
  };

  return (
    <Dialog size="lg" isOpen onClose={onClose}>
      <div className="space-y-6">
        <div className="space-y-3 flex-col justify-center">
          <CustomIcon definedIcon="hourglass" />
          <p className="text-[#313437] dark:text-white text-lg text-center font-medium capitalize">customize {type} availability</p>
          <p className="text-[#6B7280] dark:text-gray-300 text-center text-[15px] font-medium">
            Applicants can start the {type} at any time within the selected period.
          </p>
        </div>

        <div className="space-y-3">
          <div className="sm:flex space-y-3 sm:space-y-0 items-center gap-5 ">
            <p className="min-w-40 dark:text-white">
              Start Date & Time <span className="text-red-600 ml-[1px]">*</span>
            </p>
            <DatePicker
              format="dd/MM/yyyy hh:mm aa"
              value={localStartDate}
              onChange={(value: Date | null) => setLocalStartDate(value as Date)}
              className="w-full"
              shouldDisableDate={beforeToday()}
              placement="topStart"
              showMeridiem
            />
          </div>

          <div className="sm:flex space-y-3 sm:space-y-0 items-center gap-5">
            <p className="min-w-40 dark:text-white">
              Expiry Date & Time <span className="text-red-600 ml-[1px]">*</span>
            </p>
            <DatePicker
              format="dd/MM/yyyy hh:mm aa"
              value={localDueDate}
              onChange={(value: Date | null) => setLocalDueDate(value as Date)}
              className="w-full"
              shouldDisableDate={beforeToday()}
              placement="topStart"
              showMeridiem
            />
          </div>
        </div>

        <div className="grid grid-cols-6 gap-4">
          <Button className="col-span-2 " colorType="tertiary" label="Cancel" onClick={onClose} />
          <Button colorType="primary" className="col-span-4 capitalize" label={`Confirm ${screen.gt.xs() ? type : ''} Date`} onClick={handleSubmit} />
        </div>
      </div>
    </Dialog>
  );
};
