import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { type RootState } from '../../store';
import { couponsValidate } from '../../middlewares/Plans.middleware';

interface AppState {
  themeColor: 'light' | 'dark';
  language: string;
  isLoading: boolean;
  couponData: any; // Will add the types of the response
}
const initialState: AppState = {
  themeColor: 'light',
  language: 'en',
  isLoading: false,
  couponData: null,
};

const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    setThemeColor(state, action: PayloadAction<'light' | 'dark'>) {
      state.themeColor = action.payload;
      localStorage.setItem('theme', action.payload);
      document.documentElement.setAttribute('data-color-mode', action.payload);
    },

    setAppLanguage(state, action: PayloadAction<string>) {
      state.language = action.payload;
    },

    setIsLoading(state, { payload }: PayloadAction<boolean>) {
      state.isLoading = payload;
    },
  },
  extraReducers: (builder) => {
    builder.addCase(couponsValidate.fulfilled, (state, action: any) => {
      state.couponData = action.payload;
    });
  },
});

export const { setThemeColor, setAppLanguage, setIsLoading } = appSlice.actions;
export const appState = (state: RootState) => state.app;
export default appSlice.reducer;
