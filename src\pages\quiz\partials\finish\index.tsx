import { Icon } from 'src';
import doneMark from 'images/Vector.svg';
export const SubmissionFinish = ({ isPhoneScreening }: { isPhoneScreening: string }) => {
  return (
    <div className="flex min-h-screen flex-1 flex-col items-center px-6 py-8 bg-white dark:bg-gray-900">
      <div className="bg-white dark:bg-gray-900 lg:w-[900px] mx-auto p-10 rounded-xl ">
        <div className="flex flex-col justify-center items-center text-center dark:text-white  shadow-[0px_0px_14px_0px_rgba(195,195,195,0.22)] dark:shadow-[0px_0px_14px_0px_rgba(195,195,195,0.08)] p-12 rounded-xl border">
          <div className="flex justify-center items-center mx-auto mb-7 text-gray-400 dark:text-gray-200">
            <img src={doneMark} alt="done mark" />
          </div>{' '}
          <h1 className="font-bold text-2xl">All Done!</h1>
          <p className="mt-2 text-gray-700 dark:text-gray-200">
            Thank you for completing the technical {isPhoneScreening ? 'screening' : 'test'} . We will review your test and get back to you with our
            feedback as soon as possible.
          </p>
        </div>
      </div>
    </div>
  );
};
