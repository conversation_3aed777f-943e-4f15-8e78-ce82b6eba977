// React
import { useEffect, useState } from 'react';
import { Turtle, Activity } from 'lucide-react';
// Core
import { Api, useAppDispatch, setFieldValue } from 'UI/src';
import { setNotifyMessage, setErrorNotify, AlertNote } from 'UI';
import { Icon, LookupText } from '@/components';

interface Category {
  categoryId: string;
  categoryName: string;
  subCategories: any[];
}

export const CategoriesAssignPage = ({ formData, disableButtons }: any) => {
  // Destructuring
  const { form } = formData || {};
  // State
  const [loading, setLoading] = useState(false);
  const [categoriesData, setCategoriesData] = useState<Category[]>([]);
  // Hooks
  const dispatch = useAppDispatch();

  const handleGetCategories = async () => {
    try {
      setLoading(true);
      const payload: any = {};
      if (form.type === 'interview') payload.type = 'interview';
      const { data } = await Api.get('templates/list/category', payload);
      setCategoriesData(data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryChange = (categoryId: string, categoryName: string) => {
    dispatch(setFieldValue({ path: 'categoryId' as any, value: categoryId }));
    dispatch(setFieldValue({ path: 'categoryName' as any, value: categoryName }));
  };

  useEffect(() => {
    handleGetCategories();
  }, [form.type]);

  useEffect(() => {
    disableButtons.setDisableNextButton(!form.categoryId);
  }, [form.categoryId]);

  // Get selected category data
  const selectedCategory = categoriesData?.find((category: any) => category?.categoryId === form.categoryId);
  const currentSubCategoryIds = form?.subCategoryId || [];

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="w-15 h-15 rounded-full bg-[#F9F8FA] flex justify-center items-center">
          <Icon className="text-[#743AF5]" icon="pepicons-pencil:file" width={'30'} />
        </div>
        <div>
          <p className="thepassSubHone text-[#1B1F3B] font-semibold">Skills Test</p>
          <p className="thepassBtwo text-[#4E5E82]">Select a tailored test Category and start assigning now</p>
        </div>
      </div>
      <div className="space-y-2">
        <p className="thepassHsix font-semibold text-[#1B1F3B]">Tests Categories</p>
        <AlertNote message="Select a test category and start assigning now." />
      </div>
      {!loading ? (
        <div className="space-y-4">
          <div className="flex flex-wrap  gap-4">
            {categoriesData?.map((singleData: any, index: number) => {
              const isSelected = form.categoryId === singleData?.categoryId;
              return (
                <div key={singleData?.categoryId}>
                  <label
                    className={`w-[250px] text-center flex items-start gap-2 drop-shadow-2xl p-5 border rounded-lg cursor-pointer ${
                      isSelected ? 'bg-[#F9F6FF] border-[#8D5BF8]' : 'border-gray-300'
                    }`}
                  >
                    <input
                      type="radio"
                      name="category"
                      value={singleData?.categoryId}
                      checked={isSelected}
                      onChange={() => handleCategoryChange(singleData?.categoryId, singleData?.categoryName)}
                      className="w-4 h-4 text-[#8D5BF8] bg-gray-100 mt-1 border-gray-300 focus:ring-[#8D5BF8] focus:ring-2"
                    />
                    <div className="text-left">
                      <p className="text-lg font-medium capitalize leading-tight">{singleData?.categoryName}</p>
                    </div>
                  </label>
                </div>
              );
            })}
          </div>

          {selectedCategory && selectedCategory?.subCategories?.length > 0 && (
            <div className="mt-8 space-y-5">
              <p className="thepassHsix font-semibold text-[#1B1F3B]">Subcategories</p>
              <div className="flex flex-wrap justify-center gap-3">
                {selectedCategory.subCategories.map((subCategory: any, subIndex: number) => (
                  <p
                    key={subIndex}
                    className={`capitalize border font-medium rounded-lg px-12 py-2 text-lg cursor-pointer transition-colors duration-200 ${
                      currentSubCategoryIds.includes(subCategory?.subCategoryId) ? 'bg-[#F9F6FF] border-[#8D5BF8]' : 'border-gray-300'
                    }`}
                    onClick={() => {
                      const currentIds = Array.isArray(currentSubCategoryIds) ? currentSubCategoryIds : [];
                      const subCategoryId = subCategory?.subCategoryId;

                      let newSubCategoryIds;

                      if (currentIds.includes(subCategoryId)) {
                        newSubCategoryIds = currentIds.filter((id) => id !== subCategoryId);
                      } else {
                        newSubCategoryIds = [...currentIds, subCategoryId];
                      }

                      dispatch(
                        setFieldValue({
                          path: 'subCategoryId',
                          value: newSubCategoryIds.length > 0 ? newSubCategoryIds : null,
                        })
                      );
                    }}
                  >
                    {subCategory?.subCategoryName}
                  </p>
                ))}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="size-12 border-y-2 border-purple-500 animate-spin rounded-full" />
      )}
    </div>
  );
};
