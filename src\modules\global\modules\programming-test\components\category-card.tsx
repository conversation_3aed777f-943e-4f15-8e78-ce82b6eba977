// UI
import { useScreenSize } from 'UI/src';

import { Card } from 'src';

// Flowbite
import { Tooltip } from 'flowbite-react';

// Components
import { CategoryCardData } from './category-card-data';

export const CategoryCard = ({ singleBlock, showMoreInfo, creationSubmissionsDialog }: any) => {
  const screen = useScreenSize();

  return (
    <div className="h-full border border-gray-200 rounded-lg overflow-hidden relative">
      {/* Subcategory Name */}
      <div className="py-4 relative border-gray-200 border-b rounded-t-lg">
        <h2 className="text-2xl font-medium bg-clip-text truncate px-4 capitalize text-[#2A3348]">{singleBlock?.subCategoryName}</h2>

        {screen.gt.lg() && (
          <Tooltip
            content={singleBlock?.subCategoryName}
            placement="bottom"
            arrow={false}
            className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit text-xs"
          >
            <div className="w-full h-full absolute left-0 top-0"></div>
          </Tooltip>
        )}
      </div>

      <div className="px-2">
        {singleBlock?.quiz?.length > 0 ? (
          singleBlock?.quiz
            // URGENTLY: DON'T REMOVE SLICE(0, 3)
            ?.slice(0, 3)
            .map((quiz: any) => <CategoryCardData test={quiz} key={quiz._id} creationSubmissionsDialog={creationSubmissionsDialog} />)
        ) : (
          <p className="text-gray-500">No test available</p>
        )}
      </div>

      {singleBlock?.quiz?.length > 3 && (
        <div className="flex justify-between items-center px-5">
          <span className="text-gray-400 text-sm">(+{singleBlock?.quiz?.length - 3} more)</span>
          <button className="py-2 pl-4 text-base text-gray-500" onClick={() => showMoreInfo(singleBlock?._id)}>
            <span className="underline text-[#743AF5] text-sm">View All</span>
          </button>
        </div>
      )}
    </div>
  );
};
