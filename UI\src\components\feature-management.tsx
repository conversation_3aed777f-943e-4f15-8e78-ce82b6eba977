import { RootState, useAppSelector, UserData } from 'UI/src';

// This should be a custom hook, not a regular function
const CheckFeatureManagement = () => {
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const checkFeature = (featuresPermission: string) => {
    const isSuperAdmin = (userData?.role as any)?.some((role: any) => ['super-admin'].includes(role));
    const hasFeatureAccess = (userData as any)?.features?.[featuresPermission] > 0;

    return isSuperAdmin || hasFeatureAccess;
  };

  return { checkFeature };
};

export default CheckFeatureManagement;
