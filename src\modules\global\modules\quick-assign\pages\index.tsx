// React
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Core
import { Form, Button, useAppSelector, UserData, RootState, useAppDispatch, setFieldValue } from 'UI/src';

// Components
import { TypeQuickAssignPage } from '../components/type';
import { CategoriesAssignPage } from '../components/categories';
import { TestDifficultyAssignPage } from '../components/test-difficulty';
import { AssignTestInterviewDialog } from '../components/assign-dialog';

export const QuickAssignPage = () => {
  // State
  const [activeStage, setActiveStage] = useState(0);
  const [disableNextButton, setDisableNextButton] = useState(false);
  const [testUrlData, setTestUrlData] = useState(null);
  const [isAssignDialogVisible, setAssignDialogVisibility] = useState(false);

  const now = new Date();
  const twoDaysLater = new Date(now.getTime());
  twoDaysLater.setDate(now.getDate() + 2);

  // Hooks
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const form = useAppSelector((state: RootState) => state.form.data);

  const onSubmit = async () => {
    navigate(`/programming-test/single/${form.testId}`);
  };

  const stage = [
    {
      label: 'What would you like to assigin?',
      header: 'Choose the type of assessment that best fits your hiring needs',
      nextButton: 'Show Recommendations',
      component: <CategoriesAssignPage formData={{ form }} disableButtons={{ disableNextButton, setDisableNextButton }} />,
    },
    {
      label: `Recommended Tests`,
      header: `Select a test to start your hiring process `,
      backButton: 'Back',
      nextButton: 'Next',
      component: (
        <TestDifficultyAssignPage
          formData={{ form }}
          disableButtons={{ disableNextButton, setDisableNextButton }}
          setAssignDialogVisibility={setAssignDialogVisibility}
        />
      ),
    },
  ];

  return (
    <Form onSubmit={() => {}}>
      <div className="container mx-auto space-y-10 mb-8">
        <div className="space-y-2 text-center">
          <h1 className="text-[#171618] text-3xl sm:text-[38px] font-semibold">{stage[activeStage]?.label}</h1>
          <p className="text-[#797B7E] text-base sm:text-2xl">{stage[activeStage]?.header}</p>
        </div>

        {/* TypeQuickAssignPage is always visible */}
        <TypeQuickAssignPage formData={{ form }} disableButtons={{ disableNextButton, setDisableNextButton }} stage={activeStage} />

        {/* Render the current stage component (if it exists) */}
        <div className="border w-full border-gray-200 rounded-xl p-6 px-8 dark:border-gray-700 space-y-5">
          {stage[activeStage]?.component}

          {(stage[activeStage]?.backButton || stage[activeStage]?.nextButton) && (
            <div className="sm:flex space-y-3 sm:space-y-0 justify-between gap-4">
              {stage[activeStage]?.backButton && (
                <Button
                  className="w-full sm:w-auto"
                  colorType="secondary"
                  label={stage[activeStage]?.backButton}
                  onClick={() => setActiveStage((prev) => prev - 1)}
                />
              )}

              {stage[activeStage]?.nextButton && (
                <Button
                  label={stage[activeStage]?.nextButton}
                  onClick={() => (activeStage === stage.length - 1 ? onSubmit() : setActiveStage((prev) => prev + 1))}
                  disabled={disableNextButton}
                  className="w-full sm:w-auto ml-auto"
                />
              )}
            </div>
          )}
        </div>
      </div>

      {!!isAssignDialogVisible && (
        <AssignTestInterviewDialog
          formData={{ form }}
          singleData={isAssignDialogVisible}
          onSubmit={onSubmit}
          testUrlData={testUrlData}
          onClose={() => {
            setAssignDialogVisibility(false);
            setTestUrlData(null);

            dispatch(setFieldValue({ path: 'applicantName' as any, value: null }));
            dispatch(setFieldValue({ path: 'applicantEmail' as any, value: null }));
            dispatch(setFieldValue({ path: 'quizId' as any, value: null }));
            dispatch(setFieldValue({ path: 'userEmail' as any, value: null }));
            dispatch(setFieldValue({ path: 'userPassword' as any, value: null }));
          }}
        />
      )}
    </Form>
  );
};
