import React from 'react';
import { EnumText } from 'src';
import { Logs } from 'UI/src';

export const WeiredBehavior = ({
  stage,
}: {
  stage: {
    weirdBehavior: { type: number; data: { keyCode: number }; createdAt: Date }[];
  };
}) => {
  return (
    <>
      {stage?.weirdBehavior?.map((behavior, index: number) => (
        <div className="py-2" key={index}>
          <span className="text-[#111827] dark:text-white font-medium">
            {/* <EnumText name={'Logs'} value={behavior?.type} /> */}
            {Logs[behavior?.type as any]}
          </span>
          {behavior?.type === Logs.KeyboardKeyDown && (
            <span className="break-words text-[#111827] dark:text-white font-medium">
              {behavior?.data?.keyCode === 44 ? 'The PrtSc key (Screenshot)' : null} !!
            </span>
          )}
          <p className="text-grayDetail text-sm">{new Date(behavior?.createdAt).toLocaleString()}</p>
        </div>
      ))}
    </>
  );
};
