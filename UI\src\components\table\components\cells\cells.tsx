import React, { useState } from 'react';
import { Tags } from '../../../tags';
import { Icon } from '../../../../../../src/components/icon';


export const TextCell: React.FC<{ value?: number | string; classname?: string; }> =
  ({ value, classname = '', ...otherProps }) => {
    return <span className={`text-textStroke-primary-900 ${classname}`} {...otherProps}>
      {String(value ?? '')}
    </span>
  }

// AmountCell: simple currency/number formatter using Intl
export const AmountCell: React.FC<{ value?: number | string; currency?: string; locale?: string; minimumFractionDigits?: number }>
  = ({ value, currency = '$', locale = 'en-US', minimumFractionDigits }) => {
    const num = typeof value === 'string' ? Number(value) : (value as number);
    if (!isFinite(num)) return <span className="text-textStroke-primary-900">{String(value)}</span>;
    const formatted = new Intl.NumberFormat(locale, {
      minimumFractionDigits: minimumFractionDigits ?? 0,
    }).format(num);
    return <span className="text-textStroke-primary-900">{formatted} {currency}</span>;
  };

// DateCell: formats to `01 Aug 2025` style
export const DateCell: React.FC<{ value?: string | number | Date; locale?: string }>
  = ({ value = '', locale = 'en-GB' }) => {
    const d = value instanceof Date ? value : new Date(value);
    if (isNaN(d.getTime())) return <span className="text-textStroke-primary-900">{String(value)}</span>;
    const fmt = new Intl.DateTimeFormat(locale, { day: '2-digit', month: 'short', year: 'numeric' }).format(d);
    return <span className="text-textStroke-primary-900">{fmt}</span>;
  };

// StatusCell: uses Tags with the tag key matching your tagsList (paid|pending|failed|...)
export const StatusCell: React.FC<{ status?: string; label?: string }>
  = ({ status = '', label }) => {
    return <Tags type={status}>{label ?? status.charAt(0).toUpperCase() + status.slice(1)}</Tags>;
  };

// CopyCell: shows a value with a copy icon button
export const CopyCell: React.FC<{ text?: string; display?: React.ReactNode }>
  = ({ text = '', display }) => {
    const [copied, setCopied] = useState(false);
    const handleCopy = async () => {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 1200);
    };

    return (
      <div className="flex items-center gap-2">
        <span className="text-textStroke-primary-900">{display ?? text}</span>
        <button
          type="button"
          onClick={handleCopy}
          className="shrink-0 h-7 w-7 rounded-md border border-[#CBB0FF] text-[#8D5BF8] flex items-center justify-center"
          title={copied ? 'Copied' : 'Copy'}
        >
          <Icon icon={copied ? 'tabler:check' : 'solar:copy-bold'} width="16" />
        </button>
      </div>
    );
  };


export const ActionsCell: React.FC<{ classname?: string; iconProps:{ [key: string]: any } }> =
  ({ classname = '', iconProps, ...otherProps }) => {
    return <span className={`text-textStroke-primary-900 ${classname}`} {...otherProps}>
     im here
    </span>
  }
