// React
import { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { CalendarClock, Check, Unlink } from 'lucide-react';

import { format } from 'date-fns';

// Core
import { setNotifyMessage, setErrorNotify } from 'UI';
import { useAppSelector, useAppDispatch, RootState, UserData, updateUser, Api, Regex } from 'UI/src';
import { TextInput, Icon } from 'src';
import { Dialog, Button, CookieStorage, useUserPermissions } from 'UI';

// Components
import { TemplateData } from './template-data';

export const AssignTestInterviewDialog = ({ formData, onClose, singleData, onSubmit, testUrlData }: any) => {
  // Destructuring
  const { form, setFieldValue, setFormValue, resetForm } = formData || {};

  // Redux
  const dispatch = useAppDispatch();
  const {handleGetUserRole} = useUserPermissions();

  // Static Data
  const whatWillYouGet = ['Track applicant’s progress', 'Full report for applicant', 'Evaluate and compare results', 'Extra assessments to assign'];

  // States
  const [showPassword, setShowPassword] = useState(false);
  const [applicantDataSubmitted, setApplicantDataSubmitted] = useState(false);
  const [loading, setLoading] = useState(false);

  // Hooks
  const navigate = useNavigate();

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Signin
  const handleSignin = async () => {
    try {
      setLoading(true);
      const { data } = await Api.post('auth/login', {
        email: form?.userEmail,
        password: form?.userPassword,
      });
      dispatch(setNotifyMessage(`Login succeeded!`));
      // @FIXME: Fix local storage
      CookieStorage.setItem('userData', JSON.stringify(data));
      updateUser(data);
      await handleGetUserRole();
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.statusCode === 401 ? 'Invalid Email or Password' : error?.response?.data?.message));
    } finally {
      setLoading(false);
    }
  };

  const NeedSignIn = () => {
    return (
      <div className="text-center space-y-4">
        {singleData === 'generate' ? (
          <Unlink className="size-16 text-[#8D5BF8] mx-auto" />
        ) : (
          <Icon icon="hugeicons:task-done-02" width="70" className="text-[#8D5BF8]" />
        )}

        <h2 className="text-xl font-medium capitalize">your {singleData === 'generate' ? 'link' : form?.type} is ready</h2>
        <p className="text-[#656C86]">
          You're one step closer to finding the perfect applicant. Just sign up & the {singleData === 'generate' ? 'link' : form?.type} will be
          automatically send to applicant.
        </p>

        <div className="space-y-4 text-left">
          <p className="font-medium">What will you get?</p>

          <div className="grid grid-cols-2 gap-3">
            {whatWillYouGet?.map((phrase) => (
              <div key={phrase} className="flex items-center gap-2">
                <Check className="size-5 text-[#9E77ED]" strokeWidth={2} />
                <p className="text-[#4E4F51]">{phrase}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-center items-center gap-2 p-4 bg-[#F9F6FF] text-[#6B7280] rounded-xl">
          <Icon icon="si:credit-card-line" width="20" />
          <span>No credit card required • 100% Free</span>
        </div>

        <TextInput
          name="userEmail"
          label="Email Address"
          placeholder="Email..."
          value={form.userEmail}
          onChange={setFieldValue('userEmail')}
          requiredLabel
        />

        <div className="flex w-full ">
          <div className="w-full relative">
            <TextInput
              name="userPassword"
              label="Password"
              placeholder="Password..."
              autoComplete="new-password"
              type={showPassword ? 'text' : 'password'}
              value={form.userPassword}
              rightIcon={() => {}}
              onChange={setFieldValue('userPassword')}
              requiredLabel
            />
          </div>

          <div className="mt-[29px] absolute right-1" onClick={() => setShowPassword((prev) => !prev)}>
            <Icon
              className="ml-3 p-5  w-8 h-8 rounded-md cursor-pointer text-gray-400"
              width="25"
              icon={!showPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
            />
          </div>
        </div>

        <Button
          variant="sm"
          label="Login"
          colorType="primary"
          className="w-full  "
          onClick={() => {
            if (!Regex.email.test(form?.userEmail)) dispatch(setErrorNotify('Invalid email'));
            else handleSignin();
          }}
        />

        <p className="text-[#797B7E] cursor-pointer hover:underline" onClick={() => navigate('/auth/register')}>
          Don't have an account? Signup
        </p>
      </div>
    );
  };

  const TestCreatedSucessfully = () => {
    return (
      <div className="text-center space-y-4">
        {!!testUrlData ? (
          <>
            {singleData === 'generate' ? (
              <Unlink className="size-16 text-[#8D5BF8] mx-auto" />
            ) : (
              <Icon icon="lucide:mail-check" width="70" className="text-[#8D5BF8]" />
            )}

            <h2 className="text-xl font-medium capitalize">
              your {singleData === 'generate' ? 'link' : form?.type} has been {singleData === 'generate' ? 'generated' : 'sent'}
            </h2>
            <p className="text-[#656C86]">
              We’ve sent the assessment to {form?.applicantEmail || 'your applicant'}. You can now track their progress
            </p>

            {singleData === 'generate' && (testUrlData?.quizUrl || testUrlData?.interviewUrl) && (
              <div className="text-left space-y-2">
                <p className="text-[#566577]">Share Link</p>
                <div className="flex gap-2">
                  <p className="p-3 bg-[#F8FAFC] text-[#2F2F2F] border border-[#E2E8F0] rounded-lg text-[15px] truncate">
                    {testUrlData?.quizUrl || testUrlData?.interviewUrl}
                  </p>
                  <Icon
                    className="p-3 bg-[#F8FAFC] text-[#6B7280] border border-[#E2E8F0] rounded-lg cursor-pointer"
                    icon="material-symbols:content-copy-outline"
                    width="22"
                    onClick={() => {
                      navigator.clipboard.writeText(testUrlData?.quizUrl || testUrlData?.interviewUrl);
                      dispatch(setNotifyMessage('Email copied!'));
                    }}
                  />
                </div>
              </div>
            )}

            <div className="flex items-center gap-3">
              <CalendarClock width={20} />
              <p className="text-[#3D3F44] text-[14px]">
                Valid until <span className="font-medium">{form.dueDate ? format(new Date(form.dueDate), "MMMM d, yyyy 'at' h:mm a") : '--'}</span>{' '}
              </p>
            </div>

            {singleData !== 'generate' && (
              <Button
                colorType="primary"
                className="w-full"
                label="Track Applicant Progress"
                onClick={() => navigate(`/app/applicants/progress/${testUrlData?.applicantId}`)}
              />
            )}

            <Button className="w-full" colorType="tertiary" label="Continue to Dashboard" onClick={() => navigate('/app')} />
          </>
        ) : (
          <div className="size-12 mx-auto border-y-2 border-purple-500 animate-spin rounded-full" />
        )}
      </div>
    );
  };

  const EnterApplicantDetails = () => {
    return (
      <div className="space-y-4">
        <div className="p-2 bg-[#F9FAFB] rounded-md">
          <TemplateData singleData={singleData} />
        </div>

        <TextInput
          name="applicantName"
          label="Applicant Name"
          placeholder="Applicant name"
          value={form.applicantName}
          onChange={setFieldValue('applicantName')}
          requiredLabel
        />

        <TextInput
          name="applicantEmail"
          label="Applicant Email"
          placeholder="Applicant email"
          value={form.applicantEmail}
          onChange={setFieldValue('applicantEmail')}
          requiredLabel
        />

        <div>
          <div className="p-4 rounded-lg" style={{ background: '#F9F6FF' }}>
            <div className="text-[#6B7280] text-sm font-medium mb-2">Test link is available for 2 days starting from:</div>
            <div className="flex items-center gap-4 text-[#3D3F44] text-lg font-medium">
              <CalendarClock width={20} />
              <span className="text-[15px]">{form.startDate ? format(new Date(form.startDate), "MMMM d, yyyy 'at' h:mm a") : '—'}</span>
              <span className="mx-2">→</span>
              <span className="text-[15px]">{form.dueDate ? format(new Date(form.dueDate), "MMMM d, yyyy 'at' h:mm a") : '—'}</span>
            </div>
          </div>
        </div>

        <Button
          label={`Assign ${form?.type?.charAt(0).toUpperCase() + form?.type?.slice(1)}`}
          className="w-full"
          onClick={() => {
            if (!form.applicantName) dispatch(setNotifyMessage(`Add applicant name`));
            else if (!form.applicantEmail) dispatch(setNotifyMessage(`Add applicant email`));
            else if (!Regex.email.test(form?.applicantEmail)) dispatch(setNotifyMessage(`Invalid email`));
            else setApplicantDataSubmitted(true);
          }}
        />
      </div>
    );
  };

  const handleRenderElement = () => {
    if (singleData === 'generate') {
      if (!userData?.access_token) return NeedSignIn();
      else return TestCreatedSucessfully();
    } else {
      if (applicantDataSubmitted) {
        if (!userData?.access_token) return NeedSignIn();
        else return TestCreatedSucessfully();
      } else return EnterApplicantDetails();
    }
  };

  useEffect(() => {
    handleRenderElement();
    if (userData?.access_token) {
      if (singleData === 'generate' || applicantDataSubmitted) onSubmit();
    }
  }, [applicantDataSubmitted, userData]);

  return (
    <Dialog
      size="xl"
      isOpen
      onClose={onClose}
      title={singleData !== 'generate' && !testUrlData && !applicantDataSubmitted ? `assign ${form?.type ?? ''}` : undefined}
    >
      {handleRenderElement()}
    </Dialog>
  );
};
