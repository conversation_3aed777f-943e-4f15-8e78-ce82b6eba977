import React from 'react';
import Button from './button';
import { PermissionProtectedComponent } from './protected-components';
import { UserPermissions } from '../constants/enums'

export interface ProfileComCardProps {
  // Basic applicant information
  applicantName: string;
  applicantRole?: string;
  applicantLocation?: string;
  joinedDate: string;
  // buttonsData?: any;

  // Contact information
  email: string;
  phoneNumber?: string;

  // Avatar
  avatarSrc?: string;
  gender?: number; // 1 for male, 2 for female
  defaultAvatarFemale?: string;
  defaultAvatarMale?: string;

  // Performance summary
  performanceSummary?: {
    hasIssues: boolean;
    onClick: () => void;
  };

  // Action buttons
  actions?: {
    editProfile?: {
      label?: string;
      className?: string;
      definedIcon?: string;
      onClick: () => void;
    };
    assignToApplicant?: {
      label?: string;
      className?: string;
      onClick: () => void;
      definedIcon?: string;
    };
  };

  // Metadata
  metadata?: {
    createdBy?: string;
    createdDate?: string;
    updatedDate?: string;
  };

  // Styling
  className?: string;
}

export const ProfileComCard: React.FC<ProfileComCardProps> = ({
  applicantName,
  applicantRole,
  applicantLocation,
  joinedDate,
  email,
  phoneNumber,
  avatarSrc,
  gender = 1,
  defaultAvatarFemale,
  defaultAvatarMale,
  performanceSummary,
  actions,
  metadata,
  // buttonsData,
  className = '',
}) => {
  // Default avatar images - use props or fallback to relative paths
  const femaleAvatar = defaultAvatarFemale || '../../../../../../src/images/avatar-female.svg';
  const maleAvatar = defaultAvatarMale || '../../../../../../src/images/avatar-male.svg';

  const avatarImage = avatarSrc || (gender === 2 ? femaleAvatar : maleAvatar);

  const handleJoinDate = (date: string) => {
    if (!date) return '-';
    try {
      const [mon, day, year] = new Date(date).toDateString().slice(4).split(' ');
      return `${day} ${mon}, ${year}`;
    } catch (error) {
      return '-';
    }
  };

  const handleCopyToClipboard = (text: string, type: string) => {
    navigator.clipboard.writeText(text);
    // You can add a toast notification here if needed
  };

  return (
    <div className={`bg-opacity-30 p-2 pb-2 border-b border-[#DEE2E4] ${className}`}>
      <div className="flex flex-col sm:flex-row w-full align-middle px-3 gap-4">
        <div className="flex flex-col lg:flex-row justify-between w-full items-start">
          <div className="space-y-3 w-full">
            <div className="flex flex-wrap gap-5 xsmd:justify-between">
              {/* name and seniority level */}
              <div className="flex flex-row items-center gap- mx-auto xsmd:mx-0 w-full">
                {/* avatar image */}
                <div className="xsmd:min-w-28  mx-auto ">
                  <img src={avatarImage} className="w-20 h-20 rounded-full" alt="Avatar" />
                </div>

                {/* Applicant Name, button popUp */}
                <div className="flex flex-col sm:flex-row items-start sm:justify-between w-full gap-3">
                  <div className="flex flex-col items-start space-y-1">
                    <h2 className="text-2xl pb-1 capitalize w-full sm:max-w-xl mt-0 sm:mt-1 sm:text-start truncate whitespace-nowrap font-bold dark:text-white">
                      {applicantName}
                    </h2>

                    {/* Location and Joined Date */}
                    <div className="sm:flex hidden flex-wrap  flex-col sm:flex-row items-center sm:items-start gap-2 sm:gap-4 text-sm text-[#667085] dark:text-linaDarkHalfGray">
                      {/* Location */}
                      {applicantLocation && (
                        <div className="flex items-center gap-1">
                          <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path
                              d="M20 10C20 14.993 14.461 20.193 12.601 21.799C12.4277 21.9293 12.2168 21.9998 12 21.9998C11.7832 21.9998 11.5723 21.9293 11.399 21.799C9.539 20.193 4 14.993 4 10C4 7.87827 4.84285 5.84344 6.34315 4.34315C7.84344 2.84285 9.87827 2 12 2C14.1217 2 16.1566 2.84285 17.6569 4.34315C19.1571 5.84344 20 7.87827 20 10Z"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                          <span>{applicantLocation}</span>
                        </div>
                      )}

                      {/* Joined Date */}

                      <div className="flex items-center gap-1">
                        <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" />
                        </svg>
                        <span>Joined {handleJoinDate(joinedDate)}</span>
                      </div>
                    </div>

                    {/* Performance summary */}
                    {performanceSummary && (
                      <div
                        className={`w-fit text-nowrap flex items-center p-2 gap-2 ${performanceSummary.hasIssues ? 'bg-[#FEF3F2] text-[#C72716]' : 'bg-[#FAFFF5] text-[#0F766E]'
                          } rounded-lg hover:cursor-pointer`}
                        onClick={performanceSummary.onClick}
                      >
                        {performanceSummary.hasIssues && (
                          <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
                          </svg>
                        )}
                        <p className="text-sm font-normal">Performance Summary</p>
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* {buttonsData.map((button: any) => {
                    return <Button className={button.className} colorType={button.colorType} label={button.label} key={button.id} />;
                  })} */}

                  <div className="space-y-2 hidden md:block" style={{ marginTop: window.innerWidth >= 640 ? '20px' : '0px' }}>
                    <div className="flex justify-center sm:justify-start gap-3">
                      {actions?.editProfile && (
                        <PermissionProtectedComponent permissions={UserPermissions.UPDATE_APPLICANT}>
                          <Button
                            label={actions.editProfile.label || 'Edit Profile'}
                            onClick={actions.editProfile.onClick}
                            colorType="tertiary"
                            className={actions.editProfile.className + ' text-nowrap '}
                            customIcon={
                              actions.editProfile.definedIcon
                                ? {
                                  definedIcon: actions.editProfile.definedIcon,
                                  width: '18',
                                  height: '18',
                                  className: 'flex items-center justify-center',
                                  stroke: '#fff',
                                }
                                : undefined
                            }
                          />
                        </PermissionProtectedComponent>
                      )}
                      {actions?.assignToApplicant && (
                        <PermissionProtectedComponent permissions={UserPermissions.ASSIGN_ASSESSMENT}>
                          <Button
                            label={actions.assignToApplicant.label || 'Assign To Applicant'}
                            onClick={actions.assignToApplicant.onClick}
                            className={actions.assignToApplicant.className + ' text-nowrap '}
                            customIcon={
                              actions.assignToApplicant.definedIcon
                                ? {
                                  definedIcon: actions.assignToApplicant.definedIcon,
                                  width: '18',
                                  height: '18',
                                  className: 'flex items-center justify-center',
                                  stroke: '#fff',
                                }
                                : undefined
                            }
                          />
                        </PermissionProtectedComponent>

                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="sm:hidden flex justify-between flex-wrap  flex-row items-center sm:items-start gap-2 sm:gap-4 text-sm text-[#667085] dark:text-linaDarkHalfGray">
              {/* Joined Date */}

              <div className="flex items-center gap-1">
                <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z" />
                </svg>
                <span>Joined {handleJoinDate(joinedDate)}</span>
              </div>

              {/* Location */}
              {applicantLocation && (
                <div className="flex items-center gap-1">
                  <svg className="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path
                      d="M20 10C20 14.993 14.461 20.193 12.601 21.799C12.4277 21.9293 12.2168 21.9998 12 21.9998C11.7832 21.9998 11.5723 21.9293 11.399 21.799C9.539 20.193 4 14.993 4 10C4 7.87827 4.84285 5.84344 6.34315 4.34315C7.84344 2.84285 9.87827 2 12 2C14.1217 2 16.1566 2.84285 17.6569 4.34315C19.1571 5.84344 20 7.87827 20 10Z"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <span>{applicantLocation}</span>
                </div>
              )}
            </div>

            {/* Contact Information */}
            <div className="flex  px-0 gap-4 text-linaHalfGray dark:text-linaDarkHalfGray font-semibold flex-wrap sm:flex-nowrap">
              {/* Email */}
              <div className="flex items-center gap-2 border bg-[#F9FAFB] border-[#EAECF0] rounded-lg px-1 py-1 max-w-full overflow-hidden">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M22 7L13.009 12.727C12.7039 12.9042 12.3573 12.9976 12.0045 12.9976C11.6517 12.9976 11.3051 12.9042 11 12.727L2 7"
                    stroke="#868D9C"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M20 4H4C2.89543 4 2 4.89543 2 6V18C2 19.1046 2.89543 20 4 20H20C21.1046 20 22 19.1046 22 18V6C22 4.89543 21.1046 4 20 4Z"
                    stroke="#868D9C"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>

                <p className="text-[#333] dark:text-gray-300 font-normal truncate">{email}</p>

                <svg
                  onClick={() => handleCopyToClipboard(email, 'Email')}
                  className="cursor-pointer"
                  width="18"
                  height="18"
                  viewBox="0 0 18 18"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15 6H7.5C6.67157 6 6 6.67157 6 7.5V15C6 15.8284 6.67157 16.5 7.5 16.5H15C15.8284 16.5 16.5 15.8284 16.5 15V7.5C16.5 6.67157 15.8284 6 15 6Z"
                    stroke="#1B1F3B"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M3 12C2.175 12 1.5 11.325 1.5 10.5V3C1.5 2.175 2.175 1.5 3 1.5H10.5C11.325 1.5 12 2.175 12 3"
                    stroke="#1B1F3B"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </div>

              {/* Phone */}
              {phoneNumber && (
                <div className="flex items-center gap-2 bg-[#F9FAFB] border border-gray-200 rounded-lg px-3 py-1.5">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M13.832 16.568C14.0385 16.6628 14.2712 16.6845 14.4917 16.6294C14.7122 16.5744 14.9073 16.4458 15.045 16.265L15.4 15.8C15.5863 15.5516 15.8279 15.35 16.1056 15.2111C16.3833 15.0723 16.6895 15 17 15H20C20.5304 15 21.0391 15.2107 21.4142 15.5858C21.7893 15.9609 22 16.4696 22 17V20C22 20.5304 21.7893 21.0391 21.4142 21.4142C21.0391 21.7893 20.5304 22 20 22C15.2261 22 10.6477 20.1036 7.27208 16.7279C3.89642 13.3523 2 8.7739 2 4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H7C7.53043 2 8.03914 2.21071 8.41421 2.58579C8.78929 2.96086 9 3.46957 9 4V7C9 7.31049 8.92771 7.61672 8.78885 7.89443C8.65 8.17214 8.44839 8.41371 8.2 8.6L7.732 8.951C7.54842 9.09118 7.41902 9.29059 7.36579 9.51535C7.31256 9.74012 7.33878 9.97638 7.44 10.184C8.80668 12.9599 11.0544 15.2048 13.832 16.568Z"
                      stroke="#868D9C"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                  <p className="text-gray-900 dark:text-gray-100 font-semibold truncate">{phoneNumber}</p>
                  <svg
                    onClick={() => handleCopyToClipboard(phoneNumber, 'Phone')}
                    className="cursor-pointer"
                    width="16"
                    height="16"
                    viewBox="0 0 18 18"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M15 6H7.5C6.67157 6 6 6.67157 6 7.5V15C6 15.8284 6.67157 16.5 7.5 16.5H15C15.8284 16.5 16.5 15.8284 16.5 15V7.5C16.5 6.67157 15.8284 6 15 6Z"
                      stroke="#1B1F3B"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M3 12C2.175 12 1.5 11.325 1.5 10.5V3C1.5 2.175 2.175 1.5 3 1.5H10.5C11.325 1.5 12 2.175 12 3"
                      stroke="#1B1F3B"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
              )}
            </div>

            {/* Mobile-only buttons at the end */}
            {actions && (
              <div className="mt-3 md:hidden">
                <div className="flex justify-start flex-wrap gap-3">
                  {actions.editProfile && (
                    <Button
                      label={actions.editProfile.label || 'Edit Profile'}
                      onClick={actions.editProfile.onClick}
                      colorType="tertiary"
                      className={actions.editProfile.className + ' text-nowrap '}
                      customIcon={
                        actions.editProfile.definedIcon
                          ? {
                            definedIcon: actions.editProfile.definedIcon,
                            width: '18',
                            height: '18',
                            className: 'flex items-center justify-center',
                            stroke: '#fff',
                          }
                          : undefined
                      }
                    />
                  )}
                  {actions.assignToApplicant && (
                    <Button
                      label={actions.assignToApplicant.label || 'Assign To Applicant'}
                      onClick={actions.assignToApplicant.onClick}
                      className={actions.assignToApplicant.className + ' text-nowrap '}
                      customIcon={
                        actions.assignToApplicant.definedIcon
                          ? {
                            definedIcon: actions.assignToApplicant.definedIcon,
                            width: '18',
                            height: '18',
                            className: 'flex items-center justify-center',
                            stroke: '#fff',
                          }
                          : undefined
                      }
                    />
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
