import { useEffect, useState, useRef } from 'react';
import { useLocation } from 'react-router-dom';
// UI
import { TextInput, Icon } from 'src';
import { Dialog, Button } from 'UI';

import { uploadLogoToS3 } from 'UI/src/services/uploadS3';
import { useValidate, Form, initializeForm, RootState, setFieldValue, useAppDispatch, UserProfile, useAppSelector, Api, Regex } from 'UI/src';
import { setErrorNotify, setNotifyMessage } from 'UI';
import { useFormik } from 'formik';

export const ProfileEditPage = ({ onClose }: { onClose: () => void }) => {
  // State
  const dispatch = useAppDispatch();
  const location = useLocation();

  const [confirmPassword, setConfirmPassword] = useState('');
  const { isRequired, validateRegex, minLength, maxLength, validatePasswordRegex, isNotSpaces } = useValidate();
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showError, setShowError] = useState(false);
  const [loading, setLoading] = useState(false);

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      name: form.name || '',
      email: form.email || '',
      password: '',
      profilePicture: form.profilePicture || '',
      logo: null,
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  // File input reference
  const fileInputRef = useRef<any>(null);
  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get<UserProfile>('auth/profile', {});
      console.log('auth/profile', response.data);

      // Just set the form value with API data
      dispatch(initializeForm(response.data));
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    }
  };

  const handleSubmit = async () => {
    if (!form.image) {
      dispatch(setErrorNotify('Select image!'));
    } else {
      try {
        setLoading(true);
        const key = await uploadLogoToS3(form.image, '', '');
        dispatch(setNotifyMessage('Logo uploaded successfully!'));
        onClose();
      } catch (error) {
        dispatch(setErrorNotify('Failed to upload logo'));
      } finally {
        setLoading(false);
      }
    }

    // Only validate password if user entered one
    if (form.password || confirmPassword) {
      if (!form.password || !confirmPassword) {
        dispatch(setErrorNotify('Please enter both password and confirm password'));
        return;
      }

      if (form.password !== confirmPassword) {
        setShowError(true);
        dispatch(setErrorNotify('Passwords do not match'));
        return;
      }

      if (form.password.length < 6) {
        dispatch(setErrorNotify('Password must be at least 6 characters long'));
        return;
      }
    }

    // Handle logo upload if logo is selected
    if (!form.logo) {
      dispatch(setErrorNotify('Select logo!'));
    } else {
      try {
        setLoading(true);
        const key = await uploadLogoToS3(form.logo, 'orgId', form.name); // Replace 'orgId' with actual org ID
        dispatch(setNotifyMessage('Logo uploaded successfully!'));
        onClose();
      } catch (error) {
        dispatch(setErrorNotify('Failed to upload logo'));
      } finally {
        setLoading(false);
      }
    }
  };

  const handleShow = (type: string) => {
    if (type == 'newPass') setShowNewPassword(!showNewPassword);
    else setShowConfirmPassword(!showConfirmPassword);
  };

  // On mount
  useEffect(() => {
    handleGet();
  }, []);

  useEffect(() => {
    if (confirmPassword === form.password) setShowError(false);
  }, [confirmPassword]);

  return (
    <Dialog size="lg" isOpen title="Edit Profile" onClose={onClose}>
      <Form className="space-y-4" onSubmit={handleSubmit}>
        <div className="grid gap-4">
          {/* Profile Picture */}
          <div className="flex justify-center mb-4">
            <div className="relative">
              <div className="w-24 h-24 rounded-full overflow-hidden border-2 border-gray-200">
                {form.profilePicture ? (
                  <img src={form.profilePicture} alt="Profile" className="w-full h-full object-cover" />
                ) : (
                  <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                    <Icon icon="mdi:account" width="40" className="text-gray-400" />
                  </div>
                )}
              </div>

              {/* Upload Icon with Two-Level Background */}
              <div
                className="absolute bottom-0 right-0 rounded-full p-1 bg-primaryPurple bg-opacity-30 cursor-pointer hover:scale-110 transition-transform duration-200 shadow-md"
                onClick={() => fileInputRef.current.click()}
                title="Change profile picture"
              >
                <div className="rounded-full p-1.5 bg-primaryPurple text-white">
                  <svg width="14" height="12" viewBox="0 0 20 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M6.66797 12.3333L10.0013 9M10.0013 9L13.3346 12.3333M10.0013 9V16.5M16.668 12.9524C17.6859 12.1117 18.3346 10.8399 18.3346 9.41667C18.3346 6.88536 16.2826 4.83333 13.7513 4.83333C13.5692 4.83333 13.3989 4.73833 13.3064 4.58145C12.2197 2.73736 10.2133 1.5 7.91797 1.5C4.46619 1.5 1.66797 4.29822 1.66797 7.75C1.66797 9.47175 2.36417 11.0309 3.49043 12.1613"
                      stroke="#ffffff"
                      strokeWidth="1.66667"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
              </div>

              {/* Hidden File Input */}
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept="image/*"
                onChange={(e: any) => {
                  const file = e.target.files[0];
                  if (file) {
                    // Create a local URL for the selected image
                    const imageUrl = URL.createObjectURL(file);

                    // Update the form state with the new image URL
                    dispatch(setFieldValue({ path: 'profilePicture', value: imageUrl }));
                  }
                }}
              />
            </div>
          </div>

          <div className="flex flex-col gap-6">
            <TextInput
              label="Full Name"
              name="name"
              placeholder="User Name"
              value={form.name}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'name', value }))}
              validators={[isRequired()]}
              requiredLabel
            />

            <TextInput
              label="Email"
              name="email"
              placeholder="Email"
              value={form.email}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'email', value }))}
              validators={[isRequired()]}
              requiredLabel
            />

            <div className="relative">
              <TextInput
                label="Password"
                name="password"
                placeholder="Enter password (optional)"
                type={showNewPassword ? 'text' : 'password'}
                autoComplete="new-password"
                value={form.password}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'password', value }))}
                validators={[]}
                rightIcon={() => {}}
              />
              <span onClick={() => handleShow('newPass')} className="absolute top-9 right-3 cursor-pointer">
                <Icon icon={showNewPassword ? 'mdi:eye-outline' : 'mdi:eye-off-outline'} width="24" />
              </span>
            </div>

            <div className="relative">
              <TextInput
                label="Confirm Password"
                name="confirmPassword"
                placeholder="Re-enter password (optional)"
                type={showConfirmPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={setConfirmPassword}
                validators={[]}
                rightIcon={() => {}}
              />
              <span onClick={() => setShowConfirmPassword(!showConfirmPassword)} className="absolute top-9 right-3 cursor-pointer">
                <Icon icon={showConfirmPassword ? 'mdi:eye-outline' : 'mdi:eye-off-outline'} width="24" />
              </span>
              {form.password !== confirmPassword && confirmPassword && <p className="text-sm text-red-500 mt-1">Passwords do not match</p>}
            </div>
          </div>
        </div>
        <Button colorType="primary" className="w-full" type="submit" label={'Update'} />
      </Form>
    </Dialog>
  );
};
