import React, { useState } from 'react';
import type { FormikProps } from 'formik';
import { Eye, EyeOff, Search, Mail } from 'lucide-react';
import { Link } from 'react-router-dom';
import { classNames } from '../../utils/tailwind-classes';
import { format } from 'date-fns';

interface Props {
  label?: string;
  id: string;
  type: string;
  placeholder: string;
  defaultValue?: string;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  formik: FormikProps<any>;
  rows?: number;
  maxLength?: number;
  minLength?: number;
  disabled?: boolean;
  showForgotPassword?: boolean;
  required?: boolean;
  className?: string;
}

export const TextInput: React.FC<Props> = ({
  label,
  id,
  type,
  placeholder,
  defaultValue,
  startIcon,
  endIcon,
  formik,
  rows,
  maxLength,
  minLength,
  disabled = false,
  showForgotPassword,
  required = true,
  className = '',
}) => {
  const [passwordShowed, setPasswordShowed] = useState<boolean>(false);

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    formik?.handleBlur(e);
  };

  // const formatDate = (dateValue: Date | string) => {
  //   if (!dateValue) return '';
  //   const date = new Date(dateValue);
  //   const year = date.getFullYear();
  //   const month = String(date.getMonth() + 1).padStart(2, '0');
  //   const day = String(date.getDate()).padStart(2, '0');
  //   return `${year}-${month}-${day}`;
  // };

  if (type === 'password') {
    return (
      <div className=" w-full mt-3 flex flex-col items-start">
        <div className="flex items-center justify-between mb-2 w-full">
          <label className="flex items-center text-sm font-medium text-[#1B1F3B]" htmlFor={id}>
            {label}
            {required && <span className="text-red-500">*</span>}
            {formik?.errors?.[id] && formik?.touched?.[id] && <span className="text-[#F13E3E] text-xs font-normal ml-2">invalid</span>}
          </label>
          {showForgotPassword ? (
            <Link to={'/forget-password'} className="text-xs text-default-700">
              {'forget-password'}
            </Link>
          ) : null}
        </div>
        <div className="flex w-full" data-x-password>
          <input
            data-x-field={id}
            type={passwordShowed ? 'text' : 'password'}
            id={id}
            name={id}
            required={required}
            value={formik?.values.password}
            onChange={formik?.handleChange}
            onBlur={handleBlur}
            className={classNames(
              'form-password block w-full rounded-s-md px-4 bg-default-0-50 text-base text-default-900 py-2 ps-3 placeholder:text-default-400 placeholder:text-md transition-colors duration-200',
              disabled
                ? 'placeholder-[#DEE2E4] cursor-not-allowed  border-[0.5px] border-[#DEE2E4] shadow-[0_1px_2px_0_#21779917]'
                : 'placeholder-[#4E5E82] border border-[#DEE2E4] hover:border-[#DEE2E4] hover:bg-[#FFFFFF] hover:shadow-[0_0_9px_0_#743AF51A] focus:border-[#A47BFA] focus:shadow-[0px_1px_2px_0px_#21779917]',
              formik?.errors?.[id] && formik?.touched?.[id] ? 'border border-[#F13E3E] hover:border-[#F13E3E] focus:border-[#F13E3E]' : '',
              className
            )}
            placeholder={'enter-your-password'}
            disabled={disabled}
          />
          <button
            type="button"
            className="password-toggle inline-flex items-center justify-center py-2.5 px-4 border rounded-e-md -ms-px border-default-200 bg-default-0-50 placeholder:text-default-400 placeholder:text-md focus:outline-2 focus:-outline-offset-2 focus:outline-primary-500  sm:text-sm/6"
          >
            {passwordShowed ? (
              <EyeOff className="password-eye-off h-5 w-5 text-default-600" onClick={() => setPasswordShowed((value) => !value)} />
            ) : (
              <Eye className="password-eye-on h-5 w-5 text-default-600" onClick={() => setPasswordShowed((value) => !value)} />
            )}
          </button>
        </div>
        {formik?.errors?.[id] && formik?.touched?.[id] ? (
          <p id="email-error" className="mt-2 text-sm text-red-600">
            {typeof formik?.errors?.[id] === 'string'
              ? (formik?.errors?.[id] as string)
              : (formik?.errors?.[id] as string[])?.map((item) => <>{item}</>)}
          </p>
        ) : null}
      </div>
    );
  }

  if (type === 'email') {
    return (
      <div className="w-full mt-3 flex flex-col items-start">
        {label && (
          <label className="flex items-center text-sm font-medium text-[#1B1F3B] mb-2" htmlFor={id}>
            {label}
            {required && <span className="text-red-500">*</span>}
            {formik?.errors?.[id] && formik?.touched?.[id] && <span className="text-[#F13E3E] text-xs font-normal ml-2">Invalid</span>}
          </label>
        )}
        <div className="relative w-full">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Mail className="h-5 w-5 text-gray-500" />
          </div>
          <input
            id={id}
            name={id}
            type="email"
            required={required}
            value={formik?.values[id]}
            onChange={formik?.handleChange}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            className={classNames(
              'block w-full rounded-lg pl-10 pr-3 py-2 text-base text-default-900 placeholder:text-default-400 transition-colors duration-200',
              disabled
                ? 'placeholder-[#DEE2E4] cursor-not-allowed  border-[0.5px] border-[#DEE2E4] shadow-[0_1px_2px_0_#21779917]'
                : 'placeholder-[#4E5E82] border border-[#DEE2E4] hover:border-[#DEE2E4] hover:bg-[#FFFFFF] hover:shadow-[0_0_9px_0_#743AF51A] focus:border-[#A47BFA] focus:shadow-[0px_1px_2px_0px_#21779917]',
              formik?.errors?.[id] && formik?.touched?.[id] ? 'border border-[#F13E3E] hover:border-[#F13E3E] focus:border-[#F13E3E]' : '',
              className
            )}
          />
        </div>
      </div>
    );
  }

  if (type === 'textarea') {
    return (
      <div className="w-full mt-3 flex flex-col items-start">
        {label ? (
          <label htmlFor={id} className="block text-sm/6 font-medium text-default-900">
            {label} {required && <span className="text-red-500">*</span>}
          </label>
        ) : null}
        <div className="mt-2 grid grid-cols-1 w-full items-center justify-center">
          <textarea
            defaultValue={defaultValue}
            id={id}
            name={id}
            rows={rows}
            maxLength={maxLength}
            minLength={minLength}
            required={required}
            value={formik?.values[id]}
            onBlur={handleBlur}
            onReset={formik?.handleReset}
            placeholder={placeholder}
            onChange={formik?.handleChange}
            disabled={disabled}
            className={classNames(
              'col-start-1 row-start-1 block w-full rounded-md bg-default-0-50 py-2 ps-3 text-base text-default-900 placeholder:text-default-400 placeholder:text-md transition-colors duration-200',
              disabled
                ? 'placeholder-[#DEE2E4] cursor-not-allowed  border-[0.5px] border-[#DEE2E4] shadow-[0_1px_2px_0_#21779917]'
                : 'placeholder-[#4E5E82] border border-[#DEE2E4] hover:border-[#DEE2E4] hover:bg-[#FFFFFF] hover:shadow-[0_0_9px_0_#743AF51A] focus:border-[#A47BFA] focus:shadow-[0px_1px_2px_0px_#21779917]',
              startIcon ? 'sm:pl-9 pl-10' : 'pl-3',
              formik?.errors?.[id] && formik?.touched?.[id] ? 'border border-[#F13E3E] hover:border-[#F13E3E] focus:border-[#F13E3E]' : '',
              className
            )}
          />
          {startIcon ?? null}

          <div
            aria-hidden="true"
            className="pointer-pointer flex col-start-1 row-start-1 me-4 size-5 self-center items-center justify-self-end sm:size-4 absolute z-10 hover:brightness-125"
          >
            {endIcon && formik?.errors[id] === undefined ? endIcon : null}
          </div>
          {/* {formik?.errors?.[id] && formik?.touched?.[id] ? (
            <ExclamationCircleIcon
              aria-hidden="true"
              className="pointer-events-none col-start-1 row-start-1 me-3 size-5 self-center justify-self-end text-red-500 sm:size-4"
            />
          ) : null} */}
        </div>
        {formik?.errors?.[id] && formik?.touched?.[id] ? (
          <p id="email-error" className="mt-2 text-sm text-red-600">
            {typeof formik?.errors?.[id] === 'string'
              ? (formik?.errors?.[id] as string)
              : (formik?.errors?.[id] as string[])?.map((item) => <>{item}</>)}
          </p>
        ) : null}
      </div>
    );
  }

  if (type === 'search') {
    return (
      <div className="w-full mt-3 flex flex-col items-start">
        {label && (
          <label className="flex items-center text-sm font-medium text-[#1B1F3B] mb-2" htmlFor={id}>
            {label}
            {required && <span className="text-red-500">*</span>}
          </label>
        )}
        <div className="relative w-full">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="h-5 w-5 text-gray-500" />
          </div>
          <input
            id={id}
            name={id}
            type="text"
            required={required}
            value={formik?.values[id]}
            onChange={formik?.handleChange}
            onBlur={handleBlur}
            placeholder={placeholder}
            disabled={disabled}
            className={classNames(
              'block w-full rounded-lg pl-10 pr-3 py-2 text-base text-default-900 placeholder:text-default-400 transition-colors duration-200',
              disabled
                ? 'placeholder-[#DEE2E4] cursor-not-allowed  border-[0.5px] border-[#DEE2E4] shadow-[0_1px_2px_0_#21779917]'
                : 'placeholder-[#4E5E82] border border-[#DEE2E4] hover:border-[#DEE2E4] hover:bg-[#FFFFFF] hover:shadow-[0_0_9px_0_#743AF51A] focus:border-[#A47BFA] focus:shadow-[0px_1px_2px_0px_#21779917]',
              formik?.errors?.[id] && formik?.touched?.[id] ? 'border border-[#F13E3E] hover:border-[#F13E3E] focus:border-[#F13E3E]' : '',
              className
            )}
          />
        </div>
      </div>
    );
  }

  if (type === 'otp') {
    const OTP_LENGTH = 4;
    const [otp, setOtp] = useState(Array(OTP_LENGTH).fill(''));

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>, idx: number) => {
      const val = e.target.value.replace(/[^0-9]/g, '');
      if (val.length > 1) return;
      const newOtp = [...otp];
      newOtp[idx] = val;
      setOtp(newOtp);
      formik.setFieldValue(id, newOtp.join(''));
      if (val && idx < OTP_LENGTH - 1) {
      }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, idx: number) => {
      if (e.key === 'Backspace' && !otp[idx] && idx > 0) {
      }
    };

    return (
      <div className="w-full mt-3 flex flex-col items-start">
        {label && (
          <label className="flex items-center text-sm font-medium text-[#1B1F3B] mb-2" htmlFor={id}>
            {label}
            {required && <span className="text-red-500">*</span>}
            {formik?.errors?.[id] && formik?.touched?.[id] && <span className="text-[#F13E3E] text-xs font-normal ml-2">Invalid</span>}
          </label>
        )}
        <div className="flex gap-6">
          {Array.from({ length: OTP_LENGTH }).map((_, idx) => (
            <input
              key={idx}
              type="text"
              inputMode="numeric"
              maxLength={1}
              disabled={disabled}
              value={otp[idx] || ''}
              onChange={(e) => handleChange(e, idx)}
              onKeyDown={(e) => handleKeyDown(e, idx)}
              onBlur={handleBlur}
              className={`w-12 h-12 text-center text-2xl rounded-full outline-none bg-white transition-all duration-200 ${
                disabled
                  ? 'border border-[#DEE2E4] text-[#DEE2E4] bg-white cursor-not-allowed'
                  : formik?.errors?.[id] && formik?.touched?.[id]
                  ? 'border border-[#F13E3E] text-[#F13E3E] hover:border-[#F13E3E] focus:border-[#F13E3E]'
                  : 'border border-[#DEE2E4] text-[#DEE2E4] hover:border-[#DEE2E4] hover:bg-[#FFFFFF] hover:shadow-[0_0_9px_0_#743AF51A] focus:border-[#A47BFA] focus:shadow-[0px_1px_2px_0px_#21779917]'
              }`}
              placeholder="-"
              autoFocus={idx === 0}
            />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full mt-3 flex flex-col items-start">
      {label ? (
        <label htmlFor={id} className="block text-sm/6 font-medium text-default-900">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
      ) : null}
      <div className="mt-2 grid grid-cols-1 w-full bg-default-0-50 rounded-md">
        <input
          defaultValue={defaultValue}
          id={id}
          name={id}
          type={type}
          maxLength={maxLength}
          minLength={minLength}
          required={required}
          value={type === 'date' && formik?.values[id] ? format(formik?.values[id], 'yyyy-MM-dd') : formik?.values[id]}
          onBlur={handleBlur}
          disabled={disabled}
          onReset={formik?.handleReset}
          placeholder={placeholder}
          onChange={formik?.handleChange}
          className={classNames(
            'col-start-1 row-start-1 block w-full rounded-md bg-default-0-50 py-2 ps-3 text-base text-default-900 placeholder:text-default-400 placeholder:text-md transition-colors duration-200',
            disabled
              ? 'placeholder-[#DEE2E4] cursor-not-allowed  border-[0.5px] border-[#DEE2E4] shadow-[0_1px_2px_0_#21779917]'
              : 'placeholder-[#4E5E82] border border-[#DEE2E4] hover:border-[#DEE2E4] hover:bg-[#FFFFFF] hover:shadow-[0_0_9px_0_#743AF51A] focus:border-[#A47BFA] focus:shadow-[0px_1px_2px_0px_#21779917]',
            startIcon ? 'sm:pl-12 pl-10' : 'pl-3',
            formik?.errors?.[id] && formik?.touched?.[id] ? 'border border-[#F13E3E] hover:border-[#F13E3E] focus:border-[#F13E3E]' : '',
            className
          )}
        />
        {startIcon ?? null}
        <div
          aria-hidden="true"
          className="pointer-pointer flex col-start-1 row-start-1 me-4 size-5 self-center items-center justify-self-end sm:size-4 absolute z-10 hover:brightness-125"
        >
          {endIcon && formik?.errors[id] === undefined ? endIcon : null}
        </div>
        {/* {formik?.errors?.[id] && formik?.touched?.[id] ? (
          <ExclamationCircleIcon
            aria-hidden="true"
            className="pointer-events-none col-start-1 row-start-1 me-3 size-5 self-center justify-self-end text-red-500 sm:size-4"
          />
        ) : null} */}
      </div>
      {formik?.errors?.[id] && formik?.touched?.[id] ? (
        <p id="email-error" className="mt-2 text-sm text-red-600">
          {typeof formik?.errors?.[id] === 'string'
            ? (formik?.errors?.[id] as string)
            : (formik?.errors?.[id] as string[])?.map((item) => <>{item}</>)}
        </p>
      ) : null}
    </div>
  );
};
