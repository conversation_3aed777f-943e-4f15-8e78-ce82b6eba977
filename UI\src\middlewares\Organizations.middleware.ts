import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';
import type { OrganizationType } from '../types/Organization.type';

// Fetch single organization
export const fetchOrganization = createAsyncThunk(
  'organizations/fetchOrganization',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get<OrganizationType>(`organizations/single/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch organization');
    }
  }
);

// Update organization
export const updateOrganization = createAsyncThunk(
  'organizations/updateOrganization',
  async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
    try {
      const response = await Api.put(`organizations/single/${id}`, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to update organization');
    }
  }
);

// Get organization activity logs
export const fetchOrganizationActivityLogs = createAsyncThunk(
  'organizations/fetchActivityLogs',
  async (params: any, { rejectWithValue }) => {
    try {
      const response = await Api.get('organizations/activity/logs', params || {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch activity logs');
    }
  }
);

// Get organization engagement
export const fetchOrganizationEngagement = createAsyncThunk(
  'organizations/fetchEngagement',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`organizations/engagement/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch engagement');
    }
  }
);

// Get organization overview
export const fetchOrganizationOverview = createAsyncThunk(
  'organizations/fetchOverview',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`organizations/overview/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch overview');
    }
  }
);

// Get organization growth
export const fetchOrganizationGrowth = createAsyncThunk(
  'organizations/fetchGrowth',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`organizations/growth/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch growth');
    }
  }
);

// Get organization users
export const fetchOrganizationUsers = createAsyncThunk(
  'organizations/fetchUsers',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`organizations/users/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch users');
    }
  }
);

// Get organization plan overview
export const fetchOrganizationPlanOverview = createAsyncThunk(
  'organizations/fetchPlanOverview',
  async (organizationId: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`organizations/plan/overview/${organizationId}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch plan overview');
    }
  }
); 
