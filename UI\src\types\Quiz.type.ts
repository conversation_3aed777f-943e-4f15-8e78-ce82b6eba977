export interface QuizListItem {
  _id: string;
  title: string;
  duration: number;
  createdAt: string;
  updatedAt: string;
  techpassQuiz: true;
  difficulty: number;
  numOfQuestions: number;
  seniorityLevel: number;
  description: string;
  categoryName: string[];
  category: string[];
  subCategory: string[];
  subCategoryName: string[];
  authorId: string;
  authorName: string;
  topicName: string[];
  topic: string[];
}

export type QuizType = QuizListItem & {
  questionIds: string[];
};

export type QuizzesList = {
  items: QuizListItem[];
  count: number;
};

export type QuizzesSearch = {
  _id: string;
  title: string;
  createdAt: string;
}[];
