/* src/app.css (Tailwind v4) */
@import 'tailwindcss';

/* Flowbite (v4 uses CSS plugin directive) */
/* @plugin "flowbite/plugin"; */

/* ===== Design tokens (was: theme.extend) ===== */
@theme {
  /* Breakpoints (was: theme.screens) */
  --breakpoint-xslg: 530px;
  --breakpoint-xsmd: 425px;
  --breakpoint-xssm: 375px;

  /* Background images (was: theme.extend.backgroundImage) */
  --background-image-magic-gradient: linear-gradient(80deg, #3b82f6, #a855f7, #ec4899);

  /* Colors (was: theme.extend.colors) */
  --color-pastelPink: #f4a8b6;
  --color-pastelPurple: #d3b4e0;
  --color-pastelBlue: #a2dff7;
  --color-pastelGreen: #b6f0a1;
  --color-pastelYellow: #f7e0a2;
  --color-pastelOrange: #f9a65a;
  --color-pastelRed: #f9a1a1;
  --color-lightgraybg: #f9f9f9;
  --color-darkGrayBackground: #181720;
  --color-secondaryGray: #838383;
  --color-grayDetail: #6b7280;
  --color-primaryPurple: #7e3af2;
  --color-grayTextOnDarkMood: #d6dbe3;
  --color-darkBackgroundCard: #22212a;
  --color-darkBlueText: #2f327d;
  --color-halfGray: #798296;
  --color-arkHalfGray: #d1d5db;

  /* Primary palette */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;

  /* Charts – light */
  --color-chartPoorCircle: #ef4444;
  --color-chartPoorTextColor: #991b1b;
  --color-chartPoorBackground: #fee2e2;
  --color-chartPoorBorder: #fecaca;

  --color-chartGoodCircle: #d97706;
  --color-chartGoodTextColor: #d97706;
  --color-chartGoodBackground: #fffbeb;
  --color-chartGoodBorder: #d97706;

  --color-chartExcellentCircle: #22c55e;
  --color-chartExcellentTextColor: #24c081;
  --color-chartExcellentBackground: #ebfff0;
  --color-chartExcellentBorder: #e1ffe6;

  /* Charts – dark */
  --color-darkChartPoorCircle: #fca5a5;
  --color-darkChartPoorTextColor: #fca5a5;
  --color-darkChartPoorBackground: #4a1d1d;
  --color-darkChartPoorBorder: #b91c1c;

  --color-darkChartGoodCircle: #fef3c7;
  --color-darkChartGoodTextColor: #fef3c7;
  --color-darkChartGoodBackground: #4e3b12;
  --color-darkChartGoodBorder: #fef3c7;

  /* NOTE: original had missing '#' for 86EFAC */
  --color-darkChartExcellentCircle: #86efac;
  --color-darkChartExcellentTextColor: #86efac;
  --color-darkChartExcellentBackground: #1e3a28;
  --color-darkChartExcellentBorder: #16a34a;

  /* Status (light) */
  --color-statusColorNotStartedText: #667085;
  --color-statusColorNotStartedBackground: #f9fafb;
  --color-statusColorNotStartedBackgroundAssign: #edf1f5;
  --color-statusColorInProgressText: #1976d2;
  --color-statusColorInProgressBackground: #e3f2fdc2;
  --color-statusColorSubmittedText: #10b981;
  --color-statusColorSubmittedBackground: #dcfce7;
  --color-statusColorScheduleldText: #6a1b9a;
  --color-statusColorScheduleldBackground: #f3e5f5c2;
  --color-statusColorMissedText: #c62828;
  --color-statusColorMissedBackground: #ffebee;
  --color-statusColorOverdueText: #fb8c00;
  --color-statusColorOverdueBackground: #fff3e0;

  /* Status (dark) */
  --color-statusDarkColorNotStartedText: #f1faff;
  --color-statusDarkColorNotStartedBackground: #686774;
  --color-statusDarkColorInProgressText: #81d4fa;
  --color-statusDarkColorInProgressBackground: #2d6f8ec7;
  --color-statusDarkColorScheduleldText: #d1b3ff;
  --color-statusDarkColorScheduleldBackground: #684d7dc2;
  --color-statusDarkColorMissedText: #f6cfcb;
  --color-statusDarkColorMissedBackground: #d92d2066;
  --color-statusDarkColorOverdueText: #ffebce;
  --color-statusDarkColorOverdueBackground: #4a3621;

  /* Status Submitted (light + dark) */
  --color-statusColorExcellent: #079455;
  --color-statusBackgroundExcellent: #ecfdf3;
  --color-statusColorGood: #dc6803;
  --color-statusBackgroundGood: #fff6db;
  --color-statusColorPoor: #d92d20;
  --color-statusBackgroundPoor: #fef3f2;

  --color-statusDarkColorExcellent: #ecfdf3;
  --color-statusDarkBackgroundExcellent: #07945580;
  --color-statusDarkColorGood: #fffaeb;
  --color-statusDarkBackgroundGood: #dc680380;
  --color-statusDarkColorPoor: #fef3f2;
  --color-statusDarkBackgroundPoor: #d92d2080;

  /* Inputs */
  --color-inputLabel: #6b7280;
  --color-inputDarkLabel: #d1d5db;
  --color-inputSubLabel: #8c939f;

  /* New Question/Answer */
  --color-newQuAnsText: #8a43f9;
  --color-newQuAnsDarkText: #9ca3af;
  --color-newQuAnsBg: #f6eafc52;
  --color-newQuAnsDarkBg: #1f2937;
  --color-newQuAnsHoverBg: #f6f5ff;

  /* Buttons */
  --color-buttonDefault: #743af5;
  --color-buttonHover: #bfa3fb;
  --color-buttonPressed: #6835ee;
  --color-buttonDisabled: #e8ecf3;

  --color-secondaryHover: #bfa3fb;
  --color-secondaryPressed: #d9c8fc;
  --color-secondaryDisabled: #dee2e4;
  --color-secondaryTextDisabled: #868d9c;
  --color-secondaryBorderDisabled: #dee2e4;

  --color-tertiaryDefault: #ffffff;
  --color-tertiaryBorder: #dee2e4;
  --color-tertiaryBorderDisabled: #dee2e4;
  --color-tertiaryHover: #bfa3fb;
  --color-tertiaryPressed: #6835ee;
  --color-tertiaryDisabled: #f9fafb;

  --color-destructiveDefault: #f13e3e;
  --color-destructiveDefaultBorder: #dee2e4;
  --color-destructiveHover: #f8d7da;
  --color-destructivePressed: #a80000;
  --color-destructiveDisabled: #f1f6ff;
  --color-destructiveTextDisabled: #899cc9;

  /* Fonts (was: theme.fontFamily.sans/body) */
  --font-sans: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  /* If you used a `body` alias before, prefer using `font-sans` or define a class below */
}
@custom-variant dark (&:where(.dark, .dark *));

/* =======================
   THEME TOKENS (v4)
   ======================= */
@theme {
  /* Breakpoints */
  --breakpoint-xslg: 530px;
  --breakpoint-xsmd: 425px;
  --breakpoint-xssm: 375px;

  /* If you still need sm/md/lg/xl, leave Tailwind defaults in place.
     For “range-only” (min+max) variants, see custom variants below. */

  /* Background images */
  --background-image-magic-gradient: linear-gradient(80deg, #3b82f6, #a855f7, #ec4899);

  /* Flat color tokens (flattened from nested objects) */
  --color-pastelPink: #f4a8b6;
  --color-pastelPurple: #d3b4e0;
  --color-pastelBlue: #a2dff7;
  --color-pastelGreen: #b6f0a1;
  --color-pastelYellow: #f7e0a2;
  --color-pastelOrange: #f9a65a;
  --color-pastelRed: #f9a1a1;
  --color-lightgraybg: #f9f9f9;
  --color-darkGrayBackground: #181720;
  --color-secondaryGray: #838383;
  --color-grayDetail: #6b7280;
  --color-primaryPurple: #7e3af2;
  --color-grayTextOnDarkMood: #d6dbe3;
  --color-darkBackgroundCard: #22212a;
  --color-darkBlueText: #2f327d;
  --color-halfGray: #798296;
  --color-arkHalfGray: #d1d5db;

  /* ThePass and different unlist colors */
  --color-thePass-50: #f1e9fe;
  --color-thePass-700: #562ce5;
  --color-category-bg: #f3f4f6;
  --color-category-border: #e5e7eb;

  /* Primary (custom palette) */
  --color-primary-shade-50: #f1e9fe;
  --color-primary-shade-100: #d9c8fc;
  --color-primary-shade-200: #bfa3fb;
  --color-primary-shade-300: #a47bfa;
  --color-primary-shade-400: #8d5bf8;
  --color-primary-normal-500: #743af5;
  --color-primary-tent-600: #6835ee;
  --color-primary-tent-700: #562ce5;
  --color-primary-tent-800: #4426df;
  --color-primary-tent-900: #2119d1;
  --color-primary-disable: #d8d8d8;
  --color-primary-hover: #bfa3fb;
  --color-primary-default: #743af5;
  --color-primary-pressed: #6835ee;
  --color-primary-text: #1B1F3B;

  /* textStroke */
  --color-textStroke-border-100: #dee2e4;
  --color-textStroke-shade-100: #d8d8d8;
  --color-textStroke-shade-200: #a3a7b0;
  --color-textStroke-shade-400: #8b98ac;
  --color-textStroke-secondary-300: #868d9c;
  --color-textStroke-normal-400: #4e5e82;
  --color-textStroke-tent-600: #4a5568;
  --color-textStroke-tent-700: #3a4458;
  --color-textStroke-tent-800: #2a3348;
  --color-textStroke-primary-900: #1b1f3b;
  --color-text-500: #4e5e82;

  /* Semantic sets */
  --color-success-light: #eefff1;
  --color-success-mid: #009217;
  --color-success-dark: #056816;

  --color-danger-light: #ffece9;
  --color-danger-mid: #f13e3e;
  --color-danger-dark: #a80000;
  --color-danger-disable: #d8d8d8;
  --color-danger-hover: #f8d7da;
  --color-danger-default: #f13e3e;
  --color-danger-pressed: #a80000;

  --color-warning-light: #fffcdf;
  --color-warning-mid: #ebcb39;
  --color-warning-dark: #ba8500;

  --color-info-light: #e0f3fb;
  --color-info-mid: #7cccef;
  --color-info-dark: #11abe6;

  /* Charts – light */
  --color-chartPoorCircle: #ef4444;
  --color-chartPoorTextColor: #991b1b;
  --color-chartPoorBackground: #fee2e2;
  --color-chartPoorBorder: #fecaca;

  --color-chartGoodCircle: #d97706;
  --color-chartGoodTextColor: #d97706;
  --color-chartGoodBackground: #fffbeb;
  --color-chartGoodBorder: #d97706;

  --color-chartExcellentCircle: #22c55e;
  --color-chartExcellentTextColor: #24c081;
  --color-chartExcellentBackground: #ebfff0;
  --color-chartExcellentBorder: #e1ffe6;

  /* Charts – dark */
  --color-darkChartPoorCircle: #fca5a5;
  --color-darkChartPoorTextColor: #fca5a5;
  --color-darkChartPoorBackground: #4a1d1d;
  --color-darkChartPoorBorder: #b91c1c;

  --color-darkChartGoodCircle: #fef3c7;
  --color-darkChartGoodTextColor: #fef3c7;
  --color-darkChartGoodBackground: #4e3b12;
  --color-darkChartGoodBorder: #fef3c7;

  /* fix: missing '#' in original */
  --color-darkChartExcellentCircle: #86efac;
  --color-darkChartExcellentTextColor: #86efac;
  --color-darkChartExcellentBackground: #1e3a28;
  --color-darkChartExcellentBorder: #16a34a;

  /* Status (light) */
  --color-statusColorNotStartedText: #667085;
  --color-statusColorNotStartedBackground: #f9fafb;
  --color-statusColorNotStartedBackgroundAssign: #edf1f5;
  --color-statusColorInProgressText: #1976d2;
  --color-statusColorInProgressBackground: #e3f2fdc2;
  --color-statusColorSubmittedText: #10b981;
  --color-statusColorSubmittedBackground: #dcfce7;
  --color-statusColorScheduleldText: #6a1b9a;
  --color-statusColorScheduleldBackground: #f3e5f5c2;
  --color-statusColorMissedText: #c62828;
  --color-statusColorMissedBackground: #ffebee;
  --color-statusColorOverdueText: #fb8c00;
  --color-statusColorOverdueBackground: #fff3e0;

  /* Status (dark) */
  --color-statusDarkColorNotStartedText: #f1faff;
  --color-statusDarkColorNotStartedBackground: #686774;
  --color-statusDarkColorInProgressText: #81d4fa;
  --color-statusDarkColorInProgressBackground: #2d6f8ec7;
  --color-statusDarkColorScheduleldText: #d1b3ff;
  --color-statusDarkColorScheduleldBackground: #684d7dc2;
  --color-statusDarkColorMissedText: #f6cfcb;
  --color-statusDarkColorMissedBackground: #d92d2066;
  --color-statusDarkColorOverdueText: #ffebce;
  --color-statusDarkColorOverdueBackground: #4a3621;

  /* Submitted (light + dark) */
  --color-statusColorExcellent: #079455;
  --color-statusBackgroundExcellent: #ecfdf3;
  --color-statusColorGood: #dc6803;
  --color-statusBackgroundGood: #fff6db;
  --color-statusColorPoor: #d92d20;
  --color-statusBackgroundPoor: #fef3f2;

  --color-statusDarkColorExcellent: #ecfdf3;
  --color-statusDarkBackgroundExcellent: #07945580;
  --color-statusDarkColorGood: #fffaeb;
  --color-statusDarkBackgroundGood: #dc680380;
  --color-statusDarkColorPoor: #fef3f2;
  --color-statusDarkBackgroundPoor: #d92d2080;

  /* Inputs */
  --color-inputLabel: #6b7280;
  --color-inputDarkLabel: #d1d5db;
  --color-inputSubLabel: #8c939f;

  /* New Q/A */
  --color-newQuAnsText: #8a43f9;
  --color-newQuAnsDarkText: #9ca3af;
  --color-newQuAnsBg: #f6eafc52;
  --color-newQuAnsDarkBg: #1f2937;
  --color-newQuAnsHoverBg: #f6f5ff;

  /* Buttons */
  --color-buttonDefault: #743af5;
  --color-buttonHover: #bfa3fb;
  --color-buttonPressed: #6835ee;
  --color-buttonDisabled: #e8ecf3;

  --color-secondaryHover: #bfa3fb;
  --color-secondaryBgHover: #ebebff;
  --color-secondaryPressed: #d9c8fc;
  --color-secondaryDisabled: #dee2e4;
  --color-secondaryTextDisabled: #868d9c;
  --color-secondaryBorderDisabled: #dee2e4;

  --color-tertiaryDefault: #ffffff;
  --color-tertiaryBorder: #dee2e4;
  --color-tertiaryBorderDisabled: #dee2e4;
  --color-tertiaryHover: #bfa3fb;
  --color-tertiaryPressed: #6835ee;
  --color-tertiaryDisabled: #f9fafb;

  --color-destructiveDefault: #f13e3e;
  --color-destructiveDefaultBorder: #dee2e4;
  --color-destructiveHover: #f8d7da;
  --color-destructivePressed: #a80000;
  --color-destructiveDisabled: #f1f6ff;
  --color-destructiveTextDisabled: #899cc9;

  /* Fonts */
  --font-sans: Inter, ui-sans-serif, system-ui, -apple-system, system-ui, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  /* Spacing tokens to power classes like m-custom-xs etc. */
  --spacing-custom-xs: 16px;
  --spacing-custom-sm: 24px;
  --spacing-custom-md: 32px;
  --spacing-custom-lg: 40px;
  --spacing-custom-xl: 48px;
}
@media (min-width: 360px) and (max-width: 575px) {
  .xs-only\:block {
    display: block;
  }
  .xs-only\:hidden {
    display: none;
  }
  /* add any frequently used pairs you want… */
}

/* Usage example: md-only:bg-red-500 */

/* =======================
   UTILITIES (v4 @utility)
   ======================= */

/* Spacing shorthands carried over */
@utility spacing-1 {
  margin: 4px;
}
@utility spacing-2 {
  margin: 8px;
}
@utility spacing-3 {
  margin: 12px;
}
@utility spacing-4 {
  margin: 16px;
}
@utility spacing-6 {
  margin: 24px;
}
@utility spacing-8 {
  margin: 32px;
}
@utility spacing-10 {
  margin: 40px;
}
@utility spacing-12 {
  margin: 48px;
}

/* Hide scrollbars cross-browser */
@utility scrollbar-hidden {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge legacy */
  &::-webkit-scrollbar {
    display: none;
  } /* Safari/Chrome */
}

/* Shadows & surfaces */
@utility shadow-down-table {
  box-shadow: 0px 0px 20px 0px #743af51a;
  background-color: #fff;
}
@utility shadow-field-dropDown {
  background-color: #fff;
  box-shadow: 0px 0px 9px 0px #743af51a;
}
@utility shadow-cards {
  box-shadow: 0px 7px 10px 0px #743af51a;
  background-color: #fff;
}
@utility shadow-hover-button {
  box-shadow: 0px 7px 10px 0px #d8d8d8;
  background-color: #fff;
}
@utility shadow-expanded-sidebar {
  box-shadow: 6px -1px 10.9px 0px #743af51a;
  background-color: #fff;
}
@utility shadow-fixed-button-holder {
  box-shadow: 0px 0px 11.6px 0px #743af51a;
}
@utility shadow-bg-behind-dialog {
  background: #d8d8d85e;
}

/* Word-breaking safety */
@utility native-break-all-words {
  overflow-wrap: anywhere;
  word-break: break-word;
}

/* Type scale utilities (fixed) */
@utility thepassHone {
  font-family: var(--font-sans);
  font-weight: 700;
  font-size: 28px;
}
@utility thepassSubHone {
  font-family: var(--font-sans);
  font-weight: 500;
  font-size: 20px;
}
@utility thepassHtwo {
  font-family: var(--font-sans);
  font-weight: 600;
  font-size: 18px;
}
@utility thepassHthree {
  font-family: var(--font-sans);
  font-weight: 500;
  font-size: 18px;
}
@utility thepassHfour {
  font-family: var(--font-sans);
  font-weight: 400;
  font-size: 16px;
}

@utility thepassHsix {
  font-family: var(--font-sans);
  font-weight: 500;
  font-size: 16px;
}

@utility thepassBone {
  font-family: var(--font-sans);
  font-weight: 600;
  font-size: 14px;
}
@utility thepassBtwo {
  font-family: var(--font-sans);
  font-weight: 500;
  font-size: 14px;
}
@utility thepassBthree {
  font-family: var(--font-sans);
  font-weight: 400;
  font-size: 14px;
}
@utility thepassBfour {
  font-family: var(--font-sans);
  font-weight: 500;
  font-size: 12px;
}

@utility thepassBfive {
  font-family: var(--font-sans);
  font-weight: 400;
  font-size: 12px;
}
@utility inputsLabel {
  @apply thepassBfour;
  color: #1b1f3b;
}

/* Optional font alias if you used font-body before */
.font-body {
  font-family: var(--font-sans);
}

/* ===== Custom utilities (was: plugins/addUtilities) ===== */

/* Hide scrollbars cross-browser */
@utility scrollbar-hidden {
  /* Firefox */
  scrollbar-width: none;
  /* IE/Edge legacy */
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

/* Safe word-breaking for long tokens */
@utility native-break-all-words {
  overflow-wrap: anywhere; /* modern, preferred */
  word-break: break-word; /* fallback */
}

/* Optional: ‘body’ font alias if you used `font-body` classes previously */
.font-body {
  font-family: var(--font-sans);
}

/* Smooth scrolling for hash navigation */
html {
  scroll-behavior: smooth;
}

/* Ensure proper scroll offset for fixed headers */
:target {
  scroll-margin-top: 80px;
}

/* Target the list in the markdown-editor */
.wmde-markdown ul {
  list-style-type: disc;
}

.wmde-markdown ol {
  list-style-type: decimal;
}

.w-md-editor-area .w-md-editor-text {
  min-height: 100% !important;
}

/*
  @TODO: This should be fixed and replaced by classnames
  Because Datepicker don't accept classnames
*/
@media (max-width: 370px) {
  .calendar-left > div:last-of-type {
    left: -30px;
  }
}
@media screen and (min-width: 640px) {
  .calendar div.relative div:last-of-type {
    right: 0;
  }
}
.dark .calendar input {
  background-color: #22212a !important;
}

/* Targting markdown edtior in adding question */
.space-y-2 .container {
  max-width: 100%;
}

/* This will make any dialog in the center of the page in the small screen */
/* because its a flowbite bug */
/* need to enhacne in iphone mobile */
div[role='dialog'] {
  height: auto !important;
}
/*
  @TODO: To be replaced when creating our main components instead of flowbite-react
*/
button[role='switch'] div::after {
  background: white;
  border-radius: 50%;
  transition-duration: 0.1s;
}

/* width */
.custom-scroll::-webkit-scrollbar {
  width: 8px;
}

/* Track */
.custom-scroll::-webkit-scrollbar-track {
  background: transparent;
}

/* Handle */
.dark .custom-scroll::-webkit-scrollbar-thumb {
  background: #22212a;
  border-radius: 999px;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: #ceced4;
  border-radius: 999px;
}

/* Handle on hover */
.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #555;
}

@media print {
  @page {
    size: A4;
    margin: 0;
  }
  /* a[href]:after {
    content: none !important;
  } */
  #print-pdf {
    print-color-adjust: exact;
  }
  html {
    height: 100%;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden;
  }
  body {
    /* page-break-after: always; */
    height: 100vh;
    margin: 48px 20px 24px !important;
    padding: 0 !important;
    overflow: hidden;
  }
  header,
  footer {
    display: none;
  }
  .print-footer {
    display: block;
    position: fixed;
    bottom: 24px;
    width: 100%;
  }

  .print-footer img {
    width: 29px;
  }
}

/* Target the dropdown styles*/
@media screen and (min-width: 640px) {
  div[role='menu'] {
    left: -36px !important;
  }
}

/* @TODO: To remove background color when browser automatically fill the input */
:root {
  --input-bg-light: #fff;
  --input-text-light: #000;
  --input-bg-dark: #374151;
  --input-text-dark: #fff;
}
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px var(--input-bg-light) inset !important;
  -webkit-text-fill-color: var(--input-text-light) !important;
}
.dark input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 30px var(--input-bg-dark) inset !important;
  -webkit-text-fill-color: var(--input-text-dark) !important;
}
/* Todo end */

@-webkit-keyframes fadeInTest {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInTest {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.typing-word {
  display: inline-block;
  -webkit-animation: fadeIn 0.3s ease-in-out;
  animation: fadeIn 0.3s ease-in-out;
}

.rs-picker-popup.rs-picker-popup-date {
  z-index: 111;
}

.rs-input::-webkit-input-placeholder {
  font-size: 14px;
  font-weight: 400;
  color: #6b7280;
}

.rs-input::-moz-placeholder {
  font-size: 14px;
  font-weight: 400;
  color: #6b7280;
}

.rs-input:-ms-input-placeholder {
  font-size: 14px;
  font-weight: 400;
  color: #6b7280;
}

.rs-input::-ms-input-placeholder {
  font-size: 14px;
  font-weight: 400;
  color: #6b7280;
}

.rs-input::placeholder {
  font-size: 14px;
  font-weight: 400;
  color: #6b7280;
}

.rs-input {
  background-color: #f9fafb;
}

.custom-tooltip-spacing {
  transform: translateX(190px);
}

button[aria-selected='true'] {
  padding-bottom: 15px;
  border-bottom: 1px solid #a169fb;
  background-color: transparent !important;
}

button[aria-selected='false'] {
  background-color: transparent !important;
}

.custom-tab-border {
  position: relative;
}

.custom-tab-border::before {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 50%;
  width: 0%;
  height: 2px;
  background-color: #a169fb;
  transition: all 0.2s ease;
}

.custom-tab-border:hover::before {
  width: 100%;
  left: 0;
}

/* Phone number styling */
/* Container with gap between flag and input */
.country-code-container {
  display: flex !important;
  flex-direction: row-reverse;
  gap: 8px;
}

/* Input */
.country-code-container .country-code-input {
  background-color: w;
  border-color: #d1d5db;
  width: 100%;
  height: 42px;
  padding-left: 12px;
  border-radius: 6px;
}
.dark .country-code-container .country-code-input {
  background-color: #374151;
  color: white;
  border-color: #4b5563;
}
.country-code-container .country-code-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button */
.country-code-container .country-code-button {
  padding-right: 11px;
  padding-left: 10px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid #d1d5db;
  height: 42px;
  width: 60px;
  flex-shrink: 0; /* Prevent button from shrinking */
  position: relative; /* Reset position for separated layout */
  border-radius: 6px;
}

.dark .country-code-container .country-code-button {
  background-color: #4b5563;
  border: 1px solid #4b5563;
}
.country-code-container .country-code-button:hover {
  background-color: #e5e7eb;
}
.dark .country-code-container .country-code-button:hover {
  background-color: #4b5563;
}
.country-code-container .country-code-button > div,
.country-code-container .country-code-button > div:hover {
  background-color: transparent;
}
.country-code-container .country-code-button > div:disabled {
  cursor: not-allowed;
}
.country-code-container .country-code-button .selected-flag .flag {
  transform: scale(1.5);
}
.dark .country-code-container .country-code-button .selected-flag .flag .arrow {
  border-top-color: white;
}
.country-code-container .country-code-input:disabled ~ .country-code-button .selected-flag .flag .arrow {
  display: none;
}
.country-code-container .country-code-input:disabled ~ .country-code-button .selected-flag {
  cursor: not-allowed;
}

/* Button List */
.country-code-container .country-list {
  max-width: 230px;
  max-height: 200px;
  overflow-y: auto;
  position: absolute;
  left: 10px;
  z-index: 1050;
  border: 1px solid #d1d5db;
  border-radius: 8px;
}
.country-code-container .country-list .country {
  color: black;
}
.dark .country-code-container .country-list .country {
  background-color: #374151;
  color: white;
  border: 1px solid #696f7a;
}
.dark .country-code-container .country-list .country:hover {
  background-color: #4b5563;
  color: white;
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.dark .country-code-container .country-list .country.highlight {
  background-color: #4b5563;
}
/* End of Phone number styling */

/* Hero Section Animations */
@-webkit-keyframes slideDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}
@keyframes slideDown {
  from {
    transform: translateY(0);
    opacity: 1;
  }
  to {
    transform: translateY(100%);
    opacity: 0;
  }
}

@-webkit-keyframes slideUp {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@-webkit-keyframes slideDownFromTop {
  from {
    transform: translate(-50%, -150%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

@keyframes slideDownFromTop {
  from {
    transform: translate(-50%, -150%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

@-webkit-keyframes slideDownFromTopBaseline {
  from {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

@keyframes slideDownFromTopBaseline {
  from {
    transform: translateX(-50%) translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(-50%) translateY(0);
    opacity: 1;
  }
}

.gradient-text {
  background: linear-gradient(90deg, #8484e1 0%, #4897ff 50%, #5f19d5 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.talent-gradient-text {
  background: linear-gradient(90deg, #c6d7fe 0%, #8098f9 50%, #3e57da 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}
/* End of Hero Section Animations */

/* this is for editing rich-text when read-only  */
.custom-read-only-input .w-md-editor-preview div p {
  color: #6b7280;
}

/* Light and Dark Logo */
.dark-logo {
  display: none;
}
.dark .light-logo {
  display: none;
}
.dark .dark-logo {
  display: block;
}

input[type='checkbox'] {
  -webkit-appearance: checkbox;
  appearance: checkbox;
  accent-color: #743af5;
}
.dark input[type='checkbox'] {
  accent-color: #bfa3fb;
}

input[type='radio'] {
  -webkit-appearance: radio;
  appearance: radio;
  accent-color: #743af5;
}
.dark input[type='radio'] {
  accent-color: #bfa3fb;
}

/* Remove default focus outlines/rings on radios and checkboxes */
input[type='radio']:focus,
input[type='radio']:focus-visible,
input[type='checkbox']:focus,
input[type='checkbox']:focus-visible {
  outline: none !important;
  box-shadow: none !important;
}
