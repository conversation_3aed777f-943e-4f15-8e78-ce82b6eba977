// React
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Core
import { Button } from 'UI';

import { CurrencySymbol } from 'src';
import { useFetchList, RootState, useAppSelector, UserData, PricingPeriod } from 'UI/src';

// Component
import { BasePlanCard } from './base-plan-card';

type TabCountType = {
  base: number;
  [key: string]: number;
};

type BasePlansType = {
  setTabCount?: (updater: (prev: TabCountType) => TabCountType) => void;
  pageType?: string;
};

export const BasePlans = ({ setTabCount, pageType }: BasePlansType) => {
  // State
  const [selectedFilter, setSelectedFilter] = useState(0);

  const navigate = useNavigate();

  // Hooks
  const { ready, loading, setLoading, list, count, filters, setFilters, search, pagination, refresh } = useFetchList('plans/list', {
    search: '',
    pagination: {
      page: 1,
      size: 10,
    },
    id: String(selectedFilter + 1),
  });

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Permissions
  const isPermittedAdmin = Array.isArray(userData?.role)
    ? userData.role.some((role) => role === 'admin')
    : typeof userData?.role === 'object' && userData?.role?.name === 'admin';
  const isSuperAdmin = Array.isArray(userData?.role)
    ? userData.role.includes('super-admin')
    : typeof userData?.role === 'object' && userData?.role?.name === 'super-admin';

  useEffect(() => {
    if (count && setTabCount) {
      setTabCount((prev) => ({
        ...prev,
        base: count,
      }));
    }
  }, [count, setTabCount]);

  useEffect(() => {
    refresh();
  }, [selectedFilter]);

  if (!ready) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 animate-pulse">
        <div className="p-4 space-y-4 border border-gray-300 rounded-2xl">
          <p className="h-4 bg-gray-300 rounded-full" />
          <p className="w-32 h-10 bg-gray-300 rounded-full" />
          <p className="h-1 bg-gray-300 rounded-full" />
          <p className="h-1 bg-gray-300 rounded-full" />
          <p className="h-1 bg-gray-300 rounded-full" />
        </div>
        <div className="p-4 space-y-4 border border-gray-300 rounded-2xl">
          <p className="h-4 bg-gray-300 rounded-full" />
          <p className="w-32 h-10 bg-gray-300 rounded-full" />
          <p className="h-1 bg-gray-300 rounded-full" />
          <p className="h-1 bg-gray-300 rounded-full" />
          <p className="h-1 bg-gray-300 rounded-full" />
        </div>
        <div className="p-4 space-y-4 border border-gray-300 rounded-2xl">
          <p className="h-4 bg-gray-300 rounded-full" />
          <p className="w-32 h-10 bg-gray-300 rounded-full" />
          <p className="h-1 bg-gray-300 rounded-full" />
          <p className="h-1 bg-gray-300 rounded-full" />
          <p className="h-1 bg-gray-300 rounded-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 flex flex-col items-center justify-center w-full">
      {!isSuperAdmin && (
        <div className="text-center space-y-4">
          <h2 className="text-4xl font-medium">Subscription Plans</h2>
          <p className="thepassSubHone text-[#4E5E82]">
            View and manage all subscription plans offered in the system, including features, pricing, and availability.
          </p>
        </div>
      )}

      <div
        className={`w-fit flex gap-2 p-1 rounded-full border border-gray-200 ${
          (pageType === 'pricing' || (pageType === 'plans' && !isSuperAdmin)) && 'mx-auto'
        }`}
      >
        {Object.keys(PricingPeriod)
          ?.filter((value: any) => isNaN(value))
          ?.map((value, index: number) => (
            <div
              key={value}
              className={`px-4 py-2 font-semibold rounded-full  cursor-pointer ${
                selectedFilter === index ? 'bg-[#743AF5] text-white' : 'text-[#A1A1AA]'
              }`}
              onClick={() => setSelectedFilter(index)}
            >
              {value}
            </div>
          ))}
      </div>

      <div className={`flex flex-row flex-wrap gap-6 relative items-start justify-center mx-auto `}>
        {list
          ?.filter((item: any) => item.type !== 1)
          ?.map((item: any) => {
            // const hasSubscription = list.some((plan: any) => plan.isSubscribed);
            /*
          TODO:
            - Beside header will have small flag "Current plan"
            - It's button will be tertiary "Cancel Subscription"
          */

            return (
              <div
                className={`flex flex-col min-w-[280px] max-w-[320px] rounded-lg overflow-hidden ${item?.mostPopular ? '' : 'mt-10'}`}
                key={item._id}
              >
                {item?.mostPopular && (
                  <div className="bg-gradient-to-r from-[#4897FF] via-[#5F19D5] to-[#8484E1] px-4 py-2 text-center text-[#FAFBFF] font-semibold">
                    Most Popular
                  </div>
                )}

                <div
                  className={`grow p-4 space-y-6 bg-[#FAFBFF] border ${
                    item?.mostPopular ? 'border-t-0' : 'rounded-t-lg'
                  } border-[#DCD4FB] rounded-b-lg`}
                >
                  <div className="space-y-2">
                    <p className="thepassSubHone text-[#18181B] capitalize">{item?.name}</p>
                    <p className="text-[#65676D] thepassHfour text-sm">{item?.description}</p>
                  </div>
                  <div className="flex flex-wrap justify-between items-center gap-3">
                    <div className="flex gap-3">
                      {item?.pricing?.discountPercentage > 0 && (
                        <div className="flex items-end text-[#A7A7A7] line-through">
                          <span className="font-light text-base">
                            <CurrencySymbol currency={item?.pricing?.currency} />
                          </span>
                          <span className="font-light text-xl">{item?.pricing?.originalAmount}</span>
                        </div>
                      )}
                      <div className="flex gap-1 items-center">
                        <p>
                          <span className="text-[#566577] text-lg">
                            <CurrencySymbol currency={item?.pricing?.currency} />
                          </span>
                          <span className="text-[32px] font-bold leading-none">{item?.pricing?.discountedAmount}</span>
                        </p>
                        <p className="text-sm font-normal text-[#18181B]"> /mo</p>
                      </div>
                    </div>
                    {item?.pricing?.discountPercentage > 0 && (
                      <div className="bg-[#E7E0FB] border border-[#E9EAEB] px-2.5 py-0.5 rounded-full text-[#6941C6] text-sm font-semibold text-nowrap">
                        Save {item?.pricing?.discountPercentage}%
                      </div>
                    )}
                  </div>

                  {!isSuperAdmin &&
                    (item.isSubscribed ? (
                      <p className="px-3 py-2 mx-auto bg-[#f9f5ff] text-[#6941c6] text-sm text-center font-medium rounded-full">Current Plan</p>
                    ) : (
                      <Button
                        label="Choose plan"
                        variant="sm"
                        colorType="tertiary"
                        className={`w-full ${
                          !item?.mostPopular
                            ? 'border border-tertiaryBorder text-[#743AF5] bg-white hover:bg-[#EBEBFF] hover:border hover:border-[#BFA3FB] hover:text-[#743AF5] hover:shadow-[0_0_15.9px_0_#D8D8D8] active:bg-[#D9C8FC] active:border active:border-[#743AF5]'
                            : '!bg-[#743AF5] text-white hover:!bg-[#BFA3FB] hover:text-white hover:shadow-[0_0_15.9px_0_#D8D8D8] active:bg-[#743AF5]'
                        }`}
                        onClick={() => {
                          // window.sessionStorage.setItem('last-page', window.location.pathname);
                          navigate((userData as any)?.organizationId ? `/payment/${item?._id}/${selectedFilter + 1}` : '/auth/login');
                        }}
                        // outline={!item?.mostPopular}
                      />
                    ))}

                  <hr className="border-t border-dashed text-[#1A1A1A2E]" />

                  <div className="space-y-2">
                    <BasePlanCard features={item?.pricing?.features} />
                  </div>
                </div>
              </div>
            );
          })}

        {loading && (
          <div className="absolute top-0 bottom-0 left-0 right-0 z-50 flex items-center justify-center bg-white/80 dark:bg-gray-800/80">
            <div className="size-12 border-y-2 border-purple-500 animate-spin rounded-full"></div>
          </div>
        )}
      </div>

      {/* Enterprise Plan Card */}
      {/* {!isSuperAdmin && (
        <div className="flex flex-wrap justify-between space-y-4 border rounded-lg p-4 bg-white">
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <p className="text-lg font-medium">Enterprise</p>
              <span className="px-2 py-0.5 text-[#6941C6] text-xs font-medium border border-[#E9EAEB] bg-[#F9F5FF]  rounded-full">Customizable</span>
            </div>

            <p className="text-[#65676D] text-sm">Tailored hiring tools for large teams and advanced use cases that fit your teams' needs</p>
          </div>

          <div className="flex justify-center">
            <Button tertiary label="Contact Us" size="sm" onClick={() => navigate('/contact-us')} />
          </div>
        </div>
      )} */}
    </div>
  );
};
