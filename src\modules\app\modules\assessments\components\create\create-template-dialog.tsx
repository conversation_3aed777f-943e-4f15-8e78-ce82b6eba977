// React
import { useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

// Core
import { TextInput, Select, Form } from 'src';
import { Dialog, Button } from 'UI';
import { setNotifyMessage, setErrorNotify } from 'UI';
import { useAppDispatch, setFieldValue, Regex, Api, useLookups, useAppSelector, RootState } from 'UI/src';

export const CreateTemplateDialog = ({ onClose, isCreateTemplateVisible, refresh }: any) => {
  // Reference
  const subCategoryRef = useRef<any>(null);

  // State
  const [loading, setLoading] = useState(false);

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const dispatch = useAppDispatch();

  // Hooks
  const { type } = useParams();
  // const { isRequired, isNotSpaces, validateRegex, minLength, maxLength, isValidateMaxAndMinNumber, isNumber } = useValidate();
  const { lookups: subCategoryLookups } = useLookups('subcategory', { cached: false, params: { categoryId: form.category } });
  const { lookups: categoryLookups } = useLookups('category');

  const getCategoryName = (lookups: any, id: any) => {
    if (!Array.isArray(lookups)) return '—';
    return lookups.find((item) => item._id === id)?.name || '—';
  };

  // Submit functions
  const onSubmit = async () => {
    const selectedSubCategoryId = Array.isArray(form.subCategory) ? form.subCategory[0] : form.subCategory;
    const categoryName = getCategoryName(categoryLookups, form.category);
    const subCategoryName = getCategoryName(subCategoryLookups, selectedSubCategoryId);

    let payload = {
      seniorityLevel: form.seniorityLevel,
      category: [categoryName],
      subCategory: [subCategoryName],
      questionNumber: Number(form.questionNumber),
    };

    try {
      setLoading(true);
      const { data } = await Api.post('ai-interview/generate/multiple-questions', payload);
      try {
        const questionsWithCategory = ((Object.values(data)[0] as any).questions || []).map((q: any) => ({
          ...q,
          categoryId: form.category,
          category: categoryName,
          subCategoryId: selectedSubCategoryId,
          subCategory: subCategoryName,
        }));

        const payload = {
          title: form.title,
          description: 'description',
          questionsList: questionsWithCategory,
          difficulty: form.difficulty,
          duration: form.duration,
          seniorityLevel: form.seniorityLevel,
          categoryId: form.category,
          category: form.category,
          subCategoryIds: [selectedSubCategoryId],
          subCategoryId: selectedSubCategoryId,
          subcategoryIds: [selectedSubCategoryId],
          subcategoryId: selectedSubCategoryId,
        };
        if (type === 'test') {
          await Api.post('templates/generate', { ...payload, type: 'test' });
        } else if (type === 'interview') {
          await Api.post('templates/generate', { ...payload, type: 'interview', skips: form.skips });
        }
        dispatch(setNotifyMessage('Ai template created successfully'));
      } catch (error: any) {
        dispatch(setErrorNotify(error?.response?.data?.message));
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      refresh();
      setLoading(false);
      onClose();
    }
  };

  return (
    <Dialog size="lg" isOpen onClose={onClose} title={`Create ${isCreateTemplateVisible} Template`}>
      <Form className="space-y-4" onSubmit={onSubmit}>
        <TextInput
          label="Template Name"
          className="dark:text-white"
          name="title"
          placeholder={`E.g., Frontend developer ${type}`}
          value={form.title}
          onChange={(value: string) => dispatch(setFieldValue({ path: 'title', value: value }))}
          // validators={[isRequired(), validateRegex(Regex.name), minLength(2), maxLength(50)]}
          requiredLabel
        />

        <Select
          label="Category"
          name="category"
          placeholder="Search for category"
          value={form.category}
          onChange={(newCategory: any) => {
            subCategoryRef.current?.blur();
            dispatch(setFieldValue({ path: 'category', value: newCategory }));
            dispatch(setFieldValue({ path: 'subCategory', value: null }));
          }}
          lookup="category"
          optionValueKey="_id"
          optionLabelKey="name"
          dropIcon
          requiredLabel
        />

        <Select
          key={form.category}
          label="Subcategory"
          requiredLabel
          name="subCategory"
          placeholder="Search for subcategory"
          value={Array.isArray(form.subCategory) ? form.subCategory : []}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'subCategory', value: value }))}
          disabled={!form.category}
          disabledMessage="Please select category first"
          lookup="subcategory"
          params={{ categoryId: form.category }}
          optionValueKey="_id"
          optionLabelKey="name"
          dropIcon
        />

        <Select
          label="Seniority Level"
          name="seniorityLevel"
          placeholder="Select seniority level"
          value={form.seniorityLevel}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'seniorityLevel', value: value }))}
          // validators={[isRequired()]}
          lookup="$QuizDifficulty"
          optionValueKey="value"
          optionLabelKey="label"
          dropIcon={true}
          className="w-full"
          requiredLabel
        />

        <Select
          label="Difficulty"
          name="difficulty"
          placeholder="Select difficulty level"
          value={form.difficulty}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'difficulty', value: value }))}
          // validators={[isRequired()]}
          dropIcon={true}
          lookup="$QuestionDifficulty"
          optionValueKey="value"
          optionLabelKey="label"
          requiredLabel
        />

        <TextInput
          label="Number of Questions"
          name="questionNumber"
          placeholder="Question number"
          value={form.questionNumber}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'questionNumber', value: value }))}
          // validators={[isRequired(), isNumber(), isValidateMaxAndMinNumber('min', 3), isValidateMaxAndMinNumber('max', 30)]}
          type="number"
          requiredLabel
        />

        <TextInput
          label={type == 'interview' ? 'Estimation time' : 'Duration'}
          name="duration"
          placeholder={`${type == 'interview' ? 'Estimation time' : 'Duration'}`}
          value={form.duration}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'duration', value: value }))}
          // validators={[isRequired(), isNumber(), isValidateMaxAndMinNumber('min', 10), isValidateMaxAndMinNumber('max', 60)]}
          type="number"
          requiredLabel
        />

        {type == 'interview' && (
          <div>
            <TextInput
              label="Max Skips"
              name="skips"
              placeholder="Choose maximum allows for applicants to skip questions"
              value={form.skips}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'skips', value: value }))}
              // validators={[isRequired(), isValidateMaxAndMinNumber('max', form.questionNumber, 'the Number of Questions')]}
              type="number"
              className="w-full"
              requiredLabel
            />

            {Number(form.questionNumber) > 0 && Number(form.skips) >= Number(form.questionNumber) && (
              <p className="text-red-500 text-sm">Max skips cannot be greater than or equal to number of questions</p>
            )}
          </div>
        )}
        <div className="grid grid-cols-2 gap-4">
          <Button colorType="tertiary" label="Cancel" loading={loading} disabled={loading} onClick={onClose} />
          <Button type="submit" colorType="primary" label="Create" className="w-full" loading={loading} disabled={loading} />
        </div>
      </Form>
    </Dialog>
  );
};
