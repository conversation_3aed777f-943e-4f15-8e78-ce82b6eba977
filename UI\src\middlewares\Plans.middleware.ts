import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';
import type { PlanDataType } from '../types/Plan.type';

// Fetch single plan
export const fetchPlan = createAsyncThunk('plans/fetchPlan', async (planId: string, { rejectWithValue }) => {
  try {
    const response = await Api.get<PlanDataType>(`plans/single/${planId}`, {});
    return response.data;
  } catch (error: any) {
    return rejectWithValue(error?.response?.data?.message || 'Failed to fetch plan');
  }
});

// Fetch single plan
export const couponsValidate = createAsyncThunk(
  'coupons/validate',
  async (
    props: {
      planId: string;
      pricing: string;
      code: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await Api.get<PlanDataType>(`coupons/validate`, props);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch plan');
    }
  }
);

// Create subscription verify
export const createSubscriptionVerify = createAsyncThunk(
  'plans/createSubscriptionVerify',
  async (
    payload: {
      planId: string;
      pricing: string;
      card: {
        name: string;
        number: string;
        month: string;
        year: string;
        cvc: string;
      };
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await Api.post('/subscription/create-verify', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create subscription');
    }
  }
);
