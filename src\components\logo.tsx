import React from 'react';
import LogoIcon from 'images/Thepass-1.ico';
import LogoSVG from 'images/Thepass-1.svg';
import Logo2SVG from 'images/Thepass-2.svg';

interface LogoProps {
className?: string;
icon?: boolean;
}

export const Logo = ({ className, icon }: LogoProps) => {
  return (
    <div>
      {icon ? (
        <div>
          <img src={LogoIcon} className={className} />
        </div>
      ) : (
        <div>
          <img src={LogoSVG} className={`${className} light-logo`} alt="Logo" />
          <img src={Logo2SVG} className={`${className} dark-logo`} alt="Logo" />
        </div>
      )}
    </div>
  );
};
