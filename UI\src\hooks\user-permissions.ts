import { useAppDispatch, type RootState, useAppSelector  } from '../store';
import type { CurrentUserRole } from '../types/User.type';
import { updateUserRole } from '../slices/auth/auth.slice';
import { setErrorNotify } from '../slices/notify/notify.slice';
import { getUserRole } from '../middlewares/UserRole.middleware';

// This should be a custom hook, not a regular function
export const useUserPermissions = () => {
  const dispatch = useAppDispatch();
  // User Data
  const userRole: CurrentUserRole = useAppSelector((state: RootState) => state.auth.userRole);

  const handleGetUserRole = async () => {
    try {
      const data = await dispatch(getUserRole()).unwrap();
      const { name, permissions } = data;
      dispatch(updateUserRole({ name, permissions }));
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    }
  }

  const hasPermission = (Permissions: number | number[], operator: "every" | "some" = "every"): boolean => {
    const userPermissions = userRole?.permissions?.map((item) => item.value) || [];

    // Normalize input (make it always an array)
    const permissionsValue = Array.isArray(Permissions) ? Permissions : [Permissions];

    // Check if ALL needed permissions exist in userPermissions
    const isHasPermission =  permissionsValue[operator]((item) => userPermissions.includes(item));

    return isHasPermission;
  };

  return { handleGetUserRole, hasPermission };
};