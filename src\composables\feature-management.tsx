import { RootState, useAppSelector, UserData } from 'UI/src';

// This should be a custom hook, not a regular function
const CheckFeatureManagement = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const checkFeature = (featuresPermission: string): boolean => {
    const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');

    const hasFeatureAccess = (userData as any)?.features?.[featuresPermission] > 0;

    return isSuperAdmin || hasFeatureAccess;
  };

  return { checkFeature };
};

export default CheckFeatureManagement;
