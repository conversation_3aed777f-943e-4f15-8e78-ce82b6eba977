// UI
import { StaticData } from 'UI/src';
import { useScreenSize } from 'UI/src';

// Flowbite
import { Pagination } from 'flowbite-react';
import { FC } from 'react';

// types

export type PaginationType = {
  page: number;
  size: number;
};

type StepperPaginationProps = {
  count: number;
  pagination: PaginationType;
  setPagination: (value: PaginationType | ((prev: PaginationType) => PaginationType)) => void;
};

export const StepperPagination:FC<StepperPaginationProps> = ({ count, pagination, setPagination }) => {
  // Hooks
  const screen = useScreenSize();

  return (
    count > pagination?.size && (
      <Pagination
        theme={StaticData.paginationTheme}
        currentPage={pagination?.page}
        onPageChange={(page: number) => setPagination((prev: PaginationType) => ({ ...prev, page }))}
        showIcons
        totalPages={Math.max(Math.ceil(count / pagination?.size), 1)}
        layout="pagination"
        // FIXME: pagination in flowbite have an error with null in pagination so the best is undefined 
        previousLabel={undefined}
        nextLabel={undefined}
        // previousLabel={null}
        // nextLabel={null}
      />
    )
  );
};
