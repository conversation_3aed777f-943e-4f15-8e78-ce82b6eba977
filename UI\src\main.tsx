import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { store } from './store';
import App from './app';
import { ToastContainer } from 'react-toastify';
import ConfirmationDialog from './components/confirmation-popup';

const rootElement = document.getElementById('root');

createRoot(rootElement!).render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
      <ToastContainer
        position="top-center"
        autoClose={3000}
        hideProgressBar={true}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
      />
      <ConfirmationDialog />
    </Provider>
  </React.StrictMode>
);
