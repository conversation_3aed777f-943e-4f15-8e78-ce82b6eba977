import { useState } from 'react';

// Core
import { Icon, Card, ResultChart, EnumText, CustomIcon, <PERSON><PERSON>, Drawer, SubscribeDialog } from 'src';
import { TestStatusTag } from 'UI/src';

import { api } from 'UI/src/services/axios';

// Flowbite
import { Tooltip } from 'flowbite-react';
import CheckFeatureManagement from 'src/composables/feature-management';
import { useAppDispatch, useScreenSize, PlanFeatures, ToggleSwitch } from 'UI/src';
import { setNotifyMessage, setErrorNotify, QuizDifficulty } from 'UI';

interface TestCardProps {
  test: any;
  type: string;
}

export const TestCard = ({ test, type }: TestCardProps) => {
  const { checkFeature } = CheckFeatureManagement();

  // State
  const [needSubscription, setNeedSubscription] = useState(false);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [showMore, setShowMore] = useState(false);
  const [showQuestions, setShowQuestions] = useState(false);
  const [searchQuestions, setSearchQuestions] = useState('');
  const [toggleSwitch, setToggleSwitch] = useState(false);
  const [expandQuestionToggle, setExpandQuestionToggle] = useState(false);

  // Hooks
  const screen = useScreenSize();
  const dispatch = useAppDispatch();
  const ORIGIN = window.location.origin;

  const WeirdBehavior =
    test?.weirdBehavior?.tabSwitchedCount > 0 || test?.weirdBehavior?.ipChangeCount > 0 || test?.weirdBehavior?.openContextMenuCount > 0;

  const correctAnswers = test?.questionsSummary?.correctAnswers;
  const skippedQuestions = test?.questionsSummary?.skippedQuestions;
  const wrongAnswers = test?.questionsSummary?.wrongAnswers;
  const unAnsweredQuestions = test?.questionsSummary?.unAnsweredQuestions;

  const totalQuestions = test?.questionsSummary?.totalQuestions;

  const correctPercentage = Math.round((correctAnswers / totalQuestions) * 100);
  const skippedPercentage = Math.round((skippedQuestions / totalQuestions) * 100);
  const wrongPercentage = Math.round((wrongAnswers / totalQuestions) * 100);
  const unansweredPercentage = Math.round((unAnsweredQuestions / totalQuestions) * 100);

  const toggleDropdown = () => {
    setIsDropdownVisible(!isDropdownVisible);
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return '—';

    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short',
    }).format(date);
  };

  const downloadDocument = async () => {
    try {
      const response = await api.get(
        `${
          type === 'submissions' ? 'submissions' : type === 'interviews' ? 'ai-interview' : type === 'screening' ? 'submissions' : '—'
        }/stages/report/${test._id}`,
        {
          responseType: 'blob',
        }
      );
      console.log('/stages/report', response.data);
      const url = window.URL.createObjectURL(
        new Blob([response.data], {
          type: response.headers['content-type'],
        })
      );
      console.log(response.data);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${type === 'submissions' ? 'test' : type === 'interviews' ? 'interview' : type === 'screening' ? 'screening' : '—'}-report.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      dispatch(setErrorNotify(error.response?.data.message));
    }
  };

  const copyTestLink = () => {
    const ORIGIN = window.location.origin;
    const testLink = `${type === 'submissions' || type === 'screening' ? `${ORIGIN}/test` : `${ORIGIN}/interview`}/${test._id}`;

    navigator.clipboard.writeText(testLink);
    dispatch(
      setNotifyMessage(
        `${type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : 'Test'} link copied`
      )
    );
  };

  const statusIsNotSubmitted = () => {
    const statusIsNotSubmittedResults = () => {
      if (test?.expired) {
        if (!test?.startedAt) {
          // Missed
          return {
            title: 'Missed Deadline !',
            subTitle: `The applicant did not attempt the ${
              type === 'submissions'
                ? 'test'
                : type === 'interviews'
                ? 'interview'
                : type === 'screening'
                ? 'screening'
                : type === 'warnings'
                ? test.type
                : '—'
            } before the deadline.`,
            svg: (
              <svg width="216" height="90" viewBox="0 0 216 90" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M119 15C122.866 15 126 18.134 126 22C126 25.866 122.866 29 119 29H183C186.866 29 190 32.134 190 36C190 39.866 186.866 43 183 43H205C208.866 43 212 46.134 212 50C212 53.866 208.866 57 205 57H186C182.134 57 179 60.134 179 64C179 67.866 182.134 71 186 71H192C195.866 71 199 74.134 199 78C199 81.866 195.866 85 192 85H140C139.485 85 138.983 84.9444 138.5 84.8389C138.017 84.9444 137.515 85 137 85H46C42.134 85 39 81.866 39 78C39 74.134 42.134 71 46 71H7C3.13401 71 0 67.866 0 64C0 60.134 3.13401 57 7 57H47C50.866 57 54 53.866 54 50C54 46.134 50.866 43 47 43H22C18.134 43 15 39.866 15 36C15 32.134 18.134 29 22 29H62C58.134 29 55 25.866 55 22C55 18.134 58.134 15 62 15H119ZM209 71C212.866 71 216 74.134 216 78C216 81.866 212.866 85 209 85C205.134 85 202 81.866 202 78C202 74.134 205.134 71 209 71Z"
                  fill="#F3F7FF"
                />
                <path
                  d="M141.562 14.0625H130.312V8.4375C130.312 5.0625 128.062 2.8125 124.688 2.8125H119.062C115.688 2.8125 113.438 5.0625 113.438 8.4375V14.0625H96.5625V8.4375C96.5625 5.0625 94.3125 2.8125 90.9375 2.8125H85.3125C81.9375 2.8125 79.6875 5.0625 79.6875 8.4375V14.0625H68.4375C65.0625 14.0625 62.8125 16.3125 62.8125 19.6875V30.9375H147.188V19.6875C147.188 16.3125 144.938 14.0625 141.562 14.0625Z"
                  fill="#A47BFA"
                />
                <path
                  d="M141.562 11.25H133.125V8.4375C133.125 3.9375 129.188 0 124.688 0H119.062C114.562 0 110.625 3.9375 110.625 8.4375V11.25H99.375V8.4375C99.375 3.9375 95.4375 0 90.9375 0H85.3125C80.8125 0 76.875 3.9375 76.875 8.4375V11.25H68.4375C63.9375 11.25 60 15.1875 60 19.6875V30.9375C60 32.625 61.125 33.75 62.8125 33.75H144.375V81.5625C144.375 83.25 143.25 84.375 141.562 84.375H68.4375C66.75 84.375 65.625 83.25 65.625 81.5625V42.1875C65.625 40.5 64.5 39.375 62.8125 39.375C61.125 39.375 60 40.5 60 42.1875V81.5625C60 86.0625 63.9375 90 68.4375 90H141.562C146.062 90 150 86.0625 150 81.5625V19.6875C150 15.1875 146.062 11.25 141.562 11.25ZM116.25 8.4375C116.25 6.75 117.375 5.625 119.062 5.625H124.688C126.375 5.625 127.5 6.75 127.5 8.4375V11.25H116.25V8.4375ZM82.5 8.4375C82.5 6.75 83.625 5.625 85.3125 5.625H90.9375C92.625 5.625 93.75 6.75 93.75 8.4375V11.25H82.5V8.4375ZM144.375 28.125H65.625V19.6875C65.625 18 66.75 16.875 68.4375 16.875H141.562C143.25 16.875 144.375 18 144.375 19.6875V28.125Z"
                  fill="#8D5BF8"
                />
                <path
                  d="M124.688 59.0625C124.688 48.375 115.688 39.375 105 39.375C94.3125 39.375 85.3125 48.375 85.3125 59.0625C85.3125 69.75 94.3125 78.75 105 78.75C115.688 78.75 124.688 69.75 124.688 59.0625ZM90.9375 59.0625C90.9375 51.1875 97.125 45 105 45C112.875 45 119.062 51.1875 119.062 59.0625C119.062 66.9375 112.875 73.125 105 73.125C97.125 73.125 90.9375 66.9375 90.9375 59.0625Z"
                  fill="#A47BFA"
                />
                <path
                  d="M111.211 53.7888C110.159 52.7371 108.455 52.7371 107.404 53.7888L105.5 55.6926L103.596 53.7888C102.544 52.7371 100.841 52.7371 99.7888 53.7888C98.7371 54.8406 98.7371 56.5445 99.7888 57.5963L101.693 59.5L99.7888 61.4037C98.7371 62.4555 98.7371 64.1594 99.7888 65.2112C100.315 65.7371 101.004 66 101.693 66C102.381 66 103.07 65.7371 103.596 65.2112L105.5 63.3074L107.404 65.2112C107.93 65.7371 108.619 66 109.307 66C109.996 66 110.685 65.7371 111.211 65.2112C112.263 64.1594 112.263 62.4555 111.211 61.4037L109.307 59.5L111.211 57.5963C112.263 56.5445 112.263 54.8406 111.211 53.7888Z"
                  fill="#8D5BF8"
                />
              </svg>
            ),
          };
        } else if (test?.startedAt) {
          // Overdue
          return;
        }
      } else if (test?.locked) {
        // Scheduled
        return {
          title: `${type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : '—'} Scheduled.`,
          subTitle: `${
            type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : '—'
          } will be accessible once the start time arrives.`,
          svg: (
            <svg width="216" height="90" viewBox="0 0 216 90" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M119 15C122.866 15 126 18.134 126 22C126 25.866 122.866 29 119 29H183C186.866 29 190 32.134 190 36C190 39.866 186.866 43 183 43H205C208.866 43 212 46.134 212 50C212 53.866 208.866 57 205 57H186C182.134 57 179 60.134 179 64C179 67.866 182.134 71 186 71H192C195.866 71 199 74.134 199 78C199 81.866 195.866 85 192 85H140C139.485 85 138.983 84.9444 138.5 84.8389C138.017 84.9444 137.515 85 137 85H46C42.134 85 39 81.866 39 78C39 74.134 42.134 71 46 71H7C3.13401 71 0 67.866 0 64C0 60.134 3.13401 57 7 57H47C50.866 57 54 53.866 54 50C54 46.134 50.866 43 47 43H22C18.134 43 15 39.866 15 36C15 32.134 18.134 29 22 29H62C58.134 29 55 25.866 55 22C55 18.134 58.134 15 62 15H119ZM209 71C212.866 71 216 74.134 216 78C216 81.866 212.866 85 209 85C205.134 85 202 81.866 202 78C202 74.134 205.134 71 209 71Z"
                fill="#F3F7FF"
              />
              <g clip-path="url(#clip0_19997_6359)">
                <path
                  d="M140.562 14.0625H129.312V8.4375C129.312 5.0625 127.062 2.8125 123.688 2.8125H118.062C114.688 2.8125 112.438 5.0625 112.438 8.4375V14.0625H95.5625V8.4375C95.5625 5.0625 93.3125 2.8125 89.9375 2.8125H84.3125C80.9375 2.8125 78.6875 5.0625 78.6875 8.4375V14.0625H67.4375C64.0625 14.0625 61.8125 16.3125 61.8125 19.6875V30.9375H146.188V19.6875C146.188 16.3125 143.938 14.0625 140.562 14.0625Z"
                  fill="#A47BFA"
                />
                <path
                  d="M140.562 11.25H132.125V8.4375C132.125 3.9375 128.188 0 123.688 0H118.062C113.562 0 109.625 3.9375 109.625 8.4375V11.25H98.375V8.4375C98.375 3.9375 94.4375 0 89.9375 0H84.3125C79.8125 0 75.875 3.9375 75.875 8.4375V11.25H67.4375C62.9375 11.25 59 15.1875 59 19.6875V30.9375C59 32.625 60.125 33.75 61.8125 33.75H143.375V81.5625C143.375 83.25 142.25 84.375 140.562 84.375H67.4375C65.75 84.375 64.625 83.25 64.625 81.5625V42.1875C64.625 40.5 63.5 39.375 61.8125 39.375C60.125 39.375 59 40.5 59 42.1875V81.5625C59 86.0625 62.9375 90 67.4375 90H140.562C145.062 90 149 86.0625 149 81.5625V19.6875C149 15.1875 145.062 11.25 140.562 11.25ZM115.25 8.4375C115.25 6.75 116.375 5.625 118.062 5.625H123.688C125.375 5.625 126.5 6.75 126.5 8.4375V11.25H115.25V8.4375ZM81.5 8.4375C81.5 6.75 82.625 5.625 84.3125 5.625H89.9375C91.625 5.625 92.75 6.75 92.75 8.4375V11.25H81.5V8.4375ZM143.375 28.125H64.625V19.6875C64.625 18 65.75 16.875 67.4375 16.875H140.562C142.25 16.875 143.375 18 143.375 19.6875V28.125Z"
                  fill="#8D5BF8"
                />
                <path
                  d="M123.688 59.0625C123.688 48.375 114.688 39.375 104 39.375C93.3125 39.375 84.3125 48.375 84.3125 59.0625C84.3125 69.75 93.3125 78.75 104 78.75C114.688 78.75 123.688 69.75 123.688 59.0625ZM89.9375 59.0625C89.9375 51.1875 96.125 45 104 45C111.875 45 118.062 51.1875 118.062 59.0625C118.062 66.9375 111.875 73.125 104 73.125C96.125 73.125 89.9375 66.9375 89.9375 59.0625Z"
                  fill="#A47BFA"
                />
                <path
                  d="M106.812 64.6875C107.375 64.6875 108.5 64.6875 109.062 64.125C110.187 63 110.187 61.3125 109.062 60.1875L106.812 57.9375V53.4375C106.812 51.75 105.688 50.625 104 50.625C102.312 50.625 101.188 51.75 101.188 53.4375V59.0625C101.188 59.625 101.75 60.75 101.75 61.3125L104.563 64.125C105.125 64.6875 106.25 64.6875 106.812 64.6875Z"
                  fill="#A47BFA"
                />
              </g>
              <defs>
                <clipPath id="clip0_19997_6359">
                  <rect width="90" height="90" fill="white" transform="translate(60)" />
                </clipPath>
              </defs>
            </svg>
          ),
        };
      } else if (test?.submittedAt) {
        // Submitted
        return;
      } else if (test?.startedAt) {
        // In Progress
        return {
          title: 'Pending Score...',
          subTitle: `The results will appear once the applicant completes the ${
            type === 'submissions' ? 'test' : type === 'interviews' ? 'interview' : type === 'screening' ? 'screening' : '—'
          }.`,
          svg: (
            <svg width="216" height="95" viewBox="0 0 216 95" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M119 20C122.866 20 126 23.134 126 27C126 30.866 122.866 34 119 34H183C186.866 34 190 37.134 190 41C190 44.866 186.866 48 183 48H205C208.866 48 212 51.134 212 55C212 58.866 208.866 62 205 62H186C182.134 62 179 65.134 179 69C179 72.866 182.134 76 186 76H192C195.866 76 199 79.134 199 83C199 86.866 195.866 90 192 90H140C139.485 90 138.983 89.9444 138.5 89.8389C138.017 89.9444 137.515 90 137 90H46C42.134 90 39 86.866 39 83C39 79.134 42.134 76 46 76H7C3.13401 76 0 72.866 0 69C0 65.134 3.13401 62 7 62H47C50.866 62 54 58.866 54 55C54 51.134 50.866 48 47 48H22C18.134 48 15 44.866 15 41C15 37.134 18.134 34 22 34H62C58.134 34 55 30.866 55 27C55 23.134 58.134 20 62 20H119ZM209 76C212.866 76 216 79.134 216 83C216 86.866 212.866 90 209 90C205.134 90 202 86.866 202 83C202 79.134 205.134 76 209 76Z"
                fill="#F3F7FF"
              />
              <path
                d="M141.865 46.824V6.96054C141.865 5.13362 140.385 3.65381 138.559 3.65381H82.2527C80.4258 3.65381 78.9277 5.13362 78.9277 6.96054V88.0394C78.9277 89.8663 80.4258 91.3461 82.2527 91.3461H138.559C139.07 91.3461 139.545 91.2365 139.965 91.0173M86.8785 16.6231H133.922M86.8785 29.3002H133.922M86.8785 41.9772H133.922M113.435 54.6615H86.8748"
                stroke="#8D5BF8"
                stroke-width="4"
                stroke-miterlimit="10"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M164.549 69.043C164.549 57.7617 155.049 48.2617 143.768 48.2617C132.486 48.2617 122.986 57.7617 122.986 69.043C122.986 80.3242 132.486 89.8242 143.768 89.8242C155.049 89.8242 164.549 80.3242 164.549 69.043ZM128.924 69.043C128.924 60.7305 135.455 54.1992 143.768 54.1992C152.08 54.1992 158.611 60.7305 158.611 69.043C158.611 77.3555 152.08 83.8867 143.768 83.8867C135.455 83.8867 128.924 77.3555 128.924 69.043Z"
                fill="#A47BFA"
              />
              <path
                d="M145.105 76.4648C145.699 76.4648 146.887 76.4648 147.48 75.8711C148.668 74.6836 148.668 72.9023 147.48 71.7148L145.105 69.3398V64.5898C145.105 62.8086 143.918 61.6211 142.137 61.6211C140.355 61.6211 139.168 62.8086 139.168 64.5898V70.5273C139.168 71.1211 139.762 72.3086 139.762 72.9023L142.73 75.8711C143.324 76.4648 144.512 76.4648 145.105 76.4648Z"
                fill="#A47BFA"
              />
            </svg>
          ),
        };
      } else {
        // Not Started
        return {
          title: ' Waiting for applicant...',
          subTitle: `The ${
            type === 'submissions' ? 'test' : type === 'interviews' ? 'interview' : type === 'screening' ? 'screening' : '—'
          } is available, but the applicant hasn’t started yet.`,
          svg: (
            <svg width="216" height="92" viewBox="0 0 216 92" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M119 30C122.866 30 126 32.4624 126 35.5C126 38.5376 122.866 41 119 41H183C186.866 41 190 43.4624 190 46.5C190 49.5376 186.866 52 183 52H205C208.866 52 212 54.4624 212 57.5C212 60.5376 208.866 63 205 63H186C182.134 63 179 65.4624 179 68.5C179 71.5376 182.134 74 186 74H192C195.866 74 199 76.4624 199 79.5C199 82.5376 195.866 85 192 85H140C139.485 85 138.983 84.9559 138.5 84.873C138.017 84.9559 137.515 85 137 85H46C42.134 85 39 82.5376 39 79.5C39 76.4624 42.134 74 46 74H7C3.13401 74 5.73992e-07 71.5376 0 68.5C0 65.4624 3.13401 63 7 63H47C50.866 63 54 60.5376 54 57.5C54 54.4624 50.866 52 47 52H22C18.134 52 15 49.5376 15 46.5C15 43.4624 18.134 41 22 41H62C58.134 41 55 38.5376 55 35.5C55 32.4624 58.134 30 62 30H119ZM209 74C212.866 74 216 76.4624 216 79.5C216 82.5376 212.866 85 209 85C205.134 85 202 82.5376 202 79.5C202 76.4624 205.134 74 209 74Z"
                fill="#F3F7FF"
              />
              <path
                d="M113.5 80.75C113.485 77.4578 112.575 74.2273 110.861 71.3835C109.147 68.5398 106.689 66.1829 103.735 64.55C104.897 62.8549 105.562 60.8868 105.66 58.855C105.758 56.8231 105.285 54.8037 104.291 53.0114C103.297 51.2192 101.819 49.7212 100.016 48.677C98.2118 47.6328 96.1493 47.0813 94.0475 47.0813C91.9457 47.0813 89.8832 47.6328 88.0794 48.677C86.2757 49.7212 84.7983 51.2192 83.8044 53.0114C82.8105 54.8037 82.3373 56.8231 82.4351 58.855C82.5329 60.8868 83.1982 62.8549 84.36 64.55C81.4348 66.2019 79.0082 68.5669 77.322 71.4094C75.6358 74.2519 74.749 77.4724 74.75 80.75V84.5H113.5V80.75Z"
                fill="#8D5BF8"
              />
              <path
                d="M113.5 9.50003C108.411 9.50003 103.372 10.47 98.671 12.3546C93.9696 14.2391 89.6979 17.0013 86.0996 20.4835C78.8326 27.5161 74.75 37.0544 74.75 47H82.5C82.5041 41.3196 84.1746 35.757 87.3172 30.9593C90.4598 26.1616 94.9453 22.3259 100.252 19.8983C105.559 17.4708 111.469 16.5512 117.294 17.2465C123.12 17.9418 128.622 20.2234 133.16 23.826C137.699 27.4285 141.087 32.204 142.931 37.5968C144.775 42.9897 144.999 48.7784 143.576 54.2896C142.154 59.8007 139.144 64.8079 134.897 68.7287C130.65 72.6495 125.339 75.3227 119.584 76.4375L121.095 83.9375C130.738 82.3206 139.392 77.2323 145.318 69.6947C151.244 62.1571 154.004 52.7286 153.042 43.3032C152.08 33.8778 147.469 25.1536 140.134 18.8832C132.799 12.6128 123.284 9.2606 113.5 9.50003Z"
                fill="#8D5BF8"
              />
              <path d="M109.625 24.5V48.5375L122.374 60.9125L127.876 55.5875L117.375 45.4625V24.5H109.625Z" fill="#8D5BF8" />
            </svg>
          ),
        };
      }
    };

    return (
      <div className="flex flex-col gap-3 justify-center items-center min-h-48">
        <span>{statusIsNotSubmittedResults()?.svg}</span>
        <h2 className="dark:text-white pt-0 mt-0 text-gray-500 font-semibold text-lg text-center">{statusIsNotSubmittedResults()?.title}</h2>
        <p className="text-gray-400 font-light text-base text-center">{statusIsNotSubmittedResults()?.subTitle}</p>
      </div>
    );
  };

  const englishProficiency = (percentage: number) => {
    const handleEnglishProficiencyColors = () => {
      if (percentage < 50)
        return {
          label: 'Poor',
          color: 'text-statusColorPoor dark:text-statusDarkColorPoor',
          bg: 'bg-statusBackgroundPoor dark:bg-statusDarkBackgroundPoor',
        };
      else if (percentage <= 80)
        return {
          label: 'Good',
          color: 'text-statusColorGood dark:text-statusDarkColorGood',
          bg: 'bg-statusBackgroundGood dark:bg-statusDarkBackgroundGood',
        };
      else if (percentage > 80)
        return {
          label: 'Excellent',
          color: 'text-statusColorExcellent dark:text-statusDarkColorExcellent',
          bg: 'bg-statusBackgroundExcellent dark:bg-statusDarkBackgroundExcellent',
        };
    };

    return (
      <div className="flex items-center gap-1">
        <p className={`px-2 text-base font-medium ${handleEnglishProficiencyColors()?.color}`}>{percentage}%</p>
        <p
          className={`px-2.5 py-[3px] text-xs rounded-full self-center ${handleEnglishProficiencyColors()?.color} ${
            handleEnglishProficiencyColors()?.bg
          }`}
        >
          {handleEnglishProficiencyColors()?.label}
        </p>
      </div>
    );
  };

  const filteredQuestions = () =>
    toggleSwitch ? test.questionsWithAnswers.filter((question: { answer: string }) => question.answer === '') : test.questionsWithAnswers;

  return (
    <>
      <Card className="bg-white dark:bg-darkBackgroundCard rounded-lg mt-4 !p-4">
        <div className="bg-white dark:bg-darkBackgroundCard pb-4 dark:border-gray-700 space-y-3">
          <div className={`space-y-2 flex ${test?.status === 2 ? '' : 'grid-cols-10'} flex-wrap justify-between items-center gap-5`}>
            <div className={`flex col-span-5 gap-5 ${type === 'screening' ? 'col-span-5' : 'col-span-6'}`}>
              <p className="text-[#101828] dark:text-white text-lg font-medium line-clamp-2 md:max-w-full lg:max-w-[650px]">
                {test.title ? test.title : Array.isArray(test.subCategoryName) ? test.subCategoryName.join(' & ') : test.subCategoryName}{' '}
              </p>
              {(test?.status === 1 || test?.status === 2 || (test?.expired && test?.startedAt)) && <TestStatusTag test={test} hideScore />}
            </div>

            <div className={`flex lg:justify-end  ${type === 'screening' ? 'col-span-5' : 'col-span-4'}`}>
              {/* Buttons */}
              {test?.status === 3 ? (
                <div className={`flex flex-wrap gap-4 w-fit sm:justify-end ${type === 'screening' ? 'md:min-w-[480px]' : 'md:min-w-[330px]'}`}>
                  {type === 'screening' && (
                    <Button
                      onClick={() => setShowQuestions(!showQuestions)}
                      icon={showQuestions ? `iconamoon:eye-off` : 'carbon:question-answering'}
                      label={!showQuestions ? 'Show Q&A' : 'Hide Q&A'}
                      size="md"
                      tertiary
                    />
                  )}

                  <Button
                    onClick={() => setShowMore(!showMore)}
                    icon={showMore ? 'iconamoon:eye-off' : `ph:eye`}
                    label={showMore ? 'Less Details' : 'More Details'}
                    size="md"
                    className={`${showMore && 'pr-[6px]'}`}
                    tertiary
                  />

                  <div className="relative">
                    <Button onClick={toggleDropdown} size="md" icon="mdi:file-document-arrow-right-outline" label="Export Report" tertiary />

                    {/* Dropdown Menu */}
                    {isDropdownVisible && (
                      <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-darkBackgroundCard dark:text-white border dark:border-gray-700 rounded shadow-md z-10">
                        <ul className="py-1 divide-y dark:divide-gray-700">
                          {type !== 'screening' && (
                            <li
                              onClick={() => {
                                if (checkFeature(PlanFeatures.EXPORT_REPORTS)) {
                                  window.open(`/app/tests/pdf/${test._id}?type=${type}`, '_blank', 'noopener,noreferrer');
                                }
                              }}
                              className="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700"
                            >
                              <Icon icon="fa6-regular:file-pdf" className="mr-2 text-[#6B7280]" width="20" height="20" />
                              {`${
                                type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : '—'
                              } Score`}
                            </li>
                          )}
                          <li
                            onClick={() => {
                              downloadDocument();
                            }}
                            className="flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-700"
                          >
                            <Icon icon="hugeicons:xls-02" className="mr-2 text-[#6B7280]" width="20" height="20" />
                            {`${
                              type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : '—'
                            } Details`}
                          </li>
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                (test?.status === 1 || test?.status === 2) && (
                  <Button
                    onClick={copyTestLink}
                    icon="material-symbols:content-copy-outline"
                    label={`Copy ${
                      type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : 'Test'
                    } Link`}
                    size="md"
                    tertiary
                  />
                )
              )}
            </div>
          </div>

          {/* Started At and Time Taken */}
          {test.status >= 2 && (
            <div className="flex flex-wrap gap-4 items-center py-2 dark:border-gray-700 border-gray-200">
              <div className="flex flex-wrap items-center gap-2 text-md text-[#667085] dark:text-gray-400">
                <Icon icon="octicon:play-24" className="text-[#667085] dark:text-gray-400" width="22" />
                <span>Started At:</span>
                <span className="px-2 text-base text-[#333333] dark:text-grayTextOnDarkMood  font-medium">{formatDate(test.startedAt)}</span>
              </div>

              {test.status === 3 && (
                <div className="flex flex-wrap items-center gap-2 text-md text-[#667085]  dark:text-gray-400">
                  <Icon icon="ph:clock-light" className="text-[#667085] dark:text-gray-400" width="22" />
                  <span>Time Taken:</span>
                  <span className="px-2 text-base text-[#333333]  dark:text-grayTextOnDarkMood  font-medium">
                    {test?.timeTaken ? Math.round(test?.timeTaken) : '0'} min
                  </span>
                  {test?.duration - test?.timeTaken < 0 && (
                    <p className="text-[#D92D20] bg-[#FEF3F2] dark:text-[#FEE2E2] dark:bg-[#712224] dark:bg-opacity-45  px-2 py-1 text-sm font-normal rounded-md ">
                      {Math.round(test?.timeTaken - test?.duration)} mins Exceeded
                    </p>
                  )}
                </div>
              )}
            </div>
          )}

          {(showMore || test?.status !== 3) && (
            <div className="space-y-3 mt-2">
              {test?.status === 3 && (
                <h2 className="font-medium text-base mt-3 dark:text-white ">
                  {type === 'submissions' ? 'Test' : type === 'interviews' ? 'Interview' : type === 'screening' ? 'Screening' : '—'} Details
                </h2>
              )}

              <div className="flex flex-col sm:flex-row justify-around gap-4 px-0.5 py-4 rounded-md  dark:bg-gray-600 dark:bg-opacity-50   bg-[#F8F9FA]">
                {screen.gt.md() ? (
                  <>
                    <div className="flex items-center gap-2 text-[#667085]">
                      <Icon icon="heroicons:chart-bar-square" className="dark:text-gray-400" width={'24'} />
                      <p className="text-base dark:text-gray-400 font-medium">Difficulty:</p>
                      <p className="text-base px-2 text-[#101828] font-medium dark:text-white">
                        {/* <EnumText name={'QuizDifficulty'} value={test.difficulty} /> */}
                        {QuizDifficulty[test.difficulty]}
                      </p>
                    </div>

                    <div className="border border-[#79829666] h-5 mt-1 hidden lg:block" />

                    <div className="flex items-center gap-2 text-[#667085]">
                      <CustomIcon definedIcon="questions" className="dark:text-gray-400" />
                      <p className="text-base  dark:text-gray-400 font-medium">Total Questions:</p>
                      <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{test?.numOfQuestions ?? '0'}</p>
                    </div>

                    <div className="border border-[#79829666] h-5 mt-1 hidden lg:block" />

                    <div className="flex items-center gap-2 text-[#667085]">
                      <Icon icon="mynaui:clock-three" className="dark:text-gray-400" width={'24'} />
                      <p className="text-base dark:text-gray-400 font-medium">{type == 'interviews' ? 'Estimation time:' : 'Duration:'}</p>
                      <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{test?.duration ?? '0'} min</p>
                    </div>

                    <div className="border border-[#79829666] h-5 mt-1 hidden lg:block" />

                    <div className={`flex flex-wrap sm:items-center gap-1 text-[#667085]`}>
                      <div className="flex items-center gap-2">
                        <Icon icon="material-symbols:calendar-month-rounded" className="dark:text-gray-400" width={'24'} />
                        <p className="text-base font-medium dark:text-gray-400">Due Date:</p>
                      </div>
                      <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{formatDate(test.dueDate)}</p>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="space-y-3">
                      <div className="flex items-center gap-1 text-[#667085]">
                        <Icon icon="heroicons:chart-bar-square" className="dark:text-gray-400" width={'24'} />
                        <p className="text-base dark:text-gray-400 font-medium">Difficulty:</p>
                        <p className="text-base px-2 text-[#101828] font-medium dark:text-white">
                          {/* <EnumText name={'QuizDifficulty'} value={test.difficulty} /> */}
                          {QuizDifficulty[test.difficulty]}
                        </p>
                      </div>

                      <div className="flex items-center gap-1 text-[#667085]">
                        <CustomIcon definedIcon="questions" className="dark:text-gray-400" />
                        <p className="text-base  dark:text-gray-400 font-medium">Total Questions:</p>
                        <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{test?.numOfQuestions ?? '0'}</p>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center gap-1 text-[#667085]">
                        <Icon icon="mynaui:clock-three" className="dark:text-gray-400" width={'24'} />
                        <p className="text-base dark:text-gray-400 font-medium">Estimation time:</p>
                        <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{test?.duration ?? '0'} min</p>
                      </div>

                      <div className={`flex flex-wrap sm:items-center gap-1 text-[#667085]`}>
                        <div className="flex items-center gap-1">
                          <Icon icon="material-symbols:calendar-month-rounded" className="dark:text-gray-400" width={'24'} />
                          <p className="text-base font-medium dark:text-gray-400">Due Date:</p>
                        </div>
                        <p className="text-base px-2 text-[#101828] font-medium dark:text-white">{formatDate(test.dueDate)}</p>
                      </div>
                    </div>
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {test.status === 3 ? (
          type === 'screening' ? (
            <>
              {/* TODO: Add when need */}
              {/* <div className="bg-[#C3CCD71A] bg-opacity-10 p-4 mt-3 mb-5 rounded-lg border border-[#f1f3f6] dark:border-gray-700">
              <p className="text-lg text-black font-semibold dark:text-white ">Screening Summary</p>
              <p className="text-base text-[#667085] dark:text-gray-400 pt-2">{test.description}</p>
            </div> */}

              {(showQuestions || test?.status !== 3) && (
                <div className="space-y-4">
                  <div className="flex w-full items-center px-2 justify-between rounded-lg text-nowrap">
                    <Drawer.Search
                      children={null}
                      value={''}
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuestions(e.target.value)}
                      className="w-full dark:rounded-md"
                    />
                    <div className="flex gap-2 items-center align-middle px-2">
                      {/* ToggleSwitch */}
                      <div className="flex justify-start gap-3 text-sm font-medium m-4 pr-4">
                        <span className="text-[#798296] text-sm font-medium">Unanswered Questions</span>
                        <ToggleSwitch
                          sizing="sm"
                          checked={toggleSwitch}
                          onChange={() => setToggleSwitch((prev) => !prev)}
                          color="purple"
                        />
                      </div>

                      <div
                        className="text-[#8A43F9] font-medium text-sm cursor-pointer"
                        onClick={() => setExpandQuestionToggle(!expandQuestionToggle)}
                      >
                        {expandQuestionToggle ? 'Collapse All Answers' : 'Expand All Answers'}
                      </div>
                    </div>
                  </div>
                  {!filteredQuestions()?.length && toggleSwitch ? (
                    <div className="flex flex-col items-center gap-4 mt-16">
                      <svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M23.6885 0.931968C24.0152 1.04475 24.335 1.17697 24.6481 1.32864L28.3902 3.17197C28.8911 3.41851 29.4419 3.54672 30.0002 3.54672C30.5585 3.54672 31.1093 3.41851 31.6102 3.17197L35.3523 1.32864C36.2974 0.863379 37.3248 0.588846 38.376 0.520714C39.4272 0.452582 40.4816 0.592185 41.4788 0.931551C42.476 1.27092 43.3967 1.8034 44.1881 2.49859C44.9796 3.19378 45.6263 4.03806 46.0914 4.98322L46.3043 5.45572L46.4881 5.9428L47.8298 9.88905C48.1943 10.9624 49.0373 11.8024 50.1077 12.167L54.0568 13.5086C55.1371 13.8762 56.1265 14.47 56.9589 15.2503C57.7914 16.0307 58.4478 16.9797 58.8843 18.034C59.3207 19.0883 59.5272 20.2236 59.4899 21.364C59.4526 22.5045 59.1725 23.6239 58.6681 24.6474L56.8277 28.3895C56.5811 28.8904 56.4529 29.4412 56.4529 29.9995C56.4529 30.5577 56.5811 31.1086 56.8277 31.6095L58.6681 35.3516C59.172 36.375 59.4517 37.494 59.4887 38.6342C59.5258 39.7743 59.3192 40.9092 58.8827 41.9631C58.4463 43.017 57.7901 43.9657 56.9579 44.7459C56.1257 45.5261 55.1367 46.1198 54.0568 46.4874L50.1077 47.8291C49.5794 48.0093 49.0996 48.3084 48.7051 48.7033C48.3107 49.0982 48.0122 49.5785 47.8327 50.107L46.4881 54.0561C46.1205 55.136 45.5268 56.125 44.7466 56.9572C43.9664 57.7894 43.0177 58.4456 41.9638 58.882C40.9099 59.3185 39.775 59.5251 38.6349 59.488C37.4948 59.451 36.3757 59.1712 35.3523 58.6674L31.6102 56.827C31.1093 56.5804 30.5585 56.4522 30.0002 56.4522C29.4419 56.4522 28.8911 56.5804 28.3902 56.827L24.6481 58.6674C23.6247 59.1712 22.5056 59.451 21.3655 59.488C20.2254 59.5251 19.0905 59.3185 18.0366 58.882C16.9826 58.4456 16.0339 57.7894 15.2537 56.9572C14.4736 56.125 13.8799 55.136 13.5123 54.0561L12.1706 50.107C11.9901 49.5784 11.6905 49.0983 11.2951 48.7039C10.8996 48.3094 10.4188 48.0111 9.88976 47.832L5.94351 46.4874C4.86339 46.1201 3.87405 45.5266 3.04154 44.7466C2.20904 43.9665 1.55251 43.0179 1.1158 41.9639C0.679087 40.9099 0.472239 39.7749 0.509062 38.6347C0.545886 37.4944 0.825536 36.3751 1.32934 35.3516L3.17267 31.6095C3.41921 31.1086 3.54742 30.5577 3.54742 29.9995C3.54742 29.4412 3.41921 28.8904 3.17267 28.3895L1.32934 24.6474C0.825536 23.6238 0.545886 22.5045 0.509062 21.3643C0.472239 20.224 0.679087 19.089 1.1158 18.035C1.55251 16.9811 2.20904 16.0324 3.04154 15.2524C3.87405 14.4723 4.86339 13.8788 5.94351 13.5116L9.88976 12.1699C10.4189 11.9898 10.8995 11.6904 11.2945 11.2949C11.6895 10.8994 11.9882 10.4184 12.1677 9.88905L13.5093 5.9428C13.8485 4.94528 14.3809 4.02435 15.076 3.23262C15.7712 2.44089 16.6155 1.79389 17.5608 1.32856C18.5061 0.86323 19.5338 0.588701 20.5852 0.520653C21.6366 0.452605 22.6911 0.592372 23.6885 0.931968ZM17.651 7.34864L16.3093 11.2978C15.9137 12.461 15.2561 13.5178 14.3873 14.3866C13.5185 15.2554 12.4617 15.913 11.2985 16.3086L7.35226 17.6503C6.86111 17.8172 6.41124 18.0871 6.0327 18.4418C5.65417 18.7965 5.35568 19.2278 5.15718 19.7071C4.95869 20.1864 4.86475 20.7025 4.88165 21.2209C4.89854 21.7394 5.02589 22.2483 5.25517 22.7136L7.09851 26.4557C7.64145 27.5581 7.92381 28.7706 7.92381 29.9995C7.92381 31.2283 7.64145 32.4408 7.09851 33.5432L5.25517 37.2824C5.02589 37.7477 4.89854 38.2566 4.88165 38.7751C4.86475 39.2935 4.95869 39.8096 5.15718 40.2889C5.35568 40.7682 5.65417 41.1996 6.0327 41.5543C6.41124 41.9089 6.86111 42.1788 7.35226 42.3457L11.2985 43.6874C12.4617 44.083 13.5185 44.7406 14.3873 45.6094C15.2561 46.4782 15.9137 47.535 16.3093 48.6982L17.651 52.6474C17.8171 53.1391 18.0865 53.5896 18.4411 53.9687C18.7956 54.3478 19.2271 54.6466 19.7067 54.8452C20.1862 55.0439 20.7027 55.1376 21.2214 55.1202C21.7401 55.1029 22.2492 54.9747 22.7143 54.7445L26.4564 52.9011C27.5589 52.3582 28.7713 52.0758 30.0002 52.0758C31.229 52.0758 32.4415 52.3582 33.5439 52.9011L37.2831 54.7445C37.427 54.8125 37.5728 54.8718 37.7206 54.9224C38.6357 55.2339 39.6371 55.1693 40.5045 54.7427C41.372 54.316 42.0345 53.5624 42.3464 52.6474L43.6881 48.6982C44.0837 47.535 44.7413 46.4782 45.6101 45.6094C46.4789 44.7406 47.5357 44.083 48.6989 43.6874L52.6481 42.3457C52.7939 42.2952 52.9398 42.2349 53.0856 42.1649C53.5152 41.9533 53.8989 41.6592 54.2149 41.2993C54.5308 40.9394 54.7727 40.5208 54.9268 40.0674C55.0809 39.614 55.1442 39.1347 55.113 38.6568C55.0819 38.179 54.9569 37.7119 54.7452 37.2824L52.9018 33.5403C52.3594 32.4383 52.0773 31.2263 52.0773 29.998C52.0773 28.7697 52.3594 27.5578 52.9018 26.4557L54.7452 22.7136C54.9745 22.2483 55.1018 21.7394 55.1187 21.2209C55.1356 20.7025 55.0417 20.1864 54.8432 19.7071C54.6447 19.2278 54.3462 18.7965 53.9676 18.4418C53.5891 18.0871 53.1392 17.8172 52.6481 17.6503L48.6989 16.3086C47.5357 15.913 46.4789 15.2554 45.6101 14.3866C44.7413 13.5178 44.0837 12.461 43.6881 11.2978L42.3464 7.35155L42.2618 7.12989L42.1656 6.91405L41.9848 6.5903C41.5056 5.82494 40.7595 5.26461 39.8909 5.01782C39.0223 4.77104 38.0931 4.85537 37.2831 5.25447L33.541 7.0978C32.439 7.64028 31.227 7.92239 29.9987 7.92239C28.7704 7.92239 27.5585 7.64028 26.4564 7.0978L22.7143 5.25447C22.2492 5.02569 21.7405 4.89875 21.2224 4.88211C20.7043 4.86548 20.1885 4.95953 19.7096 5.15799C19.2307 5.35645 18.7996 5.65477 18.4452 6.03302C18.0907 6.41128 17.8209 6.86079 17.6539 7.35155M24.3127 36.9703L40.121 21.162C40.5113 20.7706 41.0354 20.5415 41.5877 20.5207C42.1401 20.4999 42.6798 20.689 43.0985 21.05C43.5171 21.411 43.7835 21.9171 43.8442 22.4665C43.9049 23.0159 43.7553 23.5679 43.4256 24.0115L43.2127 24.2566L25.7127 41.7565C25.317 42.1522 24.7847 42.3811 24.2254 42.3963C23.6661 42.4115 23.1222 42.2118 22.7056 41.8382L22.4868 41.6107L15.1952 32.8607C14.8426 32.4365 14.6633 31.8947 14.6932 31.3439C14.7231 30.7931 14.96 30.2739 15.3565 29.8903C15.753 29.5068 16.2797 29.2872 16.8312 29.2755C17.3827 29.2638 17.9183 29.461 18.3306 29.8274L18.5552 30.0607L24.3127 36.9703Z"
                          fill="#9CA3AF"
                        />
                      </svg>
                      <p className="text-sm font-medium leading-4 text-[#6B7280]">All questions have been answered</p>
                    </div>
                  ) : (
                    filteredQuestions()?.map(
                      (item: any, index: number) =>
                        item.question.toLowerCase().includes(searchQuestions.toLowerCase()) && (
                          <Drawer.Body.QuestionOfScreening
                            className="dark:text-white "
                            isExpandAllAnswers={expandQuestionToggle}
                            key={item._id}
                            index={index}
                            row={item} //@TODO: Markos will fix this
                          />
                        )
                    )
                  )}
                </div>
              )}
            </>
          ) : (
            <>
              {/* Chart Progress Section */}
              <div className="flex flex-col lg:flex-row justify-start items-center mt-3 gap-6 w-full">
                <div className="flex flex-col items-start justify-center">
                  <div className="font-medium dark:text-white space-y-4">
                    {/* Test Charts */}
                    <ResultChart test={test} size={140} fontSize="30px" />
                  </div>
                </div>

                {(correctAnswers > 0 || wrongAnswers > 0 || unAnsweredQuestions > 0 || skippedQuestions > 0) && (
                  <div className="w-full">
                    <div className="space-y-2 w-full lg:w-7/12">
                      <h2 className="text-base font-medium text-gray-900 dark:text-white">Score Breakdown</h2>

                      {/* Breakdown Bar */}
                      <div className="flex h-3 w-full space-x-2">
                        {correctPercentage > 0 && (
                          <div
                            style={{ width: `${correctPercentage}%` }}
                            className="h-full bg-[#4AA264] dark:bg-[#3a8a3a] dark:bg-opacity-60  rounded-sm "
                          ></div>
                        )}

                        {wrongPercentage > 0 && (
                          <div
                            style={{ width: `${wrongPercentage}%` }}
                            className="h-full bg-[#DC7E83] dark:bg-[#c9302c]  dark:bg-opacity-60 rounded-sm"
                          ></div>
                        )}

                        {unansweredPercentage > 0 && (
                          <div style={{ width: `${unansweredPercentage}%` }} className="h-full bg-[#cbcbcb] dark:bg-[#939090] rounded-sm"></div>
                        )}

                        {skippedPercentage > 0 && (
                          <div style={{ width: `${skippedPercentage}%` }} className="h-full bg-pastelYellow dark:bg-pastelYellow rounded-sm"></div>
                        )}
                      </div>
                    </div>

                    {/* Breakdown Labels */}
                    <div className="flex flex-col sm:flex-row items-start justify-start gap-4 mt-2">
                      {correctAnswers > 0 && (
                        <div className="flex px items-center gap-2">
                          <div className="w-2.5 h-2.5 inline-block bg-[#4AA264] dark:bg-[#3a8a3a] dark:bg-opacity-60  rounded-full"></div>
                          <span className="text-gray-900 dark:text-gray-100 text-sm">Correct answers:</span>
                          <span className="dark:text-gray-400 font-medium">{correctAnswers}</span>
                        </div>
                      )}
                      {wrongAnswers > 0 && (
                        <div className="flex px items-center gap-2">
                          <div className="w-2.5 h-2.5 inline-block  bg-[#DC7E83] dark:bg-[#c9302c]  dark:bg-opacity-60 rounded-full"></div>
                          <span className="text-gray-900 dark:text-gray-100 text-sm">Wrong answers:</span>
                          <span className="dark:text-gray-400 font-medium">{wrongAnswers}</span>
                        </div>
                      )}

                      {unAnsweredQuestions > 0 && (
                        <div className="flex px items-center gap-2">
                          <div className="w-2.5 h-2.5 inline-block  bg-[#cbcbcb] dark:bg-[#939090] rounded-full"></div>
                          <span className="text-gray-900 dark:text-gray-100 text-sm">Unanswered:</span>
                          <span className="dark:text-gray-400 font-medium">{unAnsweredQuestions}</span>
                        </div>
                      )}

                      {skippedQuestions > 0 && (
                        <div className="flex px items-center gap-2">
                          <div className="w-2.5 h-2.5 inline-block  bg-pastelYellow dark:bg-pastelYellow rounded-full"></div>
                          <span className="text-gray-900 dark:text-gray-100 text-sm">Skipped questions:</span>
                          <span className="dark:text-gray-400 font-medium">{skippedQuestions}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* English and attitude score */}
              {test?.english_score >= 0 && test?.attitude_score >= 0 && (
                <div className="mt-4">
                  <div className="flex flex-col md:flex-row gap-4 py-3 rounded-md bg-opacity-30 dark:bg-darkBackgroundCard">
                    <div className="flex flex-col xslg:flex-row sm:items-center gap-2 text-[#667085]">
                      <div className="flex gap-2">
                        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M20.3125 3H4.6875C3.82456 3 3.125 3.67157 3.125 4.5V19.5C3.125 20.3284 3.82456 21 4.6875 21H20.3125C21.1754 21 21.875 20.3284 21.875 19.5V4.5C21.875 3.67157 21.1754 3 20.3125 3Z"
                            stroke="#667085"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                          <path
                            d="M10.9382 8.5H6.77148V15.5H10.6777M6.77148 12H10.6777M13.5423 9.5V15.5V12.25C13.5423 11.6533 13.7892 11.081 14.2288 10.659C14.6683 10.2371 15.2645 10 15.8861 10C16.5077 10 17.1038 10.2371 17.5433 10.659C17.9829 11.081 18.2298 11.6533 18.2298 12.25V15.5"
                            stroke="#667085"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          />
                        </svg>
                        <p className="text-base dark:text-gray-400 font-medium">English Proficiency:</p>
                      </div>
                      {englishProficiency(test?.english_score)}
                    </div>

                    <div className="flex flex-col xslg:flex-row sm:items-center gap-` text-[#667085]">
                      <div className="flex gap-2">
                        <svg width="23" height="24" viewBox="0 0 25 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M6.39088 5.7778C6.1583 5.67326 5.89373 5.66539 5.65535 5.75591C5.41696 5.84643 5.2243 6.02793 5.11973 6.26049L3.03992 10.8788C2.93526 11.1111 2.92711 11.3755 3.01725 11.6138C3.1074 11.8522 3.28847 12.045 3.52069 12.1499L4.95338 12.7951C5.06847 12.8471 5.19268 12.8759 5.3189 12.8798C5.44512 12.8838 5.57089 12.8629 5.68902 12.8182C5.80715 12.7736 5.91533 12.7061 6.00738 12.6196C6.09943 12.5332 6.17354 12.4294 6.2255 12.3143L8.3053 7.69511C8.40996 7.46277 8.41811 7.19839 8.32797 6.96004C8.23782 6.7217 8.05675 6.52888 7.82453 6.42395L6.39088 5.7778ZM4.68607 11.4768C4.84444 11.5482 5.02468 11.5538 5.18715 11.4923C5.34962 11.4308 5.48101 11.3073 5.55242 11.149C5.62382 10.9906 5.62939 10.8103 5.5679 10.6479C5.50641 10.4854 5.3829 10.354 5.22453 10.2826C5.06617 10.2112 4.88593 10.2056 4.72345 10.2671C4.56098 10.3286 4.42959 10.4521 4.35819 10.6105C4.28678 10.7689 4.28121 10.9491 4.3427 11.1116C4.40419 11.274 4.52771 11.4054 4.68607 11.4768ZM21.1688 12.2759C21.2868 12.2311 21.3949 12.1634 21.4869 12.0768C21.5788 11.9902 21.6528 11.8864 21.7045 11.7712C21.7563 11.656 21.7849 11.5318 21.7887 11.4055C21.7924 11.2793 21.7713 11.1536 21.7265 11.0355L19.9332 6.30088C19.8885 6.18272 19.8209 6.07453 19.7344 5.9825C19.6478 5.89046 19.544 5.81639 19.4288 5.76451C19.3136 5.71262 19.1893 5.68395 19.063 5.68013C18.9367 5.6763 18.8109 5.6974 18.6928 5.74222L17.2245 6.29895C17.1065 6.34377 16.9984 6.4114 16.9064 6.49799C16.8145 6.58458 16.7405 6.68844 16.6888 6.80362C16.637 6.91881 16.6084 7.04307 16.6046 7.1693C16.6009 7.29554 16.622 7.42128 16.6668 7.53934L18.4611 12.2749C18.5514 12.5133 18.7327 12.7061 18.9651 12.8109C19.1975 12.9156 19.4621 12.9238 19.7005 12.8336L21.1688 12.2759ZM18.3447 7.0403C18.2643 7.07079 18.1907 7.11683 18.128 7.17579C18.0654 7.23474 18.015 7.30546 17.9796 7.38389C17.9443 7.46233 17.9248 7.54696 17.9222 7.63294C17.9196 7.71892 17.9339 7.80458 17.9644 7.88501C17.9949 7.96544 18.041 8.03909 18.0999 8.10173C18.1589 8.16437 18.2296 8.21479 18.308 8.2501C18.3865 8.28541 18.4711 8.30493 18.5571 8.30754C18.6431 8.31015 18.7287 8.29579 18.8092 8.2653C18.9716 8.20371 19.1029 8.08012 19.1742 7.9217C19.2456 7.76329 19.251 7.58303 19.1894 7.42059C19.1279 7.25814 19.0043 7.12682 18.8458 7.0555C18.6874 6.98418 18.5072 6.97871 18.3447 7.0403Z"
                            fill="#667085"
                          />
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M17.196 7.33352H17.2037C17.2666 7.32803 17.3279 7.31019 17.3839 7.28103C17.44 7.25187 17.4898 7.21195 17.5304 7.16355C17.571 7.11516 17.6017 7.05924 17.6207 6.99898C17.6397 6.93873 17.6467 6.87531 17.6412 6.81237C17.6357 6.74942 17.6179 6.68817 17.5887 6.63212C17.5595 6.57607 17.5196 6.52631 17.4712 6.48568C17.4228 6.44506 17.3669 6.41436 17.3067 6.39535C17.2464 6.37633 17.183 6.36938 17.12 6.37487H17.1123L17.0873 6.37775L16.995 6.38737L16.6547 6.42006C16.3691 6.44891 15.9739 6.4941 15.5373 6.55179C14.6768 6.66525 13.6104 6.83833 12.9123 7.07102C12.5739 7.18352 12.2162 7.40179 11.8739 7.65179C11.5268 7.90564 11.1672 8.21429 10.8229 8.53448C10.212 9.11103 9.6283 9.71586 9.07389 10.347C8.671 10.8028 8.57773 11.5297 9.05081 12.0451C9.3585 12.3778 9.83927 12.7989 10.4623 12.9874C11.1133 13.1826 11.8633 13.1066 12.6297 12.5374L13.5902 11.9172L13.6075 11.9066C13.745 11.9903 13.9364 12.1345 14.1652 12.3258C14.4152 12.5354 14.6864 12.7826 14.9393 13.0201C15.2263 13.2906 15.5084 13.5663 15.7854 13.847L15.8383 13.9018L15.8518 13.9153L15.8566 13.9201L15.9133 13.9778L15.9854 14.0153C16.3729 14.2076 16.7989 14.1383 17.1297 14.0268C17.4739 13.9114 17.8162 13.7153 18.1075 13.522C18.4723 13.2765 18.8179 13.0036 19.1412 12.7056L19.1585 12.6903L19.1633 12.6854L19.1643 12.6845C19.1643 12.6845 19.0797 12.3864 18.7489 12.0383L18.4883 12.3653L18.2681 12.9999L15.8566 13.9201L16.345 13.4806L16.0873 12.7912C15.9262 12.6325 15.7633 12.4754 15.5989 12.3201C15.3393 12.0758 15.0527 11.8153 14.7816 11.5893C14.5191 11.3681 14.246 11.1585 14.0181 11.0335C13.6393 10.8258 13.2681 10.9797 13.0681 11.1095L12.0797 11.7479L12.0662 11.7576C11.5325 12.1576 11.0941 12.1729 10.7393 12.0662C10.3547 11.9508 10.0133 11.6701 9.7585 11.3931C9.67869 11.3066 9.65562 11.1403 9.79408 10.9835C10.3281 10.3759 10.8903 9.7935 11.4787 9.23833C11.8056 8.93352 12.1345 8.65179 12.4412 8.42775C12.7547 8.19891 13.0181 8.04987 13.2172 7.98352C13.8172 7.78352 14.7989 7.6191 15.6633 7.50468C16.136 7.44256 16.6098 7.38903 17.0845 7.3441L17.1739 7.33545L17.196 7.33352Z"
                            fill="#667085"
                          />
                          <path
                            d="M16.468 13.17C16.3424 13.0427 16.2158 12.9164 16.0882 12.7911L16.3459 13.4806L15.8574 13.92L18.269 12.9998L18.4892 12.3652L18.7497 12.0383L18.5045 11.9863L18.5017 11.9883L18.4882 12.0008L18.4363 12.0488C18.1657 12.293 17.8781 12.5176 17.5757 12.7209C17.3161 12.894 17.0536 13.0383 16.8228 13.1152C16.644 13.1758 16.5324 13.1806 16.468 13.17Z"
                            fill="#667085"
                          />
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M7.73749 8.25796L7.54807 7.81565L7.35864 7.37335L7.3971 7.35796L7.50576 7.31181C8.03885 7.08546 8.57413 6.8643 9.11153 6.64835C9.54799 6.47224 9.98782 6.30458 10.4308 6.14546C10.6259 6.07623 10.8077 6.01662 10.9596 5.97335C11.0904 5.93585 11.2577 5.89258 11.3942 5.89258C11.5192 5.89258 11.6509 5.92142 11.7596 5.95027C11.8769 5.982 12.0058 6.02431 12.1365 6.07239C12.4 6.16854 12.6971 6.29354 12.9721 6.41469C13.2928 6.55803 13.6109 6.70742 13.926 6.86277L13.9894 6.89354L14.0067 6.90219L14.0125 6.90508C14.1266 6.96207 14.2134 7.06207 14.2538 7.18307C14.2942 7.30406 14.2849 7.43615 14.2279 7.55027C14.1709 7.66439 14.0709 7.75119 13.9499 7.79159C13.8289 7.83198 13.6968 7.82265 13.5827 7.76566L13.5779 7.76277L13.5625 7.75508L13.5029 7.72623C13.1982 7.5764 12.8907 7.43214 12.5808 7.29354C12.327 7.1792 12.0695 7.07338 11.8086 6.97623C11.7109 6.94017 11.6118 6.90809 11.5115 6.88008C11.4739 6.86943 11.4357 6.86108 11.3971 6.85508L11.3885 6.857C11.3692 6.85989 11.3183 6.87046 11.2231 6.89739C11.0639 6.94444 10.9062 6.99607 10.75 7.05219C10.3769 7.18392 9.91826 7.36181 9.46922 7.54162C8.93783 7.75445 8.40863 7.97273 7.88172 8.19642L7.77499 8.24162L7.73749 8.25796ZM7.10576 8.00508C7.05571 7.88789 7.05424 7.75563 7.10166 7.63736C7.14907 7.51908 7.24151 7.42447 7.35864 7.37431L7.54807 7.81662L7.73749 8.25796C7.67941 8.28289 7.61699 8.29612 7.55379 8.2969C7.49059 8.29768 7.42786 8.286 7.36919 8.26251C7.31051 8.23902 7.25705 8.20419 7.21184 8.16002C7.16664 8.11585 7.13059 8.0632 7.10576 8.00508ZM5.27595 11.8128C5.31932 11.7669 5.3713 11.7299 5.42894 11.7041C5.48658 11.6783 5.54874 11.6641 5.61187 11.6623C5.675 11.6605 5.73786 11.6712 5.79687 11.6937C5.85588 11.7162 5.90987 11.7502 5.95576 11.7935L5.62499 12.1426L5.29518 12.4926C5.24927 12.4492 5.21235 12.3972 5.18653 12.3396C5.16072 12.2819 5.14651 12.2198 5.14472 12.1567C5.14294 12.0935 5.15361 12.0307 5.17612 11.9717C5.19864 11.9127 5.23256 11.8587 5.27595 11.8128ZM9.67018 15.0695L12.3269 16.0657C12.5862 16.1628 12.8679 16.1833 13.1385 16.1246C13.4091 16.066 13.6571 15.9307 13.8529 15.7349L15.8625 13.7253C15.9528 13.6351 16.0752 13.5845 16.2027 13.5846C16.3303 13.5847 16.4526 13.6355 16.5428 13.7258C16.6329 13.816 16.6835 13.9384 16.6834 14.066C16.6833 14.1936 16.6326 14.3159 16.5423 14.406L14.5327 16.4157C14.2063 16.7417 13.7929 16.967 13.3419 17.0645C12.8909 17.1621 12.4214 17.1277 11.9894 16.9657L9.27787 15.9493L9.25576 15.9387C9.10871 15.8572 8.96908 15.7629 8.83845 15.657C8.68268 15.5378 8.49999 15.3878 8.30287 15.2205C7.86154 14.8431 7.42623 14.4588 6.9971 14.0676C6.47277 13.5913 5.95256 13.1105 5.43653 12.6253L5.33172 12.5272L5.29518 12.4926L5.62499 12.1426L5.95576 11.7935L5.99134 11.8272L6.09422 11.9243C6.60628 12.4055 7.12232 12.8824 7.6423 13.3551C8.0846 13.756 8.54134 14.1628 8.92403 14.4878C9.11634 14.6503 9.28653 14.7887 9.42403 14.8955C9.55095 14.9926 9.63076 15.0464 9.67018 15.0685M6.41345 17.1233C6.49519 17.0259 6.61222 16.9648 6.73892 16.9534C6.86561 16.9421 6.99165 16.9814 7.08941 17.0628L8.01153 17.832C8.21817 18.0045 8.46898 18.1158 8.73557 18.1532L9.92403 18.3205C9.98796 18.3275 10.0498 18.3472 10.106 18.3786C10.1622 18.41 10.2115 18.4523 10.251 18.503C10.2905 18.5538 10.3194 18.6119 10.336 18.6741C10.3527 18.7362 10.3567 18.801 10.3478 18.8647C10.3389 18.9284 10.3173 18.9897 10.2843 19.0449C10.2514 19.1002 10.2076 19.1482 10.1557 19.1862C10.1038 19.2242 10.0449 19.2514 9.98227 19.2662C9.91968 19.281 9.85475 19.2831 9.79133 19.2724L8.60191 19.106C8.15757 19.0436 7.73956 18.8581 7.39518 18.5705L6.47307 17.8012C6.42452 17.7607 6.38445 17.7111 6.35512 17.6551C6.3258 17.5991 6.30781 17.5378 6.30219 17.4749C6.29656 17.4119 6.30341 17.3484 6.32233 17.2881C6.34126 17.2278 6.37286 17.1718 6.41345 17.1233Z"
                            fill="#667085"
                          />
                          <path
                            fillRule="evenodd"
                            clipRule="evenodd"
                            d="M12.4997 24.0574C18.6064 24.0574 23.5574 19.1064 23.5574 12.9997C23.5574 6.89297 18.6064 1.94201 12.4997 1.94201C6.39297 1.94201 1.44201 6.89297 1.44201 12.9997C1.44201 19.1064 6.39297 24.0574 12.4997 24.0574ZM12.4997 25.0189C19.1382 25.0189 24.5189 19.6382 24.5189 12.9997C24.5189 6.36124 19.1382 0.980469 12.4997 0.980469C5.86124 0.980469 0.480469 6.36124 0.480469 12.9997C0.480469 19.6382 5.86124 25.0189 12.4997 25.0189Z"
                            fill="#667085"
                          />
                        </svg>
                        <p className="text-base dark:text-gray-400 font-medium">Attitude Score:</p>
                      </div>
                      {englishProficiency(test?.attitude_score)}
                    </div>
                  </div>
                </div>
              )}

              {/* WeirdBehavior */}
              {WeirdBehavior && (
                <div className="flex flex-wrap  items-center w-full mt-5 p-3 bg-[#ffdddd] dark:bg-[#561a1c] dark:bg-opacity-25  dark:border-gray-700  bg-opacity-30  gap-4">
                  <div className="flex items-center gap-2">
                    <Icon icon="fluent:warning-16-filled" className="text-[#C72716] dark:text-[#e5a2a2]" width={'25'} />
                    <p className="text-[#C72716] dark:text-[#e5a2a2] text-sm ">Suspicious behavior detected:</p>
                  </div>

                  <div className="flex flex-wrap w-fit lg:gap-6  gap-4 text-sm items-center">
                    {test?.weirdBehavior?.tabSwitchedCount > 0 && (
                      <div className="flex flex-wrap items-center">
                        <h2 className="dark:text-gray-200 px-2">Tab Switches:</h2>
                        <div className="flex items-center">
                          <div className="text-[#C72716]">
                            {test?.weirdBehavior?.tabSwitchedCount}
                            <span className="text-[#000000] px-1 dark:text-white">times</span>
                          </div>
                          <Tooltip className="z-[100]" content=" The user switched tabs during the test, possibly to look up answers.">
                            <Icon icon="quill:info" className="text-[#1C274C] dark:text-gray-400" width={'20'} />
                          </Tooltip>
                        </div>
                      </div>
                    )}
                    {test?.weirdBehavior?.ipChangeCount > 0 && (
                      <div className="flex flex-wrap items-center g">
                        <h2 className="dark:text-gray-200 ">IP Changes:</h2>
                        <div className="flex items-center">
                          <div className="text-[#C72716] px-2">
                            {test?.weirdBehavior?.ipChangeCount} <span className="text-[#000000] px-1 dark:text-white">times</span>
                          </div>
                          <Tooltip className="z-[100] " content=" The IP address changed during the test, possibly using external resources.">
                            <Icon icon="quill:info" className="text-[#1C274C] dark:text-gray-400" width={'20'} />
                          </Tooltip>
                        </div>
                      </div>
                    )}
                    {test?.weirdBehavior?.openContextMenuCount > 0 && (
                      <div className="flex flex-wrap items-center gap-1">
                        <h2 className="dark:text-gray-200 ">Context Menu:</h2>
                        <div className="flex items-center">
                          <div className="text-[#C72716] px-2">
                            {test?.weirdBehavior?.openContextMenuCount} <span className="text-[#000000] dark:text-white px-1 ">times</span>
                          </div>
                          <Tooltip className="z-[100]" content="The context menu was opened, suggesting attempts to find answers.">
                            <Icon icon="quill:info" className="text-[#1C274C] dark:text-gray-400" width={'20'} />
                          </Tooltip>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </>
          )
        ) : (
          statusIsNotSubmitted()
        )}
      </Card>

      {/* Need Subscription */}
      {needSubscription && <SubscribeDialog onClose={() => setNeedSubscription(false)} />}
    </>
  );
};
