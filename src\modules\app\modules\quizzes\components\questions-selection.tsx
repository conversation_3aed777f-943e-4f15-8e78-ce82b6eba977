// UI
import { Drawer } from 'src';

// Components
import { Placeholder } from 'UI/src';
// Types

type QuestionsSelectionProps = {
  currentQuestions: any[];
  form: any;
  currentPage: number;
  questionsPerPage: number;
  setFieldValue: (...args: any[]) => void;
  questionDatabase: any[];
  setQuestionDatabase: (...args: any[]) => void;
  setAnyQuestionHasEditMode: (...args: any[]) => void;
};

export const QuestionsSelection = ({
  currentQuestions,
  form,
  currentPage,
  questionsPerPage,
  setFieldValue,
  questionDatabase,
  setQuestionDatabase,
  setAnyQuestionHasEditMode,
}: QuestionsSelectionProps) => {
  // const noDataFound = {
  //   imageSrc: noData,
  //   messageHeader: 'No questions have been added yet.',
  // };

  return (
    <div className="my-2 overflow-y-auto">
      {currentQuestions.length ? (
        currentQuestions.map((row: any, index: number) => (
          <div className="px-2 py-1">
            {/* TODO: FIXME: Markos fix this */}
            {/* @ts-ignore */}
            <Drawer.QuestionOfTest
              key={row?._id}
              row={row}
              index={index}
              mainQuestionsListForm={form}
              mainSetFieldValueForm={setFieldValue}
              currentPage={currentPage}
              questionsPerPage={questionsPerPage}
              questionDatabase={questionDatabase}
              setQuestionDatabase={setQuestionDatabase}
              canRemoveQuestion
              setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
            />
          </div>
        ))
      ) : (
        <div className="ml-auto mr-auto w-2/4 space-y-2">
          <Placeholder image="/UI/src/assets/placeholder/NoQuestions.svg" title="No questions created yet" subTitle="" />
        </div>
      )}
    </div>
  );
};
