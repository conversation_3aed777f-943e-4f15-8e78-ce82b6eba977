// React
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { format, isValid } from 'date-fns';

// Components
import { CandidateProfileCard } from './candiadte-card';

import {
  CustomIcon,
  Card,
  NoDataFound,
  TestSeniorityLevel,
  Table,
  AvarageScore,
  NameFieldColumn,
  EmailFieldColumn,
  ResultStatusSubmission,
  Icon,
} from 'src';
import { AlertNote, Dialog, Tags, api } from 'UI';
import { SubmissionPublic, Api, useFetchList, setErrorNotify, setNotifyMessage, useAppDispatch } from 'UI/src';
import { PlanFeatures, Button } from 'UI';
import { ExportReportsData } from './export-reports-data';

interface ComponentOverlayInterviewProps {
  onClose: () => void;
  id?: string;
  type?: string;
  onInterviewRecord?: any;
}

interface ApiDataPublicTypes {
  averageScore: number;
  completionRate: number;
  totalUsage: number;
  authorName?: string;
  createdAt?: string;
  dueDate?: string;
  assessmentUrl?: string;
}

interface LoadingInterviewRecord {
  id: string;
  loading: boolean;
}

interface AssignedApplicant {
  _id: string;
  applicantId: string;
  applicantName: string;
  applicantEmail: string;
  seniorityLevel: string;
  status: string;
  averageScore: number;
  assessmentId: string;
  recordInterview?: boolean;
  score?: number;
}

export const ComponentOverlayInterview = ({ onClose, id, type, onInterviewRecord }: ComponentOverlayInterviewProps) => {
  // State
  const [showMoreMap, setShowMoreMap] = useState<Record<string, boolean>>({});
  const [backupList, setBackupList] = useState<AssignedApplicant[]>([]);
  const [apiDataPublic, setApiDataPublic] = useState<ApiDataPublicTypes | undefined>(undefined);
  const [applicantDetails, setApplicantDetails] = useState<AssignedApplicant | null>(null);
  const [loadingInterviewRecord, setLoadingInterviewRecord] = useState<LoadingInterviewRecord[]>([]);
  const [isExportReportsDataVisible, setExportReportsDataVisibility] = useState<Boolean>(false);

  const getBlocksCards = () => {
    return [
      {
        header: 'Average Score',
        subHeader: apiDataPublic?.averageScore,
        icon: 'average',
        percentage: true,
      },
      {
        header: 'Completion Rate',
        // Handle both questionIds and questions array for screening
        subHeader: apiDataPublic?.completionRate,
        icon: 'nike',
        percentage: true,
      },
      {
        header: 'Usage',
        subHeader: apiDataPublic?.totalUsage,
        icon: 'userWithNike',
      },
    ];
  };

  // Hooks
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const initialFilters = {
    seniorityLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },
    status: {
      label: 'Status',
      enum: 'SubmissionStatus',
    },
    score: {
      label: 'Score',
      enum: 'AverageScore',
    },
  };
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters } = useFetchList(
    `${type === 'interview' ? 'ai-interview' : 'submissions'}/assigned-applicants/by-link`,
    {
      search: '',
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
      id: id,
    }
  );

  const formatDate = (customDate?: string | Date) => {
    if (!customDate) return 'Invalid date';
    const date = new Date(customDate);
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, "dd MMMM , yyyy 'At' hh:mm a");
  };

  const handleGetData = async () => {
    let AssessmentType;
    if (type === 'interview') {
      AssessmentType = 'ai-interview';
    } else {
      AssessmentType = 'submissions';
    }
    try {
      setLoading(true);
      const response = await Api.get<SubmissionPublic>(`${AssessmentType}/public/${id}`);
      console.log('${AssessmentType}/public', response.data);
      setApiDataPublic(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message || 'Error fetching assessment public data'));
    } finally {
      setLoading(false);
    }
  };

  const handleInterviewRecord = async (row: AssignedApplicant) => {
    setApplicantDetails(row);
    onInterviewRecord(row);
    setLoadingInterviewRecord((prev) => prev.map((item) => (item.id === row.assessmentId ? { ...item, loading: true } : item)));
  };

  const handleMimicLoading = () => {
    if (applicantDetails) {
      const recordToUpdate = loadingInterviewRecord.find((item) => item.id === applicantDetails.assessmentId);
      if (recordToUpdate && recordToUpdate.loading) {
        const timer = setTimeout(() => {
          setLoadingInterviewRecord((prev) => prev.map((item) => (item.id === applicantDetails.assessmentId ? { ...item, loading: false } : item)));
        }, 500);
        return () => clearTimeout(timer);
      }
    }
  };

  const downloadDocument = async (row: any) => {
    try {
      const response = await api.get(
        `${type === 'test' ? 'submissions' : type === 'interviews' ? 'ai-interview' : type === 'screening' ? 'submissions' : '—'}/stages/report/${
          row._id
        }`,
        {
          responseType: 'blob',
        }
      );
      const url = window.URL.createObjectURL(
        new Blob([response.data], {
          type: response.headers['content-type'],
        })
      );
      const a = document.createElement('a');
      a.href = url;
      a.download = `${type === 'test' ? 'test' : type === 'interviews' ? 'interview' : type === 'screening' ? 'screening' : '—'}-report.xlsx`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  useEffect(() => {
    handleGetData();
  }, []);

  useEffect(() => {
    if (backupList.length === 0 && Array.isArray(list)) {
      setBackupList(list as AssignedApplicant[]);
      setLoadingInterviewRecord(
        (list as AssignedApplicant[]).map((item) => ({
          id: item.assessmentId,
          loading: false,
        }))
      );
    }
  }, [list, backupList.length]);

  useEffect(() => {
    handleMimicLoading();
  }, [loadingInterviewRecord]);

  return (
    <>
      <Dialog size="6xl" isOpen title="View Generated Link" onClose={onClose}>
        <div className="space-y-4 max-h-[calc(100vh-200px)] overflow-y-auto">
          <div className="flex justify-between items-center">
            <p className="text-[#667085]">
              Created by {apiDataPublic?.authorName || '—'} , <span className="font-semibold">{formatDate(apiDataPublic?.createdAt)}</span>
            </p>
            {/* FIXME: Make this button works fine */}
            {/* <Button
              label="Export 4 Reports"
              icon={<Icon icon="ri:download-line" />}
              colorType="secondary"
              variant="lg"
              onClick={() => setExportReportsDataVisibility(true)}
            /> */}
          </div>

          {/* blocks */}
          <div className="grid sm:grid-cols-1 lg:grid-cols-3 gap-4">
            {getBlocksCards().map((block) => (
              <div key={block.header} className="px-3 py-5 shadow-lg shadow-[#743AF51A] rounded-lg text-center">
                <p className="text-sm text-[#4E5E82] font-medium">{block.header}</p>
                <p className="text-base font-semibold text-[#1B1F3B]">
                  {block.subHeader != null ? block.subHeader : '—'}
                  {block.percentage && typeof block.subHeader === 'number' && !isNaN(block.subHeader) ? ' %' : ''}
                </p>
                {/* <CustomIcon definedIcon={block.icon} /> */}
              </div>
            ))}
          </div>

          {apiDataPublic?.dueDate && new Date(apiDataPublic.dueDate).getTime() - new Date().getTime() > 0 && (
            <>
              {/* <h3 className="text-[#566577]">Share Link</h3> */}
              <div className="flex gap-4 flex-col sm:flex-row items-center sm:items-stretch">
                <div className="flex w-full  items-center justify-between space-y-0 rounded-lg grow ">
                  <input
                    type="text"
                    placeholder="https://assessment.thepass.ai/test_a331717712621611661"
                    value={apiDataPublic?.assessmentUrl || ''}
                    className="w-full px-4 py-3 dark:bg-gray-700 bg-gray-white text-[13.5px] bg-[#F8FAFC] text-gray-800 border border-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white truncate rounded-lg focus:ring-0 focus:border-gray-300 shadow-sm"
                    readOnly
                  />
                </div>
                <Button
                  label="Copy"
                  customIcon={{ definedIcon: 'copy', width: '16', height: '16', stroke: '#743AF5' }}
                  colorType="tertiary"
                  variant="md"
                  className="bg-[#E2E8F0]  w-fit "
                  onClick={() => {
                    const input = document.querySelector(
                      'input[placeholder="https://assessment.thepass.ai/test_a331717712621611661"]'
                    ) as HTMLInputElement | null;
                    if (input) {
                      navigator.clipboard.writeText(input.value);
                      dispatch(setNotifyMessage('Link copied'));
                    }
                  }}
                />
              </div>
            </>
          )}

          <AlertNote dateMessage={apiDataPublic?.dueDate || ''} />

          <Table
            addButtonPermission={false}
            ready={ready}
            loading={loading}
            title="Usage this Generated Link"
            searchPlaceholder="Search for applicants..."
            count={count}
            search={search}
            filters={filters}
            setFilters={setFilters}
            pagination={pagination}
            rows={list as AssignedApplicant[]}
            backupRows={backupList}
            slots={{
              applicantName: (_: any, row: AssignedApplicant) => (
                <NameFieldColumn
                  id={row?._id}
                  name={row?.applicantName}
                  showMoreMap={showMoreMap}
                  onClick={() => navigate(`/app/applicants/progress/${row.applicantId}`)}
                />
              ),
              applicantEmail: (_: unknown, row: AssignedApplicant) => <EmailFieldColumn email={row?.applicantEmail} onClick={() => {}} />,
              seniorityLevel: (_: unknown, row: AssignedApplicant) => {
                const getSeniorityLevelText = (level: number): string => {
                  switch (level) {
                    case 1:
                      return 'intern';
                    case 2:
                      return 'fresh';
                    case 3:
                      return 'junior';
                    case 4:
                      return 'mid-level';
                    case 5:
                      return 'senior';
                    default:
                      return '-';
                  }
                };

                return (
                  <div className="w-fit">
                    <Tags type={getSeniorityLevelText(Number(row?.seniorityLevel))} color="bg-transparent" />
                  </div>
                );
              },

              activeStatus: (_: unknown, row: AssignedApplicant) => <ResultStatusSubmission statusSubmission={row.status} />, // statusSubmission prop
              averageScore: (_: unknown, row: AssignedApplicant) => {
                const getScoreColor = (score: number | undefined) => {
                  if (score === undefined || score === null) {
                    return 'bg-gray-100 text-gray-800';
                  }
                  if (score >= 0 && score < 50) {
                    return 'bg-[#FFECE9] text-[#A80000]';
                  } else if (score >= 50 && score < 75) {
                    return 'bg-[#FFEDD8] text-[#E9760F]';
                  } else if (score >= 75 && score < 100) {
                    return 'bg-[#FFFCDF] text-[#BA8500]';
                  } else if (score >= 100) {
                    return 'bg-[#EEFFF1] text-[#056816]';
                  }
                  return 'bg-gray-100 text-gray-800';
                };

                const getScoreText = (score: number | undefined) => {
                  if (score === null || score === undefined) return '—';
                  return `${score}%`;
                };

                return (
                  <div className="w-fit">
                    <Tags type="score" color={getScoreColor(row?.score)}>
                      {getScoreText(row?.score)}
                    </Tags>
                  </div>
                );
              },
            }}
            columns={[
              {
                key: 'applicantName',
                label: 'Name',
                primary: true,
                width: '22%',
              },
              {
                key: 'applicantEmail',
                label: 'Email',
                primary: true,
                width: '23%',
                className: 'w-full',
              },
              {
                key: 'seniorityLevel',
                label: 'Seniority Level',
                primary: true,
                width: '18%',
                inline: true,
              },
              {
                key: 'activeStatus',
                label: 'Status',
                width: '19%',
                inline: true,
              },
              {
                key: 'averageScore',
                label: 'Score',
                width: '17%',
              },
              {
                key: 'actions',
                label: 'Actions',
                width: '10%',
                buttons(_: unknown, row: AssignedApplicant) {
                  return [
                    {
                      label: 'View',
                      customIcon: 'eye',
                      iconWidth: '22',
                      iconHeight: '22',
                      color: 'text-black dark:text-white',
                      onClick: () => navigate(`/app/applicants/progress/${row.applicantId}/tests`),
                    },
                    {
                      label: 'Export Report',
                      icon: 'mdi:file-document-arrow-right-outline',
                      iconWidth: '22',
                      iconHeight: '22',
                      color: 'text-black dark:text-white',
                      dropDown: [
                        {
                          label: `${type?.charAt(0).toUpperCase() + (type ?? '').slice(1)} Score`,
                          color: '',

                          permission: PlanFeatures.EXPORT_REPORTS,
                          icon: 'fa6-regular:file-pdf',
                          onClick: () =>
                            window.open(
                              `/app/tests/pdf/${row?.assessmentId}?type=${type === 'interview' ? 'interviews' : 'submissions'}`,
                              '_blank',
                              'noopener,noreferrer'
                            ),
                        },

                        {
                          label: `${type?.charAt(0).toUpperCase() + (type ?? '').slice(1)} Details`,
                          icon: 'hugeicons:xls-02',
                          color: '',
                          permission: '',
                          onClick: downloadDocument,
                        },
                      ],
                    },
                    ...(row.recordInterview === true
                      ? [
                          {
                            label: 'Interview Record',
                            customIcon: 'interview',
                            iconWidth: '22',
                            iconHeight: '22',
                            color: 'text-black dark:text-white',
                            loading: loadingInterviewRecord.find((item) => item.id === row.assessmentId)?.loading || false,
                            onClick: () => handleInterviewRecord(row),
                          },
                        ]
                      : []),
                  ];
                },
              },
            ]}
            // noDataFound={{
            //   customIcon: 'assignedApllicantsNotFound',
            //   message: 'No Assigned Applicants',
            // }}
            placeholder={{
              title: 'No applicants created yet',
              subTitle: 'Add applicants to start building your candidate pipeline.',
              image: '/UI/src/assets/placeholder/NoUsers.svg',
            }}
            // showMoreMap={showMoreMap}
            setShowMoreMap={setShowMoreMap}
            hideJumbotron
            isScrollableTabsExists
          />
        </div>
      </Dialog>

      {isExportReportsDataVisible && <ExportReportsData onClose={() => setExportReportsDataVisibility(false)} />}
    </>
  );
};
