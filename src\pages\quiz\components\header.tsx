import React, { useState, useEffect, FC } from 'react';

import { Icon, Button, Logo } from 'src';
import { RootState, setThemeColor, useAppDispatch, useAppSelector } from 'UI/src';

type HeaderProps={
  showThemeIcon:boolean;
  setShowBookMark:(value: boolean) => void;
  startQuiz: boolean;
  duration: number;
}

export const Header:FC<HeaderProps> = ({ showThemeIcon, setShowBookMark, startQuiz, duration }) => {
  //state
  const [timeLeft, setTimeLeft] = useState<string|null>(null);

  const newMode = (): 'light' | 'dark' => {
    if (themeColor === 'light') {
      return 'dark';
    }
    return 'light';
  };
  const themeColor = useAppSelector((state: RootState) => state.app.themeColor);
  const dispatch = useAppDispatch();

  // Methods
  const getTimeUnitFormated = (number:number):string => `${number < 10 ? `0${number}` : number}`;

  let hours = 0;
  let minutes = 0;
  let seconds = 0;

  const getTime = () => {
    seconds++;
    if (seconds == 60) {
      seconds = 0;
      minutes++;
      if (minutes == 60) {
        minutes = 0;
        hours++;
        if (hours == 24) {
          hours = 0;
        }
      }
    }

    return `${getTimeUnitFormated(hours)}:${getTimeUnitFormated(minutes)}:${getTimeUnitFormated(seconds)}`;
  };

  const showBookMarkList = () => {
    setShowBookMark(true);
  };

  // Effects
  useEffect(() => {
    setTimeLeft(getTime());
    const timer = setInterval(() => {
      setTimeLeft(getTime());
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, []);

  return (
    <nav className="bg-ligthgraybg dark:bg-darkBackgroundCard mb-1 border-b border-[rgb(244,244,244)] px-4 pt-[14px] pb-[10px] dark:border-[#374151] fixed left-0 right-0 top-0 z-[60]">
      <div className="mx-6">
        <div className="flex justify-between items-center">
          <div className="flex justify-between items-center">
            <div className="flex items-center justify-between mr-4 cursor-pointer">
              <Logo className="h-6" />
            </div>
          </div>
          <div className="flex items-center gap-4 md:gap-3 xl:gap-4">
            {startQuiz && (
              <div className="flex font-medium gap-4">
                <div className="flex items-center text-base font-normal text-secondaryGray">
                  <p>
                    Estimation time {duration - 10}m - {duration + 10}m
                  </p>
                </div>
                {!!timeLeft && (
                  <div className="dark:bg-gray-900 dark:text-white flex gap-2 px-3 py-2 bg-[#F9FAFC] text-xl text-secondaryGray rounded-md">
                    <Icon icon="octicon:clock-16"></Icon>
                    <span>{timeLeft}</span>
                  </div>
                )}
                <Button onClick={showBookMarkList} label="Questions" icon="ion:book-outline" />
              </div>
            )}
            {/* <!-- Dark --> */}
            {showThemeIcon && (
              <button
                type="button"
                className="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 flex items-center justify-center"
                onClick={() => dispatch(setThemeColor(newMode()))}
              >
                <Icon icon={themeColor === 'light' ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'} width="20px" />
              </button>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};
