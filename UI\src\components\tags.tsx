import React from 'react';
import { tagsList } from '../constants/tags';
import { tagIcons } from './tag-icons';

// type: "high" | "medium" | "low"
type TagsProps = {
  type: string;
  color?: string;
  icon?: string | React.ReactNode;
  children?: React.ReactNode;
};

//FIXME: review this logic if there is better than this
export const Tags: React.FC<TagsProps> = ({ type, color, icon, children }) => {
  const resolveTagKey = (rawKey: string): keyof typeof tagsList | undefined => {
    // 1) Exact match
    if (rawKey in tagsList) return rawKey as keyof typeof tagsList;

    // 2) Case-insensitive match
    const lower = rawKey?.toLowerCase();
    const ciKey = (Object.keys(tagsList).find((k) => k.toLowerCase() === lower) ?? undefined) as keyof typeof tagsList | undefined;
    if (ciKey) return ciKey;

    // 3) Dynamic canonical match against tagsList keys (no manual aliases)
    const canonicalize = (value: string) => {
      const withSpaces = value?.replace(/([a-z])([A-Z])/g, '$1 $2');
      return withSpaces?.toLowerCase()?.replace(/[\s\-_]/g, '');
    };

    const inputCanonical = canonicalize(rawKey);

    for (const key of Object.keys(tagsList) as Array<keyof typeof tagsList>) {
      const tagValue = tagsList[key];
      if (typeof tagValue === 'string' && tagValue.trim().startsWith('<svg')) continue; // skip icon entries
      if (canonicalize(String(key)) === inputCanonical) return key;
    }

    return undefined;
  };

  const getIconForType = (type: string) => {
    const iconMap: { [key: string]: string } = {
      senior: 'SeniorIcon',
      fresh: 'FreshIcon',
      intern: 'InternIcon',
      junior: 'JuniorIcon',
      'mid-level': 'MidLevelIcon',
      active: 'ActiveIcon',
      expired: 'ExpiredIcon',
    };
    return iconMap[type];
  };

  const renderIcon = () => {
    // If icon is a React component (ReactNode), render it directly
    if (React.isValidElement(icon)) {
      return icon;
    }

    // If icon is a string, use the safe React components
    if (typeof icon === 'string') {
      const iconKey = icon || getIconForType(type);
      if (iconKey && tagIcons[iconKey as keyof typeof tagIcons]) {
        const IconComponent = tagIcons[iconKey as keyof typeof tagIcons];
        return <IconComponent width={16} height={16} />;
      }
    } else {
      // Fallback to type-based icon
      const iconKey = getIconForType(type);
      if (iconKey && tagIcons[iconKey as keyof typeof tagIcons]) {
        const IconComponent = tagIcons[iconKey as keyof typeof tagIcons];
        return <IconComponent width={16} height={16} />;
      }
    }
    return null;
  };

  const shouldShowIcon = icon || getIconForType(type);
  const resolvedKey = resolveTagKey(type);
  const tagClasses = color ?? (resolvedKey ? tagsList[resolvedKey] : 'bg-gray-100 text-gray-800');

  return (
    <div className={`rounded-lg px-3 w-fit text-xs font-medium py-1 ${shouldShowIcon ? 'flex items-center gap-1 ' : ''} ${tagClasses}`}>
      {renderIcon()}
      {children ?? type?.charAt(0)?.toUpperCase() + type?.slice(1)}
    </div>
  );
};
