import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FaUserGraduate, Fa<PERSON>ser, FaStar, FaMedal, FaTrophy } from 'react-icons/fa';

// React Icons
import { LiaCircle } from 'react-icons/lia';
import { LuDiamond } from 'react-icons/lu';
import { PiCube } from 'react-icons/pi';
import { BiPolygon } from 'react-icons/bi';

// Components
import { Table, Icon, EnumText } from 'src';
// import { SubmissionsLockDialog } from '../components/lock-dialog';
import { SubmissionsCreationDialog } from '../../submissions/components/creation-dialog';
import { SubmissionsCreationTestDialog } from '../../submissions/components/creation-test-dialog';
import { AiIntreviewDialog } from '../../submissions/components/ai-dialog';

// Flowbite
import { Tooltip } from 'flowbite-react';
import { RootState, useAppSelector, UserData, useFetchList, useScreenSize, QuizDifficulty, SubmissionStatus } from 'UI/src';
import { Api, useAppDispatch } from 'UI/src';
import { setNotifyMessage } from 'UI';

export const AiInterviewsListPage = () => {
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const isPermitted = Array.isArray(userData?.role) && userData?.role.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const navigate = useNavigate();
  const ORIGIN = window.location.origin;

  // State
  const screen = useScreenSize();
  // const [isLockDialogVisible, setLockDialogVisibility] = useState(false);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [isCreateTestDialogVisible, setCreateTestDialogVisibility] = useState(false);
  const [aiDialog, setAiDialog] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);
  const [lockUnlockAll, setLockUnlockAll] = useState(false);
  const [showMoreMap, setShowMoreMap] = useState<{ [key: string]: boolean }>({});

  // UI Hooks
  const dispatch = useAppDispatch();

  // List Hook
  const filterList = {
    status: {
      label: 'Status',
      enum: 'SubmissionStatus',
    },
    quizDifficulty: {
      label: 'Difficulty',
      enum: 'QuizDifficulty',
    },
    category: {
      label: 'Category',
      lookup: 'category',
    },
    subCategory: {
      label: 'Sub Category',
      lookup: 'subcategory',
      parentLookup: { key: 'category', fieldName: 'categoryId' },
    },
    // scope: {
    //   label: 'Scope',
    //   enum: 'Scope',
    // },
  };
  const {
    // Load States
    ready,
    loading,
    setLoading,
    // List
    list,
    count,
    refresh,
    // Data Manipulation
    search,
    pagination,
    filters,
  } = useFetchList('ai-interview/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
    filters: filterList,
  });

  return (
    <>
      {/* Table */}
      <Table
        ready={ready}
        loading={loading}
        title="Assigned Interviews"
        searchPlaceholder={screen.customScreen ? 'Search by name or applicant' : 'Name or applicant'}
        addButtonLabel={isPermitted ? 'Create Interview' : ''}
        rows={list}
        count={count}
        search={search}
        filters={filters}
        onClickAdd={() => {
          setAiDialog(true);
        }}
        pagination={pagination}
        slots={{
          quiz: (_: string, row: { _id: string; subCategoryName: string }) => {
            const element = row.subCategoryName;
            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div
                    className={`text-gray-800 font-medium capitalize dark:text-grayTextOnDarkMood lg:truncate ${
                      !showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'
                    }`}
                  >
                    {element}
                  </div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
          applicant: (_: string, row: { applicantName: string; applicantEmail: string }) => {
            const element = (
              <>
                <div className="lg:truncate">{row.applicantName || '—'}</div>
                <div className="lg:truncate">{row.applicantEmail}</div>
              </>
            );
            const modifiedEmail = row.applicantEmail;
            const [emailName, emailDomain] = modifiedEmail.split('@');

            return (
              <div className="flex gap-x-1 relative">
                <div className={`break-words overflow-auto whitespace-normal text-clip`}>
                  <div className="text-gray-800 font-medium  dark:text-grayTextOnDarkMood  lg:truncate">
                    {emailName}
                    <span className="truncate">@{emailDomain}</span>
                  </div>
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
          difficulty: (_: string, row: { difficulty: number }) => {
            let difficultyIcon;
            let difficultyColor;
            let iconSize = 'text-sm';

            switch (row.difficulty) {
              case 1:
                difficultyIcon = <FaUserGraduate className={`${iconSize} text-teal-700`} />; // Intern
                difficultyColor = ' text-teal-700 ';
                break;

              // Star Icon fresh level
              case 2:
                difficultyIcon = <FaUser className={`${iconSize} text-sky-800`} />; // Fresh
                difficultyColor = 'text-sky-800 ';
                break;
              // Medal Star junior
              case 3:
                difficultyIcon = <FaStar className={`${iconSize} text-amber-700`} />; // Junior
                difficultyColor = ' text-amber-700 ';
                break;
              // betetr medal star midlevel
              case 4:
                difficultyIcon = <FaMedal className={`${iconSize} text-orange-700`} />; // Mid-level
                difficultyColor = 'text-orange-700';
                break;

              // Tropy icon for senior with star
              case 5:
                difficultyIcon = <Icon icon="solar:crown-star-bold" width="18" className={`${iconSize} text-red-800`} />; // Senior
                difficultyColor = 'text-red-800';
                break;
              default:
                difficultyIcon = null;
            }
            return (
              <span className={`inline-flex items-center  py-1 text-sm font-medium rounded-full capitalize ${difficultyColor}`}>
                <span className="mr-1 flex items-center justify-center">{difficultyIcon}</span>
                {/* <EnumText name={'QuizDifficulty'} value={row.difficulty} /> */}
                {QuizDifficulty[row.difficulty]}
              </span>
            );
          },
          score: (_: string, row: { score: number; status: number }) => {
            if (row.status === 1 || row.status === 2) {
              return <div className="flex gap-2">—</div>;
            }

            if (row.status !== 1 && row.status !== 2) {
              if (row.score < 50) {
                return <div className="flex gap-2 text-chartPoorCircle">{row.score}%</div>;
              } else if (row.score <= 80) {
                return <div className="flex gap-2 text-chartGoodCircle">{row.score}%</div>;
              } else if (row.score > 80) {
                return <div className="flex gap-2 text-chartExcellentCircle">{row.score}%</div>;
              }
            }
          },
          status: (_: string, row: { status: number }) => {
            const status = row.status;
            const statusResult = SubmissionStatus[status];
            const statusColor = () => {
              switch (status) {
                case 1:
                  return 'text-statusColorNotStartedText bg-statusColorNotStartedBackground';
                case 2:
                  return 'text-statusColorInProgressText bg-statusColorInProgressBackground';
                case 3:
                  return 'text-statusColorSubmittedText bg-statusColorSubmittedBackground';
                default:
                  return '';
              }
            };
            return (
              <div className={`w-fit px-3 py-2 rounded-full text-xs font-medium flex gap-1 ${statusColor()}`}>
                <Icon icon="material-symbols:circle" width="8" />
                {statusResult}
              </div>
            );
          },
          authorName: (_: string, row: { authorName: string; createdAt: string }) => {
            const date = new Date(row.createdAt);
            const element = (
              <>
                <div className="lg:truncate">{row.authorName}</div>
                <div>{date.toDateString().slice(4)}</div>
              </>
            );
            return (
              <div className="flex gap-x-1 relative">
                <div
                  className={`break-all ${screen.lt.sm() && 'truncate'} ${
                    screen.gt.md() && 'truncate'
                  } overflow-auto whitespace-normal text-clip text-[#949BA5] font-normal`}
                >
                  {element}
                </div>
                {screen.gt.md() && (
                  <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-full h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'quiz',
            label: 'Interview Topic',
            width: '15%',
            tooltip: true,
          },
          {
            key: 'applicant',
            label: 'Applicant',
            width: '25px',
            tooltip: true,
          },
          {
            key: 'difficulty',
            label: 'Difficulty',
            width: '12%',
            // enum: 'QuizDifficulty'
          },
          { key: 'score', label: 'Score', width: '12%' },
          {
            key: 'status',
            label: 'Status',
            width: '14%',
          },
          { key: 'authorName', label: 'Creation', width: '12%' },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_: string, row: { _id: string; status: number }) {
              return [
                ...(row.status !== 3
                  ? [
                      {
                        label: 'Copy Link',
                        color: 'text-black dark:text-white',
                        icon: 'material-symbols:content-copy-outline',
                        isCopied: row.status,
                        onClick() {
                          navigator.clipboard.writeText(`${ORIGIN}/interview/${row._id}`);
                          dispatch(setNotifyMessage('Link copied'));
                        },
                      },
                    ]
                  : []),
                {
                  label: 'Show Progress',
                  color: 'text-black dark:text-white',
                  icon: 'iconamoon:eye',
                  path: `/app/interviews/view/${row._id}`,
                  // @TODO: Delete when done testing
                  // onClick() {
                  //   navigate(`/app/tests/interviews-result/view/${row._id}`);
                  // },
                },
                // @TODO: Add when needed
                // {
                //   label: 'Archive',
                //   color: '#000000',
                //   icon: 'hugeicons:archive-02',
                //   onClick() {
                //     handleArchive(row);
                //   },
                // },
              ];
            },
            report(_: string, row: any) {
              return [
                {
                  label: 'Download',
                  color: 'text-black dark:text-white',
                  icon: 'mingcute:file-download-line',
                  onClick() {
                    window.open(`/app/tests/pdf/${row._id}?type=ai-interview`, '_blank', 'noopener,noreferrer');
                  },
                },
              ];
            },
          },
        ]}
        // multiSelectedRow={{
        // selectedIds: selectedIds,
        // setSelectedIds: setSelectedIds,
        // handleArchiveSelectedIds: handleArchiveSelectedIds,
        // lockUnlockAll: lockUnlockAll,
        // handleLockSelectedIds: handleLockSelectedIds,
        // }}

        placeholder={{
          title: 'No interviews created yet',
          subTitle: 'Start by creating an interview to assess applicants.',
          image: '/UI/src/assets/placeholder/NoInterview.svg',
        }}
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
      />

      {/* TODO: When implement all the files */}
      {/* Creation Dialog */}
      {isCreateDialogVisible && <SubmissionsCreationDialog back backButton={() => {}} testId="" onClose={() => setCreateDialogVisibility(false)} />}

      {/* Creation New Test Dialog */}
      {isCreateTestDialogVisible && (
        <SubmissionsCreationTestDialog
          blockDetails={{}}
          refresh={refresh}
          setBlockDetails={() => {}}
          setCreateBlockVisibility={() => {}}
          setCreateTestDialogVisibility={() => {}}
          onClose={() => setCreateTestDialogVisibility(false)}
        />
      )}

      {/* Ai Interview */}
      {aiDialog && <AiIntreviewDialog onClose={() => setAiDialog(false)} onCreate={refresh} />}

      {/* Lock Dialog */}
      {/* {isLockDialogVisible && <SubmissionsLockDialog onClose={() => setLockDialogVisibility(false)} onFinish={refresh} />} */}
    </>
  );
};
