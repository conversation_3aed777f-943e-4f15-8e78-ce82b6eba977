import React, { forwardRef, useEffect, useState, useRef } from 'react';

import { Label, TextInput as Input, ListGroup, Tooltip, HelperText, ListGroupItem } from 'flowbite-react';
import { asField } from '../hocs/field';

import { Icon, Button, TextInput } from 'src';

import { Api, StaticData, useLookups, useAppDispatch } from 'UI/src';
import { setNotifyMessage, setErrorNotify } from 'UI';

export const Select = asField(
  forwardRef(
    (
      {
        name,
        label,
        subLabel = '',
        placeholder,
        value,
        disabled,
        disabledMessage,
        onChange,
        onSearch,
        filterOnly,
        optionLabelKey,
        optionValueKey,
        dropIcon,
        isCustomValue = false,
        multiSelect,
        lookup,
        params,
        cached = false,
        errorMessage,
        validatorsScroll,
        customAddIconApplicant,
        creationOptions,
        requiredLabel,
        disallowNumbers,
        readOnly,
        labelTooltip,
        // @TODO: Confirmation warning message
        // confirmOptions = false,
      },
      ref
    ) => {
      // State
      const [keyword, setKeyword] = useState('');
      const [listVisibility, setListVisibility] = useState(false);
      const [isCreating, setIsCreating] = useState(false);
      const [customValue, setCustomValue] = useState('');
      const [errorCustomMessage, setErrorCustomMessage] = useState('');
      const [isLoading, setIsLoading] = useState(false);

      // Hooks
      const { lookups, loading, refresh } = useLookups(lookup, { cached, params });
      // Reference
      const listRef = useRef<HTMLDivElement>(null);
      const inputRef = useRef<HTMLInputElement>(null);

      // dispatch
      const dispatch = useAppDispatch();

      const customTheme = {
        field: {
          input: {
            colors: {
              gray: `${
                readOnly ? 'cursor-not-allowed dark:bg-gray-800 text-gray-500' : 'dark:bg-gray-700 text-gray-800'
              } block w-full border truncate bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 border-gray-300  dark:border-gray-600 dark:text-white dark:placeholder-gray-400 p-2.5 text-sm rounded-lg focus:ring-0 focus:border-gray-300`,
              failure:
                'border-red-500 truncate bg-white-500 text-gray-900 dark:text-white placeholder-gray-400 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-[#374151] dark:focus:border-red-500 dark:focus:ring-red-500',
            },
          },
        },
      };

      const tooltipCustomTheme = {
        target: 'w-auto',
      };
      const handleSubmit = async (e: any) => {
        if (e && typeof e.stopPropagation === 'function') e.stopPropagation();
        try {
          setIsLoading(true);
          if (!customValue) {
            setErrorCustomMessage('This field is required');
            return;
          }
          const response = await Api.post(creationOptions.url, { ...params, [creationOptions?.fieldName]: customValue });
          if (response) {
            await refresh();
            dispatch(setNotifyMessage(`${label || 'Option'} created successfully`));
            setCustomValue('');
            onChange('');
            setKeyword('');
            setIsCreating(false);
          }
        } catch (error: any) {
          dispatch(setErrorNotify(error.response.data.message || 'Failed to create option'));
        } finally {
          setIsLoading(false);
        }
      };

      const filteredLookup = () => {
        if (onSearch) {
          return lookups;
        }

        return lookups.filter((option: any) => option[optionLabelKey]?.toLowerCase().includes(keyword?.toLowerCase()));
      };

      const updateInput = (updatedValue = value) => {
        const selected = lookups.find((option: any) => option[optionValueKey] === updatedValue);
        if (selected) {
          setKeyword((selected as any)?.[optionLabelKey]);
        } else if (!value && keyword && !isCustomValue) {
          setKeyword('');
        }
      };

      const handleSelect = (selectedValue: string) => () => {
        onChange(selectedValue);
        setListVisibility(false);
        setIsCreating(false);
        if (!filterOnly) {
          updateInput(selectedValue);
        } else {
          updateInput('');
        }
      };

      const handleReset = () => {
        // // @TODO: Confirmation warning message
        // const element = () => {
        onChange('');
        setKeyword('');
        setListVisibility(true);
        setIsCreating(false);

        if (onSearch) {
          onSearch('');
        }
        // };

        // // @TODO: Confirmation warning message
        // if (confirmOptions?.isConfirmationDialog) {
        //   showConfirm(confirmOptions.confirmTextNotChangeInputs(), {
        //     onConfirm() {
        //       element();
        //       hideConfirm();
        //       confirmOptions.doAfterConfirmation();
        //     },
        //   });
        // } else {
        //   element();
        // }
      };

      const handleUpdateKeyword = (e: React.FormEvent<HTMLInputElement>) => {
        let keywordValue = (e.target as HTMLInputElement).value;
        if (disallowNumbers) {
          keywordValue = keywordValue.replace(/[0-9]/g, '');
        }
        setKeyword(keywordValue);

        if (onSearch) {
          if (multiSelect) {
            onSearch(keywordValue);
          } else {
            onChange('');
            onSearch(keywordValue);
          }
        }
      };

      const handleClickOutside = (event: any) => {
        if (
          listRef.current &&
          !listRef.current.contains(event.target as Node) &&
          inputRef.current &&
          !inputRef.current.contains(event.target as Node)
        ) {
          setListVisibility(false);
          setIsCreating(false);
          updateInput();
        } else if (inputRef.current && inputRef.current.contains(event.target as Node)) {
          // If the input is clicked again, toggle the list visibility
          setListVisibility((prev) => !prev);
        }
      };

      useEffect(() => {
        // // @TODO: Confirmation warning message
        // if (!confirmOptions.isConfirmationDialog) {
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
          document.removeEventListener('mousedown', handleClickOutside);
        };
        // }
      }, []);

      useEffect(() => {
        if (value) {
          updateInput();
        }
      }, [value, lookups]);

      /* 
        - Calling onSearch is similar to calling lookups
        - lookups invokes with it's main hook
        - onSearch invokes here with useEffect
      */
      useEffect(() => {
        if (onSearch) {
          onSearch('');
        }
      }, []);

      const onFocus = (e: React.FocusEvent<HTMLInputElement>) => {
        // // @TODO: Confirmation warning message
        // const element = () => {

        // Behavior same as handleReset
        onChange('');
        setKeyword('');

        setListVisibility(true);
        setIsCreating(false);
        // handleUpdateKeyword(e);
        // };

        // // @TODO: Confirmation warning message
        // if (confirmOptions?.isConfirmationDialog) {
        //   inputRef.current.blur();
        //   showConfirm(confirmOptions.confirmTextNotChangeInputs(), {
        //     onConfirm() {
        //       element();
        //       hideConfirm();
        //       confirmOptions.doAfterConfirmation();
        //     },
        //   });
        // } else {
        //   element();
        // }
      };

      const element = (
        <>
          <Input
            ref={inputRef} // Attach ref to the input
            theme={customTheme}
            id={name}
            placeholder={placeholder}
            value={keyword}
            onInput={handleUpdateKeyword}
            autoComplete="off"
            onFocus={(e) => !readOnly && onFocus(e)}
            disabled={disabled}
            color={errorMessage || customAddIconApplicant ? 'failure' : 'gray'}
            // helperText={errorMessage || (customAddIconApplicant && 'This field pattern is invalid')}
            rightIcon={undefined}
            readOnly={readOnly}
          />
          {/* TODO: Add when apply the validation */}
          {/* <HelperText>{errorMessage || (customAddIconApplicant && 'This field pattern is invalid')}</HelperText> */}
        </>
      );

      return (
        <div className="relative" ref={listRef}>
          {label && (
            <div className="flex items-center gap-2 mb-2">
              <Label htmlFor={name}>
                <span className="thepassBtwo">{label}</span> <span className="text-inputSubLabel dark:text-inputLabel">{subLabel}</span>
                {requiredLabel && <span className="text-red-600 dark:text-red-800"> *</span>}
              </Label>
              {labelTooltip && (
                <Tooltip theme={StaticData.customTooltipTheme} content={labelTooltip} style="auto" className="border border-white">
                  <Icon icon="solar:info-circle-outline" className="text-gray-600 dark:text-gray-200" width="18" />
                </Tooltip>
              )}
            </div>
          )}
          <div>
            <div className="relative">
              {disabled && disabledMessage ? (
                <Tooltip content={disabledMessage} placement="top" theme={tooltipCustomTheme}>
                  {element}
                </Tooltip>
              ) : (
                <>
                  {element}
                  {!readOnly && (!!value || dropIcon) && !disabled && !filterOnly && (
                    <button className="absolute right-3 top-3" onClick={handleReset} type="button">
                      <Icon icon={!dropIcon ? 'ic:baseline-clear' : 'charm:chevron-down'} className="text-[#743AF5] dark:text-[#7659ce]" />
                    </button>
                  )}
                </>
              )}
            </div>

            {!readOnly && listVisibility && !loading && (
              <ListGroup className="absolute top-[75px] left-0 right-0 z-50 overflow-y-auto max-h-[200px] custom-scroll dark:border-gray-800">
                {filteredLookup()?.length > 0 ? (
                  filteredLookup()?.map((option: any) => (
                    <ListGroupItem key={(option as any)[optionValueKey]} onClick={handleSelect((option as any)[optionValueKey])}>
                      <p className="truncate">{(option as any)[optionLabelKey]}</p>
                    </ListGroupItem>
                  ))
                ) : (
                  <div className="p-2 px-4 border-b border-b-white/10">
                    <p className="py-1 cursor-default">No results found...</p>
                  </div>
                )}
                {creationOptions && !isCreating && filteredLookup()?.length < 4 && (
                  <div
                    className="text-white py-2 m-2 rounded-md text-center cursor-pointer bg-[#702ede] hover:opacity-90"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsCreating(true);
                    }}
                  >
                    Create new +
                  </div>
                )}
                {creationOptions && isCreating && (
                  <ListGroupItem className="my-1 [&_div]:w-full">
                    <div className="text-start">
                      <div className="flex flex-col xssm:flex-row gap-2">
                        <TextInput
                          className="text-white"
                          name="name"
                          placeholder={`Enter new ${label.toLowerCase() || 'option'}`}
                          value={customValue}
                          onChange={(event: string) => {
                            setCustomValue(event);
                            if (event === '' || creationOptions?.validation?.test(event)) {
                              setErrorCustomMessage('');
                            } else {
                              setErrorCustomMessage('Please enter a valid text');
                            }
                          }}
                        />

                        <Button
                          label="Create"
                          disabled={!customValue.trim() || !!errorCustomMessage}
                          loading={isLoading}
                          gradientMonochrome="purple"
                          onClick={handleSubmit}
                        />
                      </div>
                      {errorCustomMessage && <p className="mt-2 ms-1 text-sm text-red-600 dark:text-red-500">{errorCustomMessage}</p>}
                    </div>
                  </ListGroupItem>
                )}
              </ListGroup>
            )}
          </div>
        </div>
      );
    }
  )
);
