import { Respons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip } from 'recharts';

export interface PayloadItem {
  name: string;
  value: number;
  count: number;
  color: string;
  payload: {
    color: string;
    count: number;
  };
}
[];

interface CustomTooltipProps {
  active: boolean;
  payload: PayloadItem[];
}
// Custom Tooltip Component
const CustomTooltip = ({ active, payload }: CustomTooltipProps) => {
  if (active && payload && payload?.length) {
    const data = payload[0] as PayloadItem;
    return (
      <div className=" relative p-3 rounded-md shadow-md border bg-white text-black dark:bg-gray-800 dark:text-white" style={{ zIndex: 99999 }}>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-sm flex-shrink-0" style={{ backgroundColor: data.payload.color }}></div>
          <span className="font-medium text-gray-900 dark:text-white whitespace-nowrap">{data.name}</span>
        </div>
        <div className="mt-1 text-sm text-gray-600 dark:text-gray-300">{data.payload.count}</div>
      </div>
    );
  }
  return null;
};

interface ChartsDonutProps {
  data: PayloadItem[];
  centeredTextOfChart: () => React.ReactNode;
  rightData: () => React.ReactNode;
  bottomData: () => React.ReactNode;
  metrics?: {
    name: string;
    value: number;
    gain: boolean;
    lose: boolean;
  };
  innerRadius: number;
  outerRadius?: number;
  width?: string;
  height?: number;
  rightDataStyles: string;
}
export const ChartsDonut = ({
  data,
  centeredTextOfChart = () => null,
  rightData = () => null,
  bottomData = () => null,
  metrics,
  innerRadius = 60,
  outerRadius = 95,
  width = '100%',
  height = 200,
  rightDataStyles,
}: ChartsDonutProps) => {
  return (
    <div>
      <div className="flex flex-col xslg:flex-row items-center">
        <div className="w-full xslg:w-3/5 relative">
          <ResponsiveContainer width={width} height={height}>
            <PieChart>
              <Pie data={data} cx="50%" cy="50%" innerRadius={innerRadius} outerRadius={outerRadius} fill="#8884d8" dataKey="value">
                {data?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<CustomTooltip active={true} payload={data} />} />
            </PieChart>
          </ResponsiveContainer>

          {centeredTextOfChart() && (
            <div
              className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center pointer-events-none"
              style={{ zIndex: -1 }}
            >
              {centeredTextOfChart()}
            </div>
          )}
        </div>

        {rightData() && <div className={`w-fit flex flex-col justify-center space-y-2 ${rightDataStyles}`}>{rightData()}</div>}
      </div>
      {bottomData() && <div className="mt-6 pt-6 border-t">{bottomData()}</div>}
    </div>
  );
};
