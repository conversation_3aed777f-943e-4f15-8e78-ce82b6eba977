// React
import { useState, useEffect, useRef } from 'react';

// Core
import { ImageUploader, TextInput, Textarea, CustomEmojiPicker } from 'src';
import { useValidate, Form, Dialog, Button, setTitle, setEmoji, setDescription } from 'UI';
import { RootState, setFieldValue, useAppDispatch, useAppSelector } from 'UI/src';

interface CreateCategoryDialog {
  onClose: () => void;
  onCreate: (e: any) => void;
  modalHeader?: string;
  showDescription?: boolean;
  showIcon?: boolean;
  defaultTitle?: string;
  defaultDescription?: string;
  defaultIcon?: string;
  actionText?: string;
  subcategories?: Array<{ _id: string; id: string; name: string }>;
  onEditSubcategory?: (subcategory: any) => void;
  onDeleteSubcategory?: (subcategory: any) => void;
}

export const CreateCategoryDialog = ({
  onClose,
  onCreate,
  modalHeader = 'Create Category',
  showDescription = true,
  showIcon = true,
  defaultTitle = '',
  defaultDescription = '',
  defaultIcon = '',
  actionText = 'Create',
  subcategories = [],
  onEditSubcategory,
  onDeleteSubcategory,
}: CreateCategoryDialog) => {
  // State
  const [loading, setLoading] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  // Refs
  const emojiPickerRef = useRef<HTMLDivElement>(null);

  const { isRequired } = useValidate();

  // Form
  const form = useAppSelector((state: RootState) => state.categoryManagement);
  const dispatch = useAppDispatch();

  const handleSubmit = async () => {
    onCreate({
      title: form?.title,
      description: form?.description,
      icon: form?.emoji,
    });
  };

  // Set default values when component mounts or props change
  useEffect(() => {
    if (defaultTitle) {
      dispatch(setTitle(defaultTitle));
    }
    if (defaultIcon) {
      dispatch(setEmoji(defaultIcon));
    }
    if (defaultDescription) {
      dispatch(setDescription(defaultDescription));
    }
  }, [defaultTitle, defaultIcon, defaultDescription, dispatch]);

  // Close emoji picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (emojiPickerRef.current && !emojiPickerRef.current.contains(event.target as Node)) {
        setShowEmojiPicker(false);
      }
    };

    if (showEmojiPicker) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showEmojiPicker]);

  return (
    <Dialog size="lg" isOpen onClose={onClose} title={modalHeader}>
      <Form onSubmit={handleSubmit}>
        <div className="space-y-5">
          {/* FIXME: Add form validation */}

          <div className="flex flex-row items-end gap-2">
            <div className="flex-1">
              <TextInput
                name="title"
                label="Title"
                placeholder="Enter title"
                value={form.title}
                onChange={(value: any) => dispatch(setTitle(value))}
                // validators={[isRequired()]}
                // inputClassName="w-full text-lg py-3"
              />
            </div>
            {showIcon && (
              <div className="flex justify-end  w-18" ref={emojiPickerRef}>
                <CustomEmojiPicker value={form.emoji} onChange={(value: any) => dispatch(setEmoji(value))} label="Emoji" />
              </div>
            )}
          </div>
          {/* {showDescription && (
            <Textarea
              label="Description"
              placeholder="Enter description"
              value={form.description}
              onChange={(value) => dispatch(setDescription(value))}
              validators={[isRequired()]}
              className=" resize-none"
            />
          )} */}

          {/* Subcategories Section */}
          {subcategories && subcategories.length > 0 && (
            <div className="space-y-3">
              <div className="text-sm font-medium text-gray-700">Subcategories ({subcategories.length})</div>
              <div className="grid grid-cols-2 gap-3">
                {subcategories.map((sub) => (
                  <div key={sub._id || sub.id} className="flex items-center bg-gray-50 border border-gray-200 rounded-lg p-3">
                    <span className="text-sm font-medium text-gray-800 truncate">{sub.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          <div className="flex items-center justify-between gap-3">
            <Button type="button" label="Cancel" colorType="tertiary" className="w-full " loading={loading} disabled={loading} onClick={onClose} />
            <Button type="submit" colorType="primary" label={actionText} className="w-full" loading={loading} disabled={loading} />
          </div>
        </div>
      </Form>
    </Dialog>
  );
};
