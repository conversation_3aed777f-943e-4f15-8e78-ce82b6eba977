import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { set } from 'object-path';
import type { RootState } from '../../store';

interface FormState {
  data: Record<string, any>;
  backup: Record<string, any>;
}

const initialState: FormState = {
  data: {},
  backup: {},
};

const formSlice = createSlice({
  name: 'form',
  initialState,
  reducers: {
    initializeForm: (state, action: PayloadAction<Record<string, any>>) => {
      state.data = action.payload;
      state.backup = JSON.parse(JSON.stringify(action.payload));
    },

    setFieldValue: (
      state,
      action: PayloadAction<{
        path: string;
        value: any;
        type?: (val: any) => any;
      }>
    ) => {
      const { path, value, type } = action.payload;
      set(state.data, path, type ? type(value) : value);
    },

    setForm: (state, action: PayloadAction<Record<string, any>>) => {
      state.data = action.payload;
    },

    resetForm: (state) => {
      state.data = JSON.parse(JSON.stringify(state.backup)); // reset to backup
    },
  },
});

export const formState = (state: RootState) => state.form;
export const { initializeForm, setFieldValue, setForm, resetForm } = formSlice.actions;
export default formSlice.reducer;
