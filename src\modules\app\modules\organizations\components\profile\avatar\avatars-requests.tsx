// React
import { useMemo, useState } from 'react';

// Core
import { Card, Icon, ScrollableTabs } from 'src';
import { Dialog as UIDialog, Button as UIButton } from 'UI/src';

// Assets
import adamBody from 'src/images/models/adam-body.png';

type RequestCard = {
  id: string;
  name: string;
  role: string;
  submittedAt: string;
  by: string;
  avatar: string;
  rejectionReason?: string;
};

type RequestUIProps = {
  request: RequestCard;
  variant: 'pending' | 'approved' | 'rejected';
  expanded?: boolean;
  onToggle?: () => void;
};

const RequestUI = ({ request, variant, expanded, onToggle }: RequestUIProps) => (
  <div className="p-4 rounded-2xl shadow-[0px_7px_10px_0px_#743AF51A] ">
    <div className="flex items-center justify-between gap-4 ">
      <div className="flex items-center gap-3">
        <img src={request.avatar} alt={request.name} className="w-14 h-14 rounded-full object-cover" />
        <div className="space-y-0.5">
          <p className="text-[15px] font-semibold text-[#1B1F3B] dark:text-white">{request.name}</p>
          <p className="text-sm text-[#4E5E82]">{request.role}</p>
        </div>
      </div>

      {variant === 'pending' && (
        <button className="text-primaryPurple" onClick={onToggle} aria-label="review-request">
          <svg width="19" height="15" viewBox="0 0 19 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M1.66406 7.5C1.66406 7.5 4.57315 1.5 9.66406 1.5C14.755 1.5 17.6641 7.5 17.6641 7.5C17.6641 7.5 14.755 13.5 9.66406 13.5C4.57315 13.5 1.66406 7.5 1.66406 7.5Z"
              stroke="#743AF5"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M9.66406 9.75C10.869 9.75 11.8459 8.74264 11.8459 7.5C11.8459 6.25736 10.869 5.25 9.66406 5.25C8.45908 5.25 7.48224 6.25736 7.48224 7.5C7.48224 8.74264 8.45908 9.75 9.66406 9.75Z"
              stroke="#743AF5"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </button>
      )}

      {variant === 'rejected' && (
        <button className="text-primaryPurple" onClick={onToggle} aria-label="toggle-reason">
          <Icon icon="mdi:chevron-up" className={`text-2xl transition-transform ${expanded ? 'rotate-0' : 'rotate-180'}`} />
        </button>
      )}
    </div>

    <div className="mt-3 space-y-1">
      <p className="text-xs text-[#868D9C]">Submitted at {request.submittedAt}</p>
      <p className="text-xs text-gray-500">By {request.by}</p>
    </div>

    {variant === 'rejected' && expanded && request.rejectionReason && (
      <div className="mt-3">
        <p className="text-sm font-semibold text-gray-700">Rejection reason</p>
        <p className="text-sm text-[#4E5E82]">{request.rejectionReason}</p>
      </div>
    )}
  </div>
);

export const AvatarsRequests = () => {
  const [historyTab, setHistoryTab] = useState(0);
  const [expandedRejectedId, setExpandedRejectedId] = useState<string | null>(null);
  const [reviewOpen, setReviewOpen] = useState(false);
  const [reviewStep, setReviewStep] = useState<'actions' | 'reject'>('actions');
  const [selectedRequest, setSelectedRequest] = useState<RequestCard | null>(null);
  const [rejectionText, setRejectionText] = useState('');

  // Demo data in state so we can update it
  const [pendingRequests, setPendingRequests] = useState<RequestCard[]>([
    {
      id: '1',
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody,
    },
    {
      id: '2',
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody,
    },
    {
      id: '3',
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody,
    },
  ]);

  const [historyApproved, setHistoryApproved] = useState<RequestCard[]>([
    {
      id: 'a1',
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody,
    },
    {
      id: 'a2',
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody,
    },
    {
      id: 'a3',
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody,
    },
    {
      id: 'a4',
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody,
    },
  ]);

  const [historyRejected, setHistoryRejected] = useState<RequestCard[]>([
    {
      id: 'r1',
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody,
      rejectionReason: 'Not suitable for our system',
    },
    {
      id: 'r2',
      name: 'Ahmed',
      role: 'HR Representative',
      submittedAt: '20 April 2025, 5:30PM',
      by: 'Mona Elghazaly',
      avatar: adamBody,
      rejectionReason: 'Incomplete requirements',
    },
  ]);

  const tabs = [
    { title: 'Approved', component: null },
    { title: 'Rejected', component: null },
  ];

  return (
    <div className="space-y-6 py-4">
      {/* Pending Requests */}
      <div className="space-y-3 ">
        <div className="flex items-center gap-5">
          <h2 className="text-lg font-semibold text-[#1B1F3B] dark:text-white">Pending Requests</h2>
          <span className="text-xs font-medium px-2 py-1 bg-[#F9F8FA] text-[#743AF5] rounded-full">{pendingRequests.length}</span>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {pendingRequests.map((req) => (
            <RequestUI
              key={req.id}
              request={req}
              variant="pending"
              onToggle={() => {
                setSelectedRequest(req);
                setReviewStep('actions');
                setRejectionText('');
                setReviewOpen(true);
              }}
            />
          ))}
        </div>
      </div>

      {/* History */}
      <div className="space-y-4">
        <div className="flex items-center gap-5">
          <h2 className="text-lg font-semibold text-gray-800 dark:text-white">History</h2>
          <span className="text-xs font-medium px-2 py-1 bg-[#F9F8FA] text-[#743AF5] rounded-full">
            {historyTab === 0 ? historyApproved.length : historyRejected.length}
          </span>
        </div>

        <ScrollableTabs
          data={tabs}
          selectedTab={{
            activeTab: historyTab,
            setActiveTab: setHistoryTab,
          }}
        />

        {historyTab === 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {historyApproved.map((req) => (
              <RequestUI key={req.id} request={req} variant="approved" />
            ))}
          </div>
        ) : (
          <div className="columns-1 sm:columns-2 lg:columns-3 gap-4">
            {historyRejected.map((req) => (
              <div key={req.id} className="break-inside-avoid mb-4">
                <RequestUI
                  request={req}
                  variant="rejected"
                  expanded={expandedRejectedId === req.id}
                  onToggle={() => setExpandedRejectedId((prev) => (prev === req.id ? null : req.id))}
                />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Review Modal using UI/src Dialog */}
      <UIDialog isOpen={reviewOpen} onClose={() => setReviewOpen(false)} title="Review Avatar Request" size="md">
        {reviewStep === 'actions' && selectedRequest && (
          <div className="space-y-6">
            <div className="flex items-start gap-4">
              <img src={selectedRequest.avatar} alt={selectedRequest.name} className="w-[162px] border h-[162px] rounded-xl object-cover" />
              <div>
                <p className="text-base font-semibold text-gray-800">{selectedRequest.name}</p>
                <p className="text-sm text-gray-500">{selectedRequest.role}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <UIButton
                variant="md"
                colorType="destructive"
                label="Reject"
                onClick={() => setReviewStep('reject')}
                className="w-full bg-[#F13E3E] "
              />
              <UIButton
                variant="md"
                colorType="success"
                label="Approve"
                onClick={() => {
                  if (selectedRequest) {
                    setPendingRequests((prev) => prev.filter((p) => p.id !== selectedRequest.id));
                    setHistoryApproved((prev) => [{ ...selectedRequest, id: `${selectedRequest.id}-app-${Date.now()}` }, ...prev]);
                  }
                  setReviewOpen(false);
                }}
                className="w-full "
              />
            </div>
          </div>
        )}

        {reviewStep === 'reject' && (
          <div className="space-y-4">
            <label className="text-sm font-medium ">Rejection Reason</label>
            <textarea
              placeholder="Type..."
              rows={5}
              className="w-full border border-gray-300 rounded-lg p-3 outline-none focus:ring-2 focus:ring-purple-200"
              value={rejectionText}
              onChange={(e) => setRejectionText(e.target.value)}
            />

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <UIButton variant="md" colorType="secondary" label="Back" onClick={() => setReviewStep('actions')} className="w-full" />
              <UIButton
                variant="md"
                colorType="destructive"
                label="Confirm Rejection"
                onClick={() => {
                  // Move from pending to rejected and persist reason
                  if (selectedRequest) {
                    setPendingRequests((prev) => prev.filter((p) => p.id !== selectedRequest.id));
                    setHistoryRejected((prev) => [
                      {
                        ...selectedRequest,
                        rejectionReason: rejectionText,
                        id: `${selectedRequest.id}-rej-${Date.now()}`,
                      },
                      ...prev,
                    ]);
                  }
                  setReviewOpen(false);
                }}
                disabled={!rejectionText.trim()}
              />
            </div>
          </div>
        )}
      </UIDialog>
    </div>
  );
};
