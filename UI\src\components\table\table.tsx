import React from 'react';

import { Ellip<PERSON>Vert<PERSON>, Eye } from 'lucide-react';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';

import DataTable from './components/data-table';
import { type ColumnDef, type Variant } from './config';

/* eslint-disable @typescript-eslint/no-explicit-any */
type Props<T> = {
  tableHeaders: string[];
  data: { [key: string]: any }[];
  tableRowData: {
    meta: {
      variant?: Variant;
      align?: 'left' | 'right' | 'center';
      props?: (row: T) => Record<string, unknown>;
      actions: any[];
    }, 
    [key: string]: any
  }[];
  search: { value: string; update: (value: string) => void };
  sectionTitle: string;
  sectionBadgeTitle: string | number;
  searchPlaceholder: string;
  pagination: {
    update(updates: any): void;
    limit: number;
    currentPage: number;
    pagesCount: number;
    total: number;
  };
  addButtonLabel?: string;
  filterFeedData?: any[];
  setFilters: (filters: any) => void;
  onClickAdd?: () => void;
  loading: boolean;
  filters: any[];
  placeholder?: {
    title: string;
    subTitle?: string;
    image?: string;
  }
};

// const ActionsDropDown = ({ actions }: { actions: any[] }) => {
//   return (
//     <Menu as="div" className="relative inline-block">
//       <MenuButton className="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white/10 px-3 py-2 text-sm font-semibold text-white inset-ring-1 inset-ring-white/5 hover:bg-white/20">
//         <EllipsisVertical aria-hidden="true" className=" size-5 text-primary-default" />
//       </MenuButton>

//       <MenuItems
//         transition
//         className="absolute left-0 z-10 mt-2 w-56 origin-top-left rounded-md bg-gray-800 outline-1 -outline-offset-1 outline-white/10 transition data-closed:scale-95 data-closed:transform data-closed:opacity-0 data-enter:duration-100 data-enter:ease-out data-leave:duration-75 data-leave:ease-in"
//       >
//         <div className="py-1">
//           {actions.map((item) => (
//             <MenuItem>
//               <a href="#" className="block px-4 py-2 text-sm text-gray-300 data-focus:bg-white/5 data-focus:text-white data-focus:outline-hidden">
//                 {item}
//               </a>
//             </MenuItem>
//           ))}
//         </div>
//       </MenuItems>
//     </Menu>
//   );
// };

const Table: React.FC<Props> = ({
  tableHeaders,
  data,
  tableRowData,
  search,
  sectionTitle,
  loading,
  sectionBadgeTitle,
  addButtonLabel,
  onClickAdd,
  pagination,
  searchPlaceholder,
  filterFeedData,
  setFilters,
  filters,
  placeholder,
}) => {
  // Build column defs from the legacy props
  
  const columns: ColumnDef<{ [key: string]: unknown }>[] = [
    ...tableHeaders.map((h, i) => ({
      // use tableRowData[i] as the column id
      id: Object.keys(tableRowData)[i],
      header: h, // visible header label
      accessorFn: (row: any) => row[Object.keys(tableRowData)[i]],
      cell: tableRowData[Object.keys(tableRowData)[i]]
    })),
  ];


  return (
    <DataTable
      data={data}
      columns={columns}
      loading={loading}
      setFilters={setFilters}
      filterFeedData={filterFeedData}
      addButtonLabel={addButtonLabel}
      onClickAdd={onClickAdd}
      sectionTitle={sectionTitle}
      sectionBadgeTitle={sectionBadgeTitle}
      search={{ ...search, placeholder: searchPlaceholder }}
      filters={filters}
      pagination={{
        pages: pagination.pagesCount,
        currentPage: pagination.currentPage,
        onPageChange: (page) => pagination.update(page),
      }}
      placeholder={placeholder}
    />
  );
};

export default Table;
