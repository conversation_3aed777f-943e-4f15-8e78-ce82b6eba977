import React, { FC, useContext } from 'react';

import { Button, Icon } from 'src';
import { hideConfirm, RootState, showConfirm, useAppDispatch, useAppSelector } from 'UI/src';
import { Submission } from '../onboarding/index';

// types
type StepperFooterProps = {
  onMove: (index: number, action: string) => void;
  buttonsAvailability: {
    previous?: boolean;
    next?: boolean;
  };
};

type SubmissionContextType = {
  loading: boolean;
  submission: Submission;
};

export const StepperFooter: FC<StepperFooterProps> = ({ onMove, buttonsAvailability }) => {
  const { submission, loading } = useAppSelector((state: RootState) => state.submission);

  const dispatch = useAppDispatch();

  const isFirstStep = submission.stage?.index === 1;
  const isLastStep = submission.stage?.index === submission.quiz?.questionIds?.length;

  const ConfirmText = () => {
    return (
      <div>
        <div className="mx-auto mb-4 text-gray-400 dark:text-gray-200">
          <Icon icon="mage:question-mark-circle" width="70" className="text-primaryPurple" />
        </div>

        <p>Are you sure you want to submit the {submission?.quiz?.phoneScreening ? 'screening' : 'test'}?</p>
      </div>
    );
  };

  return (
    <footer className="flex w-full justify-between mt-[38px]">
      <Button
        tertiary
        label="Previous"
        iconWidth="29"
        onClick={() => onMove((submission.stage?.index ?? 1) - 1, 'previous')}
        disabled={loading || isFirstStep}
        loading={buttonsAvailability?.previous}
      />

      {isLastStep ? (
        <Button
          success
          label="Submit"
          icon="material-symbols:check-rounded"
          className="min-w-36"
          iconWidth="25"
          disabled={loading}
          loading={buttonsAvailability?.next}
          onClick={() => {
            /* FIXME: Add confirm dialog */
            // dispatch(
            // showConfirm({
            // message: ConfirmText(),
            // options: {
            // onConfirm() {
            dispatch(hideConfirm());
            onMove((submission.stage?.index ?? 1) + 1, 'lastStep');
            // },
            // },
            // })
            // );
          }}
        />
      ) : (
        <Button
          label="Next"
          onClick={() => onMove((submission.stage?.index ?? 1) + 1, 'next')}
          disabled={loading || isLastStep}
          loading={buttonsAvailability?.next}
          iconWidth="29"
        />
      )}
    </footer>
  );
};
