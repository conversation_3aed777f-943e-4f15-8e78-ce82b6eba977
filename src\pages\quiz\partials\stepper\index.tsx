// React
import { FC, useContext, useEffect, useState } from 'react';

// UI
import { QuestionTypeEnum, Api } from 'UI/src';

// Components
import { MainHeader } from './main-header';
import { StepperHeader } from './header';
import { Question } from './question';
import { Answer } from './answer';
import { StepperFooter } from './footer';
import { Bookmark } from './bookmark';
import { fetchSubmission, RootState, setErrorNotify, setLoading, setSubmission, useAppDispatch, useAppSelector } from 'UI/src';
import { Submission } from '../onboarding/index';
import { QuestionListItem } from '../stepper/bookmark';
import { Logo } from 'src';

// types

type Pagination = {
  page: number;
  size: number;
};

type SubmissionContextType = {
  applicantId?: string;
  submission: Submission;
  setLoading: (loading: boolean) => void;
  handleGetSubmission: (id?: string) => Promise<void>;
  handleServerError: (error: string) => void;
  setSubmission: (submission: Submission) => void;
};

type ButtonsAvailability = {
  previous: boolean;
  next: boolean;
};

export const SubmissionStepper: FC = () => {
  // State
  const [questionList, setQuestionList] = useState<QuestionListItem[]>([]);
  const [buttonsAvailability, setButtonsAvailability] = useState<ButtonsAvailability>({ previous: false, next: false });

  // Filters State
  const [filterBookmark, setFilterBookmark] = useState<boolean>(false);
  const [filterUnanswered, setFilterUnanswered] = useState<boolean>(false);

  // Pagination State
  const [count, setCount] = useState<number>(0);
  const [pagination, setPagination] = useState<Pagination>({ page: 1, size: 25 });

  const { submission } = useAppSelector((state: RootState) => state.submission);

  const handleServerError = (error: any) => {
    if (error.response?.data.message.includes('submission-locked')) {
      dispatch(setSubmission({ locked: true }));
    } else {
      dispatch(setErrorNotify(error.response.data.message));
    }
  };

  const dispatch = useAppDispatch();

  const handleGet = async (): Promise<void> => {
    try {
      const response = await Api.get(
        `stages/list/?submissionId=${submission._id}&page=${pagination?.page}&size=${pagination?.size}&filterUnanswered=${filterUnanswered}&filterBookmark=${filterBookmark}`
      );
      setCount(response?.data?.count ?? 0);
      setQuestionList(response?.data?.items ?? []);
    } catch (error) {
      handleServerError(error instanceof Error ? error.message : String(error));
    }
  };

  const handleSubmitAnswer = async (answer: any): Promise<void> => {
    dispatch(setLoading(true));
    try {
      const payload: any = {
        submissionId: submission._id,
        stageId: submission.stage?._id,
        answer: answer,
      };
      if (submission.stage?.question?.type === 3) {
        payload.phoneScreening = true;
      }
      const response = await Api.post('submissions/progress/submit', payload);
      if (response?.data) {
        dispatch(setSubmission(response.data));
      } else {
        throw new Error('No data returned from API');
      }
    } catch (error) {
      handleServerError(error instanceof Error ? error.message : String(error));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleOnMove = async (index: number, action: string): Promise<void> => {
    const isSubmit = index > (submission.quiz?.questionIds?.length ?? 0);
    if (isSubmit) {
      handleSubmitSubmission(submission.stage?.answer);
    } else {
      dispatch(setLoading(true));
      setButtonsAvailability({
        previous: action === 'previous',
        next: action === 'next',
      });
      try {
        const payload: any = {
          submissionId: submission._id,
          index,
          action,
          answer: submission.stage?.answer,
          stageId: submission.stage?._id,
        };
        if (submission.stage?.question?.type === 3) payload.phoneScreening = true;
        const submissionProgress = await Api.post('submissions/progress/step', payload);

        dispatch(setSubmission(submissionProgress.data));
      } catch (error) {
        handleServerError(error instanceof Error ? error.message : String(error));
      } finally {
        dispatch(setLoading(false));
        setButtonsAvailability({ previous: false, next: false });
      }
    }
    handleGet();
  };

  const handleSubmitSubmission = async (answer: any): Promise<void> => {
    dispatch(setLoading(true));

    try {
      await Api.post(`submissions/submit/${submission._id}`, { answer });

      submission._id && (await dispatch(fetchSubmission(submission._id)).unwrap());
    } catch (error) {
      handleServerError(error instanceof Error ? error.message : String(error));
    } finally {
      dispatch(setLoading(false));
    }
  };

  const handleAnswerChange = (value: any, key: any): void => {
    // Radio
    if (submission.stage?.question?.type === QuestionTypeEnum.Singlechoice) {
      handleSubmitAnswer(value);
    }
    // Checkbox
    if (submission.stage?.question?.type === QuestionTypeEnum.Multichoice) {
      const answer = typeof submission?.stage?.answer === 'object' && submission?.stage?.answer !== null ? { ...submission.stage.answer } : {};
      answer[key] = value;
      handleSubmitAnswer(answer);
    }
    // Textarea
    if (submission?.stage?.question?.type === 3) {
      const updatedSubmission = { ...submission };
      updatedSubmission.stage.answer = value;
      dispatch(setSubmission(updatedSubmission));
    }
    handleGet();
  };

  // On Mount
  useEffect(() => {
    handleGet();
  }, [filterBookmark, filterUnanswered, pagination?.page]);

  /* Pagination correct page if user moved to question index out of bookmark page limits */
  useEffect(() => {
    const questionIndex = submission.stage?.index ?? 1;
    const pageStartIndex = (pagination.page - 1) * pagination.size + 1;
    const pageEndIndex = pagination.page * pagination.size > count ? count : pagination.page * pagination.size;
    if (questionIndex < pageStartIndex || questionIndex > pageEndIndex) {
      const result = Math.ceil(questionIndex / pagination.size);
      setPagination((prev) => ({ ...prev, page: result }));
    }
  }, [submission.stage?.index]);

  console.log(submission);

  // Render
  return (
    <div className="gap-3 h-full p-4">
      <Logo className="h-9 mb-5" />

      <div className="flex gap-3 p-4">
        <div className="w-full">
          <div className="w-full h-full flex flex-col grow py-6 px-6 bg-white dark:bg-gray-800 rounded-xl border border-[#DEE2E4]">
            <Question handleGetBookmark={handleGet} />
            <div className="h-[1px] bg-[#DEE2E4] mb-5"></div>
            <Answer value={submission?.stage?.answer} onChange={handleAnswerChange} />
          </div>

          <div className="h-full flex flex-grow justify-center items-center">
            <StepperFooter onMove={handleOnMove} buttonsAvailability={buttonsAvailability} />
          </div>
        </div>

        <div>
          <Bookmark
            filterUnanswered={filterUnanswered}
            setFilterUnanswered={setFilterUnanswered}
            filterBookmark={filterBookmark}
            setFilterBookmark={setFilterBookmark}
            questionList={questionList}
            handleOnMove={handleOnMove}
            count={count}
            pagination={pagination}
            setPagination={setPagination}
          />
        </div>
      </div>
    </div>
  );
};
