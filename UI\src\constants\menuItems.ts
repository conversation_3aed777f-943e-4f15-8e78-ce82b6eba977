import { UserPermissions } from './enums';
// Items
interface MenuChild {
  label: string;
  path: string;
  lucide?: string;
  icon?: string;
  permission?: number | number[];
}

export interface MenuItem {
  label: string;
  icon: string;
  path?: string;
  children?: MenuChild[];
  type?: 'header';
  permission?: number | number[];
}

export const menuItems = ({
  isPermittedSuperAdmin,
  isPermittedAdmin,
  isPermittedContentCreator,
  isPermittedHr,
}: {
  isPermittedSuperAdmin: boolean;
  isPermittedAdmin: boolean;
  isPermittedContentCreator: boolean;
  isPermittedHr: boolean;
}): MenuItem[] => [
    ...(isPermittedSuperAdmin
      ? [
        {
          label: 'Administration',
          icon: 'mdi:account-cog',
          children: [
            { label: 'Dashboard', path: '/app/dashboard-super-admin', lucide: 'ChartNoAxesColumnIncreasing' },
            { label: 'Organizations', path: '/app/organizations', lucide: 'Network' },
            { label: 'Transactions', path: '/app/transactions', lucide: 'ArrowRightLeft' },
            { label: 'Users', path: '/app/users', lucide: 'Users', permission: UserPermissions.VIEW_USER },
            { label: 'Plans', path: '/app/plans', lucide: 'ClipboardList' },
            { label: 'Roles', path: '/app/roles', lucide: 'Shield' },
            { label: 'Avatar Management', path: '/app/avatars-management', lucide: 'Shield' },
          ],
        },
      ]
      : []),

    ...(isPermittedAdmin
      ? [
        {
          label: 'Administration',
          icon: 'mdi:account-cog',
          children: [
            { label: 'Dashboard', path: '/app/dashboard', lucide: 'ChartNoAxesColumnIncreasing' },
            { label: 'Plans', path: '/app/plans', lucide: 'ClipboardList' },
            // { label: 'Add Credits', path: '/app/credits', lucide: 'Zap' },
            { label: 'Avatars', path: '/app/avatars', lucide: 'Smile' },
            { label: 'Users', path: '/app/users', lucide: 'Users', permission: UserPermissions.VIEW_USER },
            { label: 'Roles', path: '/app/roles', lucide: 'Shield' },
          ],
        },
      ]
      : []),

    ...(isPermittedSuperAdmin || isPermittedAdmin || isPermittedContentCreator
      ? [
        {
          label: 'Assessments',
          path: '/assessments',
          icon: 'mdi:clipboard-check-outline',
          permission: UserPermissions.VIEW_ASSESSMENT,
          children: [
            // ...(isPermittedSuperAdmin || isPermittedAdmin || isPermittedHr
            //   ? [{ label: 'Screenings', path: '/app/assessments/screening', lucide: 'FileSearch' }]
            //   : []),
            ...(isPermittedSuperAdmin || isPermittedAdmin || isPermittedContentCreator
              ? [
                { label: 'Tests', path: '/app/assessment-templates/test', lucide: 'FileCheck2' },
                { label: 'Interviews', path: '/app/assessment-templates/interview', lucide: 'FileVideo2' },
              ]
              : []),
          ],
        },
      ]
      : []),
    ...(isPermittedSuperAdmin || isPermittedAdmin || isPermittedHr
      ? [
        {
          label: 'Reports',
          icon: 'mdi:account-box-outline',
          permission: [UserPermissions.VIEW_APPLICANT, UserPermissions.VIEW_ASSESSMENTS_REPORT],
          children: [
            { label: 'Applicants Report', path: '/app/applicants', lucide: 'IdCard', permission: UserPermissions.VIEW_APPLICANT },
            { label: 'Assessment Report', path: '/app/assessment-report', lucide: 'FileChartColumn', permission: UserPermissions.VIEW_ASSESSMENTS_REPORT },
          ],
        },
      ]
      : []),
    ...(isPermittedSuperAdmin || isPermittedAdmin || isPermittedContentCreator
      ? [
        {
          label: 'Question Bank',
          icon: 'mdi:help-circle-outline',
          permission: [UserPermissions.VIEW_QUESTION, UserPermissions.VIEW_CATEGORY],
          children: [
            { label: 'Questions', path: '/app/questions', lucide: 'FileQuestion', permission: UserPermissions.VIEW_QUESTION },
            { label: 'Categories', path: '/app/category-management', lucide: 'NotebookText', permission: UserPermissions.VIEW_CATEGORY },
          ],
        },
      ]
      : []),
  ];
