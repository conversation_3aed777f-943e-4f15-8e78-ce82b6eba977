// React
import { useEffect, useRef, memo } from 'react';

// Core
import { Icon } from 'src';

// Components
import { Question } from './question';
import Message from './message';
import { RootState, useAppSelector } from 'UI/src';

const Chat = ({ loading, chat, transcript, currentQuestion, sendManualReply, isSpeaking }: any) => {
  const chatEndRef = useRef(null);
  const { submissionAi } = useAppSelector((state: RootState) => state.submissionAi);

  // Scroll to the bottom of the chat
  const scrollToBottom = () => {
    if (chatEndRef.current) {
      (chatEndRef.current as any).scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Scroll to bottom when chat or transcript changes
  useEffect(() => {
    scrollToBottom();
  }, [chat, transcript]);

  return (
    <div className="h-full space-y-6 overflow-y-auto">
      <h2 className="text-xl font-semibold">Chat</h2>

      <div className="grid">
        {chat.map((message: any, index: number) => (
          <Message key={index} message={message} isLastMessage={index === chat.length - 1} loading={loading} />
        ))}

        {currentQuestion && !isSpeaking && <Question question={currentQuestion} loading={loading} sendManualReply={sendManualReply} />}

        {/* Applicant writing answer */}
        {transcript && (
          <div className="flex items-start gap-2 mr-1 ml-auto max-w-64 rounded-xl p-2">
            <div className="grid h-full px-3 py-2 rounded-xl text-[#1B1F3B] thepassBthree bg-[#F1E9FE] rounded-tr-none">{transcript}</div>

            <div className="thepassBone text-[#743AF5] flex justify-center items-center rounded-full size-10 bg-[#F1E9FE]">
              {submissionAi?.applicant?.name?.charAt(0)}
            </div>
          </div>
        )}

        {loading && (
          <Message
            message={{
              type: 1,
              text: <Icon className="mt-1 text-2xl text-gray-500" icon="svg-spinners:3-dots-bounce" />,
            }}
            isLastMessage={false}
          />
        )}
        <div ref={chatEndRef}></div>
      </div>
    </div>
  );
};

export default memo(Chat);
