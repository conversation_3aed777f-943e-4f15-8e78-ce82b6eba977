import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Card, Icon } from 'src';
import { Modal } from 'flowbite-react';

import { useAppDispatch, Api } from 'UI/src';
import { setErrorNotify } from 'UI';



interface InterviewRecordProps {
  assessmentId: string | undefined;
  applicantId: string | undefined;
  onClose: () => void;
}

interface InterviewRecord {
  videoUrl: string;
  _id?: string;
}

export const ComponentInterviewRecord = ({ assessmentId, applicantId, onClose }: InterviewRecordProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [interviewRecords, setInterviewRecords] = useState<InterviewRecord[]>([]);
  const dispatch = useAppDispatch();

  // Fetch interview records from API
  const fetchInterviewRecords = async () => {
    try {
      setIsLoading(true);
      const response = await Api.get(`ai-interview/list-uploads/${assessmentId}/${applicantId}`, {});
      const recordsWithVideos: InterviewRecord[] = response.data.map((record: any) => ({
        videoUrl: record,
      }));
      setInterviewRecords(recordsWithVideos);
      setShowModal(true);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message || 'Failed to fetch interview records'));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInterviewRecords();
  }, []);

  const handleClose = () => {
    setShowModal(false);
    if (onClose) onClose();
  };

  return (
    <>
      <Modal
        show={showModal}
        onClose={handleClose}
        size="4xl"
        className="z-[1050]"
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Modal.Header>Interview Recordings</Modal.Header>
        <Modal.Body>
          {isLoading ? (
            <div className="flex items-center justify-center h-40">
              <Icon name="eos-icons:loading" className="text-4xl text-primaryPurple" />
            </div>
          ) : interviewRecords.length > 0 ? (
            <div className="space-y-6">
              {interviewRecords.map((record, index) => (
                <Card onClick={() => {}} key={record._id || index} className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold">Recording {index + 1}</h3>
                  </div>
                  {record.videoUrl ? (
                    <div className="relative">
                      <div className="aspect-video">
                        <video
                          controls
                          className="w-full h-full border rounded-lg"
                          src={record.videoUrl}
                          // type="video/webm"
                          crossOrigin="anonymous"
                          playsInline
                        >
                          <source src={record.videoUrl} type="video/webm" />
                          <source src={record.videoUrl} type="video/mp4" />
                          Your browser does not support the video tag.
                        </video>
                      </div>
                      <div className="mt-2 text-center">
                        <a
                          href={record.videoUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center text-blue-600 hover:underline"
                        >
                          <Icon icon="heroicons:arrow-top-right-on-square" className="mr-1" />
                          Open video in new tab
                        </a>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center p-8 bg-gray-100 rounded-lg dark:bg-gray-800">
                      <Icon icon="ri:video-off-line" className="mb-2 text-4xl text-gray-400" />
                      <p className="text-gray-500">No video recording available</p>
                    </div>
                  )}
                </Card>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center">
              <Icon icon="ri:video-off-line" className="mx-auto mb-3 text-4xl text-gray-400" />
              <p className="text-gray-500">No interview recordings found for this applicant.</p>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button onClick={handleClose}>Close</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
