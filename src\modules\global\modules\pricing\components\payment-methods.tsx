import React from 'react';
import visa from 'images/payment/visa.svg';
import mastercard from 'images/payment/mastercard.svg';
import amex from 'images/payment/amex.svg';
import paypal from 'images/payment/paypal.svg';
import applepay from 'images/payment/applepay.svg';
export const PaymentMethods = () => {
  const paymentMethods = [
    { name: 'Visa', logo: visa },
    { name: 'MasterCard', logo: mastercard },
    { name: 'American Express', logo: amex },
    { name: 'PayPal', logo: paypal },
    { name: 'Apple Pay', logo: applepay },
  ];

  return (
    <div className="mt-6">
      <p className="text-center text-sm text-gray-500 dark:text-gray-400 mb-3">Accepted Payment Methods</p>
      <div className="flex justify-center items-center gap-4 flex-wrap">
        {paymentMethods.map((method) => (
          <div key={method.name} className="h-8 flex items-center">
            <img src={method.logo} alt={`${method.name} logo`} className="h-full object-contain" title={method.name} />
          </div>
        ))}
      </div>
    </div>
  );
};
