// Recharts
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts';

interface ChartsWavyProps {
  data: {
    month?: number;
    monthItem?: { month: number };
    interviewItem?: { month: number };
    predefinedItem?: { month: number };
  }[];
  dataKeys: {
    a: string;
    b: string;
    c: string;
  };
  colors: {
    a: string;
    b: string;
    c: string;
  };
}
export const ChartsWavy = ({ data, dataKeys, colors }: ChartsWavyProps) => {
  const keys = dataKeys || { a: 'A', b: 'B', c: 'C' };
  const colorConfig = {
    a: { stroke: colors?.a || '#3b82f6', gradient: 'colorA' },
    b: { stroke: colors?.b || '#10b981', gradient: 'colorB' },
    c: { stroke: colors?.c || '#a855f7', gradient: 'colorC' },
  };
  return (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart data={data}>
        <defs>
          <linearGradient id="colorA" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={colorConfig.a.stroke} stopOpacity={0.5} />
            <stop offset="95%" stopColor={colorConfig.a.stroke} stopOpacity={0} />
          </linearGradient>
          <linearGradient id="colorB" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={colorConfig.b.stroke} stopOpacity={0.5} />
            <stop offset="95%" stopColor={colorConfig.b.stroke} stopOpacity={0} />
          </linearGradient>
          <linearGradient id="colorC" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={colorConfig.c.stroke} stopOpacity={0.5} />
            <stop offset="95%" stopColor={colorConfig.c.stroke} stopOpacity={0} />
          </linearGradient>
        </defs>
        <XAxis dataKey="x" />
        <YAxis />
        <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
        <Tooltip
          formatter={(value, name) => {
            const displayName = keys[name as 'a' | 'b' | 'c'] || name;
            return [value, displayName];
          }}
        />{' '}
        <Area type="monotone" dataKey="a" stroke={colorConfig.a.stroke} fill="url(#colorA)" strokeWidth={2} name="a" />
        <Area type="monotone" dataKey="b" stroke={colorConfig.b.stroke} fill="url(#colorB)" strokeWidth={2} name="b" />
        <Area type="monotone" dataKey="c" stroke={colorConfig.c.stroke} fill="url(#colorC)" strokeWidth={2} name="c" />
      </AreaChart>
    </ResponsiveContainer>
  );
};
