// React
import { useState, useEffect } from 'react';
import { useAppDispatch, Api } from 'UI/src';
import { useParams } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';

// Core
import { Jumbotron, Button, CustomIcon, Icon, ScrollableTabs, Card } from 'src';

export interface CandidateProfileCardData {
    applicantId: string;
    applicantName: string;
    applicantEmail: string;
    score: number;
    timeTaken: number;
}

interface CandidateProfileCardProps {
  data?: CandidateProfileCardData;
}

export const CandidateProfileCard = ({ data }: CandidateProfileCardProps) => {
  const dispatch = useAppDispatch();
  const { quizId } = useParams();

  // Hooks
  const navigate = useNavigate();

  return (
    <>
      <Card
        className="flex flex-col space-y-3 items-center p-4 w-[260px] rounded-lg cursor-pointer"
        onClick={() => navigate(`/app/applicants/progress/${data?.applicantId}`)}
      >
        {/* Avatar with crown */}
        <div className="relative mb-2">
          <div className="w-16 h-16 rounded-full  flex items-center justify-center text-purple-600 font-semibold text-xl"></div>
          {/* Crown icon */}
          <div className="absolute -top-2 -right-2">
            <CustomIcon definedIcon="candidateIconSvg" />
          </div>
        </div>
        {/* Name and email */}
        <div className="pt-1">
          <h3 className="font-semibold text-center text-[#181D27]">{data?.applicantName}</h3>
          <p className="text-gray-500 text-sm mb-4">{data?.applicantEmail}</p>
        </div>
        <div className="h-0.5 w-full bg-gray-200"></div>
        <div className="w-5 h-1 block text-red-600"></div>
        {/* Stats */}
        <div className="grid grid-cols-2 w-full gap-4 text-center">
          <div>
            <p className="text-gray-500 text-xs">Score</p>
            <p className="font-bold text-[#1E9107]">{data?.score}</p>
          </div>
          <div>
            <p className="text-gray-500 text-xs">Time Taken</p>
            <p className="font-bold">{data?.timeTaken}</p>
          </div>
        </div>
      </Card>
    </>
  );
};
