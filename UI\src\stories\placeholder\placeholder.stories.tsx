import type { Meta, StoryObj } from '@storybook/react';
import { Placeholder } from '../../components/placeholder';

const meta: Meta<typeof Placeholder> = {
  title: 'Components/Placeholder',
  component: Placeholder,
  tags: ['autodocs'],
  argTypes: {
    onClickButton: { action: 'clicked' },
  },
};

export default meta;

type Story = StoryObj<typeof Placeholder>;

export const Default: Story = {
  args: {
    image: 'https://via.placeholder.com/150',
    title: 'No Items Found',
    subTitle: 'Try creating something to see it here.',
    buttonText: 'Create Now',
  },
};

export const WithoutButton: Story = {
  args: {
    image: 'https://via.placeholder.com/150',
    title: 'Nothing Here Yet',
    subTitle: 'This section will populate once you add content.',
  },
};
