import React from 'react';
import { Icon } from 'src';

export const ResultStatusSubmission = ({ statusSubmission }: { statusSubmission: number | string }) => {
  const scoreStyle = () => {
    if (statusSubmission === 1) return 'text-text-500  !bg-[#DEE2E4] ';
    else if (statusSubmission === 2) return 'text-info-dark bg-info-light';
    else if (statusSubmission === 3) return 'text-success-dark bg-success-light';
  };

  return (
    <div className={`w-fit flex items-center gap-1 px-2.5 py-1 rounded-full font-semibold text-xs ${scoreStyle()} `}>
      {/* {(statusSubmission === 1 || statusSubmission === 2) && <Icon icon="material-symbols:circle" width="8" />} */}
      {statusSubmission === 1 ? 'Not Started' : statusSubmission === 2 ? 'In Progress' : statusSubmission === 3 ? 'Completed' : ''}
    </div>
  );
};

export const ResultStatusApplicant = ({ statusAvailable }: { statusAvailable: number }) => {
  const isAvailable = statusAvailable === 1;

  const availabilityStyle = () => {
    return isAvailable
      ? 'text-green-600 dark:text-green-300 bg-green-100 dark:bg-green-800 font-semibold'
      : 'text-red-600 dark:text-red-300 bg-red-100 dark:bg-red-800 font-semibold  ';
  };

  return (
    <div className={`w-fit flex items-center gap-1 px-2.5 py-1 rounded-full font-semibold text-xs ${availabilityStyle()}`}>
      <Icon icon={isAvailable ? 'material-symbols:check-circle' : 'material-symbols:cancel'} width="12" />
      {isAvailable ? 'Available' : 'Not Available'}
    </div>
  );
};
