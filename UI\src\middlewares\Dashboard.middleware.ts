import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';

// Get assessment statistics
export const fetchAssessmentStatistics = createAsyncThunk(
  'dashboard/fetchAssessmentStatistics',
  async (params: { start?: string; end?: string }, { rejectWithValue }) => {
    try {
      const response = await Api.get('dashboard/assessment/statistics', params);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch assessment statistics');
    }
  }
);

// Get super admin organizations subscriptions
export const fetchSuperAdminOrganizationsSubscriptions = createAsyncThunk(
  'dashboard/fetchSuperAdminOrganizationsSubscriptions',
  async (params: any, { rejectWithValue }) => {
    try {
      const response = await Api.get('superAdmin/organizations/subscriptions', params || {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch organizations subscriptions');
    }
  }
);

// Get super admin subscription overview
export const fetchSuperAdminSubscriptionOverview = createAsyncThunk(
  'dashboard/fetchSuperAdminSubscriptionOverview',
  async (_, { rejectWithValue }) => {
    try {
      const response = await Api.get('superAdmin/subscription/overview', {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch subscription overview');
    }
  }
);

// Get super admin organizations growth
export const fetchSuperAdminOrganizationsGrowth = createAsyncThunk(
  'dashboard/fetchSuperAdminOrganizationsGrowth',
  async (_, { rejectWithValue }) => {
    try {
      const response = await Api.get('superAdmin/organizations/growth');
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch organizations growth');
    }
  }
);

// Get super admin organizations risk
export const fetchSuperAdminOrganizationsRisk = createAsyncThunk(
  'dashboard/fetchSuperAdminOrganizationsRisk',
  async (params: any, { rejectWithValue }) => {
    try {
      const response = await Api.get('superAdmin/organizations/risk', params || {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch organizations risk');
    }
  }
);

// Get super admin organizations
export const fetchSuperAdminOrganizations = createAsyncThunk(
  'dashboard/fetchSuperAdminOrganizations',
  async (_, { rejectWithValue }) => {
    try {
      const response = await Api.get('superAdmin/organizations', {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch organizations');
    }
  }
);

// Get super admin engagement
export const fetchSuperAdminEngagement = createAsyncThunk(
  'dashboard/fetchSuperAdminEngagement',
  async (_, { rejectWithValue }) => {
    try {
      const response = await Api.get('superAdmin/engagement', {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch engagement');
    }
  }
);

// Get super admin assessment overview
export const fetchSuperAdminAssessmentOverview = createAsyncThunk(
  'dashboard/fetchSuperAdminAssessmentOverview',
  async (_, { rejectWithValue }) => {
    try {
      const response = await Api.get('superAdmin/assessment/overview');
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch assessment overview');
    }
  }
); 
