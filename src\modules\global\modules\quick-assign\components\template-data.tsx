// React
import { Clock5, HelpCircle, FileCog } from 'lucide-react';

// Core
import { TestDifficulty, SubcategoryFieldColumn } from 'src';

export const TemplateData = ({ singleData }: any) => {
  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center gap-2">
        <p className="text-[18px] font-medium">{singleData?.title}</p>{' '}
      </div>

      <div className="flex flex-wrap gap-2">
        {singleData?.subCategoryName.map((subCategory: any) => (
          <SubcategoryFieldColumn subCategoryName={subCategory} />
        ))}
      </div>

      <div className="flex flex-wrap gap-2 text-[#747C98]">
        <div className="flex items-center gap-1">
          <HelpCircle className="size-[18px]" />
          <p>
            <span className="font-semibold"> {singleData?.numOfQuestions} </span> {singleData?.numOfQuestions > 1 ? 'Questions' : 'Question'}
          </p>
        </div>

        <div className="flex items-center gap-1">
          <Clock5 className="size-4" />
          <p>
            <span className="font-semibold"> {singleData?.duration} </span>min
          </p>
        </div>

        <TestDifficulty
          difficulty={singleData?.difficulty}
          // difficultyIcon
        />
      </div>
    </div>
  );
};
