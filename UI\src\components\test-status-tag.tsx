import React from 'react';
import { Icon } from '../../../src/components/icon';

// Test Status Tag Props
type TestStatusTagProps = {
  test: any;
  hideScore?: boolean;
  hideTime?: boolean;
  hideMissed?: boolean;
};

// Test Status Tag Component
const TestStatusTag: React.FC<TestStatusTagProps> = ({ test, hideScore, hideTime, hideMissed }) => {
  const publicStyles = {
    completed: 'text-success-dark bg-success-light',
    missed: 'text-danger-dark bg-danger-light',
    scheduleld:
      'text-statusColorScheduleldText bg-statusColorScheduleldBackground dark:text-statusDarkColorScheduleldText dark:bg-statusDarkColorScheduleldBackground',
    poor: 'text-statusColorPoor dark:text-statusDarkColorPoor',
    good: 'text-statusColorGood dark:text-statusDarkColorGood',
    excellent: 'text-statusColorExcellent dark:text-statusDarkColorExcellent',
    inProgress: 'text-info-dark bg-info-light',
    notStarted: 'text-text-500  !bg-[#DEE2E4] ',
    icon: 'material-symbols:circle',
  };

  const scoreStyle = () => {
    const score = test?.score;
    if (score < 50) return publicStyles.poor;
    else if (score <= 80) return publicStyles.good;
    else if (score > 80) return publicStyles.excellent;
    return publicStyles.poor;
  };

  const handleRemaningTime = () => {
    if (!test?.dueDate) return null;
    const now = new Date();
    const dueDate = new Date(test.dueDate);
    const diffTime = dueDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) return null;
    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Tomorrow';
    if (diffDays < 7) return `${diffDays} days`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks`;
    return `${Math.ceil(diffDays / 30)} months`;
  };

  const status = () => {
    if (test?.expired) {
      if (!test?.startedAt) {
        return {
          text: 'Missed Deadline',
          textStyles: publicStyles?.missed,
        };
      }
    } else if (test?.locked) {
      return {
        text: 'Scheduled',
        textStyles: publicStyles?.scheduleld,
        remaningTime: handleRemaningTime(),
      };
    } else if (test?.submittedAt) {
      return {
        score: `${test?.score}%`,
        scoreStyles: scoreStyle(),
      };
    } else if (test?.startedAt) {
      return {
        text: 'In Progress',
        textStyles: publicStyles?.inProgress,
        icon: publicStyles?.icon,
      };
    } else {
      return {
        text: 'Not Started',
        textStyles: publicStyles?.notStarted,
      };
    }
  };

  return (
    <div className="flex items-center gap-1.5 leading-4">
      {status()?.score && !hideScore && <p className={`py-2 rounded-full font-semibold text-sm ${status()?.scoreStyles}`}>{status()?.score}</p>}
      {status()?.text && !hideMissed && (
        <div className={`flex items-center gap-1 px-2.5 py-1 rounded-full font-medium text-xs ${status()?.textStyles}`}>
          {status()?.icon && <Icon icon={status()!.icon ?? ''} width={'8'} />}
          <p className="text-nowrap">{status()?.text}</p>
        </div>
      )}
      {status()?.remaningTime && !hideTime && (
        <div className="flex gap-0.5 ml-1 w-max">
          <Icon icon="tabler:clock-hour-3" className="text-[#9CA7B6]" />
          <span className="text-[#798296]">{status()?.remaningTime}</span>
        </div>
      )}
    </div>
  );
};

export default TestStatusTag;
