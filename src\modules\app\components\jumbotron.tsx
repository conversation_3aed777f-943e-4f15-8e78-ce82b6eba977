import React from 'react';

import { Icon, Card } from 'src';
import { useBreadcrumb } from 'UI/src';

export const AppJumbotron = () => {
  // Hooks
  const { currentRoute } = useBreadcrumb();

  if (currentRoute?.data?.title) {
    return (
      <div className="relative overflow-hidden">
        <Card>
          <div className="relative z-10">
            <h3 className="text-gray-900 dark:text-white font-bold text-2xl mb-2">{currentRoute.data.title}</h3>

            {currentRoute?.data?.subtitle && <p className="text-lg text-gray-500 dark:text-gray-400 font-medium">{currentRoute.data.subtitle}</p>}
          </div>

          {currentRoute?.data.icon && (
            <div className="hidden sm:block absolute top-1 end-0 text-gray-100 dark:text-gray-700 opacity-75">
              <Icon icon={currentRoute.data.icon} width="180" />
            </div>
          )}
        </Card>
      </div>
    );
  }

  return null;
};
