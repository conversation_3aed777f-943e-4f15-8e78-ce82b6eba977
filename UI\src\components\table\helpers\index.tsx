import tableCell from '../components/cells';
import {
  type ColumnDef
} from '../config';
/* eslint-disable @typescript-eslint/no-explicit-any */
type Variant = keyof typeof tableCell;

export const cellAlign = (align?: 'left' | 'right' | 'center') => {
  if (align === 'right') return 'text-right';
  if (align === 'center') return 'text-center';
  return 'text-start';
}

export const headerAlign = (align?: 'left' | 'right' | 'center') => {
  if (align === 'right') return 'text-right';
  if (align === 'center') return 'text-center';
  return 'text-start';
}

export function resolveHeader<T extends Record<string, any>>(col: ColumnDef<T>): React.ReactNode {
  if (typeof col.header === 'function') return col.header(col);
  return col.header;
}

export function getValue<T extends Record<string, any>>(row: T, col: ColumnDef<T>): unknown {
  if (col.accessorFn) return col.accessorFn(row);
  if (col.accessorKey) return (row as any)[col.accessorKey];
  return undefined;
}

export function renderCell<T extends Record<string, any>>(row: T, col: ColumnDef<T>, rowIndex: number) {
  const value = getValue(row, col);

  const date = new Date(value as string);
  const isValidDate = !isNaN(date.getTime()) && typeof value !== 'number';

  if (typeof value === 'string' && value.includes(':') && isValidDate) {
    return (
      <span className="text-textStroke-primary-900">
        {new Intl.DateTimeFormat('en-US', { day: '2-digit', month: 'short', year: 'numeric' }).format(date)}
      </span>
    );
  }
  if (col.cell) {
    const variant = (col?.cell.meta.variant) as Variant;

    if (!variant || !tableCell[variant]) return;
    const { cell: Cell, props: mainProps } = tableCell[variant];

    const mainPropsObject = (mainProps as readonly string[]).reduce(
      (acc, propName) => {
        acc[propName] = value;
        return acc;
      },
      {} as Record<string, unknown>
    );

    const extraProps =
      typeof col?.cell.meta.props === 'function'
        ? col?.cell.meta.props(row)
        : {};

    return (
      <Cell
        {...mainPropsObject}
        {...extraProps}
      />
    );
  }
};