export const PhoneSkeleton = () => (
  <div className="relative divide-gray-200 animate-pulse dark:divide-gray-700 rounded-lg">
    <div className="hidden lg:block py-4">
      <div className="border-[1px] pb-3 rounded-lg">
        <div className="flex justify-between py-4 px-4">
          <div className="h-4 bg-gray-300 rounded-full dark:bg-gray-600 w-52"></div>
          <div className="h-8 bg-gray-300 rounded-md dark:bg-gray-700 w-28"></div>
        </div>
        <div className="flex flex-col px-4">
          <div className="col-span-4 h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-1/5"></div>
        </div>
      </div>
      <div className="flex mt-6 gap-6 px-5 align-middle items-center">
        <div className="col-span-2 h-4 bg-gray-300 rounded-full dark:bg-gray-700 w-48"></div>
        <div className="col-span-3 h-5 bg-gray-300 rounded-full dark:bg-gray-700 w-12"></div>
      </div>

      <div className="p-5 space-y-5 border rounded-lg mt-6 px-4">
        <div className="flex justify-between">
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-2/3"></div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-14"></div>
        </div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-96"></div>
      </div>

      <div className="p-5 space-y-5 border rounded-lg mt-6 px-4">
        <div className="flex justify-between">
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-2/3"></div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-14"></div>
        </div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-96"></div>
      </div>

      <div className="p-5 space-y-5 border rounded-lg mt-6 px-4">
        <div className="flex justify-between">
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-2/3"></div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-14"></div>
        </div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-96"></div>
      </div>

      <div className="p-5 space-y-5 border rounded-lg mt-6 px-4">
        <div className="flex justify-between">
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-2/3"></div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-14"></div>
        </div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-96"></div>
      </div>

      <div className="p-5 space-y-5 border rounded-lg mt-6 px-4">
        <div className="flex justify-between">
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-2/3"></div>
          <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-14"></div>
        </div>
        <div className="h-2.5 bg-gray-300 rounded-full dark:bg-gray-700 w-96"></div>
      </div>

    </div>
  </div>
);
