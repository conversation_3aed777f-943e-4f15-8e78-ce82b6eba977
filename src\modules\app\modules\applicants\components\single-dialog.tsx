import React, { useEffect } from 'react';
import { Button } from 'UI';

import {
  Regex,
  useValidate,
  Form,
  initializeForm,
  RootState,
  setFieldValue,
  ApplicantType,
  useAppDispatch,
  useAppSelector,
  updateUser,
} from 'UI/src';
import { fetchApplicant, createApplicant, updateApplicant } from 'UI/src/middlewares/Applicants.middleware';

import { Dialog } from 'UI';
import { TextInput, RadioGroup, Select, PhoneNumberInput } from 'src';
import { setErrorNotify, setNotifyMessage } from 'UI';
import { useFormik } from 'formik';
import { label } from 'three/src/nodes/TSL.js';

interface ApplicantsSingleDialogProps {
  onClose: () => void;
  onCreate: () => void;
  id?: string;
}

export const ApplicantsSingleDialog = ({ onClose, onCreate, id }: ApplicantsSingleDialogProps) => {
  // Hooks
  const dispatch = useAppDispatch();
  const { isRequired, validateRegex, minLength, maxLength, countryCodeNumberValid } = useValidate();

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      name: '',
      gender: null,
      email: '',
      track: '',
      seniorityLevel: 0,
      mobileNumber: '',
      notes: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  const handleGet = async () => {
    if (!id) return;
    try {
      const result = await dispatch(fetchApplicant(id)).unwrap();
      console.log('applicants/single', result);
      dispatch(initializeForm(result));
    } catch (error: any) {
      dispatch(setErrorNotify(error?.message || 'Failed to fetch applicant'));
    }
  };

  const handleInsert = async (e: any) => {
    try {
      let payload = { ...form };
      if (payload.track === '') {
        delete payload.track;
      }
      await dispatch(createApplicant(payload)).unwrap();

      onCreate();
      dispatch(setNotifyMessage('Applicant added successfully!'));
      onClose();
    } catch (error: any) {
      dispatch(setErrorNotify(error?.message || 'Failed to create applicant'));
    }
  };

  const handleUpdate = async (e: any) => {
    if (!id) return;
    try {
      await dispatch(updateApplicant({ id, data: form })).unwrap();

      onCreate();
      dispatch(setNotifyMessage('Applicant updated successfully!'));
      onClose();
    } catch (error: any) {
      dispatch(setErrorNotify(error?.message || 'Failed to update applicant'));
    }
  };

  // Getters
  const isEditMode = () => !!form._id;

  // On mount
  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, []);

  return (
    <Dialog size="lg" isOpen title={id ? 'Edit Applicant' : 'Create Applicant'} onClose={onClose}>
      {/* Creation Form */}

      <Form onSubmit={isEditMode() ? handleUpdate : handleInsert}>
        <div className="grid gap-4">
          <div className="flex flex-col gap-2">
            <TextInput
              label="Name"
              name="name"
              placeholder="Enter applicant’s name"
              value={form.name}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'name', value }))}
              validators={[isRequired(), validateRegex(Regex.name), minLength(2), maxLength(50)]}
            />
            {/* 
            <div className="mb-1">
              <RadioGroup
                name="gender"
                label="Gender"
                value={form.gender}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'gender', type: Number, value }))}
                lookup="$Gender"
                className="text-inputLabel dark:text-inputDarkLabel"
                requiredLabel
                direction="row"
                showSingleClear={false}
                handleSingleClear={() => {}}
                params={{}}
              />
            </div> */}

            <TextInput
              label="Email address"
              name="email"
              placeholder="Enter applicant’s email address"
              value={form.email}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'email', value }))}
              validators={[isRequired(), validateRegex(Regex.email)]}
            />

            {/* <TextInput
              label="Address"
              name="address"
              placeholder="Applicant address"
              value={form.address}
              onChange={(value)=>dispatch(setFieldValue({path:'address')}
              validators={[isRequired()]}
            /> */}

            <PhoneNumberInput
              name="mobileNumber"
              label="Mobile Number"
              value={form.mobileNumber}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'mobileNumber', value }))}
              validators={[countryCodeNumberValid()]}
            />

            {/* <div className="space-y-3">
              <Select
                label="Track"
                name="track"
                value={form.track}
                lookup="category"
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon={true}
                onChange={(selectedTrack) => {
                  dispatch(setFieldValue({path:'track')(selectedTrack);
                }}
                validators={[isRequired()]}
                creationOptions={{
                  url: 'lookups/category/single',
                  fieldName: 'name',
                  validation: Regex.categorySubcategoryTopic,
                }}
                requiredLabel
                placeholder="Select applicant’s track"
              />
            </div> */}

            <Select
              label="Level"
              name="SeniorityLevel"
              value={form.seniorityLevel}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'seniorityLevel', type: Number, value }))}
              lookup="$QuizDifficulty"
              dropIcon={true}
              validators={[isRequired()]}
              placeholder="Select applicant’s level"
              requiredLabel
              optionLabelKey="label"
              optionValueKey="value"
            />
          </div>
        </div>

        <div className="flex gap-4 mt-6">
          <Button colorType="tertiary" className="w-full" onClick={onClose} label="Cancel" />
          <Button colorType="primary" className="w-full" type="submit" label={`${!!id ? 'Update' : 'Create'}`} />
        </div>
      </Form>
    </Dialog>
  );
};
