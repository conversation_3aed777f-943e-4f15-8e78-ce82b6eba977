import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';

// Fetch categories
export const fetchCategories = createAsyncThunk(
  'categories/fetchCategories',
  async (params: any, { rejectWithValue }) => {
    try {
      const response = await Api.get('lookups/category', params || {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch categories');
    }
  }
);

// Create category
export const createCategory = createAsyncThunk(
  'categories/createCategory',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('lookups/category/single', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create category');
    }
  }
);

// Update category
export const updateCategory = createAsyncThunk(
  'categories/updateCategory',
  async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
    try {
      const response = await Api.put(`lookups/category/single/${id}`, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to update category');
    }
  }
);

// Delete category
export const deleteCategory = createAsyncThunk(
  'categories/deleteCategory',
  async (id: string, { rejectWithValue }) => {
    try {
      await Api.delete(`lookups/category/single/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to delete category');
    }
  }
);

// Fetch subcategories
export const fetchSubcategories = createAsyncThunk(
  'categories/fetchSubcategories',
  async (params: any, { rejectWithValue }) => {
    try {
      const response = await Api.get('lookups/subCategory', params || {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch subcategories');
    }
  }
);

// Create subcategory
export const createSubcategory = createAsyncThunk(
  'categories/createSubcategory',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('lookups/subCategory/single', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create subcategory');
    }
  }
);

// Update subcategory
export const updateSubcategory = createAsyncThunk(
  'categories/updateSubcategory',
  async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
    try {
      const response = await Api.put(`lookups/subCategory/single/${id}`, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to update subcategory');
    }
  }
);

// Delete subcategory
export const deleteSubcategory = createAsyncThunk(
  'categories/deleteSubcategory',
  async (id: string, { rejectWithValue }) => {
    try {
      await Api.delete(`lookups/subCategory/single/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to delete subcategory');
    }
  }
); 
