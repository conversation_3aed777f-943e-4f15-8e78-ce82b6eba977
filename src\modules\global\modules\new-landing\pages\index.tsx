import React, { useEffect } from 'react';

import { useLocation } from 'react-router-dom';

import {
  InterviewerNeverSleeps,
  CustomizeRecruiter,
  SmarterReportsDecisions,
  WhyTeamsTrust,
  HeroSection,
  ProcessFlowSection,
  TalentScreeningSection,
  TransformationSection,
} from '../components';

export const NewLandingPage = () => {
  const location = useLocation();

  useEffect(() => {
    if (location.hash) {
      setTimeout(() => {
        const element = document.getElementById(location.hash.substring(1));
        if (element) {
          const headerHeight = 80;
          const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
          const offsetPosition = elementPosition - headerHeight;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth',
          });
        }
      }, 100);
    }
  }, [location]);

  return (
    <div>
      {/* Part 1 */}
      <div>
        <HeroSection />

        <ProcessFlowSection />

        <TalentScreeningSection />
      </div>

      {/* Part 2 */}
      <div className="w-full flex flex-col space-y-8">
        <InterviewerNeverSleeps />

        <CustomizeRecruiter />

        <SmarterReportsDecisions />

        <div id="why-choose-us">
          <WhyTeamsTrust />
        </div>

        <TransformationSection />
      </div>
    </div>
  );
};
