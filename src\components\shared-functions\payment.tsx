// Cors
import { SubscriptionStatus } from 'UI';

export const CurrencySymbol = ({ currency }: { currency: string }) => {
  switch (currency) {
    case 'SAR':
      return <span className="icon-saudi_riyal"></span>;
    case 'USD':
      return '$';
    default:
      return '—';
  }
};

export const SubscriptionPlanPeriod = ({ plan }: { plan: number | string }) => {
  const handleSubscriptionPlanPeriod = () => {
    switch (plan) {
      case 1:
        return { title: 'monthly', styles: 'bg-[#F9F5FF] border border-[#E9EAEB] text-[#6941C6]' };
      case 2:
        return { title: 'yearly', styles: 'bg-[#F9F5FF] border border-[#E9EAEB] text-[#6941C6]' };
    }
  };
  return <div className={`text-sm px-3 py-0.5 rounded-full ${handleSubscriptionPlanPeriod()?.styles}`}>{handleSubscriptionPlanPeriod()?.title}</div>;
};

export const PaymentStatus = ({ status }: { status: number }) => {
  const PaymentStatus = () => {
    switch (status) {
      case 1: // Pending
        return '';
      case 2: // Paid
        return 'bg-[#ECFDF3] border border-[#ABEFC6] text-[#067647]';
      case 3: // Cancelled
        return 'bg-gray-100 border border-gray-300 text-gray-500';
      case 4: // Failed
        return 'bg-[#FEF3F2] border border-[#FECDCA] text-[#B42318]';
      case 5: // Expired
        return '';
    }
  };
  return <div className={`text-sm px-3 py-0.5 font-medium rounded-full ${PaymentStatus()}`}>{SubscriptionStatus[status]}</div>;
};
