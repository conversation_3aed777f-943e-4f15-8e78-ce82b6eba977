// React
import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Draggable from 'react-draggable';
import * as LucideIcons from 'lucide-react';

// Core
import { Jumbotron, Button, CustomIcon, Icon } from 'src';

// Components
import { RandomizeAskAiPage } from '../components/create/question-bank/randomize-ask-ai';
import { ReviewDrawer } from '../components/create/review-drawer';
import { Stage } from '../components/stage';
import { QuestionBank } from '../components/create/question-bank';
import { TemplateInfo } from '../components/create/template-info';
import { Api, Form, initializeForm, QuizType, RootState, useAppDispatch, useAppSelector, useScreenSize } from 'UI/src';
import { setNotifyMessage, setErrorNotify } from 'UI';
import { useFormik } from 'formik';

export const AssessmentsCreatePage = () => {
  // Hooks
  const dispatch = useAppDispatch();
  const { type, id } = useParams();
  const navigate = useNavigate();

  // Lucide
  const LucideCircleCheckBig = LucideIcons.CircleCheckBig;

  // Reference
  const formRef = useRef(null);

  // State
  const [activeStage, setActiveStage] = useState(0);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [anyQuestionHasEditMode, setAnyQuestionHasEditMode] = useState({});
  const [backupList, setBackupList] = useState([]);
  const [isShowReviewDrawer, setIsShowReviewDrawer] = useState(false);
  const [selectedQuestionsID, setSelectedQuestionsID] = useState<{ [key: string]: boolean }>({});
  const [isRandomizeAskAiVisible, setRandomizeAskAiVisibilty] = useState(false);
  const [disableNextButton, setDisableNextButton] = useState(false);
  const [disabledMessage, setDisabledMessage] = useState('');

  // Set edit mode based on id presence
  useEffect(() => {
    setIsEditMode(!!id);
    if (id) {
      handelGet();
    }
  }, [id]);

  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      title: '',
      description: '',
      seniorityLevel: '',
      difficulty: '',
      duration: '',
      skips: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });
  const stage = [
    {
      label: `Choose Questions`,
      // header: 'Choose Questions',
      component: (
        <QuestionBank
          disableButtons={{ setDisableNextButton, setDisabledMessage }}
          selectedQuestionsID={selectedQuestionsID}
          setSelectedQuestionsID={setSelectedQuestionsID}
          anyQuestionHasEditMode={anyQuestionHasEditMode}
          setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
          isRandomizeAskAiVisible={isRandomizeAskAiVisible}
          setRandomizeAskAiVisibilty={setRandomizeAskAiVisibilty}
        />
      ),
    },

    {
      label: 'Template Setup',
      // header: `Setup Info`,
      component: (
        <TemplateInfo formData={{} as any} disableButtons={{ disableNextButton, setDisableNextButton, disabledMessage, setDisabledMessage }} />
      ),
    },
  ];

  const handleSubmit = async () => {
    if (activeStage === stage.length - 1) {
      setIsLoading(true);

      const payload = {
        title: form.title,
        description: form.description,
        seniorityLevel: form.seniorityLevel,
        difficulty: form.difficulty,
        duration: Number(form.duration),
        skips: Number(form.skips),
        locked: false,
        questionIds: Object.keys(selectedQuestionsID).filter((key) => selectedQuestionsID[key]),
        type: (type ?? 'test') === 'test' ? 'test' : 'interview',
      };

      try {
        const successMessage = isEditMode ? 'updated' : 'created';

        const response = isEditMode ? await Api.put(`templates/single/${id}`, payload) : await Api.post('templates/single', payload);

        dispatch(setNotifyMessage(`${type && type.charAt(0).toUpperCase() + type.slice(1)} ${successMessage} successfully!`));
        navigate(`/app/assessment-templates/${type}/list`);
      } catch (error: any) {
        dispatch(setErrorNotify(error.response?.data?.message));
      } finally {
        setIsLoading(false);
      }
    } else {
      setActiveStage((prev) => prev + 1);
    }
  };

  const handelGet = async () => {
    try {
      setIsLoading(true);
      const response = await Api.get<QuizType>(`templates/single/${id}`, {});
      console.log('templates/single/', response.data);

      // Handle different response structures
      const data = Array.isArray(response.data) ? response.data[0] : response.data;

      if (!data) {
        throw new Error('No data returned from API');
      }

      // Set form values with default fallbacks
      dispatch(
        initializeForm({
          title: data.title ?? '',
          description: data.description ?? '',
          seniorityLevel: data.seniorityLevel ?? '',
          difficulty: data.difficulty ?? '',
          duration: data.duration ?? '',
          skips: data.skips ?? '',
        })
      );

      // Set selected questions
      if (data.questionIds && Array.isArray(data.questionIds)) {
        const questionsMap: { [key: string]: boolean } = {};
        data.questionIds.forEach((questionId: string) => {
          questionsMap[questionId] = true;
        });
        setSelectedQuestionsID(questionsMap);
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error.response?.data?.message));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="space-y-4">
        <Jumbotron />

        <Stage
          stage={stage}
          selectedStage={{
            activeStage: activeStage,
          }}
        />

        <Form onSubmit={handleSubmit} ref={formRef} className="space-y-4">
          {stage[activeStage]?.component}

          <div className="flex justify-end gap-4">
            <Button
              className="w-[107px]"
              label={activeStage === 0 ? 'Cancel' : 'Back'}
              tertiary
              onClick={() => (activeStage === 0 ? navigate(`/app/assessment-templates/${type}/list`) : setActiveStage((prev) => prev - 1))}
            />
            <Button
              className="w-48"
              label={activeStage === stage.length - 1 ? (id ? 'Update' : 'Create') : 'Next'}
              onClick={() => (activeStage === stage.length - 1 ? handleSubmit() : setActiveStage((prev) => prev + 1))}
              disabled={disableNextButton}
              disabledMessage={disabledMessage}
              disabledMessageClassName="z-30"
            />
          </div>
        </Form>
      </div>

      {/* {Object.keys(selectedQuestionsID).filter((key) => selectedQuestionsID[key]).length > 0 && (
          <Draggable>
            <div
              onClick={() => Object.keys(selectedQuestionsID)?.filter((key) => selectedQuestionsID[key])?.length > 0 && setIsShowReviewDrawer(true)}
              className={`flex justify-between items-center gap-2 px-3 py-2.5 bg-[#7E3AF2] text-white text-center fixed bottom-60 right-6 z-[60] shadow-lg rounded-full ${
                Object.keys(selectedQuestionsID)?.filter((key) => selectedQuestionsID[key])?.length > 0 ? 'cursor-pointer' : 'cursor-not-allowed'
              } `}
            >
              <LucideCircleCheckBig width={'20'} />
              <span className="font-medium select-none">
                Selected Questions ({Object.keys(selectedQuestionsID).filter((key) => selectedQuestionsID[key]).length})
              </span>
              <Icon icon="material-symbols:arrow-forward-ios-rounded" width="18" />
            </div>
          </Draggable>
      )} */}

      {isShowReviewDrawer && (
        <ReviewDrawer
          onClose={() => setIsShowReviewDrawer(false)}
          selectedQuestionsID={selectedQuestionsID}
          setSelectedQuestionsID={setSelectedQuestionsID}
          anyQuestionHasEditMode={anyQuestionHasEditMode}
          setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
          canRemoveQuestion={true}
        />
      )}

      {isRandomizeAskAiVisible && (
        <RandomizeAskAiPage
          type="randomize"
          onClose={() => setRandomizeAskAiVisibilty(false)}
          questionsListData={{ list: [], selectedQuestionsID, setSelectedQuestionsID }}
          // type={isRandomizeAskAiVisible} // type is not boolean
          selectedQuestionsID={selectedQuestionsID}
          setSelectedQuestionsID={setSelectedQuestionsID}
          anyQuestionHasEditMode={anyQuestionHasEditMode}
          setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
        />
      )}
    </>
  );
};
