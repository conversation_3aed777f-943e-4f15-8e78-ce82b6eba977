import { createSlice } from '@reduxjs/toolkit';
import type { RootState } from '../../store';

interface viewOnlyState {
  isVisible: boolean;
}

const initialState: viewOnlyState = {
  isVisible: false,
};

const viewOnlySlice = createSlice({
  name: 'viewOnly',
  initialState,
  reducers: {
    setViewOnly: (state, { payload }) => {
      state.isVisible = payload;
    },
  },
});

export const viewOnlyState = (state: RootState) => state.viewOnly;
export const { setViewOnly } = viewOnlySlice.actions;
export default viewOnlySlice.reducer;
