export const tagsList: { [key: string]: string } = {
  // organization
  new: 'text-success-mid ',
  active: 'text-info-dark ',
  atRisk: 'text-danger-mid ',
  activeIcon: `<svg width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="4" cy="4" r="4" fill="#11ABE6"/>
  </svg>`,

  // Engagement
  high: 'bg-success-light text-success-dark ',
  medium: 'bg-warning-light text-warning-dark',
  low: 'bg-danger-light text-danger-dark',

  // Difficulty
  easy: 'bg-success-light text-success-dark',
  hard: 'bg-danger-light text-danger-dark',

  // Role
  Admin: 'bg-[#F1E9FE] text-[#562CE5]',
  superAdmin: 'bg-[#FFFCDF] text-[#BA8500]',
  hr: 'bg-[#FFEDD8] text-[#E9760F]',
  contentCreator: 'bg-[#E0F3FB] text-[#11ABE6]',

  // Transaction Status
  paid: 'bg-success-light text-success-dark',
  pending: 'bg-warning-light text-warning-dark',
  failed: 'bg-danger-light text-danger-dark',
  cancelled: 'bg-gray-100 border border-gray-300 text-gray-500',
  expired: 'bg-gray-200 border border-gray-400 text-gray-700',

  // Transaction Type
  regular: 'bg-info-light text-info-dark',
  coupon: 'bg- text-thePass-50 text-thePass-700',

  // Seniority

  FreshIcon: `<svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: 4, verticalAlign: 'middle' }}>
    <path
      d="M12.2041 7.83594C12.7019 7.85497 13.1301 7.9608 13.498 8.1416C13.9268 8.3523 14.2563 8.61602 14.5 8.92773C14.758 9.25786 14.9775 9.6644 15.1543 10.1543C15.3347 10.6545 15.4576 11.1478 15.5234 11.6348C15.5909 12.1344 15.625 12.669 15.625 13.2393C15.625 13.868 15.4139 14.3931 14.9834 14.8408C14.5543 15.2871 14.0563 15.4999 13.4629 15.5H2.78711C2.1937 15.4999 1.6957 15.2871 1.2666 14.8408C0.836121 14.3931 0.625 13.868 0.625 13.2393C0.625013 12.669 0.659072 12.1344 0.726562 11.6348C0.792351 11.1478 0.915251 10.6545 1.0957 10.1543C1.27245 9.6644 1.49198 9.25786 1.75 8.92773C1.99365 8.61602 2.32324 8.3523 2.75195 8.1416C3.11963 7.96092 3.54757 7.85507 4.04492 7.83594C5.20231 8.72529 6.56925 9.16699 8.125 9.16699C9.68047 9.16699 11.0468 8.72497 12.2041 7.83594ZM8.125 0.5C9.34361 0.5 10.3586 0.856134 11.1982 1.55566C12.0353 2.25322 12.4248 3.06102 12.4248 4C12.4248 4.93898 12.0353 5.74678 11.1982 6.44434C10.3586 7.14387 9.34361 7.5 8.125 7.5C6.90639 7.5 5.89138 7.14387 5.05176 6.44434C4.21469 5.74678 3.8252 4.93898 3.8252 4C3.8252 3.06102 4.21469 2.25322 5.05176 1.55566C5.89138 0.856134 6.90639 0.5 8.125 0.5Z"
      fill="#56CCF2"
      stroke="#56CCF2"
    />
  </svg>`,
  InternIcon: `
  <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M12.457 10.5293C14.7097 10.6999 16.375 12.327 16.375 14.2002V14.5C16.375 14.9912 15.896 15.4998 15.1611 15.5H2.58887C1.85399 15.4998 1.375 14.9912 1.375 14.5V14.2002C1.375 12.3273 3.03986 10.7003 5.29199 10.5293L8.5459 13.376L8.875 13.6641L9.2041 13.376L12.457 10.5293ZM8.26953 0.5625C8.66636 0.479704 9.08656 0.47877 9.4834 0.561523L16.2822 1.99902C16.285 1.9996 16.2874 2.00135 16.29 2.00195C16.2874 2.00256 16.285 2.0043 16.2822 2.00488L12.8438 2.73047L12.168 2.87207L12.5137 3.46973C12.7874 3.9433 12.9463 4.45827 12.9463 5.00098C12.946 6.87327 11.1885 8.5 8.875 8.5C6.5615 8.5 4.80399 6.87327 4.80371 5.00098C4.80371 4.45997 4.96505 3.94433 5.2373 3.46777L5.57812 2.87207L4.90625 2.73047L2.5498 2.2334L2.08887 2.13574V2.13379L1.68945 2.05176L1.46387 2.00488H1.46484C1.45993 2.00384 1.45565 2.00112 1.45117 2C1.45536 1.99896 1.45931 1.99609 1.46387 1.99512L8.26855 0.561523L8.26953 0.5625ZM2.49707 7.5H1.53809L2.01758 5.82324L2.49707 7.5ZM2.21387 4.80859C2.35014 4.88013 2.375 4.96294 2.375 5.00098C2.37482 5.03555 2.35391 5.11094 2.22852 5.18359L2.01758 5.30469L1.80762 5.18359C1.68202 5.1109 1.66131 5.03557 1.66113 5.00098C1.66113 4.96296 1.68519 4.88011 1.82129 4.80859L2.01758 4.70508L2.21387 4.80859Z"
      fill="#1879D3"
      stroke="#1879D3"
    />
  </svg>
`,
  JuniorIcon: `
  <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8.125 0.75C8.14414 0.75 8.26595 0.737713 8.57227 1.1377C8.87898 1.5382 9.22807 2.16109 9.74707 3.0918L10.0098 3.56152C10.2652 4.01982 10.489 4.45153 10.8672 4.73926C11.155 4.95817 11.4935 5.06402 11.8535 5.15039L12.2197 5.23438L12.7285 5.34863C13.7376 5.57712 14.4068 5.73105 14.8613 5.91016C15.1915 6.04029 15.2996 6.14243 15.3398 6.20801L15.3633 6.26172C15.3824 6.32357 15.4092 6.46684 15.1455 6.88574C14.8757 7.31432 14.417 7.8534 13.7305 8.65625L13.3838 9.06152V9.0625C13.0442 9.45986 12.7222 9.81403 12.5801 10.2705V10.2715C12.4745 10.6119 12.4817 10.965 12.5137 11.3447L12.5508 11.7344L12.6035 12.2764C12.707 13.3467 12.7749 14.0677 12.752 14.583C12.729 15.0973 12.6217 15.1853 12.5879 15.2109C12.566 15.2276 12.4792 15.3072 12.0205 15.1758C11.552 15.0415 10.9231 14.7543 9.97852 14.3193H9.97754L9.5 14.0996C9.03895 13.8867 8.60495 13.6699 8.125 13.6699C7.64438 13.6699 7.20982 13.8873 6.74805 14.1006L6.27246 14.3193C5.32744 14.7543 4.69817 15.0414 4.22949 15.1758C3.7702 15.3074 3.68334 15.227 3.66211 15.2109C3.62652 15.1838 3.52082 15.0923 3.49805 14.583C3.47502 14.0676 3.54301 13.3468 3.64648 12.2764L3.69922 11.7354V11.7344C3.7507 11.2025 3.81063 10.724 3.66797 10.2695C3.52602 9.8124 3.20371 9.45788 2.86523 9.0625L2.51953 8.65723L2.51855 8.65625C1.83195 7.85375 1.37421 7.31425 1.10449 6.88574C0.840009 6.46555 0.867458 6.32289 0.886719 6.26074C0.903247 6.20748 0.947885 6.08389 1.38867 5.91016C1.8434 5.73095 2.51326 5.57771 3.52246 5.34961V5.34863L4.03125 5.23438C4.5263 5.12247 4.9979 5.03116 5.38184 4.74023C5.76185 4.45217 5.98572 4.01925 6.24023 3.5625L6.50293 3.0918C7.02193 2.16109 7.37102 1.5382 7.67773 1.1377C7.98405 0.73771 8.10586 0.75 8.125 0.75Z"
      fill="#D9A607"
      stroke="#D69E2E"
      stroke-width="1.5"
    />
  </svg>
`,
  MidLevelIcon: `
  <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8.25 5.5C11.0114 5.5 13.25 7.73864 13.25 10.5C13.25 13.2614 11.0114 15.5 8.25 15.5C5.48863 15.5 3.25 13.2614 3.25 10.5C3.25 7.73864 5.48863 5.5 8.25 5.5ZM8.24902 6.87891C7.98604 6.87901 7.71513 7.00066 7.5459 7.24512L7.47949 7.35742L6.86328 8.60547L5.48535 8.80762C4.78126 8.90993 4.50047 9.7761 5.00977 10.2725L6.00684 11.2441L5.77148 12.6162V12.6172C5.65229 13.3148 6.38612 13.8555 7.01855 13.5205L7.01758 13.5195L8.25 12.873L9.48242 13.5205V13.5215C10.1115 13.8529 10.8491 13.3178 10.7295 12.6172V12.6162L10.4922 11.2441L11.4902 10.2725C11.968 9.80704 11.7511 9.01603 11.1416 8.83496L11.0146 8.80762L9.63574 8.60547L9.02051 7.35742V7.35645L8.9541 7.24414C8.80884 7.03555 8.58828 6.9166 8.3623 6.88672L8.24902 6.87891ZM0.750977 0.5H4.22461C4.31079 0.500054 4.3957 0.522019 4.4707 0.564453C4.54584 0.607005 4.6089 0.669114 4.65332 0.743164L6.45312 3.74219C5.51319 3.99113 4.65156 4.42917 3.91113 5.01562L0.750977 0.5ZM12.2754 0.5H15.749L12.5879 5.01465C11.8475 4.42856 10.9858 3.99098 10.0459 3.74219L11.8467 0.743164C11.9369 0.592709 12.0999 0.500086 12.2754 0.5Z"
      fill="#D95611"
      stroke="#D95611"
    />
  </svg>
`,
  SeniorIcon: `
  <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M8.875 0.5C8.87797 0.5 8.87908 0.50034 8.88086 0.500977C8.8838 0.502041 8.89544 0.50691 8.91602 0.522461C8.96185 0.557138 9.0315 0.629308 9.12793 0.772461C9.32509 1.06518 9.54803 1.51955 9.87598 2.19141L11.666 5.8584C11.8309 6.19676 11.9701 6.4837 12.1025 6.69922C12.2346 6.91406 12.4012 7.12873 12.6533 7.25586V7.25684C12.8862 7.37462 13.1477 7.41637 13.4062 7.37598H13.4082C13.6923 7.33041 13.9128 7.16826 14.0947 7C14.2763 6.83202 14.4818 6.59745 14.7217 6.32422C15.2071 5.77174 15.5456 5.38643 15.8096 5.13965C15.9403 5.01745 16.0317 4.94977 16.0938 4.91504C16.1105 4.90568 16.1231 4.9006 16.1309 4.89746C16.1621 4.90316 16.1927 4.91252 16.2207 4.92773C16.2553 4.94655 16.2858 4.9723 16.3105 5.00293V5.00391L16.3145 5.00879C16.3182 5.0151 16.3337 5.04315 16.3477 5.11328C16.3659 5.20564 16.3765 5.34047 16.375 5.53613C16.3719 5.93139 16.321 6.4793 16.248 7.25391L16.0645 9.20312C15.9122 10.8195 15.8009 11.9897 15.6143 12.8789C15.4524 13.6498 15.2435 14.1568 14.9404 14.5303L14.8047 14.6826C14.4349 15.0615 13.9622 15.2723 13.2002 15.3838C12.4176 15.4983 11.3964 15.5 9.96289 15.5H7.78711C6.35418 15.5 5.33341 15.4983 4.55078 15.3838C3.78897 15.2723 3.31592 15.0616 2.94531 14.6826C2.56732 14.296 2.32047 13.7595 2.13574 12.8789C1.94924 11.9898 1.83882 10.8196 1.68652 9.20312V9.20215L1.50195 7.25391C1.42903 6.47927 1.378 5.93132 1.375 5.53613C1.37352 5.34039 1.38495 5.20552 1.40332 5.11328C1.42142 5.02254 1.44065 5.00256 1.43555 5.00879L1.44043 5.00293C1.46518 4.97236 1.49571 4.94653 1.53027 4.92773C1.55801 4.91268 1.58821 4.90318 1.61914 4.89746C1.62688 4.90054 1.63919 4.90552 1.65625 4.91504C1.71829 4.9497 1.80968 5.01745 1.94043 5.13965C2.20446 5.38645 2.54374 5.77161 3.0293 6.32422C3.26921 6.59753 3.4746 6.83201 3.65625 7C3.83827 7.16831 4.05849 7.33047 4.34277 7.37598H4.34473C4.60262 7.41638 4.86625 7.3738 5.09863 7.25488C5.35029 7.12748 5.51603 6.91324 5.64746 6.69922C5.77967 6.48388 5.91974 6.19624 6.08496 5.8584L7.87402 2.19141C8.20192 1.51966 8.42494 1.0652 8.62207 0.772461C8.71861 0.629131 8.78911 0.557118 8.83496 0.522461C8.85474 0.507521 8.86572 0.502256 8.86914 0.500977C8.87103 0.500294 8.87217 0.500017 8.875 0.5ZM8.875 7.10059C8.52168 7.10073 8.2971 7.32942 8.16504 7.50195C8.0333 7.67421 7.89952 7.91793 7.75586 8.17578H7.75488L7.67676 8.31641C7.65474 8.35596 7.63787 8.38647 7.62305 8.41211C7.61793 8.42095 7.61237 8.42795 7.6084 8.43457C7.60257 8.43608 7.59617 8.43864 7.58887 8.44043C7.56125 8.44721 7.52896 8.45412 7.48633 8.46387L7.33496 8.49902C7.05705 8.56191 6.7896 8.62019 6.58887 8.69922C6.37848 8.78208 6.11393 8.93589 6.01172 9.26367C5.91099 9.58646 6.03596 9.86331 6.15527 10.0527C6.27133 10.2369 6.45223 10.4464 6.64258 10.6689L6.74609 10.7891V10.79C6.77563 10.8247 6.7982 10.8509 6.81738 10.874C6.82606 10.8845 6.83207 10.894 6.83789 10.9014C6.83731 10.9118 6.83818 10.9247 6.83691 10.9404C6.83449 10.9706 6.83043 11.0056 6.82617 11.0508L6.81055 11.2139C6.7817 11.5115 6.75307 11.7896 6.7627 12.0078C6.77113 12.1989 6.81187 12.4582 7.00488 12.6592L7.09766 12.7412C7.37917 12.9553 7.69246 12.9118 7.90625 12.8506C8.00998 12.8209 8.12427 12.7772 8.24512 12.7256L8.62305 12.5547L8.76367 12.4902C8.80409 12.4718 8.83491 12.4579 8.86133 12.4463C8.86627 12.4441 8.87076 12.4413 8.875 12.4395C8.8794 12.4413 8.88448 12.444 8.88965 12.4463C8.91587 12.4578 8.94635 12.471 8.98633 12.4893L9.12695 12.5547C9.38757 12.675 9.63741 12.7912 9.84473 12.8506C10.0582 12.9117 10.3702 12.9543 10.6514 12.7412L10.6523 12.7422C10.9268 12.5345 10.9787 12.2266 10.9883 12.0078C10.9978 11.7897 10.9692 11.5113 10.9404 11.2139V11.2129L10.9238 11.0498C10.9196 11.0051 10.9165 10.9703 10.9141 10.9404C10.9128 10.9246 10.9117 10.9118 10.9111 10.9014C10.9171 10.8939 10.9245 10.885 10.9336 10.874C10.9528 10.8509 10.9743 10.8239 11.0039 10.7891L11.1084 10.6689C11.2987 10.4465 11.4796 10.237 11.5957 10.0527C11.7153 9.86282 11.8395 9.58623 11.7393 9.26367V9.2627L11.6934 9.14844C11.5713 8.89822 11.3458 8.77151 11.1621 8.69922C10.9615 8.62034 10.6941 8.56195 10.416 8.49902L10.2637 8.46387H10.2627C10.221 8.45433 10.1893 8.44711 10.1621 8.44043C10.1542 8.43847 10.1468 8.43618 10.1406 8.43457C10.1369 8.42832 10.1326 8.42124 10.1279 8.41309L10.0742 8.31641L9.99512 8.17578C9.85134 7.91772 9.7168 7.67425 9.58496 7.50195C9.45286 7.32936 9.22852 7.10059 8.875 7.10059Z"
      fill="#B10000"
      stroke="#B10000"
    />
  </svg>
`,
  greenIcon: `
<svg width="6" height="7" viewBox="0 0 6 7" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="3" cy="3.5" r="3" fill="#009217"/>
</svg>
`,
  blueIcon: `
<svg width="6" height="7" viewBox="0 0 6 7" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="3" cy="3.5" r="3" fill="#11ABE6"/>
</svg>
`,
  redIcon: `
<svg width="6" height="7" viewBox="0 0 6 7" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="3" cy="3.5" r="3" fill="#F13E3E"/>
</svg>
`,

  // assessment status
  completed: 'bg-success-light text-success-dark',
  inProgress: 'bg-info-light text-info-dark',
  notStarted: 'bg-secondaryDisabled text-text-500',
  missedDeadline: 'bg-danger-light bg-danger-dark',

  // category
  category: 'bg-category-bg border border-category-border text-[#1B1F3B]',
  subCategory: 'bg-whtie border border-textStroke-border-100 text-[#1B1F3B] rounded-lg',
};
