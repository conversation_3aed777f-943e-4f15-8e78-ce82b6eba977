
import Button from "../../button";

type PlaceholderProps = {
  image?: string;
  title: string;
  subTitle?: string;
  buttonText?: string;
  onClickButton?: () => void;
};

export const Placeholder = ({ image, title, subTitle, buttonText, onClickButton }: PlaceholderProps) => {
  return (
    <div className={'w-full !max-w-[500px] mx-auto flex flex-col items-center justify-center  p-6 bg-white '}>
      {image && <img src={image} alt={title} className="mb-4 w-52 h-28 object-contain" />}
      <h1 className="font-bold text-center text-lg text-[#3A4458] mb-2">{title}</h1>
      {subTitle && <p className="text-base font-normal  text-center mb-7">{subTitle}</p>}
      {buttonText && <Button label={buttonText} colorType="primary" variant="lg" state="default" onClick={onClickButton} />}
    </div>
  );
};
