// Core
import { useEffect, useState } from 'react';
import { Api, useAppDispatch } from 'UI/src';
import { ChartsDonut, Icon, CustomIcon } from 'src';
import { setErrorNotify } from 'UI';

type blockDataTypes = {
  icon: JSX.Element;
  tempPlaceHolder: boolean;
  title: string;
  placeholderMessage: string;
  value: string | number;
  differencePercentage: number;
  customChild: JSX.Element;
};

export const OrganizationsCards = () => {
  // state
  const [oranizationEngagementData, setOranizationEngagementData] = useState([]);
  const dispatch = useAppDispatch();
  // Methods
  const handleGet = async () => {
    try {
      const organizationsResponse = await Api.get('superAdmin/organizations', {});
      const engagementResponse = await Api.get('superAdmin/engagement', {});
      console.log('superAdmin/organizations', organizationsResponse.data);
      console.log('superAdmin/engagement', engagementResponse.data);

      const formatedData = [
        {
          title: 'Organizations',
          value: organizationsResponse.data?.totalCount,
          icon: (
            <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.21"
                d="M0.5 16C0.5 7.16344 7.66344 0 16.5 0C25.3366 0 32.5 7.16344 32.5 16C32.5 24.8366 25.3366 32 16.5 32C7.66344 32 0.5 24.8366 0.5 16Z"
                fill="#F1E9FE"
              />
              <path
                opacity="0.587821"
                d="M20.7676 13.1572C21.9456 13.1575 22.9004 14.113 22.9004 15.291C22.9001 16.4688 21.9454 17.4236 20.7676 17.4238C19.5895 17.4238 18.6341 16.469 18.6338 15.291C18.6338 14.1128 19.5894 13.1572 20.7676 13.1572ZM14.3682 9.60156C15.939 9.60172 17.2119 10.8754 17.2119 12.4463C17.2118 14.017 15.9389 15.2899 14.3682 15.29C12.7973 15.29 11.5236 14.0171 11.5234 12.4463C11.5234 10.8753 12.7972 9.60156 14.3682 9.60156Z"
                fill="#743AF5"
              />
              <path
                d="M20.4858 18.1357C22.9077 18.1626 24.8848 19.3858 25.0337 21.9736C25.0396 22.0781 25.0333 22.4003 24.6479 22.4004H21.9067C21.9067 20.8004 21.3777 19.3239 20.4858 18.1357ZM14.3569 16.7109C17.7616 16.7109 20.5597 18.3418 20.768 21.8311C20.7763 21.9702 20.7675 22.3992 20.2339 22.3994H8.48679C8.30845 22.3994 7.9542 22.0149 7.96921 21.8301C8.24496 18.4365 10.9997 16.7111 14.3569 16.7109Z"
                fill="#743AF5"
              />
            </svg>
          ),
          customChild: (
            <div className="flex flex-wrap gap-5">
              <div className="flex items-center gap-1">
                <div className="w-[10px] h-[10px] bg-[#7CCCEF] rounded-full"></div>
                <span className="text-sm dark:text-white">
                  New
                  <span className="text-base ml-2 font-semibold">{organizationsResponse.data.lastNewCount}</span>
                </span>
              </div>
              {/* <div className="flex items-center gap-1">
                <div className="w-[10px] h-[10px] bg-[#52C93F] rounded-full"></div>
                <span className="text-sm dark:text-white">
                  Active
                  <span className="text-base ml-2 font-semibold">{organizationsResponse.data.active}</span>
                </span>
              </div> */}
              {/* <div className="flex items-center gap-1">
                <div className="w-[10px] h-[10px] bg-[#FF0000] rounded-full"></div>
                <span className="text-sm dark:text-white">
                  At Risk <span className="text-base ml-2 font-semibold">{organizationsResponse.data.lowEngagement}</span>
                </span>
              </div> */}
            </div>
          ),
        },
        {
          title: 'Engagement',
          value: `${engagementResponse.data.current}%`,
          differencePercentage: engagementResponse.data.current - engagementResponse.data.lastMonthScore,
          icon: (
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.21"
                d="M16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16C4.5101e-07 7.16344 7.16344 4.5098e-07 16 0Z"
                fill="#EEFFF1"
              />
              <path
                d="M10.1905 21.8053H22.635C23.0931 21.8053 23.4646 22.1768 23.4646 22.635C23.4646 23.0931 23.0931 23.4646 22.635 23.4646H9.36088C8.90269 23.4646 8.53125 23.0931 8.53125 22.635V9.36088C8.53125 8.90269 8.90269 8.53125 9.36088 8.53125C9.81907 8.53125 10.1905 8.90269 10.1905 9.36088V21.8053Z"
                fill="#009217"
              />
              <path
                opacity="0.5"
                d="M13.2864 18.2272C12.9731 18.5614 12.448 18.5784 12.1138 18.265C11.7795 17.9516 11.7626 17.4266 12.0759 17.0923L15.1871 13.7738C15.4901 13.4505 15.9938 13.4225 16.3308 13.7101L18.7863 15.8055L21.9856 11.7531C22.2695 11.3935 22.7912 11.3321 23.1508 11.616C23.5105 11.8999 23.5718 12.4216 23.2879 12.7812L19.5546 17.5101C19.263 17.8795 18.7229 17.9326 18.3649 17.6271L15.856 15.4863L13.2864 18.2272Z"
                fill="#009217"
              />
            </svg>
          ),
          customChild: (
            <>
              {/* <div className="space-y-1">
              <span className="text-[#656575] dark:text-[#d1d1d1] text-sm">
                last month usage
                <span className="text-base ml-2 font-semibold">{engagementResponse.data.lastMonthScore}%</span>
              </span> */}
              {/* <button className="px-2 py-1 bg-[#F4F5F7] dark:bg-[#374151] text-sm text-[#656575] dark:text-white w-fit h-fit rounded-sm">
                View Low Engagement Organizations
                </button> */}
              {/* </div> */}
              <div className="flex items-center gap-1">
                <div className="w-[10px] h-[10px] bg-success-mid rounded-full"></div>
                <span className=" flex gap-2 items-center  dark:text-white">
                  Active
                  <span className="thepassBone">-</span>
                </span>
              </div>
            </>
          ),
        },
        {
          tempPlaceHolder: true,
          placeholderMessage: 'No tickets created',
          title: 'Tickets',
          value: '100',
          differencePercentage: -2.5,
          icon: (
            <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.21"
                d="M0.5 16C0.5 7.16344 7.66344 0 16.5 0C25.3366 0 32.5 7.16344 32.5 16C32.5 24.8366 25.3366 32 16.5 32C7.66344 32 0.5 24.8366 0.5 16Z"
                fill="#E0F3FB"
              />
              <path
                d="M22.3761 18.4729L18.9786 21.8704C17.5836 23.2654 15.3186 23.2654 13.9161 21.8704L10.6236 18.5779C9.22862 17.1829 9.22862 14.9179 10.6236 13.5154L14.0286 10.1254C14.7411 9.4129 15.7236 9.0304 16.7286 9.0829L20.4786 9.2629C21.9786 9.3304 23.1711 10.5229 23.2461 12.0154L23.4261 15.7654C23.4711 16.7779 23.0886 17.7604 22.3761 18.4729Z"
                stroke="#11ABE6"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
              <path
                d="M18.375 16C17.3395 16 16.5 15.1605 16.5 14.125C16.5 13.0895 17.3395 12.25 18.375 12.25C19.4105 12.25 20.25 13.0895 20.25 14.125C20.25 15.1605 19.4105 16 18.375 16Z"
                stroke="#11ABE6"
                stroke-width="1.5"
                stroke-linecap="round"
              />
            </svg>
          ),
          customChild: (
            <div className="space-y-2">
              <div className="flex justify-between gap-5">
                <div className="flex items-center  gap-1">
                  <div className="w-[10px] h-[10px] bg-info-mid rounded-full"></div>
                  <span className=" flex gap-2 items-center  dark:text-white">
                    Opened
                    <span className="thepassBone">-</span>
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-[10px] h-[10px] bg-success-mid rounded-full"></div>
                  <span className=" flex gap-2 items-center  dark:text-white">
                    Resolved
                    <span className="thepassBone">-</span>
                  </span>
                </div>
              </div>
            </div>
          ),
        },
        {
          tempPlaceHolder: true,
          placeholderMessage: 'No activity recorded',
          title: 'Churn Risk',
          value: '40%',
          differencePercentage: -2.5,
          icon: (
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                opacity="0.3"
                d="M16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16C4.5101e-07 7.16344 7.16344 4.5098e-07 16 0Z"
                fill="#FFECE9"
              />
              <path
                d="M9.69531 22.4546V20.5499C9.69531 18.2355 11.5714 16.3594 13.8858 16.3594H18.0763M12.7429 22.4546V20.1689M19.2191 19.407V17.8832M19.2191 21.0832V20.9308M18.8382 11.407C18.8382 13.0901 17.4737 14.4546 15.7906 14.4546C14.1074 14.4546 12.7429 13.0901 12.7429 11.407C12.7429 9.72384 14.1074 8.35938 15.7906 8.35938C17.4737 8.35938 18.8382 9.72384 18.8382 11.407ZM16.026 22.4546H22.4123C22.6955 22.4546 22.8796 22.1566 22.753 21.9033L19.5598 15.517C19.4195 15.2363 19.0187 15.2363 18.8784 15.517L15.6853 21.9033C15.5586 22.1566 15.7428 22.4546 16.026 22.4546Z"
                stroke="#A80000"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          ),
          customChild: (
            <>
              {/* <div className="flex items-center gap-3">
                <p className="text-[#656575] text-sm dark:text-[#d1d1d1]">80% of churned orgs reported poor test quality</p>
                <button className="px-3 py-1 bg-[#F4F5F7] dark:bg-[#374151] text-sm text-[#656575] dark:text-white w-fit h-fit rounded-sm">
                  View
                </button>
              </div> */}
              <div className="flex items-center gap-1">
                <div className="w-[10px] h-[10px] bg-danger-mid rounded-full"></div>
                <span className=" flex gap-2 items-center  dark:text-white">
                  At Risk
                  <span className="thepassBone">-</span>
                </span>
              </div>
            </>
          ),
        },
        ,
      ];

      setOranizationEngagementData(formatedData as any);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  useEffect(() => {
    handleGet();
  }, []);

  return (
    <>
      {Array.isArray(oranizationEngagementData) &&
        oranizationEngagementData.map((blockData: blockDataTypes, index) => (
          <div className="h-full space-y-4 p-4 dark:border dark:border-gray-600 rounded-xl shadow-[0px_7px_10px_0px_#743AF51A]" key={index}>
            {/* Header */}
            <div className="flex justify-between items-start  ">
              <p className="text-text-500  thepassHtwo ">{blockData?.title}</p>
              {blockData?.icon}
            </div>
            {/* Middle Body */}
            {blockData.tempPlaceHolder ? (
              <div className={`flex flex-col text-center items-center justify-center`}>
                {blockData?.title === 'Tickets' ? (
                  <CustomIcon definedIcon="ticketsNotCreated" width="35" height="50" className="mx-auto" />
                ) : blockData?.title === 'Churn Risk' ? (
                  <CustomIcon definedIcon="churnRateNotFound" width="35" height="50" className="mx-auto" />
                ) : (
                  <Icon icon="iconoir:warning-circle" className="dark:text-gray-500 text-gray-400 mx-auto" width="50" />
                )}
                <p className={`text-gray-400 mt-2`}>{blockData?.placeholderMessage}</p>
              </div>
            ) : (
              //   <div className={`flex flex-col text-center `}>
              //   <Icon icon="iconoir:warning-circle" className="dark:text-gray-500 text-gray-400" width="50" />
              //   <p className={`text-gray-400 mt-2`}>{blockData?.placeholderMessage}</p>
              // </div>

              <>
                <div className="flex flex-wrap gap-2 justify-between items-center">
                  <p className="text-3xl font-bold dark:text-white">{blockData.value}</p>
                  {blockData.differencePercentage !== undefined && !isNaN(blockData.differencePercentage) && (
                    <div className="flex gap-3">
                      <p className={`${blockData.differencePercentage > 0 ? 'text-[#52C93F]' : 'text-[#FF0000]'}`}>
                        {blockData.differencePercentage > 0 ? <span>&uarr;</span> : <span>&darr;</span>} {Math.abs(blockData.differencePercentage)}%
                      </p>
                      <p className="text-[#606060] dark:text-[#d1d1d1]">past week</p>
                    </div>
                  )}
                </div>
                {/* Custom Child */}
                <div>{blockData.customChild}</div>
              </>
            )}
          </div>
        ))}
    </>
  );
};
