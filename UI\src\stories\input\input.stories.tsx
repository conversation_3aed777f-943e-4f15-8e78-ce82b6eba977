import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';

import { TextInput } from '../../components/form/text';

const meta: Meta<typeof TextInput> = {
  title: 'Components/Input',
  component: TextInput,
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      options: ['text', 'password', 'email', 'number', 'search', 'date'],
    },
  },
};

export default meta;
type Story = StoryObj<typeof TextInput>;

// Helper story to support controlled input for preview
const Template = (args: any) => {
  const [value, setValue] = useState(args.value || '');
  return <TextInput {...args} value={value} onChange={(e) => setValue(e.target.value)} />;
};

export const Default: Story = {
  render: Template,
  args: {
    label: 'Name',
    placeholder: 'Enter your name',
    type: 'text',
    disabled: false,
  },
};

export const Hover: Story = {
  render: Template,
  args: {
    label: 'Hover Input',
  },
};

export const Focus: Story = {
  render: Template,
  args: {
    label: 'Focus Input',
  },
};

export const Invalid: Story = {
  render: Template,
  args: {
    label: 'Invalid Input',
  },
};

export const Disabled: Story = {
  render: Template,
  args: {
    label: 'Disabled Input',
    disabled: true,
  },
};
