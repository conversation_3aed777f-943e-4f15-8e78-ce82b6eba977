import { SelectQuestions } from '../components/select-quizzes';
import { Di<PERSON>, But<PERSON> } from 'UI';
import { QuestionsListItem } from '../components/questions-list-item';
import { useState } from 'react';
import type { questionsListRes } from 'UI';

import { QuestionType, setErrorNotify } from 'UI';
import { Form, useAppDispatch, Api } from 'UI/src';

interface ManualSelectForm {
  category: string | number;
  questionIds: string[];
  [key: string]: unknown;
}

interface ManualSelectProps {
  onClose: () => void;
  questions: questionsListRes[];
  handleSearch: (value: string, selectedIds: string[]) => void;
  form: ManualSelectForm;
  setFormValue: (form: ManualSelectForm) => void;
  handleGetQuestionData: (id: string) => void;
}

export const ManualSelect = ({ onClose, questions, handleSearch, form, setFormValue, handleGetQuestionData }: ManualSelectProps) => {
  const dispatch = useAppDispatch();
  const [questionManualSelectDatabase, setQuestionManualSelectDatabase] = useState<questionsListRes[]>([]);
  const [questionManualSelectIds, setQuestionManualSelectIds] = useState<string[]>([]);

  const handleGetQuestionDataManualSelect = async (id: string) => {
    try {
      const response = await Api.get<QuestionType>(`questions/single/${id}`, {});
      console.log(`questions/single/${id}`, response.data);
      setQuestionManualSelectDatabase((prev: any) => [...prev, response.data]);
      setQuestionManualSelectIds((prev) => [...prev, id]);
    } catch (error) {
      dispatch(setErrorNotify(error));
    }
  };

  const onRemove = (id: string) => {
    setQuestionManualSelectDatabase((prev) => prev.filter((target) => id !== target._id));
    setQuestionManualSelectIds((prev) => prev.filter((target) => id !== target));
  };

  const onSubmit = () => {
    setFormValue({
      ...form,
      questionIds: questionManualSelectIds,
    });
    handleSelectingQuestion(questionManualSelectIds);
    onClose();
  };

  const handleSelectingQuestion = (questionIds: string[]) => {
    setFormValue({
      ...form,
      questionIds: [...form.questionIds, ...questionIds],
    });
    questionIds.map((question) => handleGetQuestionData(question));
  };

  return (
    <Dialog isOpen size="3xl" title="Manual Select Questions" onClose={onClose}>
      <div className="min-h-64">
        <Form className="h-full" onSubmit={onSubmit}>
          <div>
            <p className="dark:text-white mt-1 mb-3">Select Question</p>
            <SelectQuestions
              filterOnly
              name="questionIds"
              lookup={questions}
              optionLabelKey={'title'}
              optionValueKey="_id"
              onSearch={(value: string) => handleSearch(value, questionManualSelectIds)}
              multiSelect={true}
              value={form.category === 0 ? null : form.questionIds}
              onChange={(questionId: string) => handleGetQuestionDataManualSelect(questionId)}
              disabled={form.category === 0}
              validators={[]}
            />

            <div className="mt-5 lg:border lg:border-gray-200 lg:dark:border-gray-600 rounded-lg text-sm font-medium text-gray-900 dark:text-white">
              <div className="hidden lg:grid grid-cols-12 p-4 bg-white border border-gray-200 rounded-t-lg dark:bg-gray-700 dark:border-gray-600">
                <p className="col-span-4">Questions Name</p>
                <p className="col-span-2">Subcategory</p>
                <p className="col-span-3">Topic</p>
                <p className="col-span-2">Difficulty</p>
                <p className="col-span-1">Actions</p>
              </div>
              <div className="max-h-[30vh] overflow-y-auto">
                {questionManualSelectDatabase.length ? (
                  questionManualSelectDatabase.map((data, index) => (
                    <QuestionsListItem key={index} data={data} showEditIcon={false} onRemove={onRemove} />
                  ))
                ) : (
                  <div className="text-gray-500 dark:text-gray-400 p-4 hover:dark:bg-gray-700 my-4">
                    <p className="flex justify-center items-center dark:text-white">No selected questions yet</p>
                  </div>
                )}
              </div>
            </div>
          </div>
          <Button
            colorType="primary"
            className="flex w-full items-end mt-14 "
            type="submit"
            label={questionManualSelectIds.length ? 'Select' : 'Cancel'}
          />
        </Form>
      </div>
    </Dialog>
  );
};
