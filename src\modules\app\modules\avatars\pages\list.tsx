import { useState } from 'react';
import { Jumbotron, ScrollableTabs } from 'src';
import { UploadDialog } from './../../organizations/components/list/avatar/upload-dialog';
import { Button } from 'UI';

import { AiAvatarModels, PlanFeatures, RootState, setFieldValue, useAppDispatch, useAppSelector, UserData } from 'UI/src';
import { useFormik } from 'formik';
import adamBody from 'src/images/models/adam-body.png';

type AvatarCardProps = {
  name: string;
  role: string;
  avatar: string;
  submittedAt?: string;
  by?: string;
  onClick?: () => void;
};

const AvatarCard = ({ name, role, avatar, submittedAt, by, onClick }: AvatarCardProps) => (
  <div className="p-4 rounded-2xl shadow-[0px_7px_10px_0px_#743AF51A] " onClick={onClick}>
    <div className="flex items-center gap-3">
      <img src={avatar} alt={name} className="w-14 h-14 rounded-full object-cover" />
      <div className="space-y-0.5">
        <p className="text-[15px] font-semibold text-[#1B1F3B] dark:text-white">{name}</p>
        <p className="text-sm text-[#4E5E82]">{role}</p>
      </div>
    </div>
    {submittedAt && by && (
      <div className="mt-3 space-y-1">
        <p className="text-xs text-[#868D9C]">Submitted at {submittedAt}</p>
        <p className="text-xs text-gray-500">By {by}</p>
      </div>
    )}
  </div>
);

export const AvatarComponent = () => {
  // State
  const [uploadDiologVisible, setUploadDiologVisible] = useState(false);

  const dispatch = useAppDispatch();
  const formik = useFormik({
    initialValues: {
      avatarName: AiAvatarModels[0]?.value || '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // System Ready Avatars (first 6 from models)
  const systemReady = (AiAvatarModels || []).slice(0, 6).map((model: { value: string; iconPathOut: string }) => ({
    id: model.value,
    name: model.value,
    role: 'HR Representative',
    avatar: `/assets/models/${model.iconPathOut}`,
  }));

  // Your Own Avatars (demo data 3 each)
  const [ownTab, setOwnTab] = useState(0);
  const ownPending = [0, 1, 2].map((i) => ({
    id: `p-${i}`,
    name: 'Ahmed',
    role: 'HR Representative',
    submittedAt: '20 April 2025, 5:30PM',
    by: 'Mona Elghazaly',
    avatar: adamBody as unknown as string,
  }));
  const ownApproved = [0, 1, 2].map((i) => ({
    id: `a-${i}`,
    name: 'Ahmed',
    role: 'HR Representative',
    submittedAt: '20 April 2025, 5:30PM',
    by: 'Mona Elghazaly',
    avatar: adamBody as unknown as string,
  }));
  const ownRejected = [0, 1, 2].map((i) => ({
    id: `r-${i}`,
    name: 'Ahmed',
    role: 'HR Representative',
    submittedAt: '20 April 2025, 5:30PM',
    by: 'Mona Elghazaly',
    avatar: adamBody as unknown as string,
  }));

  const ownTabs = [
    { title: 'Pending', component: null },
    { title: 'Approved', component: null },
    { title: 'Rejected', component: null },
  ];

  return (
    <div className="space-y-4">
      <Jumbotron />
      <div className="p-4 border border-[#F4F4F4] rounded-lg space-y-6">
        <div className="flex flex-wrap xssm:flex-nowrap xssm:gap-0 gap-5 items-center justify-between">
          <div className="flex gap-2 items-center">
            <p className="font-medium text-xl dark:text-white">System Ready Avatars</p>
            <div className="py-[1px] px-2 text-white bg-[#8D5BF8] rounded-lg text-md">{systemReady.length}</div>
          </div>
          <Button
            label="Request Avatar"
            icon={
              <svg xmlns="http://www.w3.org/2000/svg" width={9} height={9} viewBox="0 0 1024 1024">
                <path
                  fill="currentColor"
                  d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64z"
                ></path>
              </svg>
            }
            variant="sm"
            colorType="primary"
            onClick={() => setUploadDiologVisible(true)}
          />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {systemReady.map((item) => (
            <AvatarCard
              key={item.id}
              name={item.name}
              role={item.role}
              avatar={item.avatar}
              onClick={() => dispatch(setFieldValue({ path: 'avatarName', value: item.id }))}
            />
          ))}
        </div>

        <div className="space-y-4 pt-4">
          <div className="flex items-center gap-2">
            <p className="font-medium text-xl dark:text-white">Your Own Avatars</p>
            <div className="py-[1px] px-2 text-white bg-[#8D5BF8] rounded-lg text-md">
              {ownTab === 0 ? ownPending.length : ownTab === 1 ? ownApproved.length : ownRejected.length}
            </div>
          </div>

          <ScrollableTabs
            data={ownTabs}
            selectedTab={{
              activeTab: ownTab,
              setActiveTab: setOwnTab,
            }}
          />

          {ownTab === 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {ownPending.map((card) => (
                <AvatarCard key={card.id} name={card.name} role={card.role} avatar={card.avatar} submittedAt={card.submittedAt} by={card.by} />
              ))}
            </div>
          )}
          {ownTab === 1 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {ownApproved.map((card) => (
                <AvatarCard key={card.id} name={card.name} role={card.role} avatar={card.avatar} submittedAt={card.submittedAt} by={card.by} />
              ))}
            </div>
          )}
          {ownTab === 2 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {ownRejected.map((card) => (
                <AvatarCard key={card.id} name={card.name} role={card.role} avatar={card.avatar} submittedAt={card.submittedAt} by={card.by} />
              ))}
            </div>
          )}
        </div>
      </div>
      {uploadDiologVisible && <UploadDialog onClose={() => setUploadDiologVisible(false)} orgId={(userData as any)?.organizationId} />}
    </div>
  );
};
