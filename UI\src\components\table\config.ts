
import tableCell from './components/cells';
import { PlanFeatures, UserPermissions } from '../../constants/enums'

/* eslint-disable @typescript-eslint/no-explicit-any */
// Column definition (headless, no DOM assumptions)
export type Variant = 'text' | 'number' | 'currency' | 'percent' | 'status' | 'avatar' | 'actions';
export type CellType = keyof typeof tableCell;

// ColumnDef.ts
export type ColumnDef<T> = {
  id?: string;
  header: React.ReactNode | ((col: ColumnDef<T>) => React.ReactNode);
  accessorKey?: keyof T;
  accessorFn?: (row: T) => unknown;
  meta: {
    variant?: Variant;
    align?: 'left' | 'right' | 'center';
    props?: (row: T) => Record<string, unknown>;
  };
  cell: (ctx: { row: T; value: unknown; rowIndex: number }) => React.ReactNode;
  width?: string | number;
  className?: string;
  hideOnMobile?: boolean;
  labelOnCard?: string;
};


export type TableState = {
  page?: number; // 1-based
  size?: number; // page size
  sort?: { id: string; desc?: boolean } | null;
};

export type DataTableProps<T> = {
  data: T[];
  columns: ColumnDef<T>[];
  loading?: boolean;
  addButtonLabel?: string;
  // Toolbar/header
  sectionTitle?: string;
  sectionBadgeTitle?: string | number;
  search?: { value: string; placeholder?: string; update: (value: string) => void };
  filters?: any[];
  onClickAdd?: () => void;
  filterFeedData?: any[];
  setFilters: (filters: any) => void;
  // Controlled state (server-side friendly)
  state?: TableState;
  onStateChange?: (patch: Partial<TableState>) => void;
  // Pagination controls
  pagination?: {
    pages: number; // total pages
    currentPage: number; // 1-based
    onPageChange: (page: number) => void;
    onNext?: () => void;
    onPrevious?: () => void;
  };
  // Row identification
  getRowId?: (row: T, index: number) => string | number;
  // Mobile row card override
  renderRowCard?: (row: T, index: number) => React.ReactNode;
  // ClassNames hooks
  tableClassName?: string;
  placeholder?: {
    title: string;
    subTitle?: string;
    image?: string;
  }
  addButtonFeature?: PlanFeatures[] | PlanFeatures;
  addButtonPermission?: UserPermissions[] | UserPermissions;
  addButtonPermissionOperator?: "every" | "some";
};