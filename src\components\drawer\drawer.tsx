// React
import { ReactNode, useEffect, useRef } from 'react';

// Date formate
import { format, isValid } from 'date-fns';

// UI
import { Icon, Button, EnumText, Textarea, Radio, Checkbox, TextInput, CustomIcon, Select } from 'src';

import {
  Api,
  StaticData,
  useScreenSize,
  useValidate,
  Form,
  initializeForm,
  setFieldValue,
  QuestionTypeEnum,
  QuestionDifficulty,
  ToggleSwitch,
} from 'UI/src';

import {
  DrawerSingleViewProps,
  DrawerHeaderProps,
  DrawerBodyProps,
  DrawerFooterProps,
  DrawerFooterButtonProps,
  DrawerContentProps,
  DrawerBodyTestDetailsProps,
  DrawerBodyQuestionOfScreeningProps,
  DrawerComponent,
  DatePicker,
  HeaderSectionProps,
  FilterSectionProps,
  QuestionOfTestProps,
  QuestionListItem,
  Row,
} from './drawer.type';

// Flowbite
import { Pagination, Checkbox as FlowbiteCheckbox, Tooltip, TextInput as TextInputFlowbite } from 'flowbite-react';

// React Suite
import 'rsuite/dist/rsuite-no-reset.min.css';
import { DateRangePicker } from 'rsuite';

// React icons
import { LiaCircle } from 'react-icons/lia';
import { LuDiamond } from 'react-icons/lu';
import { PiCube } from 'react-icons/pi';
import { BiPolygon } from 'react-icons/bi';

import { hideConfirm, QuestionType, RootState, showConfirm, useAppDispatch, useAppSelector } from 'UI/src';
import { toast } from 'react-toastify';
import { setErrorNotify, setNotifyMessage } from 'UI';
import {
  setAnswerView1Expanded,
  setAnswerView1Truncated,
  setAnswerView2Expanded,
  setAnswerView2Truncated,
  setDatePicker1ShowExceededTime,
  setDatePicker2ShowExceededTime,
  setFilterSectionAtStart,
  setFilterSectionAtEnd,
  setQuestionOfTestExpanded,
  setQuestionOfTestOverflow,
  setQuestionOfTestEditMode,
  setQuestionOfTestShowFullAnswers,
  setQuestionOfTestChecked,
} from 'UI/src/slices/drawer/drawer.slice';
import { useFormik } from 'formik';

// const { isRequired, isNotSpaces } = useValidate();
export const Drawer = ({ children, className, split, onClose, ...drawer }: DrawerComponent) => {
  const { isRequired, isNotSpaces } = useValidate();
  return (
    //  FIXME: i used this huge z-index because eyeICon in table appears in drawer
    <div className="fixed z-[1000000] top-0 right-0 bottom-0 left-0">
      <div
        //  FIXME: i used this huge z-index because eyeICon in table appears in drawer
        className={`h-screen w-screen md:w-[768px] lg:w-[900px] xl:w-[1024px] shadow-xl bg-white dark:bg-darkBackgroundCard text-black dark:text-grayTextOnDarkMood absolute z-[1000001] right-0 ${className}`}
        {...drawer}
      >
        {children as ReactNode}
      </div>
      {/* Overlay */}
      // FIXME: i used this huge z-index because eyeICon in table appears in drawer
      <div className="fixed z-[1000000] w-full h-screen bg-black/50 top-0 left-0" onClick={onClose} />
    </div>
  );
};

// Single View nested - main component

Drawer.SingleView = ({ children, className, ...drawerSingleView }: DrawerSingleViewProps) => {
  return (
    <div className={`flex flex-col h-full px-6 py-4 space-y-4 ${className}`} {...drawerSingleView}>
      {children as ReactNode}
    </div>
  );
};

// Header nested component
Drawer.Header = ({ children, headerChild, className, headerLabel, headerSubLabel, description, onClose, ...drawerHeader }: DrawerHeaderProps) => {
  return (
    <div className={`space-y-2 ${className}`} {...drawerHeader}>
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3 h-fit">
          <div className="flex flex-wrap justify-between items-center gap-2">
            <p className="text-[20px] sm:text-[22px] m-0 font-semibold dark:text-white">
              {headerLabel as ReactNode} {headerSubLabel && 'to: '}
            </p>
            {headerSubLabel && <p className="text-lg font-medium text-[#5C5C5C] dark:text-white">{headerSubLabel}</p>}
          </div>

          {headerChild as ReactNode}
        </div>

        {onClose && (
          <div className="cursor-pointer mr-2" onClick={onClose}>
            <Icon icon="mdi:close" className="text-2xl text-gray-500 hover:text-gray-700 transition-colors dark:text-white" />
          </div>
        )}
      </div>

      {description && <p className="text-sm text-[#595959] dark:text-white">{description}</p>}

      {children as ReactNode}
    </div>
  );
};

// ===================================================
// Drawers shared in module

// Body nested - main component
// Refactor Drawer.Body to support nested properties

type DrawerBodyComponent = React.FC<DrawerBodyProps> & {
  TestDetails: React.FC<DrawerBodyTestDetailsProps>;
  QuestionOfScreening: React.FC<DrawerBodyQuestionOfScreeningProps>;
  DatePicker: React.FC<DatePicker>;
};

const DrawerBody: DrawerBodyComponent = ({ children, className, ...drawerBody }) => {
  return (
    <div className={`overflow-hidden grow ${className}`} {...drawerBody}>
      {children as ReactNode}
    </div>
  );
};

DrawerBody.TestDetails = ({
  children,
  className,
  label,
  enumText,
  duration,
  totalQuestions,
  ...drawerBodyTestDetails
}: DrawerBodyTestDetailsProps) => {
  return (
    <div className={`space-y-2 ${className}`} {...drawerBodyTestDetails}>
      <p className="text-base font-semibold">
        {enumText} {label}
      </p>
      <div className="flex gap-2 text-[#6B7280] text-sm font-medium items-center">
        <Icon icon="tabler:clock-hour-3-filled" width="18" />
        <span>{duration} mins</span>
        <span>•</span>
        <CustomIcon definedIcon="questions" className="text-[#8A43F9]" width="15" height="18" />
        <span>
          {totalQuestions} {(totalQuestions ?? 0) > 1 ? 'Questions' : 'Question'}
        </span>
      </div>
      {children as ReactNode}
    </div>
  );
};

DrawerBody.QuestionOfScreening = ({
  children,
  className,
  index,
  row,
  setQuestionsListData,
  isExpandAllAnswers,
  canEditQuestion,
  handleSaveTest,
  ...drawerBodyQuestion
}: DrawerBodyQuestionOfScreeningProps) => {
  // Hook

  // Redux
  const dispatch = useAppDispatch();
  const { isExpanded, isTruncated } = useAppSelector((state: RootState) => state.drawer.answerView1);

  // Methods
  const AnswerView = ({ answer }: { answer: number | string | Record<string, any> }) => {
    // Ref
    const questionRef = useRef<HTMLDivElement>(null);

    // On mount
    useEffect(() => {
      if (questionRef.current) {
        dispatch(setAnswerView1Truncated(questionRef.current.scrollHeight > questionRef.current.clientHeight));
      }
    }, [dispatch]);

    return (
      <div className="text-[16px] font-medium text-[#5C5C5C] dark:text-gray-400 leading-6">
        <div ref={questionRef} className={!isExpandAllAnswers && !isExpanded[row?.id] ? 'line-clamp-2' : ''}>
          {typeof answer === 'number' ? answer : Object.keys(answer)[0]}
        </div>
        {!isExpandAllAnswers && isTruncated && (
          <button
            onClick={() => dispatch(setAnswerView1Expanded({ ...isExpanded, [row?.id]: !isExpanded[row?.id] }))}
            className="text-[#667085] font-semibold text-sm hover:underline"
          >
            {isExpanded[row?.id] ? 'Read less' : 'Read more...'}
          </button>
        )}
      </div>
    );
  };

  const toggleEditSave = () => {
    if (row?.isEditMode) {
      if (row?.pendingTitle) {
        handleSaveTest?.(row?._id);
      } else {
        toast.error('Question is empty');
      }
    } else {
      setQuestionsListData?.((prev: QuestionListItem[]) =>
        prev?.map((question: QuestionListItem) =>
          question?._id === row?._id ? { ...question, pendingTitle: row?.title, isEditMode: true } : question
        )
      );
    }
  };

  return (
    <div className={className} {...drawerBodyQuestion}>
      <div className="p-4 border dark:border-gray-700 rounded-lg space-y-2">
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="flex flex-col sm:flex-row grow items-start gap-2">
            {/* Question view or TextInput edit view */}
            {row?.isEditMode ? (
              <TextInputFlowbite
                className="w-full"
                value={row?.pendingTitle}
                onChange={(value: any) =>
                  setQuestionsListData?.((prev: QuestionListItem[]) =>
                    prev?.map((question: QuestionListItem) =>
                      question?._id === row?._id ? { ...question, pendingTitle: value?.currentTarget?.value } : question
                    )
                  )
                }
              />
            ) : (
              <div className="flex gap-1 text-sm">
                <span className="text-[14px] text-[#101828] dark:text-white font-medium">Q{++index}.</span>
                <span className="text-[14px] text-[#101828] dark:text-white font-medium">{String(row?.question || row?.title || '')}</span>
              </div>
            )}

            {/* Unanswered flag */}
            {row?.answer === '' && (
              <div className="flex items-center gap-2 text-[#798296]">
                <span className="hidden md:block">•</span>
                <Icon width={'24'} icon="mingcute:close-square-fill" className="text-[#E9A8A2]" />
                <span className="text-[#667085] text-sm font-medium">Unanswered</span>
              </div>
            )}
          </div>

          {canEditQuestion && (
            <div className="flex flex-wrap gap-2 pt-2 sm:pt-0">
              {row?.isEditMode && (
                <Button
                  size="xs"
                  icon="ic:twotone-close"
                  onClick={() =>
                    setQuestionsListData?.((prev: QuestionListItem[]) =>
                      prev?.map((question: QuestionListItem) =>
                        question?._id === row?._id ? { ...question, pendingTitle: '', isEditMode: false } : question
                      )
                    )
                  }
                  tertiary
                  className="cursor-pointer self-end sm:self-start"
                />
              )}
              <Button
                iconWidth={row?.isEditMode ? '20' : '18'}
                size="xs"
                icon={row?.isEditMode ? 'fluent:save-16-regular' : undefined}
                customIcon={!row?.isEditMode ? { definedIcon: 'edit' } : undefined}
                onClick={toggleEditSave}
                className="cursor-pointer self-end sm:self-start"
                tertiary={row?.isEditMode ? false : true}
              />
            </div>
          )}
          {row?.answer && (
            <div className="text-[#667085] text-sm font-medium text-nowrap">
              Time{' '}
              {row?.timeTaken &&
              ['hours', 'minutes', 'seconds'].every(
                (unit) => row.timeTaken[unit as keyof typeof row.timeTaken] != null && !isNaN(row.timeTaken[unit as keyof typeof row.timeTaken])
              )
                ? `${row.timeTaken.hours * 3600 + row.timeTaken.minutes * 60 + row.timeTaken.seconds}s`
                : '—'}
            </div>
          )}
        </div>

        {row?.answer && <AnswerView answer={row?.answer} />}
      </div>
      {children as ReactNode}
    </div>
  );
};

DrawerBody.DatePicker = ({
  children,
  className,
  startDate,
  dueDate,
  extraTime,
  setExtraTime,
  setTimeSettingsVisible,
  type,
  ...drawerBodyDatePicker
}: DatePicker) => {
  // Redux
  const dispatch = useAppDispatch();
  const { showExceededTime } = useAppSelector((state: RootState) => state.drawer.datePicker1);

  const formatDate = (customDate: Date | string | number | undefined): string => {
    const date = new Date(customDate || Date.now());
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, "MMMM dd, yyyy, 'at' hh:mm a");
  };

  return (
    <div className={`space-y-3 ${className}`} {...drawerBodyDatePicker}>
      <div className="flex flex-wrap justify-between items-center gap-y-2 bg-[#F9FAFB] dark:bg-darkGrayBackground border border-[#F2F2F2] p-3 rounded-md">
        <div className="space-y-2">
          <p className="text-[#6B7280] dark:text-gray-300 text-[15px] font-medium">
            <span className="capitalize">{type}</span> link is valid from:
          </p>
          <p className="flex items-center flex-wrap gap-2 text-[#3C3D3E] dark:text-white text-[15px] font-medium">
            <span>{formatDate(startDate)}</span>
            <Icon icon="line-md:arrow-right" width="16" />
            <span>{formatDate(dueDate)}</span>
          </p>
          <div className="flex items-center gap-2">
            <CustomIcon definedIcon="infoItalic" />
            <p className="text-[#6B7280] dark:text-gray-300 text-sm font-medium">The applicant will receive the {type} link in UTC time via email</p>
          </div>
        </div>
        <Button
          tertiary
          label="Customize Availability"
          customIcon={{ definedIcon: 'customize' }}
          size="sm"
          onClick={() => setTimeSettingsVisible?.()}
        />
      </div>
      {type !== 'interview' && (
        <div className="pb-3 space-y-2 items-center gap-4 bg-[#F9FAFB] dark:bg-darkGrayBackground border border-[#F2F2F2] p-3 rounded-md">
          <div className="flex items-center gap-2">
            <p className="text-[#2D3748] dark:text-gray-300 text-[15px] font-medium">Allow extra time after {type} duration ends ?</p>
            <Tooltip content="" placement="top">
              <Icon icon="solar:info-circle-outline" className="text-gray-600 dark:text-gray-200" width="18" />
            </Tooltip>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-3 w-fit">
              {showExceededTime && (
                <>
                  <label htmlFor="extend" className="text-[#667085] text-sm">
                    Extend by:
                  </label>
                  <Form onSubmit={() => {}}>
                    <Select
                      name="name"
                      placeholder="Exceeded time"
                      value={extraTime}
                      onChange={setExtraTime}
                      // validators={[isRequired()]}
                      type="number"
                      lookup={[
                        { value: 5, label: '5' },
                        { value: 10, label: '10' },
                        { value: 15, label: '15' },
                        { value: 20, label: '20' },
                      ]}
                      dropIcon={true}
                    />
                  </Form>
                </>
              )}
            </div>

            <ToggleSwitch
              checked={showExceededTime}
              onChange={(event) => {
                dispatch(setDatePicker1ShowExceededTime(!showExceededTime));
                if (!event) setExtraTime?.();
              }}
              color="purple"
              sizing="sm"
            />
          </div>
        </div>
      )}
      {children as ReactNode}
    </div>
  );
};

Drawer.Body = DrawerBody;

// Footer nested - main component
// Refactor Drawer.Footer to support nested Button

type DrawerFooterComponent = React.FC<DrawerFooterProps> & {
  Button: React.FC<DrawerFooterButtonProps>;
};

const DrawerFooter: DrawerFooterComponent = ({ children, className, isPaginationActive, paginationData, ...drawerFooter }) => {
  const screen = useScreenSize();

  return (
    <div className={className} {...drawerFooter}>
      {/* Pagination */}
      {isPaginationActive && (
        <nav className="flex justify-center items-center px-4 my-1" aria-label="Table navigation">
          {(paginationData?.count ?? 0) > (paginationData?.size ?? 0) && (
            <Pagination
              theme={StaticData.paginationTheme}
              currentPage={paginationData?.currentPage ?? 1}
              onPageChange={(page) => paginationData?.onPageChange({ page })}
              showIcons
              totalPages={paginationData?.pagesCount ?? 1}
              layout={screen.gt.sm() ? 'pagination' : 'navigation'}
              previousLabel="Previous"
              nextLabel="Next"
            />
          )}
        </nav>
      )}
      <div className="flex gap-4 px-4 py-2">{children as ReactNode}</div>
    </div>
  );
};

DrawerFooter.Button = ({ className, label, mainButton, ...drawerFooterButton }) => {
  return label && <Button label={label} className={`text-nowrap ${className} ${mainButton ? 'w-2/3' : 'w-1/3'}`} {...drawerFooterButton} />;
};

Drawer.Footer = DrawerFooter;

// ===================================================
// Drawers in applicant module

// Body Test Details nested component
Drawer.Body.TestDetails = ({
  children,
  className,
  label,
  enumText,
  duration,
  totalQuestions,
  ...drawerBodyTestDetails
}: DrawerBodyTestDetailsProps) => {
  return (
    <div className={`space-y-2 ${className}`} {...drawerBodyTestDetails}>
      <p className="text-base font-semibold">
        {enumText} {label}
      </p>
      <div className="flex gap-2 text-[#6B7280] text-sm font-medium items-center">
        <Icon icon="tabler:clock-hour-3-filled" width="18" />
        <span>{duration} mins</span>
        <span>•</span>
        <CustomIcon definedIcon="questions" className="text-[#8A43F9]" width="15" height="18" />
        <span>
          {totalQuestions} {(totalQuestions ?? 0) > 1 ? 'Questions' : 'Question'}
        </span>
      </div>
      {children as ReactNode}
    </div>
  );
};

// Body Question nested component
Drawer.Body.QuestionOfScreening = ({
  children,
  className,
  index,
  row,
  setQuestionsListData,
  isExpandAllAnswers,
  canEditQuestion,
  handleSaveTest,
  ...drawerBodyQuestion
}: DrawerBodyQuestionOfScreeningProps) => {
  // Hook
  const screen = useScreenSize();

  // Redux for second AnswerView
  const dispatch2 = useAppDispatch();
  const { isExpanded: isExpanded2, isTruncated: isTruncated2 } = useAppSelector((state: RootState) => state.drawer.answerView2);

  // Methods
  const AnswerView = ({ answer }: { answer: number | string | Record<string, any> }) => {
    // Ref
    const questionRef = useRef<HTMLDivElement>(null);

    // On mount
    useEffect(() => {
      if (questionRef.current) {
        dispatch2(setAnswerView2Truncated(questionRef.current.scrollHeight > questionRef.current.clientHeight));
      }
    }, [dispatch2]);

    return (
      <div className="text-[16px] font-medium text-[#5C5C5C] dark:text-gray-400 leading-6">
        <div ref={questionRef} className={!isExpandAllAnswers && !isExpanded2[row?.id] ? 'line-clamp-2' : ''}>
          {typeof answer === 'number' ? answer : Object.keys(answer)[0]}
        </div>
        {!isExpandAllAnswers && isTruncated2 && (
          <button
            onClick={() => dispatch2(setAnswerView2Expanded({ ...isExpanded2, [row?.id]: !isExpanded2[row?.id] }))}
            className="text-[#667085] font-semibold text-sm hover:underline"
          >
            {isExpanded2[row?.id] ? 'Read less' : 'Read more...'}
          </button>
        )}
      </div>
    );
  };

  const notify = useAppSelector((state: RootState) => state.notify);
  const toggleEditSave = () => {
    if (row?.isEditMode) {
      if (row?.pendingTitle) {
        handleSaveTest?.(row?._id);
      } else {
        toast.error('Question is empty');
      }
    } else {
      setQuestionsListData?.((prev: QuestionListItem[]) =>
        prev?.map((question: QuestionListItem) =>
          question?._id === row?._id ? { ...question, pendingTitle: row?.title, isEditMode: true } : question
        )
      );
    }
  };

  return (
    <div className={className} {...drawerBodyQuestion}>
      <div className="p-4 border dark:border-gray-700 rounded-lg space-y-2">
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="flex flex-col sm:flex-row grow items-start gap-2">
            {/* Question view or TextInput edit view */}
            {row?.isEditMode ? (
              <TextInputFlowbite
                className="w-full"
                value={row?.pendingTitle}
                onChange={(value: any) =>
                  setQuestionsListData?.((prev: QuestionListItem[]) =>
                    prev?.map((question: QuestionListItem) =>
                      question?._id === row?._id ? { ...question, pendingTitle: value?.currentTarget?.value } : question
                    )
                  )
                }
              />
            ) : (
              <div className="flex gap-1 text-sm">
                <span className="text-[14px] text-[#101828] dark:text-white font-medium">Q{++index}.</span>
                <span className="text-[14px] text-[#101828] dark:text-white font-medium">{String(row?.question || row?.title || '')}</span>
              </div>
            )}

            {/* Unanswered flag */}
            {row?.answer === '' && (
              <div className="flex items-center gap-2 text-[#798296]">
                <span className="hidden md:block">•</span>
                <Icon width={'24'} icon="mingcute:close-square-fill" className="text-[#E9A8A2]" />
                <span className="text-[#667085] text-sm font-medium">Unanswered</span>
              </div>
            )}
          </div>

          {canEditQuestion && (
            <div className="flex flex-wrap gap-2 pt-2 sm:pt-0">
              {row?.isEditMode && (
                <Button
                  size="xs"
                  icon="ic:twotone-close"
                  onClick={() =>
                    setQuestionsListData?.((prev: QuestionListItem[]) =>
                      prev?.map((question: QuestionListItem) =>
                        question?._id === row?._id ? { ...question, pendingTitle: '', isEditMode: false } : question
                      )
                    )
                  }
                  tertiary
                  className="cursor-pointer self-end sm:self-start"
                />
              )}
              <Button
                iconWidth={row?.isEditMode ? '20' : '18'}
                size="xs"
                icon={row?.isEditMode ? 'fluent:save-16-regular' : undefined}
                customIcon={!row?.isEditMode ? { definedIcon: 'edit' } : undefined}
                onClick={toggleEditSave}
                className="cursor-pointer self-end sm:self-start"
                tertiary={row?.isEditMode ? false : true}
              />
            </div>
          )}
          {row?.answer && (
            <div className="text-[#667085] text-sm font-medium text-nowrap">
              Time{' '}
              {row?.timeTaken &&
              ['hours', 'minutes', 'seconds'].every(
                (unit) => row.timeTaken[unit as keyof typeof row.timeTaken] != null && !isNaN(row.timeTaken[unit as keyof typeof row.timeTaken])
              )
                ? `${row.timeTaken.hours * 3600 + row.timeTaken.minutes * 60 + row.timeTaken.seconds}s`
                : '—'}
            </div>
          )}
        </div>

        {row?.answer && <AnswerView answer={row?.answer} />}
      </div>
      {children as ReactNode}
    </div>
  );
};

// Body DatePicker nested component
Drawer.Body.DatePicker = ({
  children,
  className,
  startDate,
  dueDate,
  extraTime,
  setExtraTime,
  setTimeSettingsVisible,
  type,
  ...drawerBodyDatePicker
}: DatePicker) => {
  // Redux
  const dispatch = useAppDispatch();
  const { showExceededTime } = useAppSelector((state: RootState) => state.drawer.datePicker2);

  const formatDate = (customDate: Date | string | number | undefined): string => {
    const date = new Date(customDate || Date.now());
    if (!isValid(date)) {
      return 'Invalid date';
    }
    return format(date, "MMMM dd, yyyy, 'at' hh:mm a");
  };

  return (
    <div className={`space-y-3 ${className}`} {...drawerBodyDatePicker}>
      <div className="flex flex-wrap justify-between items-center gap-y-2 bg-[#F9FAFB] dark:bg-darkGrayBackground border border-[#F2F2F2] p-3 rounded-md">
        <div className="space-y-2">
          <p className="text-[#6B7280] dark:text-gray-300 text-[15px] font-medium">
            <span className="capitalize">{type}</span> link is valid from:
          </p>
          <p className="flex items-center flex-wrap gap-2 text-[#3C3D3E] dark:text-white text-[15px] font-medium">
            <span>{formatDate(startDate)}</span>
            <Icon icon="line-md:arrow-right" width={'16'} />
            <span>{formatDate(dueDate)}</span>
          </p>
          <div className="flex items-center gap-2">
            <CustomIcon definedIcon="infoItalic" />
            <p className="text-[#6B7280] dark:text-gray-300 text-sm font-medium">The applicant will receive the {type} link in UTC time via email</p>
          </div>
        </div>
        <Button
          tertiary
          label="Customize Availability"
          customIcon={{ definedIcon: 'customize' }}
          size="sm"
          onClick={() => setTimeSettingsVisible?.()}
        />
      </div>
      {type !== 'interview' && (
        <div className="pb-3 space-y-2 items-center gap-4 bg-[#F9FAFB] dark:bg-darkGrayBackground border border-[#F2F2F2] p-3 rounded-md">
          <div className="flex items-center gap-2">
            <p className="text-[#2D3748] dark:text-gray-300 text-[15px] font-medium">Allow extra time after {type} duration ends ?</p>
            <Tooltip content="" placement="top">
              <Icon icon="solar:info-circle-outline" className="text-gray-600 dark:text-gray-200" width="18" />
            </Tooltip>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex items-center gap-3 w-fit">
              {showExceededTime && (
                <>
                  <label htmlFor="extend" className="text-[#667085] text-sm">
                    Extend by:
                  </label>
                  <Form onSubmit={() => {}}>
                    <Select
                      name="name"
                      placeholder="Exceeded time"
                      value={extraTime}
                      onChange={setExtraTime}
                      // validators={[isRequired()]}
                      type="number"
                      lookup={[
                        { value: 5, label: '5' },
                        { value: 10, label: '10' },
                        { value: 15, label: '15' },
                        { value: 20, label: '20' },
                      ]}
                      dropIcon={true}
                    />
                  </Form>
                </>
              )}
            </div>

            <ToggleSwitch
              checked={showExceededTime}
              onChange={(event) => {
                dispatch(setDatePicker2ShowExceededTime(!showExceededTime));
                if (!event) setExtraTime?.();
              }}
              color="purple"
              sizing="sm"
            />
          </div>
        </div>
      )}
      {children as ReactNode}
    </div>
  );
};

// ===================================================
// Drawers in quiz module

// Split View nested - main component
// Refactor Drawer.SplitView to support nested SplitLeftSide and SplitRightSide

type DrawerSplitViewComponent = React.FC<any> & {
  SplitLeftSide: React.FC<any>;
  SplitRightSide: React.FC<any>;
};

const DrawerSplitView: DrawerSplitViewComponent = ({ children, className, ...drawerSplitView }) => {
  return (
    <div className="flex flex-col h-full md:flex-row" {...drawerSplitView}>
      {children as ReactNode}
    </div>
  );
};

DrawerSplitView.SplitLeftSide = ({ children, className, ...drawerSplitLeftSide }) => {
  return (
    <div className={`flex flex-col md:w-[30%] dark:bg-darkGrayBackground p-4 shadow-md ${className}`} {...drawerSplitLeftSide}>
      {children as ReactNode}
    </div>
  );
};

DrawerSplitView.SplitRightSide = ({ children, className, ...drawerSplitRightSide }) => {
  return (
    <div className={`flex flex-col grow md:w-[70%] dark:bg-gray-800 p-4 overflow-hidden ${className}`} {...drawerSplitRightSide}>
      {children as ReactNode}
    </div>
  );
};

Drawer.SplitView = DrawerSplitView;

// Header Section nested component
Drawer.HeaderSection = ({
  children,
  className,
  onClose,
  onReset,
  icon,
  headerLabel,
  selectedQuestionsCount,
  resultsFound,
  ...drawerHeaderSection
}: HeaderSectionProps) => {
  return (
    <div className={className} {...drawerHeaderSection}>
      <div className="flex justify-between items-start">
        <div className="flex flex-wrap gap-3">
          <div className="flex gap-2 sm:gap-5 items-center flex-wrap">
            <div className="flex items-center gap-2">
              {icon && icon}
              <h3 className="text-base font-medium text-[#101828] dark:text-white">{headerLabel as ReactNode}</h3>
            </div>

            {selectedQuestionsCount > 0 && (
              <p className="text-xs border rounded-2xl py-1 px-4 w-fit text-[#8d59d1] font-normal bg-[#F4EDFF] dark:bg-[#8d59d1] dark:bg-opacity-50 dark:border-none dark:text-[#F4EDFF]">
                {selectedQuestionsCount} Selected
              </p>
            )}
          </div>
          {onReset && (
            <p className="underline h-fit pt-1 font-medium text-[14px] cursor-pointer text-[#9061F9]" onClick={onReset}>
              Clear All
            </p>
          )}
        </div>

        {onClose && (
          <div className="cursor-pointer ml-auto" onClick={onClose}>
            <Icon icon="mdi:close" className="text-2xl text-gray-500 hover:text-gray-700 transition-colors" />
          </div>
        )}
      </div>
      {resultsFound && (
        <p className="text-sm mt-2">
          <span className="font-medium">{resultsFound}</span> results found
        </p>
      )}
      <hr className="border-gray-800 border-opacity-5 dark:border-gray-700 mt-4" />

      {children as ReactNode}
    </div>
  );
};

// Filter Section nested component
Drawer.FilterSection = ({
  children,
  className,
  label,
  optional,
  form,
  lookups,
  propertyKeyObject,
  filter,
  showSingleClear,
  handleSingleClear,
  ...drawerFilterSection
}: FilterSectionProps) => {
  // Colors data
  const filterLabelColor = {
    subcategory: 'border-[#7E3AF2] text-[#7E3AF2] bg-[#9061F9] bg-opacity-5',
    subCategoryFiltration: 'border-[#7E3AF2] text-[#7E3AF2] bg-[#9061F9] bg-opacity-5',
    Difficulty: [
      'text-green-500 border-green-500 bg-green-400 bg-opacity-5',
      'text-yellow-500 border-yellow-500 bg-yellow-400 bg-opacity-5',
      'text-orange-600 border-orange-600 bg-orange-400 bg-opacity-5',
      'text-red-700 border-red-700 bg-red-400 bg-opacity-5',
    ],
    QuestionDifficulty: [
      'text-green-500 border-green-500 bg-green-400 bg-opacity-5',
      'text-yellow-500 border-yellow-500 bg-yellow-400 bg-opacity-5',
      'text-orange-600 border-orange-600 bg-orange-400 bg-opacity-5',
      'text-red-700 border-red-700 bg-red-400 bg-opacity-5',
    ],
    QuizDifficulty: [
      'text-teal-800 border-teal-300',
      'text-sky-800 border-sky-300',
      'text-amber-700 border-amber-200',
      'text-orange-700 border-orange-30',
      'text-red-800 border-red-300',
    ],
  };
  const classNameFiltersBlock = 'px-4 py-[5px] rounded-full border cursor-pointer text-center text-sm';

  // Ref
  const containerRef = useRef<HTMLDivElement>(null);

  // Redux
  const dispatch = useAppDispatch();
  const { isAtStart, isAtEnd } = useAppSelector((state: RootState) => state.drawer.filterSection);

  // Methods
  const handleFiltration = (id: string) => {
    if (form[propertyKeyObject]?.includes(id)) {
      dispatch(setFieldValue({ path: propertyKeyObject as any, value: form[propertyKeyObject].filter((filterId: string) => filterId !== id) }));
    } else {
      dispatch(setFieldValue({ path: propertyKeyObject as any, value: [...form[propertyKeyObject], id] }));
    }
  };

  const handleScroll = (direction: 'left' | 'right') => {
    containerRef.current?.scrollBy({ left: direction === 'left' ? -200 : 200, behavior: 'smooth' });
  };
  const checkScroll = () => {
    if (containerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = containerRef.current;
      dispatch(setFilterSectionAtStart(scrollLeft === 0));
      dispatch(setFilterSectionAtEnd(Math.round(scrollLeft + clientWidth) >= scrollWidth));
    }
  };

  useEffect(() => {
    if (containerRef.current) {
      checkScroll();
      containerRef.current.addEventListener('scroll', checkScroll);
    }

    return () => {
      if (containerRef.current) {
        containerRef.current.removeEventListener('scroll', checkScroll);
      }
    };
  }, []);

  return (
    <div className={className} {...drawerFilterSection}>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h2 className="font-medium text-sm text-[#667085] dark:text-grayTextOnDarkMood">
            {label}
            {/* {!optional && <span className="text-red-500">*</span>} */}
          </h2>
          {/* {optional && (
            <Tooltip content="If not selected, questions will be added randomly from categories below." placement="top">
              <div className="px-2 py-1 text-xs text-gray-600 bg-gray-100 rounded-lg cursor-pointer dark:bg-gray-800 dark:text-gray-300">
                Optional
              </div>
            </Tooltip>
          )} */}
          {showSingleClear && (
            <p className="text-[#9061F9] cursor-pointer font-medium text-[14px] select-none" onClick={handleSingleClear}>
              Clear
            </p>
          )}
        </div>
        <div className="relative flex items-center w-full overflow-hidden border-b-gray-200">
          {/* Left Arrow  */}
          <button
            className={`px-2 py-2 rounded-full text-gray-700 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600 transition ${
              isAtStart && 'hidden'
            }`}
            onClick={() => handleScroll('left')}
            disabled={isAtStart}
          >
            <Icon icon="ep:arrow-left-bold" />
          </button>

          {/* Scrollable Container */}
          <div className="overflow-x-auto whitespace-nowrap scroll-smooth scrollbar-hidden" ref={containerRef}>
            <div className="flex items-center gap-2">
              {/* Auto Selection */}
              {propertyKeyObject === 'subCategoryFiltration' &&
                form?.subCategory?.map((singleDetails) => (
                  <div
                    key={singleDetails}
                    className={`${classNameFiltersBlock} ${
                      (form?.subCategoryFiltration as any)?.includes(singleDetails) && filterLabelColor[propertyKeyObject]
                    }`}
                    onClick={() => handleFiltration(singleDetails)}
                  >
                    {lookups?.find((option) => option._id === singleDetails)?.name}
                  </div>
                ))}

              {propertyKeyObject === 'questionsDifficulty' &&
                Enums['QuestionDifficulty']?.map((singleDetails: any, index: number) => (
                  <div
                    key={singleDetails.value}
                    className={`${classNameFiltersBlock} ${
                      form[propertyKeyObject]?.includes(singleDetails.value) && filterLabelColor.Difficulty[index]
                    }`}
                    onClick={() => handleFiltration(singleDetails.value)}
                  >
                    {singleDetails.label}
                  </div>
                ))}

              {/* Manual Selection */}
              {(propertyKeyObject === 'subcategory' || propertyKeyObject === 'QuestionDifficulty') &&
                filter.options?.map((option: any, index: number) => (
                  <label
                    key={option.name}
                    className={`${filter.key === 'subcategory' && !form.subCategory.includes(option.name.split('-')[1]) && 'hidden'}`}
                  >
                    <div
                      className={`${classNameFiltersBlock} ${
                        option.value &&
                        (Array.isArray((filterLabelColor as any)[filter.key])
                          ? (filterLabelColor as any)[filter.key][index]
                          : (filterLabelColor as any)[filter.key])
                      }`}
                    >
                      <input type="checkbox" className="hidden" checked={option.value} onChange={() => option.onChange?.(!option.value)} />
                      <span>{option.label}</span>
                    </div>
                  </label>
                ))}
            </div>
          </div>

          {/* Right Arrow */}
          <button
            className={`px-2 py-2 rounded-full text-gray-700 dark:text-white hover:bg-gray-200 dark:hover:bg-gray-600 transition ${
              isAtEnd && 'hidden'
            }`}
            onClick={() => handleScroll('right')}
            disabled={isAtEnd}
          >
            <Icon icon="ep:arrow-right-bold" />
          </button>
        </div>
      </div>
      {children as ReactNode}
    </div>
  );
};

// Generate Questions nested component
Drawer.GenerateQuestions = ({ children, className, form, isButtonDisabled, onSubmit, ...drawerGenerateQuestions }: any) => {
  const dispatch = useAppDispatch();
  return (
    <div className={className} {...drawerGenerateQuestions}>
      <Form onSubmit={onSubmit} className="flex flex-wrap items-start gap-2 pt-2 mb-2">
        <div className="flex gap-2">
          <h2 className="text-sm font-medium text-black dark:text-grayTextOnDarkMood py-2.5">
            How many questions would you like to add? <span className="text-red-500">*</span>
          </h2>
        </div>
        <div className="flex gap-2 items-start flex-wrap">
          <TextInput
            name="numOfQuestions"
            type="number"
            placeholder="0"
            value={form.numOfQuestions}
            onChange={(value: any) => dispatch(setFieldValue({ path: 'numOfQuestions', type: Number, value }))}
            // validators={[isRequired()]}
            min={0}
            max={100}
          />

          <Button className="h-[42px]" label="Auto-Select" disabled={isButtonDisabled || !form.numOfQuestions} type="submit" />
        </div>
      </Form>

      {children as ReactNode}
    </div>
  );
};

// Split Right Side Search nested component
Drawer.Search = ({ children, className, value, onInput, ...drawerSplitViewSplitRightSideSearch }: any) => {
  return (
    <div className={className} {...drawerSplitViewSplitRightSideSearch}>
      <div className="relative w-full py-2">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <Icon icon="carbon:search" width="15" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
        </div>
        <input
          type="text"
          placeholder="Search for questions"
          className="block w-full p-2.5 pl-10 bg-[#f8fafc97] dark:bg-darkBackgroundCard text-gray-800 dark:text-white text-[13.5px] border border-gray-100 dark:border-gray-700 focus:border-gray-200 rounded-xl dark:placeholder-gray-400 focus:ring-0 truncate"
          value={value}
          onInput={onInput}
        />
      </div>
      {children as ReactNode}
    </div>
  );
};

Drawer.QuestionOfTest = ({
  children,
  className,
  index,
  row,
  mainQuestionsListForm,
  mainSetFieldValueForm,
  currentPage,
  questionsPerPage,
  selectedQuestionIds,
  setSelectedQuestionIds,
  handleGetQuestionGenerateData,
  questionDatabase,
  setQuestionDatabase,
  generatedQuestionsIds,
  refresh,
  canRemoveQuestion,
  setAnyQuestionHasEditMode,
  ...drawerQuestionOfTest
}: QuestionOfTestProps) => {
  const isViewOnly = useAppSelector((state: RootState) => state.viewOnly.isVisible);

  // Reference
  const textRef = useRef(null);

  // Redux
  const dispatch = useAppDispatch();
  const { isExpanded, isOverflow, isEditMode, isShowFullAnswers, isChecked } = useAppSelector((state: RootState) => state.drawer.questionOfTest);

  // Hooks

  const notify = useAppSelector((state: RootState) => state.notify);
  const screen = useScreenSize();

  // form
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {},
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  // Theme
  const customThemeCheckbox = {
    root: {
      base: 'h-4 w-4 rounded border border-gray-300 bg-gray-100 focus:ring-2 dark:border-gray-600 dark:bg-gray-700 dark:focus:bg-purple-600 dark:focus:ring-purple-600',
      color: {
        default: 'text-purple-600 rounded border-gray-300 focus:ring-purple-500 focus:ring-offset-2',
      },
    },
  };

  const customThemeRadioButton = {
    root: {
      base: 'h-4 w-4 border border-gray-300 text-purple-600 focus:ring-2 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700 dark:focus:bg-purple-600 dark:focus:ring-purple-600',
    },
  };

  const classNameQuestionButton =
    'min-h-8 flex justify-between items-center border-2 border-[#1C1B1B40] dark:border-gray-500 text-[#101828] dark:text-white px-3 py-1 rounded-lg text-xs font-medium cursor-pointer';

  const handleDifficulty = (difficultyValue: number) => {
    const iconSize = 'text-sm text-center';

    switch (difficultyValue) {
      case 1:
        return {
          difficultyIcon: <LiaCircle className={iconSize} />,
          difficultyColor: 'text-green-500 ',
        };
      case 2:
        return {
          difficultyIcon: <LuDiamond className={iconSize} />,
          difficultyColor: 'text-yellow-500',
        };
      case 3:
        return {
          difficultyIcon: <BiPolygon className={iconSize} />,
          difficultyColor: 'text-orange-600 dark:text-opacity-90',
        };
      case 4:
        return {
          difficultyIcon: <PiCube className={iconSize} />,
          difficultyColor: 'text-red-700 dark:text-red-800',
        };
      default:
        return {
          difficultyIcon: null,
          difficultyColor: 'text-red-700 dark:text-red-800',
        };
    }
  };

  const handleGetQuestionData = async () => {
    try {
      const response = await Api.get<QuestionType>(`questions/single/${row?._id}`, {});
      console.log('questions/single', response.data);
      formik.setValues(response?.data);
    } catch (error: any) {
      toast.error(error.response.data.message);
    }
  };

  const toggleExpand = () => {
    handleGetQuestionData();
    dispatch(setQuestionOfTestShowFullAnswers(!isShowFullAnswers));
  };

  const handleRemoveOption = (id: string) => {
    // Remove unwanted form options
    const updatedOptions = form.options.filter((option: { id: string }) => option.id !== id);
    dispatch(setFieldValue({ path: 'options', value: updatedOptions }));

    // Set removed form multiChoiceAnswer to false
    const updateMultiChoiceAnswer = { ...form.multiChoiceAnswer, [id]: false };
    dispatch(setFieldValue({ path: 'multiChoiceAnswer', value: updateMultiChoiceAnswer }));
  };

  const handleAddOption = () => {
    if (form.options?.length >= 4) return;
    const newOption = {
      id: form.options?.length + 1,
      label: '',
    };
    dispatch(setFieldValue({ path: 'options', value: [...form.options, newOption] }));
  };

  const handleSelectCheckbox = (value: string, id: string) => {
    const numberOfSelectedAnswers = Object.entries(form.multiChoiceAnswer).filter(([key, value]) => value).length;
    if (numberOfSelectedAnswers >= 2 && value) {
      return toast.error('Only two answers are allowed');
    }
    const newObj = { ...form.multiChoiceAnswer, [id]: value };
    dispatch(setFieldValue({ path: 'multiChoiceAnswer', value: newObj }));
  };

  const handleMinTwoAnswers = () => {
    let counter = 0;
    Object.entries(form.multiChoiceAnswer).forEach(([key, value]) => {
      value && counter++;
    });
    if (form.type === 2) {
      if (counter === 2) {
        return true;
      }
      return toast.error('Choose two answers');
    }
    return true;
  };
  const ConfirmText = () => (
    <div className="flex flex-col w-full items-center gap-2">
      <svg width="66" height="66" viewBox="0 0 66 66" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="5" y="5" width="56" height="56" rx="28" fill="#F0E7FF" />
        <rect x="5" y="5" width="56" height="56" rx="28" stroke="#F8F4FF" strokeWidth="10" />
        <g clipPath="url(#clip0_8621_5254)">
          <path
            d="M38.8085 26.3846C38.2939 25.4066 37.4884 24.5917 36.4904 24.0006C35.4929 23.4121 34.2888 23.0528 32.982 23.0528C31.3711 23.0488 30.0328 23.4685 29.0227 24.0497C28.0085 24.6288 27.5713 25.3031 27.5713 25.3031C27.4008 25.451 27.3047 25.6662 27.3087 25.8912C27.3133 26.1166 27.4171 26.3282 27.5928 26.4687L28.9964 27.593C29.2825 27.822 29.692 27.8134 29.9681 27.5727C29.9681 27.5727 30.1406 27.2611 30.6809 26.9524C31.2243 26.6458 31.9288 26.3988 32.982 26.3955C33.9006 26.3935 34.7016 26.7363 35.2482 27.2047C35.5198 27.4368 35.7227 27.6967 35.847 27.9344C35.9723 28.1741 36.0181 28.3836 36.0174 28.5428C36.0148 29.0805 35.9103 29.4322 35.7595 29.7319C35.6445 29.9557 35.4943 30.1543 35.301 30.3469C35.0125 30.6353 34.6214 30.9019 34.1825 31.1469C33.7432 31.3948 33.2698 31.613 32.7914 31.8766C32.2454 32.1789 31.6675 32.6132 31.2406 33.265C31.0277 33.5872 30.8613 33.9558 30.7556 34.3447C30.6484 34.7339 30.6001 35.1421 30.6001 35.5584C30.6001 36.0026 30.6001 36.3673 30.6001 36.3673C30.6001 36.786 30.9395 37.1255 31.3583 37.1255H33.1849C33.6036 37.1255 33.9431 36.786 33.9431 36.3673C33.9431 36.3673 33.9431 36.0026 33.9431 35.5584C33.9431 35.398 33.9614 35.2946 33.979 35.2289C34.0091 35.1308 34.0261 35.1062 34.0754 35.0466C34.1258 34.9898 34.2276 34.903 34.4152 34.7992C34.6894 34.6451 35.13 34.4369 35.6289 34.1674C36.3758 33.7589 37.2838 33.2046 38.0497 32.29C38.4306 31.8335 38.7674 31.2838 38.9998 30.6506C39.2342 30.0174 39.3608 29.3056 39.3602 28.5428C39.3595 27.7699 39.1499 27.036 38.8085 26.3846Z"
            fill="#7E3AF2"
          />
          <path
            d="M32.273 38.9297C31.1342 38.9297 30.2109 39.8533 30.2109 40.9917C30.2109 42.1298 31.1343 43.0531 32.273 43.0531C33.4111 43.0531 34.334 42.1298 34.334 40.9917C34.334 39.8533 33.4111 38.9297 32.273 38.9297Z"
            fill="#7E3AF2"
          />
        </g>
        <defs>
          <clipPath id="clip0_8621_5254">
            <rect width="20" height="20" fill="white" transform="translate(23.334 23.0527)" />
          </clipPath>
        </defs>
      </svg>

      <p className="text-xl dark:text-white">Are you sure?</p>
      <p className="items-center text-[#626262] font-light justify-center dark:text-white">
        Once confirmed, these changes will be applied to the main question as well.{' '}
      </p>
    </div>
  );

  const handleUpdate = async () => {
    if (!!handleMinTwoAnswers()) {
      if (form.title !== '') {
        dispatch(
          showConfirm({
            message: ConfirmText(),
            options: {
              onConfirm: async () => {
                try {
                  await Api.put(`questions/single/${row?._id}`, form);
                  dispatch(setNotifyMessage('Question updated successfully!'));
                  // Nothing will happen in single view
                  if (refresh) {
                    // Manual selection
                    refresh();
                  } else if (generatedQuestionsIds) {
                    // Auto selection
                    setQuestionDatabase([]);
                    setSelectedQuestionIds([]);
                  }
                  dispatch(setQuestionOfTestShowFullAnswers(false));
                  dispatch(setQuestionOfTestEditMode(false));
                  setAnyQuestionHasEditMode((prev: any) => ({ ...prev, [row?._id]: false }));
                  generatedQuestionsIds?.map((question) => handleGetQuestionGenerateData(question));
                  // setQuestionDatabase((prev) => prev.map((question) => (question._id === row?._id ? { ...question, title: form.title } : question)));
                } catch (error: any) {
                  dispatch(setErrorNotify(error.response.data.message));
                } finally {
                  dispatch(hideConfirm());
                }
              },
              confirmLabel: 'Confirm',
              cancelLabel: 'Cancel',
            },
          })
        );
      } else {
        toast.error(`Content shouldn't be empty`);
      }
    }
  };

  const handleCheckboxChange = (event: any) => {
    const { value, checked } = event.target;
    if (checked) {
      // setSelectedQuestionIds((prevValues) => [...prevValues, value]);
    } else {
      // setSelectedQuestionIds((prevValues) => prevValues.filter((val) => val !== value));
    }
  };

  // const handleRemoveQuestion = (questionId) => {
  //   // Remove the question from the database
  //   setQuestionDatabase(questionDatabase.filter((question) => question._id !== questionId));
  //   // Update the form's questionIds field
  //   mainSetFieldValueForm('questionIds')(mainQuestionsListForm.questionIds.filter((id) => id !== questionId));
  // };

  // useEffect(() => {
  //   if (textRef) {
  //     dispatch(setQuestionOfTestOverflow(textRef.current.scrollHeight > textRef.current.offsetHeight));
  //   }
  // }, [dispatch]);

  return (
    <div className={`px-1 ${className}`} {...drawerQuestionOfTest}>
      <Form onSubmit={() => handleUpdate()} key={row._id} className="border border-[#E8E8E8] dark:border-gray-700 rounded-lg p-2 sm:p-3 space-y-3">
        <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
          <div className="flex gap-2 w-full">
            {/* Checkbox */}
            {!canRemoveQuestion && !isEditMode && (
              <FlowbiteCheckbox
                value={row._id}
                checked={selectedQuestionIds.includes(row._id) || mainQuestionsListForm.questionIds.includes(row._id)}
                className={`mt-0.5 ${mainQuestionsListForm.questionIds.includes(row._id) ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                onChange={handleCheckboxChange}
                theme={customThemeCheckbox}
              />
            )}

            {/* Question text || Edit Question text */}
            {/* {isEditMode ? (
              <div className="w-full">
                <Textarea value={form?.title} onChange={(value: any) => dispatch(setFieldValue({ path: 'title', value }))} />
              </div>
            ) : (
              <>
                {!isViewOnly && canRemoveQuestion && (
                  <Checkbox
                    checked={isChecked}
                    theme={customThemeCheckbox}
                    onChange={() => {
                      dispatch(setQuestionOfTestChecked(!isChecked));
                      if (isChecked) {
                        mainSetFieldValueForm('questionIds')(mainQuestionsListForm.questionIds.filter((id) => id !== row._id));
                      } else {
                        (mainSetFieldValueForm as any)('questionIds')([...mainQuestionsListForm.questionIds, row._id]);
                      }
                    }}
                  />
                )}
                <div className={`flex gap-1 items-start ${canRemoveQuestion ? 'text-base font-normal' : 'text-sm'}`}>
                  <span className="text-[#667085]">Q{++index + (currentPage - 1) * questionsPerPage}.</span>
                  <div>
                    <p className={`dark:text-white text-[15px] native-break-all-words ${!isExpanded && 'line-clamp-2'}`} ref={textRef}>
                      {row?.title}
                    </p>
                    {isOverflow && (
                      <span
                        className="text-sm text-[#333333C2] font-medium cursor-pointer"
                        onClick={() => dispatch(setQuestionOfTestExpanded(!isExpanded))}
                      >
                        {isExpanded ? 'Read less' : 'Read more'}
                      </span>
                    )}
                  </div>
                </div>
              </>
            )} */}
          </div>

          {/* Buttons handle view */}
          {!isEditMode && (
            <div className="flex justify-between self-end sm:self-start -order-1 sm:order-1 gap-2">
              {!isViewOnly && isShowFullAnswers && (
                <Button
                  size="sm"
                  label={!screen.lt.xs() ? 'Edit' : ''}
                  tertiary
                  customIcon="edit"
                  iconWidth="18"
                  onClick={() => {
                    dispatch(setQuestionOfTestEditMode(true));
                    setAnyQuestionHasEditMode((prev: any) => ({ ...prev, [row?._id]: true }));
                  }}
                />
              )}

              <Button
                size="sm"
                label={screen.ss() ? (isShowFullAnswers ? 'Hide Answers' : 'Show Answers') : ''}
                icon={isShowFullAnswers ? 'mdi:eye-off-outline' : 'ph:eye'}
                onClick={toggleExpand}
                tertiary
                className="text-nowrap"
              />
            </div>
          )}
        </div>

        {/* Answers */}
        {isShowFullAnswers && (
          <div className="space-y-2">
            <p className={`font-medium dark:text-white ${canRemoveQuestion ? 'text-sm' : 'text-xs'}`}>Answers:</p>
            <div className="space-y-1">
              {form?.options?.map((option: any, index: number) => (
                <div key={option.id} className="w-full space-y-2 flex items-center justify-center ">
                  <div className="flex items-center gap-1">
                    {/* Radio Button for Single Choice */}
                    {form.type === QuestionTypeEnum.Singlechoice && (
                      <div className="flex  items-center ">
                        <Radio
                          name={`singleChoiceAnswer${option.id}`}
                          selectionValue={option.id}
                          value={form.singleChoiceAnswer}
                          onChange={(value: any) => dispatch(setFieldValue({ path: 'singleChoiceAnswer', type: Number, value }))}
                          disabled={!isEditMode}
                          className={`${isEditMode ? 'cursor-pointer' : 'cursor-not-allowed'} text-green-500`}
                          theme={customThemeRadioButton}
                          label=""
                        />
                      </div>
                    )}

                    {/* Checkboxes for Multi Choice */}
                    {form.type === QuestionTypeEnum.Multichoice && (
                      <div className="flex items-center">
                        <Checkbox
                          name={`multichoiceAnswer${option.id}`}
                          value={form.multiChoiceAnswer[option.id]}
                          onChange={(e: any) => handleSelectCheckbox(e, option.id)}
                          className={`${isEditMode ? 'cursor-pointer' : 'cursor-not-allowed'} text-green-500`}
                        />
                      </div>
                    )}

                    {/* {isEditMode && (
                      <span className="text-nowrap text-xs font-medium text-inputLabel dark:text-inputDarkLabel">
                        {(form.type === 2 ? index < 3 : index < 2) && <span className="text-red-600 dark:text-red-800"> *</span>}
                      </span>
                    )} */}
                  </div>

                  {/* Answer text */}
                  {isEditMode ? (
                    <div className="flex items-center gap-2 flex-grow">
                      <div className="w-full">
                        <Textarea
                          name={`answer${option.id}`}
                          placeholder="Write your answer"
                          className="w-full bg-white"
                          value={option.label}
                          onChange={(value: any) => dispatch(setFieldValue({ path: `options.${index}.label`, value }))}
                          // validators={
                          //   form.type === 2
                          //     ? index < 3
                          //       ? [isRequired(), isNotSpaces()]
                          //       : [isNotSpaces()]
                          //     : index < 2
                          //     ? [isRequired(), isNotSpaces()]
                          //     : [isNotSpaces()]
                          // }
                          requiredLabel={index < 2}
                          maxHeight="150"
                          rows={1}
                        />
                      </div>

                      {/* Show delete button only if there are more than two options */}
                      {isEditMode && (form.type === 2 ? form.options.length > 3 : form.options.length > 2) && index === form.options.length - 1 && (
                        //
                        <Button
                          tertiary
                          size="sm"
                          icon="hugeicons:delete-02"
                          width={'22'}
                          className="text-[#B83434] cursor-pointer"
                          onClick={() => handleRemoveOption(option.id)}
                        />
                      )}
                    </div>
                  ) : (
                    <div className="w-full px-2 py-1.5 text-sm dark:text-white border border-[#d9d9d9] dark:border-gray-700 rounded-lg">
                      {option.label}
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Add button to add more options */}
            {isEditMode && form?.options?.length < 4 && (
              <div
                className="bg-newQuAnsBg w-full my-2 py-2 rounded-lg dark:bg-newQuAnsDarkBg dark:bg-opacity-80 hover:bg-newQuAnsHoverBg mx-auto cursor-pointer"
                onClick={handleAddOption}
              >
                <button type="button" className="flex items-center justify-center mx-auto">
                  <Icon icon="icon-park-solid:add-one" className="text-primaryPurple text-opacity-60" width="20" />
                  <span className="ml-2 text-newQuAnsText font-medium dark:text-newQuAnsDarkText">New Answer</span>
                </button>
              </div>
            )}
          </div>
        )}

        {/* Labels */}
        {!isEditMode && (
          <div className="flex flex-wrap items-center gap-3">
            <div className={`flex items-center mr-1 text-sm font-normal capitalize ${handleDifficulty(Number(row?.difficulty))?.difficultyColor}`}>
              {QuestionDifficulty[row?.difficulty as any]}
              {/* <EnumText name={'QuestionDifficulty'} value={row?.difficulty} /> */}
            </div>
            <div className="px-3 py-[2px] text-[#667085] bg-[#F2F4F7] rounded-full text-[13px] font-normal dark:bg-gray-700 dark:text-gray-300 truncate">
              {row.subCategoryName}
            </div>
            <div className="px-3 py-[2px] text-[#667085] bg-[#F2F4F7] rounded-full text-[13px] font-normal dark:bg-gray-700 dark:text-gray-300 truncate">
              {row.topicName}
            </div>
          </div>
        )}

        {/* Buttons handle question answers */}
        {isEditMode && (
          <div className="flex gap-2">
            <Button
              label="Cancel"
              tertiary
              size="sm"
              onClick={() => {
                dispatch(setQuestionOfTestEditMode(false));
                setAnyQuestionHasEditMode((prev: any) => ({ ...prev, [row?._id]: false }));
                dispatch(setQuestionOfTestShowFullAnswers(false));
                dispatch(initializeForm({}));
              }}
            />

            <Button size="sm" label="Save Changes" className="opacity-90" onClick={handleUpdate} />
          </div>
        )}
      </Form>

      {children as ReactNode}
    </div>
  );
};

// ===================================================
// Table Filter main component
Drawer.TableFilter = ({ children, className, ...drawerTableFilter }: DrawerContentProps) => {
  return (
    <div className={`flex flex-col h-full px-6 py-4 space-y-4 ${className}`} {...drawerTableFilter}>
      {children as ReactNode}
    </div>
  );
};

// ===================================================
// A reference nested function to create nested component
Drawer.ReferenceFunction = ({ children, className, ...drawerReferenceFunction }: DrawerContentProps) => {
  return (
    <div className={className} {...drawerReferenceFunction}>
      {children as ReactNode}
    </div>
  );
};
