export interface AiInterviewerTopApplicantsItem {
  _id: string;
  score: number;
  timeTaken: number;
  applicantId: string;
  applicantName: string;
  applicantEmail: string;
}

export interface GeneratedLinksItem {
  authorName: string;
  createdAt: string;
  expired: boolean;
  dueDate: string;
  totalUsage: number;
  submittedDocuments: number;
  completionRate: number;
  title: string;
  averageScore: number;
  assessmentUrl: string;
}

export interface GeneratedLinks {
  items: GeneratedLinksItem[];
  count: number;
}

export interface GeneratedInterviewListItem {
  _id: string;
  applicantName: string;
  applicantEmail: string;
  applicantId: string;
  quizTitle: string;
  quizDifficulty: string;
  createdAt: string;
  status: string;
  locked: boolean;
  authorName: {};
  archive: boolean;
  score: number;
  categoryName: string;
  subCategoryName: string;
  cheatingPercentage: number;
}

export interface GeneratedInterviewList {
  items: GeneratedInterviewListItem[];
  count: number;
}

export interface ApplicantAssignedListItem {
  applicantId: string;
  applicantName: string;
  applicantEmail: string;
  track: string;
  seniorityLevel: string;
  score: number;
  createdAt: string;
  timeTaken: number;
  status: string;
  duration: number;
  assessmentId: string;
}

export interface ApplicantAssignedList {
  items: ApplicantAssignedListItem[];
  count: number;
}
