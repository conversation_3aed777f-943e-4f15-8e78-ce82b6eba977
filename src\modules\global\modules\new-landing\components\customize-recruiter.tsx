import { Checkbox, Button, CustomIcon, Icon } from 'src';
import { Label } from 'flowbite-react';
import { Navigate } from 'react-router-dom';
import customizeRecruiter from 'images/landing/customize-recruiter.png';
const data = [
  { id: 1, text: 'AI-Powered Smart Interviews' },
  { id: 2, text: 'Skill-Based Interviews' },
  { id: 3, text: 'Real-Time Feedback ' },
  { id: 4, text: 'Behavioral & Skill Insights' },
];

export const CustomizeRecruiter = () => {
  return (
    <div className="py-7 mt-9 mb-10 ">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row lg:justify-around items-center justify-center gap-y-4 gap-x-40">
          {/* text and left things */}
          <div className="flex flex-col justify-between items-center lg:items-start">
            <div className="w-fit h-8 text-nowrap  bg-[#ddd7ff]/30 rounded-lg flex items-center justify-start mt-8 px-4 py-2 mb-9">
              <h2 className="text-sm text-[#8d5bf8] dark:text-white  font-semibold tracking-wider uppercase">Your AI Recruiter, Made Your Ways</h2>
            </div>
            <div className="flex flex-col space-y-4 pb-4">
              <div className="flex flex-col items-center lg:items-start mb-3">
                <span className="pt-1 text-center lg:text-start flex flex-row text-nowrap items-center  gap-1 ">
                  <span className=" sm:text-nowrap text-wrap sm:text-[55px]  text-base  text-[#0D0F2C] font-medium ">Customize your</span>{' '}
                  <span className=" sm:text-nowrap text-wrap sm:text-[55px]  text-base font-medium  gradient-text p-1 py-2 text-transparent bg-clip-text">
                    Recruiter
                  </span>
                </span>
                <span className="text-[#0D0F2C] sm:text-nowrap text-wrap text-center lg:text-start capitalize  sm:text-[55px]   text-base ">
                  Reflect your Brand
                </span>
              </div>

              <div className="thepassBtwo sm:thepassSubHone text-text-500 px-8 sm:px-0 text-center sm:text-start mb-5 pb-6">
                <p>
                  Customize your recruiter's avatar to visually represent your brand Trained to ask, engage, and score applicants — so you can focus
                  on hiring the best.
                </p>
              </div>

              <div className="lg:hidden">
                <div className="relative">
                  <img src={customizeRecruiter} className="w-full scale-90" alt="Recruiter" />
                </div>
              </div>
            </div>
            <div className="flex items-center justify-start md:justify-center w-full ">
              <div className="grid  grid-cols-1 sm:grid-cols-2 gap-4 w-fit mb-7 sm:gap-4">
                {data.map((item) => (
                  <div key={item.id} className="flex items-center">
                    {/* TODO: Markos */}
                    <Checkbox className="text-[#704EE6]" value={true} onChange={() => console.log('hello')} label="" name="" />
                    <Label htmlFor={`item-${item.id}`} className="flex text-[18px] text-nowrap font-medium text-[#4E5E82] ml-2">
                      {item.text}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* image and right things */}
          <div className="hidden lg:block">
            <div className="relative">
              <img src={customizeRecruiter} alt="Recruiter" className="w-3/4  scale-125 sm:scale-200 " />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
