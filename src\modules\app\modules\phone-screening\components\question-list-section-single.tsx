// UI
import { Button } from 'src';

// Flowbite
import { TextInput } from 'flowbite-react';

export const QuestionsListSection = ({ pagination, singleData, setQuestionsListFilterdData, isViewOnly }: any) => {
  // Theme
  const customTheme = {
    field: {
      input: {
        colors: {
          gray: 'block w-full border disabled:cursor-not-allowed disabled:opacity-50 border-gray-300 bg-gray-50 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 p-2.5 text-sm rounded-lg focus:ring-0 focus:border-gray-300',
          failure:
            'border-red-500 bg-white-500 text-gray-900 dark:text-white placeholder-gray-400 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-[#374151] dark:focus:border-red-500 dark:focus:ring-red-500',
        },
      },
    },
  };

  return (
    <div key={singleData?._id || singleData?.created} className="flex flex-row justify-between gap-x-4 gap-y-2 px-2 py-2 mx-2 rounded-lg">
      <div className="flex items-center grow gap-2">
        <span className="text-[#667085]">{`${singleData?.index < 10 ? `0${singleData?.index}` : singleData?.index}`}.</span>
        <TextInput
          theme={customTheme}
          className={`w-full ${singleData?.deleted && 'line-through'}`}
          value={singleData?.title}
          onChange={(value: any) =>
            setQuestionsListFilterdData((prev: { _id: string; title: string; updated: boolean }[]) =>
              prev.map((question) => (question._id === singleData._id ? { ...question, title: value.target.value, updated: true } : question))
            )
          }
          readOnly={singleData?.deleted || isViewOnly}
        />
      </div>

      {!isViewOnly && (
        <div className="flex items-center gap-4">
          {/* Undo button */}
          {singleData.deleted && (
            <Button
              label="Undo"
              size="sm"
              tertiary
              icon="cuida:undo-outline"
              width={'22'}
              onClick={() => {
                setQuestionsListFilterdData((prev: { _id: string; title: string; updated: boolean; deleted: boolean }[]) =>
                  prev.map((question) => (question._id === singleData._id ? { ...question, deleted: false } : question))
                );
              }}
            />
          )}

          {/* Delete button */}
          {!singleData.deleted && (
            <Button
              size="sm"
              tertiary
              icon="hugeicons:delete-02"
              width={'22'}
              className="text-[#B83434] cursor-pointer text-sm"
              onClick={() => {
                if (singleData?.created) {
                  setQuestionsListFilterdData((prev: { _id: string; title: string; updated: boolean }[]) =>
                    prev.filter((question) => question._id !== singleData._id)?.map((question, index) => ({ ...question, index: index + 1 }))
                  );
                } else {
                  setQuestionsListFilterdData((prev: { _id: string; title: string; updated: boolean; deleted: boolean }[]) =>
                    prev.map((question) => (question._id === singleData?._id ? { ...question, deleted: true } : question))
                  );
                }
              }}
            />
          )}
        </div>
      )}
    </div>
  );
};
