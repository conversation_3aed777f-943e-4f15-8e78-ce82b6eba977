// React
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Core
import { Table, Icon, NameFieldColumn, EmailFieldColumn, FormatDateFieldColumn } from 'src';

// Flowbite
import { Tooltip } from 'flowbite-react';
import { Api, useAppDispatch, Tags } from 'UI/src';
import { setErrorNotify } from 'UI';

type dataItem = {
  id: string;
  name: string;
  subscriptionPlanName: string;
  numberOfUsers: number;
  engagement: string;
  lastActive: Date;
};

export const OrganizationsAtRisk = ({ setOrganizationsAtRiskCount }: { setOrganizationsAtRiskCount: Dispatch<SetStateAction<number>> }) => {
  const [data, setData] = useState<[]>();

  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get('superAdmin/organizations/risk', {});
      console.log('superAdmin/organizations/risk', response.data);
      setData(response.data);
      setOrganizationsAtRiskCount(response.data.length);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  useEffect(() => {
    handleGet();
  }, []);

  return (
    <>
      <Table
        ready={!!data}
        loading={!data}
        hideJumbotron={true}
        columns={[
          {
            key: 'organizationName',
            label: 'Name',
            width: '25%',
            primary: true,
          },
          {
            key: 'plan',
            label: 'Plan',
            width: '15%',
            primary: true,
            inline: true,
          },
          {
            key: 'employees',
            label: 'Users',
            width: '15%',
            inline: true,
          },
          {
            key: 'tickets',
            label: 'Tickets',
            width: '15%',
          },
          {
            key: 'engagement',
            label: 'Engagement',
            width: '15%',
          },
          {
            key: 'lastActive',
            label: 'Last Active',
            width: '25%',
          },
        ]}
        rows={
          data?.slice(0, 5)?.map((item: dataItem) => ({
            ...item,
            _id: item.id,
            organization: item.name,
            plan: item.subscriptionPlanName,
            employees: item.numberOfUsers,
            engagement: item.engagement,
            lastActive: item.lastActive,
          })) || []
        }
        slots={{
          organizationName: (_: unknown, row: { _id: string; name: string; email: string }) => (
            <div className="flex flex-col gap-1">
              <NameFieldColumn id={row?._id} name={row?.name} onClick={() => navigate(`/app/organizations/profile/${row?._id}`)} />
              <EmailFieldColumn id={row?._id} email={row?.email} />
            </div>
          ),
          plan: (_: unknown, row: { subscriptionPlanName: string; endDate: Date }) => (
            <>
              <p className="capitalize dark:text-white text-nowrap mr-1">{row.subscriptionPlanName}</p>
              <div className="flex items-center gap-1">
                Expiry <FormatDateFieldColumn date={row?.endDate} />
              </div>
            </>
          ),
          engagement: (_: unknown, row: { engagement: string }) => {
            const getEngagementColor = (engagement: string) => {
              if (engagement === 'low') {
                return 'bg-[#FFECE9] text-[#A80000] ';
              } else if (engagement === 'medium') {
                return 'bg-[#FFFCDF] text-[#BA8500] ';
              } else if (engagement === 'high') {
                return 'bg-[#EEFFF1] text-[#056816]';
              } else if (engagement === 'inactive') {
                return 'bg-gray-100 text-gray-700 border border-gray-300';
              } else if (engagement === 'active') {
                return 'bg-green-100 text-green-700';
              } else {
                return 'bg-gray-100 text-gray-700 border border-gray-300';
              }
            };

            return <Tags type={row.engagement} color={getEngagementColor(row.engagement)} />;
          },

          // const handleEnagement = (engagement: string) => {
          //   if (engagement === 'low') {
          //     return {
          //       text: 'low',
          //       color: 'text-red-700',
          //       background: 'bg-red-100',
          //       border: 'border-red-300',
          //     };
          //   } else if (engagement === 'medium') {
          //     return {
          //       text: 'medium',
          //       color: 'text-orange-700',
          //       background: 'bg-orange-100',
          //       border: 'border-orange-300',
          //     };
          //   } else if (engagement === 'high') {
          //     return {
          //       text: 'high',
          //       color: 'text-green-700',
          //       background: 'bg-green-100',
          //       border: 'border-green-300',
          //     };
          //   } else if (engagement === 'inactive') {
          //     return {
          //       text: 'inactive',
          //       color: 'text-gray-700',
          //       background: 'bg-gray-100',
          //       border: 'border-gray-300',
          //     };
          //   } else if (engagement === 'active') {
          //     return {
          //       text: 'active',
          //       color: 'text-green-700',
          //       background: 'bg-green-100',
          //       border: 'border-green-300',
          //     };
          //   } else {
          //     return {
          //       text: '—',
          //       color: 'text-gray-700',
          //       background: 'bg-gray-100',
          //       border: 'border-gray-300',
          //     };
          //   }
          // };

          lastActive: (_: unknown, row: { lastActive: Date }) => <FormatDateFieldColumn date={row?.lastActive} />,
        }}
        rowKey="_id"
      />
    </>
  );
};
