import { useState } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell } from 'recharts';

import { Button, Icon } from '../../../src';
import { PlanFeatures } from '../constants/enums';
// Core components/utilities available in the project
import { Clock5, HelpCircle, ChartColumnIncreasing, type LucideIcon } from 'lucide-react';
import CheckFeatureManagement from '../../../src/composables/feature-management';

interface ApplicantAssessmentCardProps {
  test: any;
  type: 'submissions' | 'interviews' | 'screening' | string;
  className?: string;
}

export const ApplicantAssessmentCard = ({ test, type, className }: ApplicantAssessmentCardProps) => {
  const { checkFeature } = CheckFeatureManagement();

  const totalQuestions = test?.questionsSummary?.totalQuestions || 0;
  const correctAnswers = test?.questionsSummary?.correctAnswers || 0;
  const wrongAnswers = test?.questionsSummary?.wrongAnswers || 0;
  const unAnsweredQuestions = test?.questionsSummary?.unAnsweredQuestions || 0;

  const [isExtended, setIsExpended] = useState<boolean>(false);

  const timeTaken = test?.timeTaken || test?.totalTime || test?.durationInMinutes;

  const score = Math.round(((correctAnswers || 0) / (totalQuestions || 1)) * 100);

  const data = [
    { name: 'Wrong', value: 30 },
    { name: 'Correct', value: 20 },
    { name: 'Unanswered', value: 30 },
  ];

  const COLORS = ['#b91c1c', '#16a34a', '#94a3b8']; // red, green, gray

  const formatDate = (dateString?: string) => {
    if (!dateString) return '—';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', { dateStyle: 'medium' }).format(date);
  };

  const statusBadge = () => {
    if (score < 50) return { label: 'Poor', classes: 'bg-[#FDF2F2] text-[#EF4444]' };
    if (score <= 80) return { label: 'Good', classes: 'bg-[#FFF7ED] text-[#F59E0B]' };
    return { label: 'Excellent', classes: 'bg-[#ECFDF5] text-[#10B981]' };
  };

  const weird = test?.weirdBehavior || {};

  const isSuspiciusBehavior = weird?.tabSwitchedCount > 0 || weird?.ipChangeCount > 0 || weird?.openContextMenuCount > 0;
  console.log(weird?.tabSwitchedCount > 0 || weird?.ipChangeCount > 0 || weird?.openContextMenuCount > 0);
  const handleExportReport = () => {
    if (checkFeature(PlanFeatures.EXPORT_REPORTS)) {
      // Try different ID properties
      const testId = test._id || test.id || test.assessmentId;
      if (testId) {
        window.open(`/app/tests/pdf/${testId}?type=${type}`, '_blank', 'noopener,noreferrer');
      } else {
        console.error('No valid ID found in test object');
      }
    }
  };

  console.log(test);

  const blocksData: { header: string; subHeader: any; definedIcon: LucideIcon }[] = [
    // {
    //   header: 'Seniority',
    //   definedIcon: User,
    //   subHeader: Enums.QuizDifficulty[seniority as keyof typeof Enums.QuizDifficulty],
    // },
    {
      header: 'Difficulty',
      definedIcon: ChartColumnIncreasing,
      subHeader: test.difficulty,
    },
    {
      header: 'Questions',
      definedIcon: HelpCircle,
      subHeader: test.numOfQuestions,
    },
    {
      header: 'Duration',
      definedIcon: Clock5,
      subHeader: `${test.duration} min`,
    },
  ];

  return (
    <div className={`bg-white dark:bg-darkBackgroundCard border border-gray-200 dark:border-[#374151] rounded-xl shadow-sm ${className}`}>
      {/* Header */}
      <div className="p-4 pb-0">
        <div className="flex items-start justify-between  gap-3">
          <div className="min-w-0">
            <div className="flex items-center gap-2 flex-wrap">
              <h3 className="text-[#111827] dark:text-white thepassHtwo truncate">{test?.title || test?.name || 'Assessment'}</h3>
              <span className={`px-2 py-0.5 rounded-md text-xs font-medium ${statusBadge().classes}`}>{statusBadge().label}</span>

              <span className="flex items-center gap-1 px-2 py-1 rounded-md text-xs font-medium bg-[#EEF2FF] text-[#6366F1]">
                <svg width="15" height="11" viewBox="0 0 16 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M14.8835 6.09564L11.1366 4.88206L9.73741 0.869588C9.71098 0.79422 9.65761 0.728259 9.58516 0.681398C9.5127 0.634538 9.42498 0.609248 9.33488 0.609248C9.24478 0.609248 9.15706 0.634538 9.08461 0.681398C9.01215 0.728259 8.95879 0.79422 8.93236 0.869588L7.53362 4.88206L3.78625 6.09564C3.70567 6.12181 3.63618 6.16893 3.5871 6.23068C3.53802 6.29242 3.51172 6.36583 3.51172 6.44103C3.51172 6.51624 3.53802 6.58965 3.5871 6.65139C3.63618 6.71314 3.70567 6.76026 3.78625 6.78643L7.53194 7.99964L8.93109 12.1344C8.95684 12.2107 9.01008 12.2776 9.08285 12.3251C9.15562 12.3727 9.24404 12.3984 9.33488 12.3984C9.42573 12.3984 9.51414 12.3727 9.58692 12.3251C9.65969 12.2776 9.71293 12.2107 9.73867 12.1344L11.1383 7.99964L14.8839 6.78643C14.9649 6.76054 15.0347 6.7135 15.0841 6.6517C15.1335 6.58989 15.1599 6.5163 15.1599 6.44091C15.1598 6.36551 15.1333 6.29194 15.0838 6.23018C15.0344 6.16843 14.9645 6.12145 14.8835 6.09564Z"
                    fill="#A47BFA"
                  />
                  <path
                    d="M6.46472 10.1473L5.49083 9.83193L5.11188 8.61614C5.08764 8.53809 5.03472 8.46919 4.96129 8.42007C4.88786 8.37095 4.798 8.34434 4.70557 8.34434C4.61313 8.34434 4.52327 8.37095 4.44984 8.42007C4.37641 8.46919 4.32349 8.53809 4.29925 8.61614L3.9203 9.83193L2.94641 10.1473C2.86583 10.1735 2.79633 10.2206 2.74726 10.2823C2.69818 10.3441 2.67188 10.4175 2.67188 10.4927C2.67188 10.5679 2.69818 10.6413 2.74726 10.703C2.79633 10.7648 2.86583 10.8119 2.94641 10.8381L3.91525 11.1523L4.29714 12.4868C4.31996 12.5664 4.37254 12.6371 4.44654 12.6877C4.52054 12.7382 4.61171 12.7656 4.70557 12.7656C4.79942 12.7656 4.89059 12.7382 4.96459 12.6877C5.03859 12.6371 5.09117 12.5664 5.11399 12.4868L5.49588 11.1523L6.46472 10.8381C6.5453 10.8119 6.6148 10.7648 6.66388 10.703C6.71295 10.6413 6.73926 10.5679 6.73926 10.4927C6.73926 10.4175 6.71295 10.3441 6.66388 10.2823C6.6148 10.2206 6.5453 10.1735 6.46472 10.1473Z"
                    fill="#FCAB40"
                  />
                  <path
                    d="M4.63717 2.16281L3.64138 1.84044L3.27296 0.969125C3.24313 0.898495 3.18928 0.837558 3.11867 0.79452C3.04805 0.751483 2.96406 0.728411 2.87801 0.728411C2.79195 0.728411 2.70796 0.751483 2.63735 0.79452C2.56674 0.837558 2.51289 0.898495 2.48306 0.969125L2.11422 1.84044L1.11885 2.16281C1.03813 2.18891 0.96849 2.23603 0.919304 2.29781C0.870118 2.3596 0.84375 2.43309 0.84375 2.50839C0.84375 2.58369 0.870118 2.65718 0.919304 2.71896C0.96849 2.78075 1.03813 2.82787 1.11885 2.85397L2.11422 3.17634L2.48306 4.04765C2.51271 4.11844 2.56649 4.17956 2.63712 4.22273C2.70776 4.26591 2.79184 4.28906 2.87801 4.28906C2.96417 4.28906 3.04826 4.26591 3.11889 4.22273C3.18953 4.17956 3.24331 4.11844 3.27296 4.04765L3.64138 3.17634L4.63717 2.85397C4.71789 2.82787 4.78753 2.78075 4.83671 2.71896C4.8859 2.65718 4.91227 2.58369 4.91227 2.50839C4.91227 2.43309 4.8859 2.3596 4.83671 2.29781C4.78753 2.23603 4.71789 2.18891 4.63717 2.16281Z"
                    fill="#5DADEC"
                  />
                </svg>
                Interactive
              </span>
            </div>
          </div>

          <div className="flex items-center gap-5">
            <Button size="sm" tertiary className="text-thepassBone xssm:min-w-[146px] cursor-pointer" onClick={handleExportReport}>
              <Icon icon="lucide:arrow-down-to-line" className={`xssm:mr-2 text-base text-[#743AF5]`} />
              <span className="xssm:block hidden">Export Report</span>
            </Button>
            <div className="cursor-pointer" onClick={() => setIsExpended(!isExtended)}>
              <Icon icon={!isExtended ? 'lucide:chevron-down' : 'lucide:chevron-up'} width="25" className="text-[#743AF5]" />
            </div>
          </div>
        </div>
        <div>
          <div className="flex flex-wrap gap-6 mb-4 mt-4">
            {blocksData?.map((block) => {
              const BlockIcon = block.definedIcon;
              return (
                <div key={block.header} className="flex items-center gap-2">
                  <BlockIcon className="size-5 text-[#743AF5]" />
                  <span className="thepassBtwo text-[#4E5E82]">{block?.header}:</span>
                  <span className="thepassBtwo text-[#2A3348]">{block?.subHeader}</span>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Weird behavior row */}
      {isSuspiciusBehavior ? (
        <div className="flex flex-wrap items-center gap-3 px-4 py-3 text-sm">
          <div className="flex items-center gap-3 text-[#EF4444]">
            <svg width="25" height="21" viewBox="0 0 25 21" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                fill-rule="evenodd"
                clip-rule="evenodd"
                d="M13.6751 0.778194C14.0423 0.965 14.3466 1.23593 14.5565 1.56284L24.6875 17.3451C25.3366 18.3563 24.9419 19.6444 23.806 20.2222C23.4481 20.4042 23.043 20.5 22.6308 20.5H2.36881C1.06055 20.5 0 19.5559 0 18.3913C0 18.0244 0.10758 17.6637 0.312107 17.3451L10.4431 1.56284C11.0922 0.551694 12.5392 0.200395 13.6751 0.778194ZM12.4363 13.9428C11.5903 13.9428 10.9558 14.4995 10.9558 15.2418C10.9558 16.0178 11.571 16.5745 12.4363 16.5745C13.2823 16.5745 13.9168 16.0178 13.9168 15.2587C13.9168 14.4995 13.2823 13.9428 12.4363 13.9428ZM13.6207 5.7717H11.2519V12.0977H13.6207V5.7717Z"
                fill="#A80000"
              />
            </svg>
            <span className="text-[#111827] dark:text-white thepassBtwo">Suspicious behavior:</span>
          </div>
          <div className="flex items-center gap-2 border-r border-[#DEE2E4] pr-3">
            <span className="text-[#3A4458] thepassBtwo">Tab Switches:</span>
            <span className="px-2 py-0.5 rounded-md bg-[#FEE2E2] text-[#EF4444] text-xs font-medium">{weird?.tabSwitchedCount || 0}</span>
          </div>
          <div className="flex items-center gap-2 border-r border-[#DEE2E4] pr-3">
            <span className="text-[#3A4458] thepassBtwo">IP Address Changes:</span>
            <span className="px-2 py-0.5 rounded-md bg-[#FEE2E2] text-[#EF4444] text-xs font-medium">{weird?.ipChangeCount || 0}</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-[#3A4458] thepassBtwo">Context Menu:</span>
            <span className="px-2 py-0.5 rounded-md bg-[#FEE2E2] text-[#EF4444] text-xs font-medium">{weird?.openContextMenuCount || 0}</span>
          </div>
        </div>
      ) : (
        <></>
      )}

      {/* Body */}
      {isExtended && (
        <div className="px-4 pb-4">
          <div className="rounded-lg border border-gray-200 dark:border-[#374151] bg-[#F9FAFB] dark:bg-transparent p-3">
            <div className="flex items-center justify-between text-sm text-[#6B7280]">
              <div className="flex items-center gap-2">
                <Icon icon="lucide:circle-play" width="18" className="text-[#743AF5]" />
                Started at: <span className="text-[#111827] dark:text-white font-medium">{formatDate(test?.startedAt)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Icon icon="lucide:clock-fading" width="18" className="text-[#743AF5]" />
                Time Taken: <span className="text-[#111827] dark:text-white font-medium">{timeTaken ? `${timeTaken} Mins` : '—'}</span>
              </div>
            </div>

            <div className="gap-4 items-center space-y-7">
              <div className="sm:col-span-1 flex justify-center">
                {/* <ResultChart test={{ score, status: 3, startedAt: test?.startedAt }} size={140} fontSize="24px" /> */}
                <div className="relative flex items-center justify-center">
                  <PieChart width={100} height={100}>
                    <Pie
                      data={data.flatMap((d) => [d, d])} // duplicate each item
                      startAngle={230}
                      endAngle={-50}
                      innerRadius={30}
                      outerRadius={40}
                      paddingAngle={6} // gap between slices
                      dataKey="value"
                      cornerRadius={5} // makes the ends rounded
                    >
                      {data.flatMap((_, index) => [
                        <Cell key={`cell-${index}-1`} fill={COLORS[index]} />,
                        <Cell key={`cell-${index}-2`} fill={COLORS[index]} />,
                      ])}
                    </Pie>
                  </PieChart>
                  <span className="absolute font-bold text-gray-800">{test.score}%</span>
                </div>
              </div>
              <div className="flex flex-wrap w-full justify-between gap-3">
                <div className="flex items-center gap-2 text-sm">
                  <span className="w-3 h-3 rounded-full bg-[#EF4444]"></span>
                  <span className="text-[#6B7280]">Wrong answers</span>
                  <span className="text-[#111827] dark:text-white font-medium">{wrongAnswers} Answers</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <span className="w-3 h-3 rounded-full bg-[#22C55E]"></span>
                  <span className="text-[#6B7280]">Correct answers</span>
                  <span className="text-[#111827] dark:text-white font-medium">{correctAnswers} Answers</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <span className="w-3 h-3 rounded-full bg-[#9CA3AF]"></span>
                  <span className="text-[#6B7280]">Unanswered questions</span>
                  <span className="text-[#111827] dark:text-white font-medium">
                    {unAnsweredQuestions} Question{unAnsweredQuestions > 1 && 's'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
