import React from 'react';
import { Icon } from '../';
export const NoDataMatches = ({ marginTop = 'mt-8', message = 'No results found' }) => {
  return (
    <>
      <div className={`flex flex-col items-center  justify-center h-full  gap-1 ${marginTop}`}>
        <Icon icon={'ic:sharp-search-off'} className="text-[4rem] dark:text-gray-500   text-gray-400" />
        {message && <p className=" text-gray-400 mt-3 text-lg font-light">{message}</p>}
      </div>
    </>
  );
};
