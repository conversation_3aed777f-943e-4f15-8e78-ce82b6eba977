import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';
import type { ApplicantType } from '../types/Applicants.type';

// Fetch single applicant
export const fetchApplicant = createAsyncThunk(
  'applicants/fetchApplicant',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get<ApplicantType>(`applicants/single/${id}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch applicant');
    }
  }
);

// Create applicant
export const createApplicant = createAsyncThunk(
  'applicants/createApplicant',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('applicants/single', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create applicant');
    }
  }
);

// Update applicant
export const updateApplicant = createAsyncThunk(
  'applicants/updateApplicant',
  async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
    try {
      const response = await Api.put(`applicants/single/${id}`, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to update applicant');
    }
  }
);

// Delete applicant
export const deleteApplicant = createAsyncThunk(
  'applicants/deleteApplicant',
  async (id: string, { rejectWithValue }) => {
    try {
      await Api.delete(`applicants/single/${id}`);
      return id;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to delete applicant');
    }
  }
);

// Delete multiple applicants
export const deleteMultipleApplicants = createAsyncThunk(
  'applicants/deleteMultipleApplicants',
  async (ids: string[], { rejectWithValue }) => {
    try {
      await Api.delete('applicants/multi', { ids });
      return ids;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to delete applicants');
    }
  }
);

// Get applicant assessments count
export const fetchApplicantAssessmentsCount = createAsyncThunk(
  'applicants/fetchAssessmentsCount',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`applicants/assessments/count/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch assessments count');
    }
  }
);

// Get applicant stages report
export const fetchApplicantStagesReport = createAsyncThunk(
  'applicants/fetchStagesReport',
  async ({ id, type }: { id: string; type: string }, { rejectWithValue }) => {
    try {
      const response = await Api.get(`${type === 'interview' ? 'ai-interview' : 'submissions'}/stages/report/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch stages report');
    }
  }
); 
