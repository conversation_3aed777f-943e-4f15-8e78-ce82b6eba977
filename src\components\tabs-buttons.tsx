import React, { useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import { Icon } from '../';

export const TabsButtons = () => {
  //Ref
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // React
  const location = useLocation();
  const navigate = useNavigate();

  //State
  const [controlButtons, setControlButtons] = useState(true);
  const [isAtStart, setIsAtStart] = useState(true);
  const [isAtEnd, setIsAtEnd] = useState(false);
  const [activeIndex, setActiveIndex] = useState<number>();

  //Data
  const tabsButtons = [
    {
      key: 1,
      title: 'Tests Bank',
      path: '/app/tests/bank',
    },
    {
      key: 2,
      title: 'Tests List',
      path: '/app/tests/list',
    },
    // {
    //   key: 3,
    //   title: 'Interviews List',
    //   path: '/app/tests/ai-interviews-list',
    // },
  ];

  const checkScrollPosition = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setIsAtStart(scrollLeft === 0);
      setIsAtEnd(Math.round(scrollLeft + clientWidth) >= scrollWidth);
    }
  };

  const scroll = (direction: string) => {
    if (scrollContainerRef.current) {
      const { scrollLeft, clientWidth } = scrollContainerRef.current;
      const scrollAmount = direction === 'next' ? clientWidth : -clientWidth;
      scrollContainerRef.current.scrollTo({
        left: scrollLeft + scrollAmount * 2,
        behavior: 'smooth',
      });
    }
  };

  const handleSetActiveIndex = () => {
    tabsButtons.forEach((tab) => {
      if (location.pathname.includes(tab.path)) {
        setActiveIndex(tab.key);
      }
    });
  };

  // @TODO: Remove both useEffect to view tabs buttons without swipper
  useEffect(() => {
    handleSetActiveIndex();

    //initially start at the left
    if (scrollContainerRef.current) {
      const { scrollWidth, clientWidth } = scrollContainerRef.current;
      scrollContainerRef.current.scrollTo({
        left: false ? 200 : 0,
        behavior: 'smooth',
      });
      scrollContainerRef.current.addEventListener('scroll', checkScrollPosition);
    }
    if (window.innerWidth < 560) {
      setControlButtons(true);
    } else {
      setControlButtons(false);
    }
  }, []);

  useEffect(() => {
    if (activeIndex === 3 && scrollContainerRef.current) {
      const { scrollLeft, clientWidth } = scrollContainerRef.current;
      scrollContainerRef.current.scrollTo({
        left: 200,
        behavior: 'smooth',
      });
    }
  }, [activeIndex]);

  return (
    <>
      {/* @TODO: Switch to the hashed code to view tabs buttons without swipper */}
      <div
        className={`w-fit dark:bg-darkBackgroundCard dark:shadow-none shadow-[0px_4px_14px_0px_rgba(195,195,195,0.22)] ${
          !controlButtons ? 'flex' : 'grid grid-cols-10'
        } rounded-3xl bg-white p-1 justify-center items-center`}
      >
        <button
          className={`w-fit text-gray-500 ${isAtStart ? 'opacity-50 cursor-not-allowed' : 'hover:text-gray-800'} ${
            controlButtons ? 'block' : 'hidden'
          }`}
          onClick={() => scroll('prev')}
        >
          <Icon icon="ep:arrow-left-bold" />
        </button>
        <div className="col-span-8 overflow-hidden scroll-smooth" ref={scrollContainerRef}>
          <div className="flex flex-row shrink md:gap-[10px] md:p-1">
            {tabsButtons?.map((item) => (
              <button
                key={item.path}
                onClick={() => navigate(item.path)}
                className={`px-3 py-2 md:px-[22px] md:py-[11px] text-sm font-medium text-secondaryGray whitespace-nowrap ${
                  location.pathname.includes(item.path) && 'bg-primaryPurple text-white rounded-3xl'
                }`}
              >
                {item.title}
              </button>
            ))}
          </div>
        </div>
        <button
          className={`justify-self-center w-fit text-gray-500 ${isAtEnd ? 'opacity-50 cursor-not-allowed' : 'hover:text-gray-300'} ${
            controlButtons ? 'block' : 'hidden'
          }`}
          onClick={() => scroll('next')}
        >
          <Icon icon="ep:arrow-right-bold" />
        </button>
      </div>
    </>
  );
};
