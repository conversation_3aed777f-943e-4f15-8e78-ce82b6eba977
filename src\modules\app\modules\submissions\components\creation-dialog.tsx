// React
import React, { FC, ReactNode, useEffect } from 'react';
import { FaCheckCircle, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import doneMark from 'images/Vector.svg';
// Components
import { Drawer, Select, Icon, EnumText, CustomIcon } from 'src';
import { Dialog, Button } from 'UI';
import { FaUserGraduate, FaUser, FaStar, FaMedal } from 'react-icons/fa';
import { TimeSettingsDialog } from 'src/modules/app/modules/applicants/components/assign-time-settings.jsx';

import {
  QuizType,
  Api,
  Regex,
  useValidate,
  initializeForm,
  resetForm,
  RootState,
  setFieldValue,
  useAppSelector,
  useAppDispatch,
  UserData,
  QuizDifficulty,
} from 'UI/src';
import {
  setCreationDialogLoading,
  setQuizUrl,
  setSubmissionId,
  setApplicants,
  setQuizzes,
  setSingleQuiz,
  setSearchResult,
  setEmailRegex,
  setExpandedSections,
  setExpandedAll,
  setStartDate,
  setDueDate,
  setExtraTime,
  setTimeSettingsVisible,
  resetCreationDialog,
} from 'UI/src/slices/submission/submission.slice';

// Flowbite
import { Spinner } from 'flowbite-react';
import { Form } from 'UI/src';
import { setErrorNotify, setNotifyMessage } from 'UI';
// import { Clipboard } from 'flowbite-react';

// --- Type Definitions ---

type SubmissionsCreationDialogProps = {
  onClose: () => void;
  testId: string;
  back: boolean;
  backButton: () => void;
};

type FormShape = {
  applicantId: string;
  quizId: string;
};

type Applicant = {
  _id: string;
  email: string;
};

type QuizSearchResult = {
  _id: string;
  title: string;
};

type SubcategoryDetail = {
  subCategoryName: string;
  count: number;
  topics: string[];
};

type SingleQuiz = {
  difficulty: number;
  title: string;
  duration: number;
  numOfQuestions: number;
  subcategoryDetails: SubcategoryDetail[];
};

export const SubmissionsCreationDialog: FC<SubmissionsCreationDialogProps> = ({ onClose, testId, back, backButton }) => {
  // Constants
  const FORM_DEFAUT_VALUE: FormShape = {
    applicantId: '',
    quizId: '',
    // willSendEmail: false,
  };

  // Hooks
  const dispatch = useAppDispatch();

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  useEffect(() => {
    dispatch(initializeForm({ FORM_DEFAUT_VALUE }));
  }, []);

  const { isRequired } = useValidate();

  // Redux state
  const {
    loading,
    quizUrl,
    submissionId,
    applicants,
    quizzes,
    singleQuiz,
    searchResult,
    emailRegex,
    expandedSections,
    expandedAll,
    startDate,
    dueDate,
    extraTime,
    isTimeSettingsVisible,
  } = useAppSelector((state: RootState) => state.submission.creationDialog);
  const type = 'test';

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);
  // Methods
  const handleSearch =
    (endpoint: string, action: (data: any[]) => void, isCustomAdd = false) =>
    async (keyword: string) => {
      try {
        const result = await Api.get(endpoint, { keyword });
        action(result?.data);
        dispatch(setEmailRegex(false));
        if (isCustomAdd) {
          if (!result?.data?.length && !!keyword) {
            // setAddIconApplicant(true);
            dispatch(setSearchResult(keyword));
          } else {
            // setAddIconApplicant(false);
          }
        }
      } catch (error: any) {
        dispatch(setErrorNotify(error.response.data.message));
      }
    };

  const handleSubmit = async () => {
    try {
      dispatch(setCreationDialogLoading(true));

      let { applicantId, ...payload }: any = form;
      payload = {
        ...payload,
        otherTest: true,
        dueDate: dueDate,
        startDate: startDate,
      };
      if (extraTime >= 1) payload.exceededTime = extraTime;

      if (form.applicantId) payload.applicantId = [form.applicantId];

      const response = await Api.post('submissions/single', payload);

      // Reset form
      dispatch(resetForm());

      // Set Quiz URL
      dispatch(setQuizUrl(response.data.quizUrl));
      dispatch(setSubmissionId(response.data.submissionId));
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    } finally {
      dispatch(setCreationDialogLoading(false));
    }
  };

  const handelGet = async () => {
    try {
      // Add Submissions Details
      const response = await Api.get<QuizType>(`templates/single/custom/${testId}`, {});
      console.log(`templates/single/custom/${testId}`, response.data);
      // Map response to SingleQuiz
      const mapped: SingleQuiz = {
        ...response.data,
        subcategoryDetails: (response.data as any).subcategoryDetails || [],
      };
      dispatch(setSingleQuiz(mapped));
      dispatch(setFieldValue({ path: 'quizId', value: testId }));
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  const handleCopyLink = () => {
    navigator.clipboard.writeText(quizUrl);
    dispatch(setNotifyMessage('Link copied'));
  };

  const generateDescription = () => {
    if (!singleQuiz) return null;
    const totalCategories = singleQuiz.subcategoryDetails.length;

    // const totalTopics = singleQuiz.subcategoryDetails.reduce((sum, sub) => sum + sub.topics.length, 0);
    const totalTopics = singleQuiz.subcategoryDetails.reduce((sum: number, sub: SubcategoryDetail) => sum + sub.topics.length, 0);

    // let difficultyText = '';
    // switch (details.difficulty) {
    //   case 1:
    //     difficultyText = 'suitable for interns';
    //     break;
    //   case 2:
    //     difficultyText = 'ideal for fresh graduates';
    //     break;
    //   case 3:
    //     difficultyText = 'great for junior-level candidates';
    //     break;
    //   case 4:
    //     difficultyText = 'targeted for mid-level professionals';
    //     break;
    //   case 5:
    //     difficultyText = 'challenging for senior professionals';
    //     break;
    //   default:
    //     difficultyText = '';
    // }

    return (
      <>
        This test covers{' '}
        <span className="font-semibold">
          {' '}
          {totalCategories} {totalCategories > 1 ? 'categories' : 'category'}{' '}
        </span>{' '}
        and includes{' '}
        <span className="font-semibold">
          {' '}
          {totalTopics} {totalTopics > 1 ? 'topics' : 'topic'}{' '}
        </span>
      </>
    );
  };

  // Check on quiz diffuculty
  let difficultyIcon: ReactNode;
  let difficultyColor: string | undefined;
  let iconSize = 'text-[15px]';
  if (singleQuiz) {
    switch (singleQuiz.difficulty) {
      case 1:
        difficultyIcon = <FaUserGraduate className={`${iconSize} text-teal-700`} />; // Intern
        difficultyColor = ' text-teal-700 ';
        break;
      // Star Icon fresh level
      case 2:
        difficultyIcon = <FaUser className={`${iconSize} text-sky-800`} />; // Fresh
        difficultyColor = 'text-sky-800 ';
        break;
      // Medal Star junior
      case 3:
        difficultyIcon = <FaStar className={`${iconSize} text-amber-700`} />; // Junior
        difficultyColor = ' text-amber-700 ';
        break;
      // betetr medal star midlevel
      case 4:
        difficultyIcon = <FaMedal className={`${iconSize} text-orange-700`} />; // Mid-level
        difficultyColor = 'text-orange-700';
        break;
      // Tropy icon for senior with star
      case 5:
        difficultyIcon = <Icon icon="solar:crown-star-bold" width="18" className={`${iconSize} text-red-800`} />; // Senior
        difficultyColor = 'text-red-800';
        break;
      default:
        difficultyIcon = null;
    }
  }

  const handleCountTopics = (tests: SubcategoryDetail[] | undefined) => {
    if (!tests) return '0 Topics';
    let topicsCount = 0;
    tests.map((test: SubcategoryDetail) => (topicsCount += test?.topics?.length));
    return `${topicsCount} ${topicsCount > 1 ? 'Topics' : 'Topic'}`;
  };

  // On Mount
  useEffect(() => {
    if (testId) {
      handelGet();
    }
  }, [testId]);

  // Sync individual sections' state with the "expand all/collapse all" action
  useEffect(() => {
    //  TODO:Markos solveThis
    //  const newState = {};
    //   singleQuiz?.subcategoryDetails.forEach((_, index) => {
    //     newState[index] = expandedAll; // Set all sections to expanded or collapsed based on expandedAll prop
    //   });
    //   setExpandedSections(newState);

    if (singleQuiz?.subcategoryDetails) {
      const newState: Record<number, boolean> = {};
      singleQuiz.subcategoryDetails.forEach((_, index) => {
        newState[index] = expandedAll;
      });
      dispatch(setExpandedSections(newState));
    }
  }, [expandedAll, singleQuiz?.subcategoryDetails]);

  const modalHeader = () => {
    // TODO: Remove case for select test
    if (!quizUrl && !singleQuiz) {
      return 'Select Test';
    } else if (!quizUrl && singleQuiz) {
      return 'Assign Test';
    } else {
      return '';
      // Test Created Successfully!
    }
  };

  return (
    <>
      <Drawer onClose={onClose}>
        <Drawer.SingleView>
          <Drawer.Header headerLabel="Assign Test" onClose={onClose} className="border-b border-[#E5E7EB] pb-2" />
          <Drawer.Body className="space-y-3 overflow-auto">
            {singleQuiz && testId ? (
              <div className="p-1 xssm:p-3 sm:p-4 space-y-2 sm:space-y-3 bg-[#F9FAFB] dark:bg-darkGrayBackground border border-[#F2F2F2] rounded-md">
                {/* Difficuluty */}
                <span className={`flex gap-1 items-center text-xs sm:text-sm ${difficultyColor}`}>
                  <span>{difficultyIcon}</span>
                  <span>{QuizDifficulty[singleQuiz.difficulty]} Level</span>
                </span>

                {/* Page title (Test Name all) */}
                <p className="text-sm sm:text-base font-medium capitalize dark:text-white">{singleQuiz.title}</p>

                {/* Meta Data */}
                <div className="flex flex-wrap items-center gap-1 xssm:gap-4 text-gray-500 dark:text-gray-300 text-xs sm:text-sm font-medium">
                  {/* Duration */}
                  <div className="flex gap-1 xssm:gap-2 items-center">
                    <CustomIcon definedIcon="clock" className="text-[#D07EAA]" />
                    <span className="text-gray-500 dark:text-gray-300">{singleQuiz.duration} mins</span>
                  </div>

                  {/* Questions */}
                  <div className="flex gap-1 xssm:gap-2 items-center">
                    <CustomIcon definedIcon="questionsCircle" className="text-[#D07EAA]" />
                    <span className="text-gray-500 dark:text-gray-300">
                      {singleQuiz.numOfQuestions} {singleQuiz.numOfQuestions > 1 ? 'Questions' : 'Question'}
                    </span>
                  </div>

                  {/* Topics */}
                  <div className="flex gap-1 xssm:gap-2 items-center">
                    <CustomIcon definedIcon="book" className="text-[#D07EAA]" />
                    <span className="text-gray-500 dark:text-gray-300">{handleCountTopics(singleQuiz?.subcategoryDetails)}</span>
                  </div>
                </div>

                {/* Start of What's Inside ? */}
                {/* <div className="flex flex-col gap-2">
                  <div className="flex justify-between items-center flex-wrap ">
                    <p className="text-lg font-semibold text-gray-900 dark:text-white  ">What's Inside?</p>

                    <div
                      className="hover:underline focus:outline-none text-primaryPurple text-opacity-90 font-medium text-base cursor-pointer"
                      onClick={() => dispatch(setExpandedAll(!expandedAll))}
                    >
                      {expandedAll ? 'Collapse all sections' : 'Expand all sections'}
                    </div>
                  </div>
                  <p className="text-[#808080] dark:text-gray-400 text-[15px] pb-1"> {generateDescription()}</p>
                </div> */}

                <div className="!hidden w-full bg-white dark:bg-gray-800 rounded-xl py-2  mt-2 border-1 border border-gray-200 dark:border-none">
                  {/* Sub category breakdown */}
                  <div className="space-y-2">
                    {singleQuiz.subcategoryDetails.map((sub, subIndex) => (
                      <div
                        key={subIndex}
                        className={`py-2 ${
                          subIndex === singleQuiz.subcategoryDetails.length - 1 ? '' : 'border-b border-gray-200 dark:border-gray-700'
                        }`}
                      >
                        {/* Subcategory Header */}
                        <div className="flex justify-between items-center px-4 flex-wrap">
                          <div className="text-base font-medium text-gray-700 dark:text-white">
                            {sub.subCategoryName}
                            <span className="text-sm font-normal text-gray-500 dark:text-gray-400 mx-2">
                              {' '}
                              ( {sub.count}
                              {sub.count > 1 ? ' Questions' : ' Question'} )
                            </span>
                          </div>
                          <div
                            onClick={() => {
                              dispatch(setExpandedSections({ ...expandedSections, [subIndex]: !expandedSections[subIndex] }));
                            }}
                            className=" cursor-pointer text-gray-700 dark:text-gray-300 text-opacity-90  hover:underline focus:outline-none text-[13px] font-medium flex items-center"
                          >
                            {expandedSections[subIndex] ? 'Hide Topics' : 'Show Topics'}
                            {expandedSections[subIndex] ? <FaChevronUp className="mx-2 " /> : <FaChevronDown className="mx-2 " />}
                          </div>
                        </div>

                        {/* Subcategory Topics */}
                        {expandedSections[subIndex] && (
                          <div className="mt-3 space-y-1 px-4">
                            {sub.topics.map((topic, index) => (
                              <div key={index} className="flex gap-2 items-center ">
                                <FaCheckCircle className="text-gray-500 dark:text-gray-600 text-[15px]" />
                                <p className="flex items-center font-medium text-[15px] text-gray-500 dark:text-gray-300">{topic}</p>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
                {/* End of What's Inside ? */}
              </div>
            ) : (
              <Spinner />
            )}
            {/* TODO:Markos */}
            {/* @ts-ignore */}

            <Drawer.Body.DatePicker
              startDate={startDate}
              dueDate={dueDate}
              extraTime={extraTime}
              setExtraTime={() => dispatch(setExtraTime(extraTime + 1))}
              setTimeSettingsVisible={() => dispatch(setTimeSettingsVisible(true))}
              type={type}
              // loading={loading}
            />

            <Form className="space-y-4" onSubmit={() => {}}>
              {!testId && (
                <Select
                  name="quizId"
                  label="Test"
                  placeholder="Find tests.."
                  value={form.quizId}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'quizId', value }))}
                  onSearch={handleSearch('quizzes/search', (data) => dispatch(setQuizzes(data)))}
                  disabled={loading}
                  lookup={quizzes.map(({ title, _id }) => ({
                    label: title,
                    value: _id,
                  }))}
                  validators={[isRequired()]}
                />
              )}
              <div className="relative">
                <Select
                  name="applicantId"
                  label="Applicant"
                  placeholder={userData ? 'Find applicants by email...' : 'Enter applicant...'}
                  value={form.applicantId}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'applicantId', value }))}
                  // @FIXME: (isCustomAdd = true)
                  onSearch={handleSearch('applicants/search', (data) => dispatch(setApplicants(data)), true)}
                  // creationOptions={{
                  //   url: 'applicants/single/custom',
                  //   fieldName: 'email',
                  //   validation: Regex.email,
                  // }}
                  disabled={loading}
                  lookup={userData ? applicants : []}
                  optionValueKey="_id"
                  optionLabelKey="email"
                  isCustomValue={true}
                  validators={[]}
                />
              </div>

              <div className="pt-2 space-y-4">
                {/* <Checkbox
              name="willSendEmail"
              label="Send link via applicant email"
              value={form.willSendEmail}
              onChange={setFieldValue('willSendEmail')}
              disabled={form.applicantId.length === 0 || loading}
              preventSendingMail={form.applicantId.length === 0 || loading}
            /> */}
              </div>
            </Form>
          </Drawer.Body>

          <div className="flex gap-2">
            {back && <Button type="button" label="Back" colorType="secondary" disabled={loading} onClick={backButton} />}
            <Button
              type="button"
              onClick={handleSubmit}
              label="Assign"
              className="w-full"
              loading={loading}
              disabled={loading || !form.applicantId}
            />
          </div>
        </Drawer.SingleView>
      </Drawer>

      {isTimeSettingsVisible && (
        <TimeSettingsDialog
          onClose={() => dispatch(setTimeSettingsVisible(false))}
          startDate={startDate}
          setStartDate={(date) => dispatch(setStartDate(date))}
          dueDate={dueDate}
          setDueDate={(date) => dispatch(setDueDate(date))}
          type={type}
        />
      )}

      {!!quizUrl && (
        <Dialog isOpen size="lg" title={modalHeader()} onClose={onClose}>
          {userData ? (
            // image true
            <div className="py-5  pt-0">
              <div>
                <div className="flex justify-center items-center mx-auto mb-5 !mt-0  text-gray-400 dark:text-gray-200">
                  <img src={doneMark} alt="done mark" />
                </div>
                {/* <hr className="h-px my-2 bg-gray-200 border-0 dark:bg-gray-700" /> */}
                <div className="text-center">
                  <h2 className="text-center dark:text-white font-medium text-xl ">Test Assigned Successfully!</h2>
                  <div className=" w-72 text-center mx-auto mt-2">
                    <p className="text-center font-normal text-base   dark:text-white text-[#626262]">
                      Send the link below for quick and easy access to the applicant
                    </p>
                  </div>
                </div>
                <div className="mt-5 py-1">
                  <div className="grid w-full max-w-80 mx-auto text-center">
                    <div className="relative flex ">
                      <input
                        value={quizUrl}
                        type="text"
                        className="col-span-6 block w-full rounded-lg border border-gray-300 bg-gray-50 px-2.5 py-4 text-sm text-[#313437] pr-12 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-400 dark:placeholder:text-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                      />
                      <Icon
                        icon={'clarity:copy-line'}
                        onClick={() => handleCopyLink()}
                        className="dark:text-white cursor-pointer text-gray-400  text-2xl absolute right-3 top-1/4"
                        width="22"
                      />
                      {/* <Clipboard.WithIconText valueToCopy="npm install flowbite-react" /> */}
                    </div>
                  </div>

                  {/* <Button
                className="w-full"
                outline
                label="Copy Link"
                icon="material-symbols:content-copy-outline-rounded"
                onClick={() => handleCopyLink()}
              /> */}
                  {/* {form.applicantId && (
                <Button className="w-9/12 mx-auto mt-8" label="View Progress" icon="pajamas:progress" to={`/app/tests/result/view/${submissionId}`} />
              )} */}
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <span className="text-gray-900 dark:text-white">Please log in to copy test link</span>
            </div>
          )}
        </Dialog>
      )}
    </>
  );
};
