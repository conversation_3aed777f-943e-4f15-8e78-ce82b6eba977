import { createContext, useState } from 'react';

// Inputs
export * from './inputs';

export const FormContext = createContext({});

interface FormProps {
  children: React.ReactNode;
  onSubmit?: (e: React.FormEvent<HTMLFormElement>) => void;
  [key: string]: any;
}

export const Form: React.FC<FormProps> = ({ children, onSubmit, ...props }) => {
  const [fields, setFields] = useState<{ [key: string]: any }>({});

  // Provider
  const provider = {
    // Methods
    registerField(name: string, field: { runValidations: () => boolean }): void {
      setFields((draft) => {
        draft[name] = field;
      });
    },
  };

  // Methods
  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const result = Object.values(fields)?.map((field) => {
      return field.runValidations();
    });

    const isAllFieldsValid = result.every((isValid) => isValid);

    // Make sure that all validations passed
    if (isAllFieldsValid) {
      // TODO: Markos
      onSubmit?.(e);
    }
  };

  return (
    <FormContext.Provider value={provider}>
      <form onSubmit={handleSubmit} {...props}>
        {children}
      </form>
    </FormContext.Provider>
  );
};
