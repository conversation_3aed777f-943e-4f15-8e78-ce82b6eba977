// React
import { FC, useState, useContext, useEffect } from 'react';

// UI
import { Api } from 'UI/src';
import { RootState, useAppDispatch, useAppSelector } from 'UI/src';
import { setErrorNotify } from 'UI';
import { Submission } from '../onboarding/index';

// types

type SubmissionContextType = {
  submission: Submission;
};

export const StepperHeader: FC = () => {
  const { submission } = useAppSelector((state: RootState) => state.submission);

  // Hooks
  const dispatch = useAppDispatch();

  // State
  const [isBookMarked, setIsBookMarked] = useState(false);

  const handleGet = async () => {
    try {
      const response = await Api.get(`stages/single/${submission.stage?._id}`);
      setIsBookMarked(response.data);
    } catch (error) {
      dispatch(setErrorNotify(error instanceof Error ? error.message : String(error)));
    }
  };

  // On Mount
  useEffect(() => {
    handleGet();
  }, [submission?.stage]);

  return (
    <div className="flex flex-row justify-between items-baseline ">
      {/* Title */}
      <h1 className="text-xl font-medium text-[#798296] dark:text-white flex align-middle items-center">
        Question {submission.stage?.index}
        <span className="text-gray-500 font-medium text-xl px-1"> of {submission?.quiz?.questionIds?.length}</span>
      </h1>
    </div>
  );
};
