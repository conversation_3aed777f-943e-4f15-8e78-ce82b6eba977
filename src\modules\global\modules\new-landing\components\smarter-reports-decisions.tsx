// Core
import { Checkbox, Button, CustomIcon, Icon } from 'src';

// Flowbite
import { Label } from 'flowbite-react';
import smarterReports from 'images/landing/smarter-reports.png';
import gradientBackground from 'images/landing/gradientBackground.png';
const data = [
  { id: 1, text: 'Detailed Applicant Reports' },
  { id: 2, text: 'Assessment Analytics' },
  { id: 3, text: 'Top Performer Highlights' },
  { id: 4, text: 'Export Reports & Results' },
];
export const SmarterReportsDecisions = () => {
  const data: { id: number; text: string }[] = [
    { id: 1, text: 'Detailed Applicant Reports' },
    { id: 2, text: 'Assessment Analytics' },
    { id: 3, text: 'Top Performer Highlights' },
    { id: 4, text: 'Export Reports & Results' },
  ];

  return (
    <div className="py-7 mt-9 mb-10   ">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row lg:justify-around items-center justify-center gap-y-4 gap-x-40">
          {/* Image and Left Content */}
          <div className="hidden lg:block ">
            <div className="relative">
              <img src={smarterReports} alt="Reports" className="w-full max-w-full scale-125 sm:scale-110" />
              <img
                className="absolute top-0 left-0 sm:left-[60px] md:left-[90px] lg:left-[185px] opacity-40 z-[-1] w-full h-auto object-contain scale-125 hidden lg:block"
                src={gradientBackground}
              />
            </div>
          </div>

          {/* Text and Right Content */}
          <div className="flex flex-col justify-between items-center lg:items-start">
            <div className="w-fit h-8 sm:text-nowrap text-wrap bg-[#ddd7ff]/30 rounded-lg flex items-center justify-start mt-8 px-4 py-2 mb-9">
              <h2 className="text-sm text-[#8d5bf8] dark:text-white leading-5 font-semibold tracking-wider uppercase">Clear data smart decisions</h2>
            </div>

            <div className="flex flex-col space-y-4 pb-4">
              <div className="flex flex-col items-center lg:items-start mb-3 ">
                <span className="pt-1  text-center lg:text-start flex  items-center  text-base md:text-[55px]   gap-1  ">
                  <span className="font-semibold  gradient-text  p-1 py-2 text-transparent bg-clip-text">Smarter</span>
                  <span className="text-[#0D0F2C] font-semibold ">Reports</span>
                </span>
                <span className="text-[#0D0F2C]  text-center lg:text-start capitalize md:text-[55px] text-base pt-1  flex  items-center  gap-1   font-semibold ">
                  <span className="text-[#0D0F2C]">Smarter</span>{' '}
                  <span className="gradient-text  p-1 py-2 text-transparent bg-clip-text">Decisions</span>
                </span>
              </div>

              <div className="text-sm sm:thepassSubHone sm:px-0 px-8 sm:text-start text-center text-text-500 mb-5 pb-6">
                <p className="">Get full visibility into every applicant and assessment — from individual scores to overall test performance.</p>
              </div>
              <div className="lg:hidden">
                <div className="relative">
                  <img src={smarterReports} className="w-full scale-90" alt="Reports" />
                  <img src={gradientBackground} className="absolute top-0 left-[125px] opacity-40 z-[-1] scale-150 hidden lg:block" />
                </div>
              </div>
            </div>

            <div className="flex items-center justify-start md:justify-center w-full ">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-fit mb-7 sm:gap-3  ">
                {data.map((item) => (
                  <div key={item.id} className="flex items-center">
                    {/* TODO: Markos */}
                    <Checkbox className="text-[#704EE6]" value={true} onChange={() => {}} name="" label="" />
                    <Label htmlFor={`item-${item.id}`} className="flex text-[18px] font-medium text-nowrap gap-x-6 text-text-500 ml-2">
                      {item.text}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* <Button label="Try our Reports" Navigate to="/app/assessments/test" className="">
              <Icon icon="si:arrow-right-fill" width={'30'} className="ml-2" />
            </Button> */}
          </div>
        </div>
      </div>
    </div>
  );
};
