# API Middleware Functions

This directory contains async thunk functions that handle API calls throughout the application. These functions provide a centralized way to manage API requests with proper error handling and loading states.

## Overview

All middleware functions follow the same pattern:
- Use `createAsyncThunk` from Redux Toolkit
- Handle errors with `rejectWithValue`
- Return the response data on success
- Provide meaningful error messages

## Available Middleware Files

### 1. Applicants.middleware.ts
- `fetchApplicant(id: string)` - Fetch single applicant
- `createApplicant(payload: any)` - Create new applicant
- `updateApplicant({ id, data })` - Update existing applicant
- `deleteApplicant(id: string)` - Delete applicant
- `deleteMultipleApplicants(ids: string[])` - Delete multiple applicants
- `fetchApplicantAssessmentsCount(id: string)` - Get applicant assessments count
- `fetchApplicantStagesReport({ id, type })` - Get applicant stages report

### 2. Users.middleware.ts
- `fetchUser(id: string)` - Fetch single user
- `createUser(userData: any)` - Create new user
- `updateUser({ id, data })` - Update existing user
- `searchUsers({ endpoint, keyword })` - Search users

### 3. Quizzes.middleware.ts
- `fetchQuiz(id: string)` - Fetch single quiz
- `fetchCustomQuiz(id: string)` - Fetch custom quiz
- `createQuiz(payload: any)` - Create new quiz
- `updateQuiz({ id, data })` - Update existing quiz
- `deleteQuiz(id: string)` - Delete quiz
- `deleteMultipleQuizzes(ids: string[])` - Delete multiple quizzes
- `searchQuizzes(keyword: string)` - Search quizzes
- `fetchQuizQuestion(id: string)` - Fetch question for quiz
- `generateQuizQuestions(payload: any)` - Generate questions
- `getQuizQuestionsTotal(params: any)` - Get questions total

### 4. Submissions.middleware.ts
- `fetchSubmissionDetails(id: string)` - Fetch single submission
- `createSubmission(payload: any)` - Create new submission
- `deleteSubmission(id: string)` - Delete submission
- `deleteMultipleSubmissions(ids: string[])` - Delete multiple submissions
- `lockSubmission({ submissionId, status })` - Lock submission
- `lockMultipleSubmissions({ ids, status })` - Lock multiple submissions
- `fetchSubmissionStages({ submissionId, page, size })` - Get submission stages
- `fetchSubmissionBehavior(id: string)` - Get submission behavior
- `fetchSubmissionStagesReport(id: string)` - Get submission stages report
- `fetchSubmissionsReport(params: any)` - Get submissions report

### 5. Organizations.middleware.ts
- `fetchOrganization(id: string)` - Fetch single organization
- `updateOrganization({ id, data })` - Update organization
- `fetchOrganizationActivityLogs(params: any)` - Get activity logs
- `fetchOrganizationEngagement(id: string)` - Get engagement data
- `fetchOrganizationOverview(id: string)` - Get overview data
- `fetchOrganizationGrowth(id: string)` - Get growth data
- `fetchOrganizationUsers(id: string)` - Get users data
- `fetchOrganizationPlanOverview(organizationId: string)` - Get plan overview

### 6. Auth.middleware.ts
- `login(credentials: { email, password })` - User login
- `logout()` - User logout
- `fetchUserProfile()` - Get user profile

### 7. Plans.middleware.ts
- `fetchPlan(planId: string)` - Fetch single plan
- `createSubscriptionVerify(payload: any)` - Create subscription verification

### 8. Categories.middleware.ts
- `fetchCategories(params: any)` - Fetch categories
- `createCategory(payload: any)` - Create category
- `updateCategory({ id, data })` - Update category
- `deleteCategory(id: string)` - Delete category
- `fetchSubcategories(params: any)` - Fetch subcategories
- `createSubcategory(payload: any)` - Create subcategory
- `updateSubcategory({ id, data })` - Update subcategory
- `deleteSubcategory(id: string)` - Delete subcategory

### 9. AiInterviews.middleware.ts
- `fetchAiInterview({ id, weirdBehavior, typicalBehavior })` - Fetch AI interview
- `createAiInterview(payload: any)` - Create AI interview
- `fetchAiInterviewStagesReport(id: string)` - Get stages report
- `fetchAiInterviewUploads({ assessmentId, applicantId })` - Get uploads
- `getAiInterviewUploadUrl({ mimeType, fileName })` - Get upload URL
- `aiInterviewTalk(payload: { interviewId, userAnswerText })` - Process AI talk
- `generateAiInterviewQuestions(payload: any)` - Generate AI questions

### 10. Dashboard.middleware.ts
- `fetchAssessmentStatistics(params: { start?, end? })` - Get assessment statistics
- `fetchSuperAdminOrganizationsSubscriptions(params: any)` - Get org subscriptions
- `fetchSuperAdminSubscriptionOverview()` - Get subscription overview
- `fetchSuperAdminOrganizationsGrowth()` - Get org growth
- `fetchSuperAdminOrganizationsRisk(params: any)` - Get org risk
- `fetchSuperAdminOrganizations()` - Get organizations
- `fetchSuperAdminEngagement()` - Get engagement
- `fetchSuperAdminAssessmentOverview()` - Get assessment overview

### 11. Questions.middleware.ts
- `fetchQuestion(id: string)` - Fetch single question
- `createQuestion(payload: any)` - Create question
- `updateQuestion({ id, data })` - Update question
- `deleteQuestion(id: string)` - Delete question
- `deleteMultipleQuestions(ids: string[])` - Delete multiple questions
- `generateQuestions(payload: any)` - Generate questions
- `getQuestionsTotal(params: any)` - Get questions total

### 12. Roles.middleware.ts
- `fetchRole(id: string)` - Fetch single role
- `createRole(payload: any)` - Create role
- `updateRole({ id, data })` - Update role
- `searchRolePermissions(keyword: string)` - Search permissions

### 13. PhoneScreening.middleware.ts
- `fetchPhoneScreening(id: string)` - Fetch phone screening
- `createPhoneScreening(payload: any)` - Create phone screening
- `updatePhoneScreening({ id, data })` - Update phone screening
- `fetchPhoneScreeningList(params: any)` - Get phone screening list
- `createPhoneScreeningSubmission(payload: any)` - Create submission

### 14. Assessments.middleware.ts
- `fetchAssessment(id: string)` - Fetch single assessment
- `createAssessment(payload: any)` - Create assessment
- `updateAssessment({ id, data })` - Update assessment
- `createScreeningSubmission(payload: any)` - Create screening submission
- `createRegularSubmission(payload: any)` - Create regular submission
- `createAssessmentAiInterview(payload: any)` - Create AI interview
- `fetchAssessmentQuiz(id: string)` - Get assessment quiz

### 15. Global.middleware.ts
- `sendContactEmail(payload: { type, ... })` - Send contact email
- `fetchCustomTemplate(quizId: string)` - Get custom template
- `fetchProgrammingTest(testId: string)` - Get programming test
- `createProgrammingTestSubmission(payload: any)` - Create submission
- `fetchProgrammingTestQuiz(quizId: string)` - Get programming quiz

### 16. AddCredits.middleware.ts
- `fetchQuotaList()` - Get quota list

## Usage Example

### Before (Direct API calls):
```typescript
import { Api } from 'UI/src';

const handleGet = async () => {
  try {
    setLoading(true);
    const response = await Api.get<PlanType>(`plans/single/${planId}`, {});
    setPlanData(response.data);
  } catch (error) {
    dispatch(setErrorNotify(error?.response?.data?.message));
  } finally {
    setLoading(false);
  }
};
```

### After (Using middleware):
```typescript
import { fetchPlan } from 'UI/src/middlewares/Plans.middleware';

const handleGet = async () => {
  if (!planId) return;
  try {
    setLoading(true);
    const result = await dispatch(fetchPlan(planId)).unwrap();
    setPlanData(result);
  } catch (error: any) {
    dispatch(setErrorNotify(error?.message || 'Failed to fetch plan'));
  } finally {
    setLoading(false);
  }
};
```

## Benefits

1. **Centralized Error Handling**: All API errors are handled consistently
2. **Type Safety**: Better TypeScript support with proper typing
3. **Loading States**: Automatic loading state management through Redux
4. **Reusability**: Functions can be reused across components
5. **Maintainability**: Easier to maintain and update API calls
6. **Testing**: Easier to test with mocked async thunks

## Migration Guide

To migrate existing API calls to use these middleware functions:

1. Import the required middleware function
2. Replace direct `Api.get/post/put/delete` calls with `dispatch(middlewareFunction()).unwrap()`
3. Update error handling to use `error.message` instead of `error.response.data.message`
4. Remove the `Api` import if no longer needed

## Error Handling

All middleware functions return errors in a consistent format:
- Success: Returns the API response data
- Error: Returns an error message string via `rejectWithValue`

The error message can be accessed via `error.message` when using `.unwrap()`. 
