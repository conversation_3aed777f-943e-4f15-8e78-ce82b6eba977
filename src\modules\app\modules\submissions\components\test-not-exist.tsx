import React, { FC } from 'react';

// Components
import { Icon } from 'src';
import { Dialog } from 'UI';

interface TestNotExistProps {
  onClose: () => void;
}

export const TestNotExist: FC<TestNotExistProps> = ({ onClose }) => {
  return (
    <Dialog isOpen size="md" onClose={onClose}>
      <div className="h-44">
        <Icon icon="ic:round-do-not-disturb-alt" width="80" className="text-gray-300 dark:text-gray-500" />
        <div className="text-center pt-4 text-secondaryGray dark:text-grayTextOnDarkMood text-xl font-medium">This test is not available</div>
      </div>
    </Dialog>
  );
};
