// React
import { useEffect, useState } from 'react';

// Core
import { setNotifyMessage, setErrorNotify } from 'UI';
import { Api, useAppDispatch, setFieldValue } from 'UI/src';

export const SubcategoriesAssignPage = ({ formData, disableButtons }: any) => {
  // Destructuring
  const { form } = formData || {};

  // State
  const [loading, setLoading] = useState(false);
  const [subCategoriesData, setSubCategoriesData] = useState<any>([]);

  // Hooks
  const dispatch = useAppDispatch();

  const handleGetSubCategories = async () => {
    try {
      setLoading(true);
      const payload: any = { categoryId: form?.categoryId };
      if (form.type === 'interview') payload.type = 'interview';
      const { data } = await Api.get('quizzes/list/subCategory', payload);
      setSubCategoriesData(data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    handleGetSubCategories();
  }, []);

  useEffect(() => {
    disableButtons.setDisableNextButton(form.subCategoryId.length <= 0);
  }, [form.subCategoryId]);

  return (
    <div className="flex flex-wrap justify-center gap-6">
      {!loading ? (
        subCategoriesData[0]?.subCategories?.map((singleData: any) => {
          return (
            <div
              key={singleData?.subCategoryId}
              className={`min-w-[170px] text-center p-3 space-y-4 border rounded-lg cursor-pointer ${
                form.subCategoryId?.includes(singleData?.subCategoryId) && 'bg-[#F9F6FF] border-[#8D5BF8]'
              }`}
              onClick={() =>
                dispatch(
                  setFieldValue({
                    path: 'subCategoryId',
                    value: form.subCategoryId?.includes(singleData?.subCategoryId)
                      ? form.subCategoryId?.filter((singleSubCategoryId: any) => singleSubCategoryId !== singleData?.subCategoryId)
                      : [...form.subCategoryId, singleData?.subCategoryId],
                  })
                )
              }
            >
              <p className="text-lg font-medium capitalize">{singleData?.subCategoryName}</p>
            </div>
          );
        })
      ) : (
        <div className="size-12 border-y-2 border-purple-500 animate-spin rounded-full" />
      )}
    </div>
  );
};
