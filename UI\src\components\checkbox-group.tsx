import { CheckBox } from './checkBox';
import type { CheckboxState } from './checkBox';

interface CheckboxOption {
  label: string;
  value: string;
  disabled?: boolean;
}

interface CheckboxGroupProps {
  name: string;
  options: CheckboxOption[];
  value: string[];
  onChange: (value: string[]) => void;
  disabled?: boolean;
  className?: string;
}

export const CheckboxGroup: React.FC<CheckboxGroupProps> = ({ name, options, value, onChange, disabled = false, className = '' }) => {
  const handleCheck = (checked: boolean, optionValue: string) => {
    if (checked) {
      onChange([...value, optionValue]);
    } else {
      onChange(value.filter((v) => v !== optionValue));
    }
  };

  return (
    <div className={`flex flex-col gap-4 ${className}`}>
      {options.map((option) => {
        let state: CheckboxState = 'default';
        if (disabled || option.disabled) state = 'disabled';

        return (
          <div
            key={option.value}
            className={`flex items-center rounded-lg px-4 py-2 transition-all duration-150 border border-[#DEE2E4] bg-white hover:bg-[#F1E9FE] focus:border-2 focus:border-[#A47BFA] ${
              disabled || option.disabled ? 'opacity-50' : ''
            }`}
            tabIndex={-1}
          >
            <CheckBox
              name={name}
              label={option.label}
              checked={value.includes(option.value)}
              onChange={(checked) => handleCheck(checked, option.value)}
              state={state}
              disabled={disabled || option.disabled}
            />
          </div>
        );
      })}
    </div>
  );
};
