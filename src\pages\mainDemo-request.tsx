import { Outlet } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { Header } from '../modules/global/components/header';
import { Sidebar } from '../modules/global/components/sidebar';

import { useScreenSize } from 'UI/src';

export const DemoMainLayout = () => {
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [isShowMenuIcon, setIsShowMenuIcon] = useState(false);

  const screen = useScreenSize();

  useEffect(() => {
    if (!screen.lt.md()) {
      setIsDrawerVisible(false);
    }
  }, [screen.size]);

  return (
    <div className="flex flex-col min-h-screen antialiased">
      {/* Header */}
      <Header showThemeIcon={true} isDrawerVisible={isDrawerVisible} setIsDrawerVisible={setIsDrawerVisible} hideTitleOnSM={true} />

      {/* <!-- Sidebar --> */}
      <Sidebar isDrawerVisible={isDrawerVisible} setIsDrawerVisible={setIsDrawerVisible} />

      <Outlet />
    </div>
  );
};
