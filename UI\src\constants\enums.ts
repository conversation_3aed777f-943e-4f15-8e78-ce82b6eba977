import { type PermissionGroup } from '../types/Role.type';

// Submission Status
export enum SubmissionStatus {
  'Not Started' = 1,
  'In Progress' = 2,
  Completed = 3,
  Scheduled = 4,
  Missed = 5,
  Overdue = 6,
}

export enum AverageScore {
  High = 1,
  Meduim = 2,
  Low = 3,
}

export enum SubmissionDueDate {
  // Today = 1,
  // LastWeek = 2,
  // LastMonth = 3,
  PickADate = 4,
}

export enum Grade {
  Excellent = 1,
  Good = 2,
  Poor = 3,
}

export enum SubmissionWarning {
  CheatingBehaviour = 1,
  MissedDeadline = 2,
  // Overdue = 3,
}

export enum QuestionDifficulty {
  Easy = 1,
  Medium = 2,
  Hard = 3,
  'Very Hard' = 4,
}

export enum QuizDifficulty {
  Intern = 1,
  Fresh = 2,
  Junior = 3,
  MidLevel = 4,
  Senior = 5,
}

export interface InterviewModel {
  value: string;
  label: string;
}
export const InterviewModels: InterviewModel[] = [
  { value: 'o3-mini', label: 'o3-mini' },
  { value: 'o4-mini', label: 'o4-mini' },
  { value: 'gpt-4.1-nano', label: 'gpt-4.1-nano' },
  { value: 'gpt-4-turbo', label: 'gpt-4-turbo' },
  { value: 'gpt-4.1-mini', label: 'gpt-4.1-mini' },
  { value: 'gpt-4o-mini', label: 'gpt-4o-mini' },
];

export enum QuestionTypeEnum {
  Singlechoice = 1,
  Multichoice = 2,
  Essay = 3,
  // Textarea = 4,
}

export enum InterviewType {
  Ready = 1,
  Interactive = 2,
}

export enum Gender {
  Male = 1,
  Female = 2,
}

export enum YesNo {
  Yes = 1,
  No = 0,
}

export enum Logs {
  WindowRefresh = 1,
  SubmitAnswer = 3,
  StepMove = 4,
  SubmitSubmission = 5,
  WindowSwitched = 6,
  ContextMenu = 8,
  IpChanged = 9,
  KeyboardKeyDown = 2,
}

export enum Scope {
  MyData = 1,
}

export enum Role {
  SuperAdmin = 1,
  ContentCreator = 2,
  HR = 3,
  Admin = 4,
}

export enum RoleWithoutSuperAdmin {
  ContentCreator = 2,
  HR = 3,
  Admin = 4,
}

export enum InternPhase {
  One = 1,
  Two = 2,
  Three = 3,
  Four = 4,
  Five = 5,
}

export enum Recommended {
  Recommended = 1,
  NotRecommended = 2,
}

export enum AssignmentType {
  Screenings = 1,
  Tests = 2,
  Interviews = 3,
}

export interface Language {
  value: number;
  label: string;
  code: string;
}
export const Languages: Language[] = [
  { value: 1, label: 'English', code: 'en-US' },
  { value: 2, label: 'Arabic', code: 'ar-XA' },
  { value: 3, label: 'Turkish', code: 'tr-TR' },
];

export interface AiAvatarModelLanguage {
  lang: string;
  langCode: string;
  voice: string;
  gender: number;
}
export const AiAvatarModelLanguages: AiAvatarModelLanguage[] = [
  { lang: 'English', langCode: 'en-US', voice: 'en-US-Wavenet-F', gender: 0 },
  { lang: 'English', langCode: 'en-US', voice: 'en-US-Wavenet-I', gender: 1 },
  { lang: 'Arabic', langCode: 'ar-XA', voice: 'ar-XA-Wavenet-D', gender: 0 },
  { lang: 'Arabic', langCode: 'ar-XA', voice: 'ar-XA-Wavenet-B', gender: 1 },
  { lang: 'Turkish', langCode: 'tr-TR', voice: 'tr-TR-Wavenet-E', gender: 1 },
  { lang: 'Turkish', langCode: 'tr-TR', voice: 'tr-TR-Wavenet-D', gender: 0 },
];

export interface AiAvatarModel {
  value: string;
  modelPath: string;
  iconPath: string;
  iconPathOut: string;
  gender: number;
}
export const AiAvatarModels: AiAvatarModel[] = [
  { value: 'ramy', modelPath: 'male-01.glb', iconPath: 'male-01.png', iconPathOut: 'ramy-body.png', gender: 1 },
  { value: 'lina', modelPath: 'female-01.glb', iconPath: 'female-01.png', iconPathOut: 'lina-out.png', gender: 0 },
  { value: 'adam', modelPath: 'male-02.glb', iconPath: 'male-02.png', iconPathOut: 'adam-body.png', gender: 1 },
  { value: 'sarah', modelPath: 'female-02.glb', iconPath: 'female-02.png', iconPathOut: 'sarah-out.png', gender: 0 },
  { value: 'deniz', modelPath: 'male-03.glb', iconPath: 'male-03.png', iconPathOut: 'deniz-body.png', gender: 1 },
  { value: 'emma', modelPath: 'female-03.glb', iconPath: 'female-03.png', iconPathOut: 'emma-out.png', gender: 0 },
];

export enum CurrentStatus {
  Inactive = 1,
  Active = 2,
}

export enum SubscriptionStatus {
  Pending = 1,
  Paid = 2,
  Cancelled = 3,
  Failed = 4,
  Expired = 5,
}

export enum BillingCycle {
  Monthly = 1,
  Yearly = 2,
}

export const PlanType = {
  Free: 1,
  Basic: 2,
  Pro: 3,
  VIP: 4,
};

export interface InterviewLanguage {
  value: string;
  icon: string;
}
export const InterviewLanguages: InterviewLanguage[] = [
  { value: 'English', icon: 'circle-flags:uk' },
  { value: 'Turkish', icon: 'emojione:flag-for-turkey' },
  { value: 'Arabic', icon: 'emojione:flag-for-saudi-arabia' },
];

export interface PlanFeature {
  key: string;
  value: string;
}
export const PlanFeaturesArr: PlanFeature[] = [
  { key: 'APPLICANTS', value: 'applicants' },
  { key: 'USERS', value: 'users' },
  { key: 'AVATARS', value: 'avatars' },
  { key: 'CREATE_CUSTOM_QUESTIONS', value: 'createCustomQuestions' },
  { key: 'CREATE_CUSTOM_TESTS', value: 'createCustomTests' },
  { key: 'CREATE_CUSTOM_INTERVIEWS', value: 'createCustomInterviews' },
  { key: 'AI_QUESTIONS', value: 'aiQuestions' },
  { key: 'AI_TESTS', value: 'aiTests' },
  { key: 'AI_INTERVIEWS', value: 'aiInterviews' },
  { key: 'ASSIGN_TESTS', value: 'assignTests' },
  { key: 'ASSIGN_CUSTOM_INTERVIEWS', value: 'assignCustomInterviews' },
  { key: 'ASSIGN_INTERACTIVE_INTERVIEWS', value: 'assignInteractiveInterviews' },
  { key: 'EXPORT_REPORTS', value: 'exportReports' },
  // { key: 'APPLICANT_ASSESSMENT_REPORT', value: 'applicantAssessmentReport' },
  // { key: 'AVATAR_CLONING', value: 'avatarCloning' },
];
export enum PlanFeatures {
  APPLICANTS = 'applicants',
  USERS = 'users',
  AVATARS = 'avatars',
  CREATE_CUSTOM_QUESTIONS = 'createCustomQuestions',
  CREATE_CUSTOM_TESTS = 'createCustomTests',
  CREATE_CUSTOM_INTERVIEWS = 'createCustomInterviews',
  AI_QUESTIONS = 'aiQuestions',
  AI_TESTS = 'aiTests',
  AI_INTERVIEWS = 'aiInterviews',
  ASSIGN_TESTS = 'assignTests',
  ASSIGN_CUSTOM_INTERVIEWS = 'assignCustomInterviews',
  ASSIGN_INTERACTIVE_INTERVIEWS = 'assignInteractiveInterviews',
  EXPORT_REPORTS = 'exportReports',
  // APPLICANT_ASSESSMENT_REPORT = 'applicantAssessmentReport',
  // AVATAR_CLONING = 'avatarCloning',
}

export enum PricingPeriod {
  Monthly = 1,
  Annually = 2,
  // TwoYears = 3,
  // FourYears = 4,
}

export enum UserPermissions {
  CREATE_USER = 1,
  UPDATE_USER = 2,
  ARCHIVE_USER = 3,
  VIEW_USER = 4,
  VIEW_ASSESSMENT = 5,
  CREATE_ASSESSMENT = 6,
  ASSIGN_ASSESSMENT = 7,
  UPDATE_ASSESSMENT = 8,
  VIEW_ASSESSMENT_ARCHIVE = 9,
  ARCHIVE_ASSESSMENT = 10,
  RECTOR_ASSESSMENT = 11,
  CREATE_CATEGORY = 12,
  UPDATE_CATEGORY = 13,
  ARCHIVE_CATEGORY = 14,
  VIEW_CATEGORY = 15,
  CREATE_APPLICANT = 16,
  UPDATE_APPLICANT = 17,
  ARCHIVE_APPLICANT = 18,
  VIEW_APPLICANT = 19,
  VIEW_ASSESSMENTS_REPORT = 20,
  CREATE_QUESTION = 21,
  UPDATE_QUESTION = 22,
  ARCHIVE_QUESTION = 23,
  VIEW_QUESTION = 24
}

export const UserPermissionsGroups: PermissionGroup[] = [
  {
    label: "Users",
    checked: false,
    children: [
      { name: "VIEW_USER", label: "VIEW", checked: false, value: UserPermissions.VIEW_USER },
      { name: "CREATE_USER", label: "CREATE", checked: false, value: UserPermissions.CREATE_USER },
      { name: "UPDATE_USER", label: "UPDATE", checked: false, value: UserPermissions.UPDATE_USER },
      { name: "ARCHIVE_USER", label: "ARCHIVE", checked: false, value: UserPermissions.ARCHIVE_USER }
    ]
  },
  {
    label: "Assessments",
    checked: false,
    children: [
      { name: "VIEW_ASSESSMENT", label: "VIEW", checked: false, value: UserPermissions.VIEW_ASSESSMENT },
      { name: "CREATE_ASSESSMENT", label: "CREATE", checked: false, value: UserPermissions.CREATE_ASSESSMENT },
      { name: "UPDATE_ASSESSMENT", label: "UPDATE", checked: false, value: UserPermissions.UPDATE_ASSESSMENT },
      { name: "ARCHIVE_ASSESSMENT", label: "ARCHIVE", checked: false, value: UserPermissions.ARCHIVE_ASSESSMENT },
      { name: "ASSIGN_ASSESSMENT", label: "ASSIGN", checked: false, value: UserPermissions.ASSIGN_ASSESSMENT },
      { name: "RECTOR_ASSESSMENT", label: "RECTOR", checked: false, value: UserPermissions.RECTOR_ASSESSMENT },
      { name: "VIEW_ASSESSMENT_ARCHIVE", label: "VIEW", checked: false, value: UserPermissions.VIEW_ASSESSMENT_ARCHIVE },
    ]
  },
  {
    label: "Categories",
    checked: false,
    children: [
      { name: "VIEW_CATEGORY", label: "VIEW", checked: false, value: UserPermissions.VIEW_CATEGORY },
      { name: "CREATE_CATEGORY", label: "CREATE", checked: false, value: UserPermissions.CREATE_CATEGORY },
      { name: "UPDATE_CATEGORY", label: "UPDATE", checked: false, value: UserPermissions.UPDATE_CATEGORY },
      { name: "ARCHIVE_CATEGORY", label: "ARCHIVE", checked: false, value: UserPermissions.ARCHIVE_CATEGORY },
    ]
  },
  {
    label: "Applicants",
    checked: false,
    children: [
      { name: "VIEW_APPLICANT", label: "VIEW", checked: false, value: UserPermissions.VIEW_APPLICANT },
      { name: "CREATE_APPLICANT", label: "CREATE", checked: false, value: UserPermissions.CREATE_APPLICANT },
      { name: "UPDATE_APPLICANT", label: "UPDATE", checked: false, value: UserPermissions.UPDATE_APPLICANT },
      { name: "ARCHIVE_APPLICANT", label: "ARCHIVE", checked: false, value: UserPermissions.ARCHIVE_APPLICANT },
    ]
  },
  {
    label: "Assessment reports",
    checked: false,
    children: [
      { name: "VIEW_ASSESSMENTS_REPORT", label: "VIEW", checked: false, value: UserPermissions.VIEW_ASSESSMENTS_REPORT },
    ]
  },
  {
    label: "Questions",
    checked: false,
    children: [
      { name: "VIEW_QUESTION", label: "VIEW", checked: false, value: UserPermissions.VIEW_QUESTION },
      { name: "CREATE_QUESTION", label: "CREATE", checked: false, value: UserPermissions.CREATE_QUESTION },
      { name: "UPDATE_QUESTION", label: "UPDATE", checked: false, value: UserPermissions.UPDATE_QUESTION },
      { name: "ARCHIVE_QUESTION", label: "ARCHIVE", checked: false, value: UserPermissions.ARCHIVE_QUESTION },
    ]
  },
];