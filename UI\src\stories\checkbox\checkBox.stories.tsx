import { CheckBox } from '../../components/checkBox';
import type { Meta, StoryObj } from '@storybook/react';
import type { CheckboxState } from '../../components/checkBox';
const meta: Meta<typeof CheckBox> = {
  title: 'Components/CheckBox',
  component: CheckBox,
  tags: ['autodocs'],
  argTypes: {
    state: {
      control: 'select',
      options: ['default', 'hover', 'pressed', 'disabled'] satisfies CheckboxState[],
    },
    onChange: { action: 'changed' },
  },
};
export default meta;
type Story = StoryObj<typeof CheckBox>;
export const Default: Story = {
  args: {
    label: 'Default Checkbox',
    checked: false,
    state: 'default',
    disabled: false,
  },
};
export const Hover: Story = {
  args: {
    label: 'Hover Checkbox',
    checked: false,
    state: 'hover',
    disabled: false,
  },
};
export const Pressed: Story = {
  args: {
    label: 'Pressed Checkbox',
    checked: true,
    state: 'pressed',
    disabled: false,
  },
};
export const Disabled: Story = {
  args: {
    label: 'Disabled Checkbox',
    checked: true,
    state: 'disabled',
    disabled: true,
  },
};
