import { useSelector, useDispatch } from 'react-redux';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter, ModalHeader } from 'flowbite-react';
import { hideConfirm } from '../slices/confirmDialog/confirmDialog.slice';
import type { RootState } from '../store';
import Button from './button';

const ConfirmationDialog = () => {
  const dispatch = useDispatch();
  const confirmState = useSelector((state: RootState) => state.confirmDialog);

  if (!confirmState.options) {
    return null;
  }

  const { onConfirm, onClose, confirmLabel = 'Confirm', cancelLabel = 'Cancel', zIndex } = confirmState.options;

  const handleConfirm = async () => {
    if (onConfirm) {
      await onConfirm();
    } else {
      dispatch(hideConfirm());
    }
  };

  const handleCancel = () => {
    if (onClose) {
      onClose();
    }
    dispatch(hideConfirm());
  };

  // Check if this is a delete confirmation
  const isDeleteConfirmation = confirmLabel === 'Delete';

  return (
    <>
      <div className="fixed top-0 left-0 w-screen h-screen bg-black/50" style={{ zIndex: zIndex || 999999999999999999 }} />
      <Modal show popup size="md" onClose={handleCancel} dismissible style={{ zIndex: zIndex || 999999999999999999 }}>
        {isDeleteConfirmation ? (
          // Special styling for delete confirmation
          <ModalBody className="p-6">
            <div className="text-center">
              {/* Message content */}
              <div className="mb-6">{confirmState.message}</div>

              {/* Buttons */}
              <div className="flex justify-center items-center gap-4">
                <Button colorType="tertiary" onClick={handleCancel} variant="lg" className="w-full ">
                  {cancelLabel}
                </Button>
                <Button colorType="destructive" onClick={handleConfirm} variant="lg" className="w-full  ">
                  {confirmLabel}
                </Button>
              </div>
            </div>
          </ModalBody>
        ) : (
          // Original styling for other confirmations
          <>
            <ModalHeader>{/* Optional header content */}</ModalHeader>
            <ModalBody>
              <div className="text-gray-700 dark:text-white text-base leading-relaxed mb-6">{confirmState.message}</div>
            </ModalBody>
            <ModalFooter className="flex justify-center items-center gap-4">
              <Button colorType="tertiary" onClick={handleCancel} variant="lg" className="w-full">
                {cancelLabel}
              </Button>
              <Button colorType="primary" onClick={handleConfirm} variant="lg" className="w-full">
                {confirmLabel}
              </Button>
            </ModalFooter>
          </>
        )}
      </Modal>
    </>
  );
};

export default ConfirmationDialog;
