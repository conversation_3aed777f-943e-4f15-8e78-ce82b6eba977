import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { type RootState } from '../../store';

interface BlockDetails {
  titleDetails: string;
  blockIdDetails: string;
  testIdDetails: string;
}

interface SubmissionsBankState {
  // UI visibility states
  isShowFullInfo: boolean;
  isCreateBlockVisible: boolean;
  isCreateDialogVisible: boolean;
  isCreateTestDialogVisible: boolean;
  isTestNotExist: boolean;
  showInfo: boolean;
  showSpecialAccessButton: boolean;
  showSubscribe: boolean;
  aiDialog: boolean;

  // Index and navigation states
  blockIndex: number;
  subBlockIndex: number;
  back: boolean;

  // Data states
  testId: string | null;
  blockDetails: BlockDetails;
}

const initialState: SubmissionsBankState = {
  // UI visibility states
  isShowFullInfo: false,
  isCreateBlockVisible: false,
  isCreateDialogVisible: false,
  isCreateTestDialogVisible: false,
  isTestNotExist: false,
  showInfo: false,
  showSpecialAccessButton: false,
  showSubscribe: false,
  aiDialog: false,

  // Index and navigation states
  blockIndex: 0,
  subBlockIndex: 0,
  back: false,

  // Data states
  testId: null,
  blockDetails: {
    titleDetails: '',
    blockIdDetails: '',
    testIdDetails: '',
  },
};

const submissionsBankSlice = createSlice({
  name: 'submissionsBank',
  initialState,
  reducers: {
    // UI visibility actions
    setShowFullInfo: (state, action: PayloadAction<boolean>) => {
      state.isShowFullInfo = action.payload;
    },
    setCreateBlockVisible: (state, action: PayloadAction<boolean>) => {
      state.isCreateBlockVisible = action.payload;
    },
    setCreateDialogVisible: (state, action: PayloadAction<boolean>) => {
      state.isCreateDialogVisible = action.payload;
    },
    setCreateTestDialogVisible: (state, action: PayloadAction<boolean>) => {
      state.isCreateTestDialogVisible = action.payload;
    },
    setTestNotExist: (state, action: PayloadAction<boolean>) => {
      state.isTestNotExist = action.payload;
    },
    setShowInfo: (state, action: PayloadAction<boolean>) => {
      state.showInfo = action.payload;
    },
    setShowSpecialAccessButton: (state, action: PayloadAction<boolean>) => {
      state.showSpecialAccessButton = action.payload;
    },
    setShowSubscribe: (state, action: PayloadAction<boolean>) => {
      state.showSubscribe = action.payload;
    },
    setAiDialog: (state, action: PayloadAction<boolean>) => {
      state.aiDialog = action.payload;
    },

    // Index and navigation actions
    setBlockIndex: (state, action: PayloadAction<number>) => {
      state.blockIndex = action.payload;
    },
    setSubBlockIndex: (state, action: PayloadAction<number>) => {
      state.subBlockIndex = action.payload;
    },
    setBack: (state, action: PayloadAction<boolean>) => {
      state.back = action.payload;
    },

    // Data actions
    setTestId: (state, action: PayloadAction<string | null>) => {
      state.testId = action.payload;
    },
    setBlockDetails: (state, action: PayloadAction<BlockDetails>) => {
      state.blockDetails = action.payload;
    },
    updateBlockDetails: (state, action: PayloadAction<Partial<BlockDetails>>) => {
      state.blockDetails = { ...state.blockDetails, ...action.payload };
    },

    // Combined actions for common operations
    showMoreInfo: (state, action: PayloadAction<{ blockIndex: number; subBlockIndex: number }>) => {
      state.blockIndex = action.payload.blockIndex;
      state.subBlockIndex = action.payload.subBlockIndex;
      state.isShowFullInfo = true;
    },
    closeSubmissionsCreationDialog: (state) => {
      state.isCreateDialogVisible = false;
      state.testId = null;
    },
    resetBlockDetails: (state) => {
      state.blockDetails = {
        titleDetails: '',
        blockIdDetails: '',
        testIdDetails: '',
      };
    },

    // Reset actions
    resetSubmissionsBankState: (state) => {
      return initialState;
    },
  },
});

export const {
  setShowFullInfo: setSubmissionsBankShowFullInfo,
  setCreateBlockVisible: setSubmissionsBankCreateBlockVisible,
  setCreateDialogVisible: setSubmissionsBankCreateDialogVisible,
  setCreateTestDialogVisible: setSubmissionsBankCreateTestDialogVisible,
  setTestNotExist: setSubmissionsBankTestNotExist,
  setShowInfo: setSubmissionsBankShowInfo,
  setShowSpecialAccessButton: setSubmissionsBankShowSpecialAccessButton,
  setShowSubscribe: setSubmissionsBankShowSubscribe,
  setAiDialog: setSubmissionsBankAiDialog,
  setBlockIndex: setSubmissionsBankBlockIndex,
  setSubBlockIndex: setSubmissionsBankSubBlockIndex,
  setBack: setSubmissionsBankBack,
  setTestId: setSubmissionsBankTestId,
  setBlockDetails: setSubmissionsBankBlockDetails,
  updateBlockDetails: updateSubmissionsBankBlockDetails,
  showMoreInfo: showSubmissionsBankMoreInfo,
  closeSubmissionsCreationDialog: closeSubmissionsBankCreationDialog,
  resetBlockDetails: resetSubmissionsBankBlockDetails,
  resetSubmissionsBankState,
} = submissionsBankSlice.actions;

export const submissionsBankState = (state: RootState) => state.submissionsBank;
export default submissionsBankSlice.reducer;
