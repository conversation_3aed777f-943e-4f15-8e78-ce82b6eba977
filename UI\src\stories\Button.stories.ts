import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';

import { fn } from 'storybook/test';

import Button from '../components/button';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'centered',
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    colorType: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'tertiary', 'destructive'],
    },
    variant: {
      control: { type: 'select' },
      options: ['xs', 'sm', 'md', 'lg'],
    },
    state: {
      control: { type: 'select' },
      options: ['default', 'hover', 'pressed', 'disabled'],
    },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: { onClick: fn() },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Primary: Story = {
  args: {
    colorType: 'primary',
    label: 'Button',
    variant: 'md',
  },
};

export const Secondary: Story = {
  args: {
    colorType: 'secondary',
    label: 'Button',
    variant: 'md',
  },
};

export const Large: Story = {
  args: {
    variant: 'lg',
    label: 'Button',
    colorType: 'primary',
  },
};

export const Small: Story = {
  args: {
    variant: 'sm',
    label: 'Button',
    colorType: 'primary',
  },
};
