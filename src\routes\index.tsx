import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { APP_ROUTE_PATH, AUTH_ROUTE_PATH } from 'UI/src/configs/router';

// Protected and Unprotected Routes
import { ProtectedRoute } from 'src/modules/auth/components/protected-route';
import { UnProtectedRoute } from 'src/modules/auth/components/un-protected-route';

// Layouts
import { AppLayout, AuthLayout, GlobalLayout, PaymentLayout } from '../layouts';
import { QuizLayout } from '../layouts/quiz';
import { QuizAiLayout } from '../layouts/quiz-ai';

// Auth Pages
import { LoginPage } from '../modules/auth/pages/login';
import { Register } from '../modules/auth/pages/register';

// App Module Pages
import { StatisticsPage } from '../modules/app/modules/dashboard/pages/statistics';
import { DashboardSuperAdmin as DashboardSuperAdminPage } from '../modules/app/modules/dashboard-super-admin/pages';
import { OrganizationsListPage } from '../modules/app/modules/organizations/pages/list';
import { PlansListPage } from '../modules/app/modules/plans/pages/list';
import { RolesListPage } from '../modules/app/modules/roles/pages/list';
import { QuestionsListPage } from '../modules/app/modules/questions/pages/list';
import { QuestionsSinglePage } from '../modules/app/modules/questions/pages/single';
import { QuizzesListPage } from '../modules/app/modules/quizzes/pages/list';
import { QuizzesSinglePage } from '../modules/app/modules/quizzes/pages/single';
import { SubmissionsListPage } from '../modules/app/modules/submissions/pages/list';
import { SubmissionsSinglePage } from '../modules/app/modules/submissions/pages/single';
import { ApplicantsListPage } from '../modules/app/modules/applicants/pages/list';
import { ApplicantProgressSingle } from '../modules/app/modules/applicants/pages/single';
import { UsersListPage } from '../modules/app/modules/users/pages/list';
import { AiInterviewsListPage as InterviewsListPage } from '../modules/app/modules/interviews/pages/list';
import { AiInterviewSinglePage as InterviewsSinglePage } from '../modules/app/modules/interviews/pages/single';
import { PhoneScreeningListPage } from '../modules/app/modules/phone-screening/pages/list';
import { SinglePhoneScreeningPage as PhoneScreeningSinglePage } from '../modules/app/modules/phone-screening/pages/single';
import { AssessmentsListPage } from '../modules/app/modules/assessments/pages/list';
import { AssessmentSingle as AssessmentsSinglePage } from '../modules/app/modules/assessments/pages/single';
import { AssessmentReportPage as AssessmentReportListPage } from '../modules/app/modules/assessment-report/pages/list';
import { AssessmentReport as AssessmentReportSinglePage } from '../modules/app/modules/assessment-report/pages/single';
import { CategoryManagementComponent } from '../modules/app/modules/category-management/pages/list';
import { StudioPage } from '../modules/app/modules/studio';

// Global Module Pages
import { PricingGlobal } from '../modules/global/modules/pricing/pages';
import { ContactUsGlobal } from '../modules/global/modules/contact-us/pages';
import { TermsOfServicePage } from '../modules/global/modules/terms/pages';
import { NewLandingPage } from '../modules/global/modules/new-landing/pages';
import { ProgrammingTestsPage as ProgrammingTestListPage } from '../modules/global/modules/programming-test/pages';

// Payment Pages
import { PaymentVisaInfoData } from '../modules/payment/pages/visa-info';
import { PaymentStatusPage } from '../modules/payment/pages/payment-status';

// Test Pages
import { QuizPage } from '../pages/quiz/QuizPage';
import { QuizAiPage } from '../pages/quiz-ai/QuizAiPage';

// Global Pages
import { Error404Page } from '../pages/404';
import { Error403Page } from '../pages/403';
import { DemoRequest } from '../pages/demo-request';
import { DemoMainLayout } from '../pages/mainDemo-request';

import AppWrapper from './RouterProvider';

const routes = [
  {
    path: '/',
    element: <AppWrapper />,
    children: [
      // Demo request
      {
        path: '/request-demo',
        element: <DemoMainLayout />,
        children: [{ path: '', element: <DemoRequest /> }],
      },

      // Auth Routes
      {
        path: AUTH_ROUTE_PATH,
        element: (
          <UnProtectedRoute>
            <AuthLayout />
          </UnProtectedRoute>
        ),
        children: [
          {
            path: 'login',
            element: <LoginPage />,
          },
          {
            path: 'register',
            element: <Register />,
          },
        ],
      },

      // App Routes
      {
        path: APP_ROUTE_PATH,
        element: (
          <ProtectedRoute>
            <AppLayout />
          </ProtectedRoute>
        ),
        children: [
          // Default
          {
            path: '',
            element: <Navigate to="/app/dashboard" />,
          },

          // Dashboard
          {
            path: 'dashboard',
            element: <StatisticsPage />,
            loader() {
              return {
                label: 'Statistics',
              };
            },
          },

          // Dashboard Super Admin
          {
            path: 'dashboard-super-admin',
            element: <DashboardSuperAdminPage />,
          },

          // Organizations
          {
            path: 'organizations',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/organizations" />,
              },
              {
                path: 'list',
                element: <OrganizationsListPage />,
              },
            ],
          },

          // Plans
          {
            path: 'plans',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/plans" />,
              },
              {
                path: 'list',
                element: <PlansListPage />,
              },
            ],
          },

          // Roles
          {
            path: 'roles',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/roles" />,
              },
              {
                path: 'list',
                element: <RolesListPage />,
              },
            ],
          },

          // Questions
          {
            path: 'questions',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/questions" />,
              },
              {
                path: 'list',
                element: <QuestionsListPage />,
              },
              {
                path: 'single/:id',
                element: <QuestionsSinglePage />,
              },
            ],
          },

          // Tests/Quizzes
          {
            path: 'tests',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/tests/list/setup" />,
              },
              {
                path: 'list/setup',
                element: <QuizzesListPage />,
              },
              {
                path: 'create',
                element: <QuizzesSinglePage />,
                loader() {
                  return {
                    label: 'Create',
                    title: 'Create Test',
                    subtitle: 'Create tests using the form below.',
                  };
                },
              },
              {
                path: 'edit/:id',
                element: <QuizzesSinglePage />,
                loader() {
                  return {
                    label: 'Edit',
                    title: 'Edit Test',
                    subtitle: "Modify the test as needed. Once satisfied with the changes, click 'Update' to save",
                  };
                },
              },
              {
                path: 'view/:id',
                element: <QuizzesSinglePage />,
                loader() {
                  return {
                    label: 'View',
                    title: 'View Test',
                    subtitle: "Review the test details below. To make changes, click 'Edit Test' to proceed.",
                  };
                },
              },
            ],
          },

          // Submissions
          {
            path: 'submissions',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/submissions" />,
              },
              {
                path: 'list',
                element: <SubmissionsListPage />,
              },
              {
                path: 'single/:id',
                element: <SubmissionsSinglePage />,
              },
            ],
          },

          // Applicants
          {
            path: 'applicants',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/applicants" />,
              },
              {
                path: 'list',
                element: <ApplicantsListPage />,
              },
              {
                path: 'progress/:id',
                element: <ApplicantProgressSingle />,
                loader() {
                  return {
                    label: 'Profile',
                    icon: 'material-symbols:person-raised-hand-rounded',
                    title: 'Applicant',
                    subtitle: 'Review and show progress',
                  };
                },
              },
            ],
          },

          // Users
          {
            path: 'users',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/users" />,
              },
              {
                path: 'list',
                element: <UsersListPage />,
              },
            ],
          },

          // Interviews
          {
            path: 'interviews',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/interviews" />,
              },
              {
                path: 'list',
                element: <InterviewsListPage />,
              },
              {
                path: 'single/:id',
                element: <InterviewsSinglePage />,
              },
            ],
          },

          // Phone Screening
          {
            path: 'phone-screening',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/phone-screening" />,
              },
              {
                path: 'list',
                element: <PhoneScreeningListPage />,
              },
              {
                path: 'single/:id',
                element: <PhoneScreeningSinglePage />,
              },
            ],
          },

          // Assessments
          {
            path: 'assessments',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/assessments" />,
              },
              {
                path: 'list',
                element: <AssessmentsListPage />,
              },
              {
                path: 'single/:id',
                element: <AssessmentsSinglePage />,
              },
            ],
          },

          // Assessment Report
          {
            path: 'assessment-report',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/app/assessment-report/tests" />,
              },
              {
                path: 'list',
                element: <AssessmentReportListPage />,
              },
              {
                path: 'single/:id',
                element: <AssessmentReportSinglePage />,
              },
            ],
          },

          // Add Credits
          {
            path: 'add-credits',
            element: <Navigate to="/app/add-credits" />,
          },

          // Category Management
          {
            path: 'category-management',
            element: <CategoryManagementComponent />,
          },

          // Studio
          {
            path: 'studio',
            element: <StudioPage />,
          },
        ],
      },

      // Global Routes
      {
        path: '/',
        element: <GlobalLayout />,
        loader() {
          return {
            svg: (
              <svg width="22" height="19" viewBox="0 0 22 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M1.25 9.99982L10.204 1.79107C10.644 1.38865 11.356 1.38865 11.795 1.79107L20.75 9.99982M3.5 7.93732V17.2186C3.5 17.7878 4.004 18.2498 4.625 18.2498H8.75V13.7811C8.75 13.2118 9.254 12.7498 9.875 12.7498H12.125C12.746 12.7498 13.25 13.2118 13.25 13.7811V18.2498H17.375C17.996 18.2498 18.5 17.7878 18.5 17.2186V7.93732M7.25 18.2498H15.5"
                  stroke="#656C7B"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            ),
            tooltip: 'Home',
          };
        },
        children: [
          {
            path: '',
            element: <NewLandingPage />,
          },

          // Programming Test
          {
            path: 'programming-test',
            element: <Outlet />,
            children: [
              {
                path: '',
                element: <Navigate to="/programming-test" />,
              },
              {
                path: 'list',
                element: <ProgrammingTestListPage />,
              },
              {
                path: 'single/:id',
                element: <Navigate to="/programming-test" />,
              },
            ],
          },

          // Global Pages
          {
            path: 'pricing',
            element: <PricingGlobal />,
          },
          {
            path: 'contact-us',
            element: <ContactUsGlobal />,
          },
          {
            path: 'terms',
            element: <TermsOfServicePage />,
          },
        ],
      },

      // Payment Routes
      {
        path: '/payment',
        element: <PaymentLayout />,
        children: [
          {
            path: '',
            element: <Navigate to="/pricing" />,
          },
          {
            path: ':planId/:planKey?',
            element: <PaymentVisaInfoData />,
          },
          {
            path: 'status/:paymentStatus/:subscriptionId',
            element: <PaymentStatusPage />,
          },
        ],
      },

      // Test Routes
      {
        path: '/test',
        element: <QuizLayout />,
        children: [
          {
            path: '',
            element: <Navigate to="/404" replace />,
          },
          {
            path: ':id',
            element: <QuizPage />,
          },
        ],
      },

      // Ai-Test Routes
      {
        path: '/interview',
        element: <QuizAiLayout />,
        children: [
          {
            path: '',
            element: <Navigate to="/404" replace />,
          },
          {
            path: ':id',
            element: <QuizAiPage />,
          },
        ],
      },
    ],
  },
  {
    path: '/403',
    element: <Error403Page />,
  },
  {
    path: '*',
    element: <Error404Page />,
  },
];

export default routes;
