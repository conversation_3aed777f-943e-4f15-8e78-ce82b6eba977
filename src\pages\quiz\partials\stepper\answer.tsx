import React, { useContext, FC, ChangeEvent } from 'react';

import { Radio, Checkbox } from 'src';
import { Applicant } from '../onboarding/index';
import { RootState, useAppSelector } from 'UI/src';
import { useState, useEffect } from 'react';

import { QuestionTypeEnum } from 'UI/src';

//Types
type Option = {
  id: number;
  label: string;
};

type AnswerProps = {
  value: any;
  onChange: (value: any, key?: any) => void;
};

type CheckboxAnswerProps = {
  value: Record<number, boolean>;
  onChange: (checked: boolean, optionId: number) => void;
};

type RadioAnswerProps = {
  value: number | string;
  onChange: (value: number) => void;
};

type TextareaAnswerProps = {
  value: string;
  onChange: (value: string) => void;
};

type SingleChoiceAnswer = number;
type MultiChoiceAnswer = Record<number, boolean>;
type TextareaAnswer = string;

export type AnswerValue = SingleChoiceAnswer | MultiChoiceAnswer | TextareaAnswer;

type SubmissionContextType = {
  applicantId?: string;
  randomId?: string;
  submission: {
    stage: {
      question: {
        type: number;
        options: Option[];
      };
      answer?: AnswerValue;
    };
    quiz: {
      questionIds: string[];
      duration?: number;
      phoneScreening?: boolean;
    };
    applicant?: Applicant;
    startedAt?: string;
    submittedAt?: string;
    locked?: boolean;
    expired?: boolean;
  };
  loading: boolean;
  setLoading?: (loading: boolean) => void;
  handleGetSubmission?: (id?: string) => Promise<void>;
  // FIXME: any problem
  [key: string]: any;
};

// Switcher
export const Answer: FC<AnswerProps> = (props) => {
  const { submission } = useAppSelector((state: RootState) => state.submission);
  const { type } = submission.stage.question;

  if (type === QuestionTypeEnum.Singlechoice) {
    return <RadioAnswer {...props} />;
  }
  if (type === QuestionTypeEnum.Multichoice) {
    return <CheckboxAnswer {...props} />;
  }
  if (type === 3) {
    return <TextareaAnswer {...props} />;
  }
  return null;
};

export const CheckboxAnswer: FC<CheckboxAnswerProps> = ({ value, onChange }) => {
  const { submission, loading } = useAppSelector((state: RootState) => state.submission);
  const { options } = submission.stage.question;

  // Limit Multichoice answer
  const handleChange = (check: boolean, option: number) => {
    const limitMultichoiceAnswer = () => {
      let counter = 0;
      // FIXME: another way
      //Object.entries(value).forEach(([_, v]) => {
      // v && counter++;
      // });

      Object.entries(value).forEach(([optionId, isChecked]) => {
        if (isChecked) counter++;
      });
      if (counter < 2) {
        return true;
      } else if (!check) {
        return true;
      }
      return false;
    };
    limitMultichoiceAnswer() && onChange(check, option);
  };

  return (
    <div>
      <h1 className="text-xs text-[#4B5563] font-normal mb-4 dark:text-white">Select two answers only</h1>

      <div className="flex mt-3 flex-col gap-7">
        {options.map((option: any) => (
          <Checkbox
            key={option.id}
            label={option.label}
            fullWidth={true}
            name={`multiChoiceAnswer${option.id}`}
            value={!!value[option.id]}
            onChange={(check: boolean) => handleChange(check, option.id)}
            disabled={loading}
            isCustomLabel
            preventSendingMail={false}
            outlineDesign
          />
        ))}
      </div>
    </div>
  );
};

export const RadioAnswer: FC<RadioAnswerProps> = ({ value, onChange }) => {
  const { submission, loading } = useAppSelector((state: RootState) => state.submission);
  const { options } = submission.stage.question;

  return (
    <div className="space-y-4">
      <p className="text-[#3A4458] thepassBtwo">Answer Options</p>

      <div className="flex flex-col gap-7">
        {options
          .filter((o: any) => !!o.label?.trim())
          .map((option: any) => (
            <Radio
              key={option.id}
              name={`singleChoiceAnswer${option.id}`}
              label={option.label}
              fullWidth={true}
              value={value}
              selectionValue={option.id}
              onChange={(value: string | number) => onChange(Number(value))}
              disabled={loading}
              isCustomLabel
              applicantTestView
              labelTooltip={undefined}
              labelTooltipStyles={''}
              pointer={false}
              outlineDesign
            />
          ))}
      </div>
    </div>
  );
};

export const TextareaAnswer: FC<TextareaAnswerProps> = ({ value, onChange }) => {
  const { submission, loading } = useAppSelector((state: RootState) => state.submission);
  const [textareaAnswer, setTextareaAnswer] = useState<string>(value || '');

  useEffect(() => {
    setTextareaAnswer(value || '');
  }, [value]);

  const handleChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = event.target.value;
    setTextareaAnswer(newValue);
    onChange(newValue);
  };

  return (
    <div>
      {/* <h1 className="text-sm font-normal text-[#4B5563] mb-4 dark:text-white">Type your answer here.</h1> */}

      <textarea
        value={textareaAnswer}
        onChange={handleChange}
        rows={8}
        className="block w-full border resize-none disabled:cursor-not-allowed disabled:opacity-50 border-gray-300 bg-gray-50 text-gray-900 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 p-2.5 text-sm rounded-lg focus:ring-0 focus:border-gray-300"
        disabled={loading}
        placeholder="Type your Answer..."
      />
    </div>
  );
};
