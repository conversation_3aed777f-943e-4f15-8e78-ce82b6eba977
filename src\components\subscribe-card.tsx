import { useNavigate } from 'react-router-dom';
import { Dialog, Button } from 'UI';
import noData from 'images/NotFoundData.png';
export interface SubscribeCardProps {
  onSubscribe: () => void;
  plan?: string;
  onClose: () => void;
}

export const SubscribeCard = ({ onClose, onSubscribe, plan }: SubscribeCardProps) => {
  const navigate = useNavigate();

  return (
    <Dialog size="md" isOpen onClose={onSubscribe}>
      <div className="flex flex-col justify-center items-center text-center gap-8">
        <img src={noData} className="w-40" alt="" />
        <div className="flex flex-col gap-4 px-7">
          <p className="font-medium text-[#374151] dark:text-white">You must Subscribe to continue</p>
          <p className="text-[#6C6C6C] text-sm dark:text-white dark:opacity-50">Once you Subscribe you can activate all tests</p>
        </div>
        <Button colorType="primary" className="w-full" label="Subscribe" onClick={() => navigate('/Pricing')} />
      </div>
    </Dialog>
  );
};
