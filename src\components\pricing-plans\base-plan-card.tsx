import { Icon } from 'src';

type BasePlanCardType = {
  features: any;
};

export const BasePlanCard = ({ features }: BasePlanCardType) => {
  const formatKey = (key: string) => {
    return key
      .replace(/([A-Z]+)([A-Z][a-z])/g, '$1 $2')
      .replace(/([a-z\d])([A-Z])/g, '$1 $2')
      .replace(/^./, (str) => str.toUpperCase());
  };

  if (!features || typeof features !== 'object') return null; // ✅ Early return if undefined/null

  return Object.entries(features)
    .sort(([keyA, valueA], [keyB, valueB]) => {
      const aActive = typeof valueA === 'number' ? valueA > 0 : !!valueA;
      const bActive = typeof valueB === 'number' ? valueB > 0 : !!valueB;
      return aActive === bActive ? 0 : aActive ? -1 : 1;
    })
    .map(([key, value]) => {
      const isActive = typeof value === 'number' ? value > 0 : !!value;

      return (
        <div className="flex gap-2 items-center" key={key}>
          <Icon
            icon={isActive ? 'material-symbols:check' : 'ix:namur-failure-filled'}
            width={'16'}
            className={`p-0.5 ${isActive ? 'bg-[#F5F6F8] text-[#009217]' : 'text-[#f57878]'} bg-[#FCFCFC] rounded-full`}
          />
          <p className={`text-[13px] font-medium ${isActive ? 'text-[#18181B]' : 'text-[#65676DA8] line-through'}`}>
            {typeof value === 'number' && value > 0 && <span className="text-base font-semibold">{value}</span>}{' '}
            <span className="thepassHfour">{formatKey(key)}</span>
            {typeof value === 'string' && `: ${value.charAt(0).toUpperCase() + value.slice(1)}`}
          </p>
        </div>
      );
    });
};
