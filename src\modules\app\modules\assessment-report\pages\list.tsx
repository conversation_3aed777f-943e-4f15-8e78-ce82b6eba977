// React
import { useState } from 'react';

// Core
import { useAppSelector, RootState } from 'UI/src';
import { Jumbotron, ScrollableTabs } from 'src';

// Components
import { TestList } from '../components/list/test-list';
import { ScreeningList } from '../components/list/screening-list';
import { InterviewList } from '../components/list/interview-list';

export const AssessmentReportPage = () => {
  // Hooks
  const assessmentReportState = useAppSelector((state: RootState) => state.assessmentReport);

  // States
  const [activeTab, setActiveTab] = useState(0);

  // tabs
  const tabs: any = [
    // { title: 'Screenings', data: <ScreeningList /> },
    {
      title: 'tests',
      data: <TestList />,
      // backupCount: assessmentReportState.backupListTest,
    },
    {
      title: 'interviews',
      data: <InterviewList />,
      // backupCount: assessmentReportState.backupListInterview,
    },
  ];

  return (
    <div className="space-y-4">
      <Jumbotron />

      <ScrollableTabs
        data={tabs}
        selectedTab={{
          activeTab: activeTab,
          setActiveTab: setActiveTab,
        }}
        nav={{
         routePrefix : `/app/assessment-report/list`
        }}
      />

      {tabs[activeTab].data}
    </div>
  );
};
