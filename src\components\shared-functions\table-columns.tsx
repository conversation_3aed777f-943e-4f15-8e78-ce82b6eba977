// Core
import { Icon } from 'src';
import { Check } from 'lucide-react';

// Format Date
import { addDays, format, isValid } from 'date-fns';

// Flowbite
import { Tooltip } from 'flowbite-react';

// dispach
import { useScreenSize, useAppDispatch } from 'UI/src';
import { setNotifyMessage, setErrorNotify } from 'UI';
import { ResultStatus } from 'src';
import { toast } from 'react-toastify';

const ORIGIN = window.location.origin;

type NameFieldColumnType = {
  id: string;
  name: string;
  showMoreMap?: { [key: string]: boolean };
  onClick?: any;
};

export const NameFieldColumn = ({ id, name, showMoreMap = {}, onClick }: NameFieldColumnType) => {
  // Hook
  const screen = useScreenSize();

  return (
    <div className={`relative ${onClick && 'cursor-pointer'}`} onClick={onClick}>
      <div className="w-full">
        <p
          className={`text-gray-800 dark:text-grayTextOnDarkMood sm:text-base lg:text-sm sm:font-semibold lg:font-medium capitalize
            native-break-all-words sm:line-clamp-2 lg:line-clamp-none lg:truncate ${!showMoreMap[id] && 'truncate sm:whitespace-normal'}`}
        >
          {name}
        </p>
        {screen.gt.md() && (
          <Tooltip
            content={<div className="whitespace-normal max-h-[300px] overflow-auto">{name}</div>}
            placement="bottom"
            arrow={false}
            className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs max-h-[300px]"
          >
            <div className="w-[92%] h-full absolute left-0 top-0"></div>
          </Tooltip>
        )}
      </div>
    </div>
  );
};

type EmailFieldColumnType = {
  id?: string;
  email: string;
  onClick?: any;
  showMoreMap?: { [key: string]: boolean };
};

export const EmailFieldColumn = ({ email, onClick }: EmailFieldColumnType) => {
  // Hook
  const screen = useScreenSize();

  const dispatch = useAppDispatch();

  return (
    <div className="flex relative gap-2">
      <div className="w-full">
        <div className="flex items-center gap-2 relative">
          <div className={`truncate max-w-[85%] ${onClick && 'cursor-pointer'}`} onClick={onClick}>
            {email ? (
              <>
                <p className="break-words overflow-auto whitespace-normal text-clip capitalize font-medium text-gray-500 dark:text-gray-400 lg:truncate">
                  {email}
                </p>
                {screen.gt.md() && (
                  <Tooltip content={email} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-[92%] h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </>
            ) : (
              <span className="text-[#626874] dark:text-gray-400">—</span>
            )}
          </div>
          {email && (
            <span
              onClick={() => {
                navigator.clipboard.writeText(email);
                // dispatch(setErrorNotify('Email copied'));
                toast.success('Email copied');
              }}
              className="inline-block cursor-pointer text-gray-500 dark:text-gray-400"
            >
              <Tooltip content="Copy Email" placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M20 8H10C8.89543 8 8 8.89543 8 10V20C8 21.1046 8.89543 22 10 22H20C21.1046 22 22 21.1046 22 20V10C22 8.89543 21.1046 8 20 8Z"
                    stroke="#A47BFA"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M4 16C2.9 16 2 15.1 2 14V4C2 2.9 2.9 2 4 2H14C15.1 2 16 2.9 16 4"
                    stroke="#A47BFA"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </Tooltip>
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export const DurationFieldColumn = ({ duration }: { duration?: number | string }) => {
  // Hook
  const screen = useScreenSize();

  return duration ? (
    <div className="flex items-center gap-1 text-sm text-[#535862]">
      <Icon icon="ic:round-access-time" className="hidden sm:block lg:hidden mt-1" width={'18'} />
      <span className="font-medium">{duration}</span>
      <span className="font-normal">{Number(duration) > 1 ? 'mins' : 'min'}</span>
    </div>
  ) : (
    '—'
  );
};

export const AssignmentOverviewFieldColumn = ({ id, lastAssessment, showMoreMap = {}, row }: any) => {
  // Hook
  const screen = useScreenSize();
  const dispatch = useAppDispatch();

  return lastAssessment?.data ? (
    <div className="space-y-2 ">
      <div className={`flex gap-3 ${showMoreMap[id] && 'flex-wrap lg:flex-nowrap'} items-center max-w-full`}>
        <div className="relative max-w-[65%]">
          <div
            className={`w-full break-words text-[#111827] text-sm font-normal capitalize dark:text-grayTextOnDarkMood lg:truncate ${
              !showMoreMap[id] && 'truncate sm:overflow-visible sm:whitespace-normal'
            }`}
          >
            {/* {element}
            {screen.gt.md() && (
              <Tooltip content={element} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                <div className="w-[90%] h-full absolute left-0 top-0"></div>
              </Tooltip>
            )} */}
          </div>
        </div>
        <div className="dark:bg-[#8485884c] text-[13px] font-normal  bg-[#eceeef] dark:text-[#e1e1e1] text-[#667085] py-[2px] px-2 rounded-md w-fit h-fit">
          {lastAssessment?.type === 'submission' ? 'Test' : lastAssessment?.type.charAt(0).toUpperCase() + lastAssessment?.type.slice(1)}
        </div>
      </div>

      <div className="flex items-center">
        <ResultStatus test={lastAssessment.data} hideMissed hideTime hideScore={lastAssessment?.type === 'screening'} />
        {row?.lastAssessment?.type !== 'screening' && !lastAssessment.data.startedAt && lastAssessment.data.submittedAt && (
          <p className="text-[#9CA3AF] italic font-normal">No score recorded</p>
        )}

        {lastAssessment.data.startedAt && lastAssessment?.data?.submittedAt && lastAssessment?.type !== 'screening' && (
          <p
            onClick={() => {
              if (lastAssessment?.type === 'submission') {
                window.open(`/app/tests/pdf/${lastAssessment.data._id}?type=submissions`, '_blank', 'noopener,noreferrer');
              } else if (lastAssessment?.type === 'interview') {
                window.open(`/app/tests/pdf/${lastAssessment.data._id}?type=interviews`, '_blank', 'noopener,noreferrer');
              }
            }}
            className="flex items-center cursor-pointer underline hover:no-underline text-[13px] dark:text-[#bfc0c3] text-[#667085] gap-1 ml-2"
          >
            <svg width="14" height="20" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M11.0156 6.82812H10.7812V4.01562C10.7812 4.01562 10.7812 4.01562 10.7812 4.00156C10.7801 3.98082 10.7761 3.96033 10.7695 3.94063V3.91953C10.7588 3.89445 10.7437 3.87145 10.725 3.85156L7.9125 1.03906C7.89261 1.02037 7.86961 1.0053 7.84453 0.994531H7.82344C7.80008 0.981739 7.77472 0.973021 7.74844 0.96875H0.703125C0.640965 0.96875 0.581351 0.993443 0.537397 1.0374C0.493443 1.08135 0.46875 1.14096 0.46875 1.20312V6.82812H0.234375C0.172215 6.82812 0.112601 6.85282 0.0686469 6.89677C0.0246931 6.94073 0 7.00034 0 7.0625V11.75C0 11.8122 0.0246931 11.8718 0.0686469 11.9157C0.112601 11.9597 0.172215 11.9844 0.234375 11.9844H0.46875V14.7969C0.46875 14.859 0.493443 14.9186 0.537397 14.9626C0.581351 15.0066 0.640965 15.0312 0.703125 15.0312H10.5469C10.609 15.0312 10.6686 15.0066 10.7126 14.9626C10.7566 14.9186 10.7812 14.859 10.7812 14.7969V11.9844H11.0156C11.0778 11.9844 11.1374 11.9597 11.1814 11.9157C11.2253 11.8718 11.25 11.8122 11.25 11.75V7.0625C11.25 7.00034 11.2253 6.94073 11.1814 6.89677C11.1374 6.85282 11.0778 6.82812 11.0156 6.82812ZM7.96875 1.76797L9.98203 3.78125H7.96875V1.76797ZM10.3125 14.5625H0.9375V11.9844H10.3125V14.5625ZM3.06328 11.0047V7.87578H3.80625C3.90788 7.87128 4.00931 7.88838 4.10384 7.92595C4.19838 7.96352 4.28387 8.02071 4.35469 8.09375C4.48388 8.23924 4.5519 8.429 4.54453 8.62344V9.07812C4.54616 9.17639 4.52789 9.27397 4.49084 9.365C4.45378 9.45603 4.3987 9.53862 4.32891 9.60781C4.26186 9.67882 4.18077 9.73508 4.09078 9.77301C4.00079 9.81094 3.90389 9.82971 3.80625 9.82812H3.53672V11L3.06328 11.0047ZM4.92422 11.0047V7.87344H5.66484C5.76288 7.87152 5.86023 7.89014 5.95064 7.92808C6.04105 7.96602 6.12253 8.02245 6.18984 8.09375C6.25957 8.16251 6.31463 8.24469 6.35169 8.33533C6.38875 8.42597 6.40705 8.52318 6.40547 8.62109V10.2617C6.40692 10.3602 6.38835 10.4579 6.35088 10.5489C6.31341 10.64 6.25783 10.7225 6.1875 10.7914C6.12019 10.8627 6.03871 10.9191 5.9483 10.9571C5.85788 10.995 5.76053 11.0136 5.6625 11.0117L4.92422 11.0047ZM7.93125 9.20234V9.67109H7.29375V11H6.825V7.87344H8.16797V8.34219H7.29375V9.19531L7.93125 9.20234ZM10.3125 6.82812H0.9375V1.4375H7.5V4.01562C7.5 4.07779 7.52469 4.1374 7.56865 4.18135C7.6126 4.22531 7.67221 4.25 7.73438 4.25H10.3125V6.82812Z"
                fill="#798296"
              />
            </svg>
            Open Report
          </p>
        )}
        {!lastAssessment.data.submittedAt && (
          <p
            onClick={() => {
              navigator.clipboard.writeText(
                `${lastAssessment.type === 'submission' || lastAssessment.type === 'screening' ? `${ORIGIN}/test` : `${ORIGIN}/interview`}/${
                  lastAssessment.data._id
                }`
              );
              dispatch(setNotifyMessage('Link copied'));
            }}
            className="flex cursor-pointer underline hover:no-underline text-[13px] dark:text-[#bfc0c3] text-[#667085] "
          >
            <div className="flex items-center gap-2">
              Copy Link
              <Tooltip content="Copy Link" placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                <Icon icon="ooui:copy-ltr" className="relative z-10 text-[#798296] text-base" />
              </Tooltip>{' '}
            </div>
          </p>
        )}
      </div>
    </div>
  ) : (
    <p className="text-[#6B7280] font-medium">Not assigned</p>
  );
};

export const CategoryFieldColumn = ({ categoryNameArray, contractMore = true }: any) => {
  const element = (category: any) => (
    <p key={category} className="px-3 py-0.5 bg-[#F3F4F6] text-[#1B1F3B] thepassBfour capitalize border-0.5 border-[#E5E7EB] rounded-lg truncate">
      {category}
    </p>
  );
  if (Array.isArray(categoryNameArray) && categoryNameArray?.length > 0) {
    if (contractMore && categoryNameArray.length > 2) {
      return (
        <div className="flex items-center gap-2">
          {categoryNameArray.slice(0, 2).map((category) => element(category))}
          <p className="border border-[#E5E7EB] rounded-full p-1 px-3 text-sm font-semibold">+{categoryNameArray.length - 2}</p>
        </div>
      );
    } else {
      return <div className="flex items-center gap-2">{categoryNameArray.map((category) => element(category))}</div>;
    }
  } else if (categoryNameArray) {
    return <div className="flex">{element(categoryNameArray)}</div>;
  } else {
    return '—';
  }
};

export const SubcategoryFieldColumn = ({ subCategoryName, contractMore = true }: any) => {
  const element = (name: any) => (
    <p key={name} className="border border-[#DEE2E4] rounded-lg px-3 py-0.5 text-[#1B1F3B] thepassBfour capitalize truncate">
      {name}
    </p>
  );
  if (Array.isArray(subCategoryName) && subCategoryName.length > 0) {
    if (contractMore && subCategoryName.length > 1) {
      return (
        <div className="flex items-center gap-2">
          {subCategoryName.slice(0, 2).map((name) => element(name))}
          <p className="border rounded-full p-1 px-3 text-sm font-medium">+{subCategoryName.length - 1}</p>
        </div>
      );
    } else {
      return <div className="flex items-center gap-2">{subCategoryName.map((name) => element(name))}</div>;
    }
  } else if (subCategoryName) {
    return <div className="flex">{element(subCategoryName)}</div>;
  } else {
    return '—';
  }
};

export const TopicsFieldColumn = ({ topicName }: any) => {
  const element = (singleTopicName: string) => (
    <p key={singleTopicName} className="flex gap-1 border border-[#EAECF0] rounded-lg p-1 px-2 text-xs text-[#747C98] bg-white font-medium truncate">
      <Check className="size-5 text-[#9E77ED]" strokeWidth={2} />
      {singleTopicName}
    </p>
  );
  return Array.isArray(topicName) && topicName?.length > 0 ? (
    <div className="flex items-center gap-2">
      {topicName?.slice(0, 2)?.map((singleTopicName: string) => element(singleTopicName))}
      {topicName?.length > 1 && <p className="border rounded-full p-1 px-3 text-sm font-medium">+{topicName?.length - 1}</p>}
    </div>
  ) : topicName ? (
    <div className="flex">{element(topicName)}</div>
  ) : (
    '—'
  );
};

export const NumberOfQuestionFieldColumn = ({ numOfQuestions }: { numOfQuestions: number }) => {
  return numOfQuestions >= 0 ? (
    <div className="flex items-center gap-1 text-sm text-[#56555C]">
      <Icon icon="lucide:file-question" className="hidden sm:block lg:hidden mt-1" width="18" />
      <span className="font-medium">{numOfQuestions} </span>
      {/* if we need it later  */}
      {/* <span className="font-normal">{numOfQuestions > 1 ? 'questions' : 'question'}</span> */}
    </div>
  ) : (
    '—'
  );
};

export const FormatDateFieldColumn = ({ date }: { date: Date | string }) => {
  const newDate = new Date(date);

  if (!date) return '—';
  else if (!isValid(newDate)) return 'Invalid Date';
  else return <p className="text-[#656575] text-md">{format(newDate, 'dd MMM yyyy')}</p>;
};

export const InvoiceFieldColumn = ({ invoice, onClick }: any) => {
  const dispatch = useAppDispatch();

  // Hook
  const screen = useScreenSize();
  return (
    <div className="flex relative gap-2">
      <div className="w-full">
        <div className="flex items-center gap-2 relative">
          <div className={`truncate max-w-[85%] ${onClick && 'cursor-pointer'}`} onClick={onClick}>
            {invoice ? (
              <>
                <p className="break-words overflow-auto whitespace-normal text-clip capitalize font-medium text-gray-900 dark:text-gray-400 lg:truncate">
                  {invoice}
                </p>
                {screen.gt.md() && (
                  <Tooltip content={invoice} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                    <div className="w-[92%] h-full absolute left-0 top-0"></div>
                  </Tooltip>
                )}
              </>
            ) : (
              <span className="text-[#626874] dark:text-gray-900">—</span>
            )}
          </div>
          {invoice && (
            <span
              onClick={() => {
                navigator.clipboard.writeText(invoice);
                dispatch(setNotifyMessage('Invoice copied'));
              }}
              className="inline-block cursor-pointer text-gray-500 dark:text-gray-400"
            >
              <Tooltip content="Copy invoice" placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                <Icon icon="ooui:copy-ltr" className="relative text-[#798296] text-base" />
              </Tooltip>
            </span>
          )}
        </div>
      </div>
    </div>
  );
};
