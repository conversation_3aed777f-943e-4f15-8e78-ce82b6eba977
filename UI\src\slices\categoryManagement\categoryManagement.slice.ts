import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { type RootState } from '../../store';

// Types for category management
interface EditingCategoryTypes {
  _id: string;
  id: string;
  name: string;
  description: string;
  icon: string;
}

type EditingSubType = {
  _id: string;
  id: string;
  name: string;
};

interface CategoryManagementState {
  // UI states
  expanded: string | null;
  showMoreMap: { [key: string]: boolean };
  categorySearch: string;
  search: string;
  currentPage: number;
  loading: boolean;

  // Modal states
  createCategoryCompVisible: boolean;
  openSubModal: boolean;
  editSubModal: boolean;
  editCategoryModal: boolean;

  // Data states
  categories: any[];
  activeCategoryId: string | null;
  editingSub: EditingSubType | null;
  editingCategory: EditingCategoryTypes | null;

  // Create New Category Dialog
  title: string;
  description: string;
  emoji: string;
}

const initialState: CategoryManagementState = {
  // UI states
  expanded: null,
  showMoreMap: {},
  categorySearch: '',
  search: '',
  currentPage: 1,
  loading: true,

  // Modal states
  createCategoryCompVisible: false,
  openSubModal: false,
  editSubModal: false,
  editCategoryModal: false,

  // Data states
  categories: [],
  activeCategoryId: null,
  editingSub: null,
  editingCategory: null,

  // Create New Category Dialog
  title: '',
  description: '',
  emoji: '',
};

const categoryManagementSlice = createSlice({
  name: 'categoryManagement',
  initialState,
  reducers: {
    // UI actions
    setExpanded: (state, action: PayloadAction<string | null>) => {
      state.expanded = action.payload;
    },
    setCategoryShowMoreMap: (state, action: PayloadAction<{ [key: string]: boolean }>) => {
      state.showMoreMap = action.payload;
    },
    setCategorySearch: (state, action: PayloadAction<string>) => {
      state.categorySearch = action.payload;
    },
    setSearch: (state, action: PayloadAction<string>) => {
      state.search = action.payload;
    },
    setCurrentPage: (state, action: PayloadAction<number>) => {
      state.currentPage = action.payload;
    },
    setCategoryLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },

    // Modal actions
    setCreateCategoryCompVisible: (state, action: PayloadAction<boolean>) => {
      state.createCategoryCompVisible = action.payload;
    },
    setOpenSubModal: (state, action: PayloadAction<boolean>) => {
      state.openSubModal = action.payload;
    },
    setEditSubModal: (state, action: PayloadAction<boolean>) => {
      state.editSubModal = action.payload;
    },
    setEditCategoryModal: (state, action: PayloadAction<boolean>) => {
      state.editCategoryModal = action.payload;
    },

    // Data actions
    setCategories: (state, action: PayloadAction<any[]>) => {
      state.categories = action.payload;
    },
    setActiveCategoryId: (state, action: PayloadAction<string | null>) => {
      state.activeCategoryId = action.payload;
    },
    setEditingSub: (state, action: PayloadAction<EditingSubType | null>) => {
      state.editingSub = action.payload;
    },
    setEditingCategory: (state, action: PayloadAction<EditingCategoryTypes | null>) => {
      state.editingCategory = action.payload;
    },

    // Create New Category Dialog
    setTitle: (state, action: PayloadAction<string>) => {
      state.title = action.payload;
    },
    setDescription: (state, action: PayloadAction<string>) => {
      state.description = action.payload;
    },
    setEmoji: (state, action: PayloadAction<string>) => {
      state.emoji = action.payload;
    },

    // Reset action
    resetCategoryManagementState: (state) => {
      return initialState;
    },
  },
});

export const {
  setExpanded,
  setCategoryShowMoreMap,
  setCategorySearch,
  setSearch,
  setCurrentPage,
  setCategoryLoading,
  setCreateCategoryCompVisible,
  setOpenSubModal,
  setEditSubModal,
  setEditCategoryModal,
  setCategories,
  setActiveCategoryId,
  setEditingSub,
  setEditingCategory,
  resetCategoryManagementState,
  setTitle,
  setDescription,
  setEmoji,
} = categoryManagementSlice.actions;

export const categoryManagementState = (state: RootState) => state.categoryManagement;
export default categoryManagementSlice.reducer;
