// React
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Core
import { Button } from 'src';

type SectionType = { heading: string; content: string[] }[];
type LanguageType = { title: string; sections: SectionType };
type TermsType = {
  en: LanguageType;
  ar: LanguageType;
};

export const TermsOfServicePage = () => {
  const navigate = useNavigate();
  const [language, setLanguage] = useState<string>('en'); // 'en' or 'ar'

  const toggleLanguage = () => {
    setLanguage((prev) => (prev === 'en' ? 'ar' : 'en'));
  };

  const terms: TermsType = {
    en: {
      title: 'Terms and Conditions',
      sections: [
        {
          heading: '1. Definitions',
          content: [
            'User: Any individual or entity who registers for or uses the Platform.',
            'Service: All features and functionalities provided by ThePass.ai, including automated interviews and candidate assessments.',
            'Platform: ThePass.ai website, web applications, and any exposed APIs.',
            'Company: The entity operating the Platform (ThePass.ai).',
          ],
        },
        {
          heading: '2. Acceptance of Terms',
          content: [
            'By accessing or subscribing to any part of the Platform, you agree to be bound by these Terms & Conditions and the accompanying Privacy Policy.',
            'If you represent an organization, you warrant that you have authority to bind that organization to these terms.',
          ],
        },
        {
          heading: '3. Registration and Accounts',
          content: [
            'Users must create an account and provide accurate, up-to-date information.',
            'Users are responsible for maintaining the confidentiality of their login credentials.',
            'Concurrent use of one account by multiple individuals is prohibited.',
          ],
        },
        {
          heading: '4. Service Description',
          content: [
            'Auto-generated interview questions based on job profiles.',
            'Live video interviews with anti-cheating measures.',
            'Analytical reports on candidate performance.',
            'Integration with external systems via API.',
          ],
        },
        {
          heading: '5. Subscription & Payments',
          content: [
            'Access is granted on a monthly or annual subscription basis.',
            'Fees may change with 30 days’ notice.',
            'No refunds for completed billing periods.',
          ],
        },
        {
          heading: '6. User Obligations',
          content: [
            'Use the service lawfully and ethically.',
            'Do not upload content that infringes on others’ rights.',
            'Follow the Company’s instructions and guidelines.',
          ],
        },
        {
          heading: '7. Intellectual Property',
          content: ['All rights to the Platform and content belong to the Company or licensors.', 'Do not copy or redistribute without permission.'],
        },
        {
          heading: '8. Confidentiality & Data Protection',
          content: ['The Company protects your data as per the Privacy Policy.', 'You may not access or use other users’ data.'],
        },
        {
          heading: '9. Limitation of Liability',
          content: [
            'The Company is not liable for indirect damages or lost profits.',
            'Liability is limited to the subscription fees paid for the affected period.',
          ],
        },
        {
          heading: '10. Indemnification',
          content: ['You agree to indemnify the Company for any breach of these terms.'],
        },
        {
          heading: '11. Changes to Terms',
          content: ['Terms may be updated at any time. Continued use implies acceptance.'],
        },
        {
          heading: '12. Termination',
          content: ['Subscriptions may be terminated for breaches, with prior notice.'],
        },
        {
          heading: '13. Governing Law & Jurisdiction',
          content: ['These terms are governed by Saudi Arabian law.', 'Saudi courts have jurisdiction over any disputes.'],
        },
      ],
    },
    ar: {
      title: 'الشروط والأحكام',
      sections: [
        {
          heading: '1. التعاريف',
          content: [
            'المستخدم: أي شخص أو جهة تسجل الدخول أو تستخدم المنصة.',
            'الخدمة: جميع الميزات والوظائف التي توفرها منصة ThePass.ai، بما في ذلك إجراء المقابلات الآلية وتقييم المرشحين.',
            'المنصة: موقع ThePass.ai وتطبيقات الويب والواجهات البرمجية الخاصة بها.',
            'الشركة: الجهة المشغلة للمنصة (ThePass.ai).',
          ],
        },
        {
          heading: '2. قبول الشروط',
          content: [
            'باستخدامك للمنصة، فإنك توافق على الالتزام بهذه الشروط وسياسة الخصوصية.',
            'إذا كنت تمثل جهة، فأنت تقر بأن لديك الصلاحية للتعاقد باسمها.',
          ],
        },
        {
          heading: '3. التسجيل والحساب',
          content: [
            'يجب إنشاء حساب وتقديم بيانات دقيقة ومحدثة.',
            'المستخدم مسؤول عن سرية بيانات الدخول.',
            'يُمنع استخدام الحساب من قبل أكثر من شخص في نفس الوقت.',
          ],
        },
        {
          heading: '4. وصف الخدمة',
          content: [
            'توليد أسئلة تلقائيًا حسب الوظيفة.',
            'مقابلات فيديو بآليات مكافحة الغش.',
            'تقارير وتحليلات أداء المرشحين.',
            'تكامل مع أنظمة خارجية عبر API.',
          ],
        },
        {
          heading: '5. الاشتراك والدفع',
          content: ['نموذج اشتراك شهري أو سنوي حسب الخطة.', 'قد تتغير الأسعار بعد إشعار لمدة 30 يومًا.', 'لا يتم رد المدفوعات عن الفترات المنتهية.'],
        },
        {
          heading: '6. التزامات المستخدم',
          content: ['الاستخدام لأغراض قانونية فقط.', 'عدم نشر محتوى ينتهك حقوق الغير.', 'اتباع تعليمات الشركة.'],
        },
        {
          heading: '7. حقوق الملكية الفكرية',
          content: ['جميع الحقوق تعود للشركة أو مرخّصيها.', 'لا يجوز النسخ أو إعادة النشر دون إذن.'],
        },
        {
          heading: '8. السرية وحماية البيانات',
          content: ['الشركة تلتزم بحماية بيانات المستخدم.', 'لا يجوز الاطلاع على بيانات مرشحين آخرين.'],
        },
        {
          heading: '9. حدود المسؤولية',
          content: ['الشركة غير مسؤولة عن أضرار غير مباشرة.', 'المسؤولية تقتصر على قيمة الاشتراك المدفوع.'],
        },
        {
          heading: '10. التعويض',
          content: ['المستخدم يلتزم بتعويض الشركة عن أي أضرار ناتجة عن مخالفته للشروط.'],
        },
        {
          heading: '11. التعديل على الشروط',
          content: ['يحق للشركة تعديل الشروط في أي وقت.', 'استمرار الاستخدام يعني الموافقة.'],
        },
        {
          heading: '12. الإنهاء',
          content: ['يجوز إنهاء الحساب عند خرق الشروط، مع إشعار مسبق.'],
        },
        {
          heading: '13. القانون والاختصاص',
          content: ['تخضع الشروط لقوانين المملكة العربية السعودية.', 'الاختصاص القضائي للمحاكم السعودية.'],
        },
      ],
    },
  };

  const { title, sections } = terms[language as keyof TermsType];

  return (
    <div className="p-6 max-w-4xl mx-auto" style={{ direction: language === 'ar' ? 'rtl' : 'ltr', textAlign: language === 'ar' ? 'right' : 'left' }}>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-3xl font-bold">{title}</h1>
        <Button onClick={toggleLanguage}>{language === 'en' ? 'العربية' : 'English'}</Button>
      </div>

      <div className="space-y-6">
        {sections.map((section, idx) => (
          <div key={idx}>
            <h2 className="text-xl font-semibold mb-2">{section.heading}</h2>
            <ul className="list-disc pl-6 space-y-1">
              {section.content.map((point, i) => (
                <li key={i}>{point}</li>
              ))}
            </ul>
          </div>
        ))}
      </div>

      {/* <div className="mt-10">
        <Button variant="outline" onClick={() => navigate('/')}>
          {language === 'en' ? 'Back' : 'الرجوع'}
        </Button>
      </div> */}
    </div>
  );
};
