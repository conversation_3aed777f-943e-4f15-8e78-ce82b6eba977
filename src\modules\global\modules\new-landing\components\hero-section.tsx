import { useState, useEffect } from 'react';

import { RootState, useAppSelector, UserData } from 'UI/src';

// import { Button } from 'src';
import { Button } from 'UI';
import { motion, AnimatePresence } from 'framer-motion';
import { useScreenSize } from 'UI/src';
import heroImage from 'images/landing/head-image.png';
import { Icon } from '@/components';
export const HeroSection = () => {
  const words = ['Smarter', 'Easier', 'Faster'];
  const [currentWordIndex, setCurrentWordIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [nextWordIndex, setNextWordIndex] = useState(1);
  // Set animation disabled by default
  const [isAnimationEnabled, setIsAnimationEnabled] = useState(false);
  const screen = useScreenSize();
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Auto-change word every 2 seconds - disabled by default now
  useEffect(() => {
    if (!isAnimationEnabled) return; // Skip if animation is disabled

    const interval = setInterval(() => {
      changeWord();
    }, 2000);

    return () => clearInterval(interval);
  }, [isAnimationEnabled]);

  const changeWord = () => {
    if (!isAnimationEnabled) return; // Skip if animation is disabled

    setIsAnimating((prevAnimating) => {
      if (prevAnimating) return prevAnimating; // Prevent multiple animations

      setCurrentWordIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % words.length;
        setNextWordIndex(nextIndex);

        setTimeout(() => {
          setCurrentWordIndex(nextIndex);
          setIsAnimating(false);
        }, 500); // Match animation duration

        return prevIndex; // Keep current index during animation
      });

      return true; // Set animating to true
    });
  };

  // Keep this in case you want to enable animation later
  const handleWordClick = () => {
    // Toggle animation on word click
    setIsAnimationEnabled(!isAnimationEnabled);
  };

  return (
    <div
      id="hero-section"
      className="relative w-full flex items-center justify-center bg-no-repeat pb-4 sm:pb-7 pt-7 lg:mt-[50px]"
      style={{
        backgroundImage: `url(${heroImage})`,
        backgroundSize: '100% auto',
        backgroundPosition: 'center center',
      }}
    >
      {/* Content Container */}
      <div className="text-center px-4 space-y-4 sm:space-y-6 md:space-y-8 max-w-4xl mx-auto">
        {/* Main Heading */}
        <div className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-medium mb-4 md:mb-6">
          {/* Upper line - Animated word + Hiring */}
          <div className={`flex items-center justify-center mb-4 sm:mb-6 `}>
            <div className="flex items-baseline gap-2 sm:gap-4 ">
              <span className="gradient-text text-[32px] sm:text-4xl md:text-[85px] font-medium " style={{ color: '#000356', lineHeight: '1' }}>
                Smarter
              </span>
              <span className="text-[32px] sm:text-4xl md:text-[85px] font-medium" style={{ color: '#000356', lineHeight: '1' }}>
                Hiring
              </span>
            </div>
          </div>

          {/* Lower line - Starts Here */}
          <div className="thepassSubHone  sm:text-[65px]" style={{ lineHeight: '1' }}>
            AI-Powered Automation Workflow
          </div>
        </div>

        {/* Subtitle */}
        <p className=" text-lg sm:thepassSubHone  mb-6 md:mb-8 mx-auto leading-relaxed sm:leading-normal text-text-500">
          Leverage AI to identify, assess, and hire the right candidates faster
        </p>

        {/* Buttons */}
        <div className="flex flex-col sm:flex-row justify-center gap-4 w-full sm:w-auto">
          <div className="flex justify-center ">
            <Button
              icon={<Icon icon="akar-icons:thunder" width="22" />}
              label="Quick assign"
              to="/quick-assign"
              variant={screen.lt.sm() ? 'md' : 'lg'}
              colorType="magic"
            />
          </div>

          <div className="flex justify-center">
            <Button colorType="secondary" label="Browse our Assessment Library" to="/programming-test/list" variant={screen.lt.sm() ? 'md' : 'lg'} />
          </div>
        </div>
        <div className="flex flex-col sm:flex-row gap-4  justify-center items-center w-full sm:w-auto">
          {/* <Button
              label="Quick Assign"
              to="/quick-assign"
              //  size="lg"
              icon="lsicon:lightning-outline"
              className="w-full sm:w-48"
            /> */}

          {/* <Button
              label="Browse our Assessment Library"
              to="/programming-test"
              // outline
              // size={screen.lt.sm() ? 'md' : 'lg'}
              className="w-full sm:w-auto"
            /> */}
        </div>
      </div>
    </div>
  );
};
