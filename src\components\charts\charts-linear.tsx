// Recharts
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer, Legend, Area, AreaChart } from 'recharts';

interface ChartsLinearProps {
  data: {
    x: number;
    uptime: number;
  }[];
  labels: {
    dataKey: string;
    stroke: string;
    fill: string;
    name: string;
  }[];
  hideLegend?: boolean;
}
export const ChartsLinear = ({ data, labels, hideLegend }: ChartsLinearProps) => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <AreaChart data={data}>
        <defs>
          <linearGradient id="colorUptime" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.5} />
            <stop offset="95%" stopColor="#3b82f6" stopOpacity={0} />
          </linearGradient>
          <linearGradient id="colorDowntime" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#10b981" stopOpacity={0.5} />
            <stop offset="95%" stopColor="#10b981" stopOpacity={0} />
          </linearGradient>
          <linearGradient id="colorResponse" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor="#a855f7" stopOpacity={0.5} />
            <stop offset="95%" stopColor="#a855f7" stopOpacity={0} />
          </linearGradient>
        </defs>

        <XAxis dataKey="x" />
        <YAxis />
        <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
        <Tooltip />
        <Legend wrapperStyle={{ display: hideLegend ? 'none' : 'block' }} />

        {labels?.map((label) => (
          <Area
            key={label?.dataKey}
            type="linear"
            dataKey={label?.dataKey}
            stroke={label?.stroke}
            fill={label?.fill}
            strokeWidth={2}
            name={label?.name}
          />
        ))}
      </AreaChart>
    </ResponsiveContainer>
  );
};
