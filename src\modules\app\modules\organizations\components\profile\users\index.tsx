// React
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// Flowbite
import { Tooltip } from 'flowbite-react';

import type { GetUsersOrganizationReq, GetUsersOrganizationRes } from 'UI/src/types/User';
import { StaticData, RootState, useAppSelector, UserData } from 'UI/src';

// Core
import { Icon, CustomIcon, ToggleFilter, Button, TestDifficulty, TestSeniorityLevel, AvarageScore, Table, EnumText } from 'src';
import { Tags } from 'UI/src/components/tags';

// Flowbite
import {} from 'flowbite-react';

// Components
// import { InlineFilter } from '../../inline-filter';
import { UsersSIngleDialog } from '../../../../users/components/single-dialog';
import { useAppDispatch, useFetchList, useScreenSize } from 'UI/src';
import { setNotifyMessage } from 'UI';

export const UsersProfile = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // State
  const [showMoreMap, setShowMoreMap] = useState<Record<string, boolean>>({});
  const [backupList, setBackupList] = useState<GetUsersOrganizationRes[]>([]);
  const [filterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);
  const [isEditUserDialogVisible, setEditUserDialogVisiblity] = useState(false);

  // Hooks
  const screen = useScreenSize();
  const { id } = useParams();
  const dispatch = useAppDispatch();
  const initialFilters = {
    role: {
      label: 'Role',
      enum: 'RoleWithoutSuperAdmin',
    },
  };

  const {
    ready,
    loading,
    list: listRaw,
    count,
    search,
    pagination,
    filters,
    setFilters,
    refresh,
  } = useFetchList('users/list', {
    search: '',
    id: id,
    pagination: {
      page: 1,
      size: 20,
    },
    filters: initialFilters,
  } as GetUsersOrganizationReq);

  // @ts-ignore
  const filterFeedData = Object.keys(initialFilters).map((key) => (key === 'difficulty' ? initialFilters.difficulty.enum : key));
  const list: GetUsersOrganizationRes[] = Array.isArray(listRaw) ? listRaw : [];

  // useEffect(() => {
  //   if (backupList.length === 0) {
  //     setBackupList(list);
  //   }
  // }, [list]);

  return (
    <div className="space-y-4">
      {/* <InlineFilter
        data={data}
        selectedInlineFilter={{
          activeInlineFilter: activeInlineFilter,
          setActiveInlineFilter: setActiveInlineFilter,
        }}
      /> */}

      {/* Need to add to table main buttons */}
      {/* <button className="inline-flex items-center gap-2 justify-center h-10 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
        <Icon icon="ion:filter" width="22" />
        <span className="hidden sm:block">Export</span>
      </button> */}
      <Table
        ready={ready}
        loading={loading}
        title="Users List"
        searchPlaceholder={screen.customScreen ? 'Search by user name or email' : 'Name or email'}
        count={count}
        search={search}
        filters={filters}
        // setFilters={setFilters}
        // filterFeedData={Object.keys(initialFilters)}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          name: (_: string, row: { _id: string; name: string }) => {
            return (
              <div className="flex relative gap-2 cursor-pointer" onClick={() => setEditUserDialogVisiblity(true)}>
                <div className="w-full">
                  <div className="break-words overflow-auto whitespace-normal text-clip capitalize font-medium text-gray-800 dark:text-grayTextOnDarkMood">
                    <p className={`lg:truncate ${!showMoreMap[row._id] && 'truncate sm:overflow-visible sm:whitespace-normal'}`}>{row?.name}</p>
                  </div>
                  {screen.gt.md() && (
                    <Tooltip content={row?.name} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs">
                      <div className="w-[92%] h-full absolute left-0 top-0"></div>
                    </Tooltip>
                  )}
                </div>
              </div>
            );
          },
          email: (_: string, row: { _id: string; email: string }) => {
            return (
              <div className="flex relative gap-2">
                <div className="w-full">
                  <div className="flex items-center gap-2">
                    <span className="text-[#626874] dark:text-gray-400 truncate">{row?.email}</span>
                    <span
                      onClick={() => {
                        navigator.clipboard.writeText(row.email);
                        dispatch(setNotifyMessage('Email copied'));
                      }}
                      className="inline-block cursor-pointer text-gray-500 dark:text-gray-400"
                    >
                      <Tooltip
                        content="Copy Email"
                        placement="bottom"
                        arrow={false}
                        className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 text-xs"
                      >
                        <Icon icon="ooui:copy-ltr" className="relative text-[#798296] text-base" />
                      </Tooltip>
                    </span>
                  </div>
                </div>
              </div>
            );
          },
          customRolesName: (_: any, row: GetUsersOrganizationRes) => {
            const roleName = row.roleName;
            let displayName = '';
            let tagColor = '';

            switch (roleName) {
              case 'admin':
                displayName = 'Admin';
                tagColor = 'bg-[#F1E9FE] text-[#562CE5]';
                break;
              case 'hr':
                displayName = 'HR';
                tagColor = 'bg-[#FFEDD8] text-[#E9760F]';
                break;
              case 'content-creator':
                displayName = 'Content Creator';
                tagColor = 'bg-[#E0F3FB] text-[#11ABE6]';
                break;
              case 'super-admin':
                displayName = 'super-admin';
                tagColor = 'bg-[#FFFCDF] text-[#BA8500]';
                break;
              default:
                displayName = roleName.charAt(0).toUpperCase() + roleName.slice(1);
            }

            return (
              <Tags type={roleName} color={tagColor}>
                {displayName}
              </Tags>
            );
          },
        }}
        columns={[
          {
            key: 'name',
            label: 'Name',
            primary: true,
            width: '20%',
          },
          {
            key: 'email',
            label: 'Email',
            primary: true,
            width: '20%',
          },
          {
            key: 'customRolesName',
            label: 'Role',
            primary: true,
            width: '15%',
          },
          // {
          //   key: 'actions',
          //   label: 'Actions',
          //   width: '10%',
          //   buttons(_: string, row) {
          //     const isSuperAdmin = userData?.role.includes('super-admin');

          //     return [
          //       {
          //         label: 'Edit',
          //         customIcon: 'edit',
          //         color: isSuperAdmin ? 'text-gray-400 dark:text-gray-600' : 'text-black dark:text-white',
          //         onClick: () => {
          //           if (isSuperAdmin) {
          //             dispatch(setNotifyMessage('SuperAdmin can’t be able to edit organization’s users.');
          //             return;
          //           }
          //           setEditUserDialogVisiblity(true);
          //         },
          //       },
          //     ];
          //   },
          // },
        ]}
        groups={[
          {
            name: 'group1',
            keys: ['name', 'customRolesName'],
          },
        ]}
        // multiSelectedRow={{
        //   selectedIds: selectedIds,
        //   setSelectedIds: setSelectedIds,
        //   handleArchiveSelectedIds: handleArchiveSelectedIds,
        // }}

        placeholder={{
          title: 'No users created yet',
          subTitle: 'Start by creating users to assign roles and manage your organization.',
          image: '/UI/src/assets/placeholder/NoUsers.svg',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={!!Object.keys(showMoreMap).length}
        setShowMoreMap={setShowMoreMap}
        hideJumbotron
        isScrollableTabsExists
        addButtonPermission
      />

      {isEditUserDialogVisible && <UsersSIngleDialog id={id as any} onClose={() => setEditUserDialogVisiblity(false)} onCreate={refresh} />}
    </div>
  );
};
