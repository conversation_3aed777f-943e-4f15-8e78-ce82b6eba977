interface RouteData {
  title?: string;
  subtitle?: string;
  infoIcon?: boolean;
  infoText?: string;
  icon?: React.ReactNode;
  label?: string;
}

export const breadCrumbRoutesMap: Record<string, RouteData> = {
  '/app/dashboard': { label: 'Dashboard', title: 'Statistics' },
  '/app/dashboard-super-admin': { label: 'Super Admin Dashboard' },
  '/app/organizations': { label: 'Organizations' },
  '/app/organizations/list': { label: 'Organizations List' },
  '/app/plans': { label: 'Plans' },
  '/app/plans/list': { label: 'Plans List' },
  '/app/roles': { label: 'Roles' },
  '/app/roles/list': { label: 'Roles List' },
  '/app/questions': { label: 'Questions' },
  '/app/questions/list': { label: 'Questions List' },
  '/app/tests': { label: 'Tests' },
  '/app/tests/list/setup': { label: 'Tests List' },
  '/app/tests/create': { label: 'Create Test', title: 'Create Test', subtitle: 'Create tests using the form below.' },
  '/app/submissions': { label: 'Submissions' },
  '/app/submissions/list': { label: 'Submissions List' },
  '/app/applicants': { label: 'Applicants' },
  '/app/applicants/list': { label: 'Applicants List' },
  '/app/users': { label: 'Users' },
  '/app/users/list': { label: 'Users List' },
  '/app/interviews': { label: 'Interviews' },
  '/app/interviews/list': { label: 'Interviews List' },
  '/app/phone-screening': { label: 'Phone Screening' },
  '/app/phone-screening/list': { label: 'Phone Screening List' },
  '/app/assessments': { label: 'Assessments' },
  '/app/assessments/list': { label: 'Assessments List' },
  '/app/assessment-report': { label: 'Assessment Report' },
  '/app/assessment-report/list': { label: 'Assessment Report List' },
  '/app/category-management': { label: 'Category Management' },
  '/app/studio': { label: 'Studio' },
};
