// React
import React, { useEffect, useRef, useState } from 'react';

// Core
import { Drawer, TextInput, Select, MultiSelect, Button, Textarea } from 'src';

// Flowbite
import { Checkbox as FlowbiteCheckbox, Label } from 'flowbite-react';

// Components
import { QuestionItem } from '../../question-item';
import {
  setErrorNotify,
  Regex,
  Api,
  useValidate,
  Form,
  initializeForm,
  RootState,
  setFieldValue,
  useAppDispatch,
  useAppSelector,
  QuestionDifficulty,
} from 'UI/src';

// Types
import { QuestionGenerationRes, CustomIconType } from 'UI';
import { useFormik } from 'formik';

/**
 * Params for handleTotalQuestions API call
 */
interface HandleTotalQuestionsParams {
  /**
   * categoryIds (required)
   * array of string
   * (query)
   */
  categoryIds: string[];
  /**
   * subCategoryIds (required)
   * array of string
   * (query)
   */
  subCategoryIds: string[];
  /**
   * difficulty (required)
   * array of string
   * (query)
   */
  difficulty: string[];
}

interface RandomizeAskAiPageProps {
  onClose: () => void;
  questionsListData: {
    list: any[];
    selectedQuestionsID: Record<string, boolean>;
    setSelectedQuestionsID: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  };
  type: 'randomize' | 'ask-ai';
  anyQuestionHasEditMode: Record<string, boolean>;
  setAnyQuestionHasEditMode: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  selectedQuestionsID: any;
  setSelectedQuestionsID: any;
}

export const RandomizeAskAiPage: React.FC<RandomizeAskAiPageProps> = ({
  onClose,
  questionsListData,
  type,
  anyQuestionHasEditMode,
  setAnyQuestionHasEditMode,
}) => {
  const { list, selectedQuestionsID, setSelectedQuestionsID } = questionsListData || {};

  // State
  const [generatedQuestions, setGeneratedQuestions] = useState<QuestionGenerationRes[]>([]);
  const [selectedGeneratedQuestions, setSelectedGeneratedQuestions] = useState<Record<string, boolean>>({});
  const [numberOfTotalQuestionsCanAdd, setNumberOfTotalQuestionsCanAdd] = useState<number | undefined>();

  // Reference
  const subCategoryRef = useRef<HTMLInputElement | null>(null);

  const dispatch = useAppDispatch();
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      numOfQuestions: null,
      category: null,
      subCategory: null,
      questionsDifficulty: {
        1: false,
        2: false,
        3: false,
        4: false,
      },
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  const { isRequired, isSelected, isNumber, isValidateMaxAndMinNumber } = useValidate();

  const handleSubmit = async () => {
    if (type === 'randomize') {
      try {
        const response = await Api.post('questions/generate', {
          numOfQuestions: form.numOfQuestions,
          category: [form.category],
          subCategory: form.subCategory,
          questionsDifficulty: Object.keys(form.questionsDifficulty)
            .filter((key) => form.questionsDifficulty[Number(key)])
            .map(Number),
          exclude: [
            ...generatedQuestions?.map((question) => question?._id),
            ...Object.entries(selectedQuestionsID)
              .filter(([_, value]) => value)
              .map(([key, _]) => key),
          ],
        });
        setGeneratedQuestions((prev) => [...prev, ...(response.data as QuestionGenerationRes[])]);
        setSelectedGeneratedQuestions((prev) => ({
          ...prev,
          ...Object.fromEntries((response?.data as QuestionGenerationRes[])?.map((id) => [id._id, true])),
        }));
      } catch (error: any) {
        dispatch(setErrorNotify(error?.response?.data?.message));
      }
    } else if (type === 'ask-ai') {
      try {
        //TODO:Handle multiple category
        //TODO:sets a maximum limit for the number of questions.
        const response = await Api.post('ai-interview/generate/questions', {
          numOfQuestions: form.numOfQuestions,
          category: [form.category],
          subCategory: form.subCategory,
          difficulty: Object.keys(form.questionsDifficulty)
            .filter((key) => form.questionsDifficulty[Number(key)])
            .map(Number),
        });
        setGeneratedQuestions((prev) => [...prev, ...(response.data as QuestionGenerationRes[])]);
      } catch (error: any) {
        dispatch(setErrorNotify(error?.response?.data?.message));
      }
    } else {
      throw new Error(`Type is not defined: ${type}`);
    }
  };

  /* Get total question exists of selected subcategories */
  const handleTotalQuestions = async (params?: HandleTotalQuestionsParams): Promise<void> => {
    try {
      const requestParams: HandleTotalQuestionsParams = params || {
        categoryIds: [form.category],
        subCategoryIds: Array.isArray(form.subCategory) ? form.subCategory : [],
        difficulty: Object.entries(form.questionsDifficulty)
          .filter(([_, value]) => value)
          .map(([key, _]) => key),
      };
      const response = await Api.get<number>('questions/total', requestParams);
      setNumberOfTotalQuestionsCanAdd(response?.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
    }
  };

  const handleAvailableQuestions = () => {
    if (typeof numberOfTotalQuestionsCanAdd !== 'number') return 0;
    return (
      numberOfTotalQuestionsCanAdd -
      Object.entries(selectedQuestionsID)
        ?.filter(([_, value]) => value)
        ?.map(([key, _]) => key)?.length -
      generatedQuestions?.length
    );
  };

  const renderMessage = () => {
    const availableQuestions = handleAvailableQuestions();
    if (availableQuestions < 100) {
      if (!form.numOfQuestions) {
        return {};
      } else if (form.numOfQuestions <= availableQuestions) {
        return {};
      } else if (
        form.numOfQuestions > 0 &&
        availableQuestions === 0 &&
        Object.values(form.questionsDifficulty).some((value: unknown) => value === true)
      ) {
        return {
          text: 'No questions available with the selected difficulty level(s). Please adjust the difficulty.',
          isButtonDisabled: true,
          color: 'text-[#C72716]',
          icon: (
            <svg width="20" height="20" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6 8.27222V8.27778M6 2.72222V6.61111M11 5.5C11 8.26144 8.76144 10.5 6 10.5C3.23858 10.5 1 8.26144 1 5.5C1 2.73858 3.23858 0.5 6 0.5C8.76144 0.5 11 2.73858 11 5.5Z"
                stroke="#C72716"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      } else if (form.numOfQuestions > availableQuestions) {
        return {
          text:
            availableQuestions === 0
              ? 'No more available questions'
              : `Only ${availableQuestions} questions are avaliable${form.category ? ' in this category' : ''}, please adjust your number.`,
          isButtonDisabled: true,
          color: 'text-[#C72716]',
          icon: (
            <svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6 8.27222V8.27778M6 2.72222V6.61111M11 5.5C11 8.26144 8.76144 10.5 6 10.5C3.23858 10.5 1 8.26144 1 5.5C1 2.73858 3.23858 0.5 6 0.5C8.76144 0.5 11 2.73858 11 5.5Z"
                stroke="#C72716"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      }
    } else if (availableQuestions >= 100) {
      if (!form.numOfQuestions) {
        return {
          text: 'You can add up to 100 questions at a time.',
          isButtonDisabled: true,
          color: 'text-[#667085]',
          icon: null,
        };
      } else if (form.numOfQuestions <= 100) {
        return {
          text: 'You can add up to 100 questions at a time.',
          isButtonDisabled: false,
          color: 'text-[#479E64]',
          icon: (
            <svg width="10" height="8" viewBox="0 0 10 8" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M1 4.30555L3.46154 6.75L9 1.25" stroke="#479E64" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          ),
        };
      } else if (form.numOfQuestions > 100) {
        return {
          text: 'You can add up to 100 questions at a time.',
          isButtonDisabled: true,
          color: 'text-[#C72716]',
          icon: (
            <svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6 8.27222V8.27778M6 2.72222V6.61111M11 5.5C11 8.26144 8.76144 10.5 6 10.5C3.23858 10.5 1 8.26144 1 5.5C1 2.73858 3.23858 0.5 6 0.5C8.76144 0.5 11 2.73858 11 5.5Z"
                stroke="#C72716"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      }
      // else if (form.numOfQuestions > questionDatabaseOfMainTest?.length) {
      else if (form.numOfQuestions > list?.length) {
        return {
          //           text: `You’ve selected ${questionDatabase.length} questions. You can add up to ${availableQuestions} more..`,

          text: `You’ve selected ${
            Object.keys(selectedQuestionsID).filter((key) => selectedQuestionsID[key]).length
          } questions. You can add up to ${availableQuestions} more..`,
          isButtonDisabled: true,
          color: 'text-[#C72716]',
          icon: (
            <svg width="12" height="11" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M6 8.27222V8.27778M6 2.72222V6.61111M11 5.5C11 8.26144 8.76144 10.5 6 10.5C3.23858 10.5 1 8.26144 1 5.5C1 2.73858 3.23858 0.5 6 0.5C8.76144 0.5 11 2.73858 11 5.5Z"
                stroke="#C72716"
                strokeWidth="0.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          ),
        };
      }
    } else {
      return {
        text: '',
        isButtonDisabled: false,
        color: '',
        icon: null,
      };
    }
  };

  useEffect(() => {
    handleTotalQuestions();
  }, [form]);

  return (
    <Drawer onClose={onClose} className="!max-w-[500px]">
      <Drawer.SingleView>
        <Drawer.Header
          headerLabel={type === 'randomize' ? 'Randomize Questions' : 'Generate Questions with AI'}
          description={
            type === 'randomize'
              ? 'Generate a random selection of questions  from question bank based on your criteria'
              : 'Create custom questions using AI based on your specifications'
          }
          className=""
        />
        <div className="h-full overflow-y-auto">
          <Drawer.Body className="flex flex-col">
            <Form className="space-y-4" onSubmit={(e) => e.preventDefault()}>
              <div className={`flex align-middle items-center gap-2 text-[13px] ${renderMessage()?.color || ''}`}>
                {renderMessage()?.icon}
                {renderMessage()?.text}
              </div>

              <TextInput
                label="Questions Number"
                name="numOfQuestions"
                type="number"
                placeholder="0"
                value={form.numOfQuestions}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'numOfQuestions', type: Number, value }))}
                validators={[isRequired()]}
              />

              <Select
                label="Category"
                name="category"
                placeholder="Search for category"
                value={form.category}
                onChange={(newCategory: string) => {
                  subCategoryRef.current?.blur();
                  dispatch(setFieldValue({ path: 'category', value: newCategory }));
                  dispatch(setFieldValue({ path: 'subCategory', value: null }));
                }}
                lookup="category"
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon
                requiredLabel
                validators={[isRequired()]}
              />

              <MultiSelect
                key={form.category}
                label="Subcategory"
                requiredLabel
                name="subCategory"
                placeholder="Search for subcategory"
                value={Array.isArray(form.subCategory) ? form.subCategory : []}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'subCategory', value }))}
                disabled={!form.category}
                disabledMessage="Please select category first"
                lookup="subcategory"
                params={{ categoryId: form.category }}
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon
                validators={[isRequired()]}
              />

              <div className="space-y-3">
                <p className="text-inputLabel dark:text-inputDarkLabel text-sm font-medium">
                  Difficulty <span className="text-red-600 dark:text-red-800"> *</span>
                </p>
                <div className="flex justify-between items-center mx-1">
                  {QuestionDifficulty.map((difficulty: { value: number; label: string }) => (
                    <div className="flex gap-2" key={difficulty.value}>
                      <FlowbiteCheckbox
                        id={String(difficulty.value)}
                        value={difficulty.value}
                        checked={form.questionsDifficulty[difficulty.value]}
                        className="text-purple-600 focus:ring-purple-500"
                        onChange={(value: React.ChangeEvent<HTMLInputElement>) =>
                          dispatch(
                            setFieldValue({
                              path: 'questionsDifficulty',
                              value: { ...form.questionsDifficulty, [difficulty.value]: value.target.checked },
                            })
                          )
                        }
                      />
                      <Label htmlFor={String(difficulty.value)} className="text-inputLabel dark:text-inputDarkLabel pb-4">
                        {difficulty.label}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {type === 'ask-ai' && (
                <Textarea
                  label="Additional Instructions"
                  name="instructions"
                  placeholder=""
                  value={form.instructions}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'instructions', value }))}
                  validators={[isRequired()]}
                  rows={4}
                />
              )}

              <Button
                label={type === 'randomize' ? 'Randomize Questions' : 'Generate Questions'}
                customIcon={{ definedIcon: type === 'randomize' ? 'random' : 'stars' } as CustomIconType}
                tertiary
                className="w-full"
                onClick={handleSubmit}
                disabled={
                  !form.numOfQuestions ||
                  !form.category ||
                  !form.subCategory ||
                  (Array.isArray(form.subCategory) && form.subCategory.length === 0) ||
                  !form.questionsDifficulty ||
                  renderMessage()?.isButtonDisabled
                }
              />

              {generatedQuestions.length > 0 && (
                <>
                  <p className="dark:text-white font-semibold">{type === 'randomize' ? 'Preview' : 'Generated'} Questions</p>
                  {generatedQuestions.map((question) => (
                    <QuestionItem
                      key={question._id}
                      questionId={question._id}
                      selectedQuestionsID={selectedGeneratedQuestions}
                      setSelectedQuestionsID={setSelectedGeneratedQuestions}
                      anyQuestionHasEditMode={anyQuestionHasEditMode}
                      setAnyQuestionHasEditMode={setAnyQuestionHasEditMode}
                      canEditQuestion={false}
                      canRemoveQuestion={true}
                    />
                  ))}
                </>
              )}
            </Form>
          </Drawer.Body>
        </div>
        {/* FIXME: */}
        <Drawer.Footer.Button
          label="Add Selected Questions"
          className="!w-full"
          onClick={() => {
            setSelectedQuestionsID((prev) => ({ ...prev, ...selectedGeneratedQuestions }));
            onClose();
          }}
          disabled={Object.keys(selectedGeneratedQuestions)?.filter((key) => selectedGeneratedQuestions[key])?.length <= 0}
        />
        {type === 'randomize' && <Button label="Cancel" className="!w-full" tertiary onClick={onClose} />}
      </Drawer.SingleView>
    </Drawer>
  );
};
