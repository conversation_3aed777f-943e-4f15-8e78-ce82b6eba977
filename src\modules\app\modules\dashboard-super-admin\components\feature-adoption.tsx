// Core
import { Icon, ChartsWavy } from 'src';

// Flowbite
import { Rating } from 'flowbite-react';
import { useEffect, useState } from 'react';
import { Api } from 'UI/src';
import { useAppDispatch } from 'UI/src';
import { setErrorNotify } from 'UI';

type assessmentData = {
  customTestGrowth: any;
  assessmentUsage: any;
};

export const FeatureAdoption = () => {
  const [assessmentData, setAssessmentData] = useState<assessmentData>();
  const dispatch = useAppDispatch();

  // Methods

  const handleGet = async () => {
    try {
      const response = await Api.get('superAdmin/assessment/overview');
      setAssessmentData(response.data);
      console.log('superAdmin/assessment/overview', response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
      // setData(allData); // Not found
    }
  };

  //  !* Transform API data into chart data format
  // !* guide  *to match data from backend to each line in chart
  const transformChartData = () => {
    if (!assessmentData?.customTestGrowth) return [];

    // !* destructing
    const { customTestCount, predefinedTestCount, interviewCount } = assessmentData.customTestGrowth;

    // Create data for all months (1-12)
    return Array.from({ length: 12 }, (_, i) => {
      const month = i + 1;
      const monthItem = customTestCount.find((item: { month: number }) => item.month === month);
      const interviewItem = interviewCount.find((item: { month: number }) => item.month === month);
      const predefinedItem = predefinedTestCount.find((item: { month: number }) => item.month === month);

      return {
        x: new Date(2024, month - 1).toLocaleString('default', { month: 'short' }),
        a: interviewItem?.count || 0,
        b: monthItem?.count || 0,
        c: predefinedItem?.count || 0,
      };
    });
  };
  const chartsdata: any = transformChartData();

  const chartLabels = {
    a: 'AI Interviews',
    b: 'Custom Assessments',
    c: 'Predefined Assessments',
  };

  const chartColors = {
    a: '#743AF5',
    b: '#11ABE6',
    c: '#AF52DE',
  };

  // Calculate month-over-month percent change per series (a, b, c)
  const getSeriesChangePercent = (type: number): number => {
    const key: 'a' | 'b' | 'c' = type === 1 ? 'a' : type === 2 ? 'b' : 'c';
    if (!chartsdata || chartsdata.length < 2) return 0;

    const lastIndex = chartsdata.length - 1;
    const currentValue = Number(chartsdata[lastIndex]?.[key] ?? 0);
    const previousValue = Number(chartsdata[lastIndex - 1]?.[key] ?? 0);
    if (!Number.isFinite(currentValue) || !Number.isFinite(previousValue)) return 0;
    if (previousValue === 0) return 0;

    const change = ((currentValue - previousValue) / previousValue) * 100;
    return change;
  };

  const colorType = (type: number) => {
    if (type === 1) return chartColors.a;
    else if (type === 2) return chartColors.b;
    else if (type === 3) return chartColors.c;
  };

  useEffect(() => {
    handleGet();
  }, []);

  const getFeatureData = () => {
    if (!assessmentData?.assessmentUsage) return [];
    const { interviewUsedPercentage, customTestUsagePercentage, predefinedTestUsagePercentage } = assessmentData.assessmentUsage;
    return [
      {
        type: 1,
        title: 'AI Interviews',
        percentage: interviewUsedPercentage,
      },
      {
        type: 2,
        title: 'Custom Assessments',
        percentage: customTestUsagePercentage,
      },
      {
        type: 3,
        title: 'Predefined Assessments',
        percentage: predefinedTestUsagePercentage,
      },
    ];
  };
  const featureData = getFeatureData();

  return (
    <div className="p-2">
      <div className="-ml-8">
        <ChartsWavy data={chartsdata} dataKeys={chartLabels} colors={chartColors} />
      </div>

      <div className="space-y-2">
        {featureData?.map((item, index) => {
          return (
            <div className="flex justify-between pt-2 dark:text-white text-sm font-medium" key={`${item.type}-${index}`}>
              <div className="flex items-start gap-2.5">
                {item?.type && (
                  <p
                    className="mt-1 size-3 rounded-[2px]"
                    style={{
                      backgroundColor: colorType(item.type),
                    }}
                  />
                )}
                <div className="space-y-1">{item?.title && <p>{item?.title}</p>}</div>
              </div>
              <div className="flex items-center gap-3">
                {item?.percentage && <p className=" text-sm font-semibold">{item?.percentage}% Usage</p>}
                {(() => {
                  const value = getSeriesChangePercent(item.type);
                  const isUp = (item?.percentage ?? 0) >= 50; // arrow & color based on 50% usage threshold
                  return (
                    <div className={`flex items-center gap-1 ${isUp ? 'text-green-600' : 'text-red-600'}`}>
                      <Icon icon={isUp ? 'mdi:arrow-up' : 'mdi:arrow-down'} width="16" />
                      <span className="text-sm font-semibold">{Math.abs(value).toFixed(1)}%</span>
                    </div>
                  );
                })()}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
