import { classNames } from '../../../utils/tailwind-classes';
import { ToggleFilter } from '../../../../../src';
import { PermissionProtectedComponent } from '../../../../../src';
import { Button } from '../../../..';
import { Search } from 'lucide-react';
import {
  //TableState, 
  type DataTableProps
} from '../config';
import { AlertNote } from '../../alert-note';
import CheckFeatureManagement from '../../../hooks/feature-management'

/* eslint-disable @typescript-eslint/no-explicit-any */

// Design-system shell that renders a table using column defs
function Toolbar<T extends Record<string, any>>({
  sectionTitle,
  sectionBadgeTitle,
  search,
  filters,
  addButtonLabel,
  onClickAdd,
  addButtonFeature,
  addButtonPermission,
  addButtonPermissionOperator
}: DataTableProps<T>) {
  const { checkFeature } = CheckFeatureManagement();


  const AddBtn = () => (
    addButtonLabel?.length
      ? addButtonPermission ?
        <PermissionProtectedComponent permissions={addButtonPermission} operator={addButtonPermissionOperator}>
          <Button label={addButtonLabel} onClick={onClickAdd} permission={addButtonFeature} />
        </PermissionProtectedComponent>
        : <Button label={addButtonLabel} onClick={onClickAdd} permission={addButtonFeature} />
      : null
  );
  return (
    <>
      {(sectionTitle || search || filters) && (
        <div className="flex items-center justify-between w-full my-5">
          <div className="flex items-center justify-start gap-3">
            {sectionTitle && <h2 className="text-[#1B1F3B] text-[18px]">{sectionTitle}</h2>}
            {sectionBadgeTitle !== undefined && (
              <span className="text-[#743AF5] bg-[#F5F6F8] text-[12px] px-2 py-1 rounded-lg">{sectionBadgeTitle}</span>
            )}
          </div>
          <div className="flex flex-col items-end justify-center">
            <AddBtn />
            <div className="flex items-end gap-3 justify-center">
              {search && (
                <div className={`rounded-lg flex w-full flex-row items-center space-x-3 space-y-0 justify-between shadow-sm`}>
                  <div className="relative w-full">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Search width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder={search.placeholder || 'Search...'}
                      className={classNames(
                        'block min-w-md w-full rounded-lg pl-10 pr-3 py-2 text-base text-default-900 placeholder:text-default-400 transition-colors duration-200',
                        'placeholder-[#4E5E82] border border-[#DEE2E4] hover:border-[#DEE2E4] hover:bg-[#FFFFFF] hover:shadow-[0_0_9px_0_#743AF51A] focus:border-[#A47BFA] focus:shadow-[0px_1px_2px_0px_#21779917]'
                      )}
                      value={search.value}
                      onInput={(e) => search.update((e.target as HTMLInputElement).value)}
                    />
                  </div>
                </div>
              )}
              {filters && <ToggleFilter filters={filters} resultsFound={Number(sectionBadgeTitle) || 0} />}
            </div>
          </div>
        </div>
      )}
      <PermissionProtectedComponent permissions={addButtonPermission} operator={addButtonPermissionOperator}>
        {!checkFeature(addButtonPermission as any) && (
          <AlertNote message="Oops! You’ve hit your limit , Add extra credits to keep enjoying all the features." nav={{}} />
        )}
      </PermissionProtectedComponent>
    </>
  );
}

export default Toolbar;
