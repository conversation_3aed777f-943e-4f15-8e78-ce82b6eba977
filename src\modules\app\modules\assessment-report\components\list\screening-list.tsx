// React
import { useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';

// Core
import {
  Icon,
  CustomIcon,
  ToggleFilter,
  Button,
  TestDifficulty,
  TestSeniorityLevel,
  AvarageScore,
  Table,
  DurationFieldColumn,
  NameFieldColumn,
} from 'src';

import { StaticData, RootState, useAppSelector, UserData, useFetchList, useScreenSize } from 'UI/src';

interface initialFiltersTypes {
  difficulty?: {
    label: string;
    enum: string;
  };
  seniorityLevel: {
    label: string;
    enum: string;
  };
}

export const ScreeningList = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Permissions
  const isPermitted = Array.isArray(userData?.role) && userData?.role.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState<boolean | undefined>(false);
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState(false);

  // Hooks
  const navigate = useNavigate();
  const screen = useScreenSize();
  const initialFilters: initialFiltersTypes = {
    // ...(userData.trackId
    //   ? {}
    //   : {
    //       category: {
    //         label: 'Category',
    //         lookup: 'category',
    //       },
    //     }),
    // subCategory: {
    //   label: 'Sub Category',
    //   lookup: 'subcategory',
    //   parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: null },
    // },
    // difficulty: {
    //   label: 'Difficulty',
    //   enum: 'QuestionDifficulty',
    // },
    seniorityLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },
  };
  const filterFeedData = Object.keys(initialFilters).map((key) => (key === 'difficulty' ? initialFilters.difficulty?.enum : key));
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters, refresh, handleDates } = useFetchList(
    'submissions/generated-screening',
    {
      search: '',
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
    }
  );

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
    }
  }, [list]);

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Screening Reports"
        searchPlaceholder="Search for template name..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        // filterFeedData={filterFeedData}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          applicantName: (_: unknown, row: { _id: string; title: string; quizId: string }) => (
            <NameFieldColumn
              id={row?._id}
              name={row?.title}
              showMoreMap={showMoreMap as any}
              onClick={() => navigate(`/app/assessment-report/view/screening/${row?.quizId}`)}
            />
          ),
          seniorityLevel: (_: unknown, row: { seniorityLevel: number }) => (
            <div className="w-fit">
              <TestSeniorityLevel seniorityLevel={row?.seniorityLevel} />
            </div>
          ),
          duration: (_: unknown, row: { duration: number }) => <DurationFieldColumn duration={row?.duration} />,
          usage: (_: unknown, row: { assignedCount: number }) => {
            return <div className="capitalize font-medium text-sm text-[#535862] truncate">{row?.assignedCount}</div>;
          },
          // averageScore: (_, row) => {
          //   return (
          //     <div className="w-fit">
          //       <AvarageScore score={row?.averageScore} />
          //     </div>
          //   );
          // },
        }}
        columns={[
          {
            key: 'applicantName',
            label: 'Name',
            primary: true,
            width: '22%',
          },
          {
            key: 'seniorityLevel',
            label: 'Seniority Level',
            primary: true,
            width: '18%',
          },
          {
            key: 'duration',
            label: 'Duration',
            // primary: true,
            width: '15%',
          },
          {
            key: 'usage',
            label: 'Usage',
            // primary: true,
            width: '10%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_: unknown, row: { quizId: string }) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/assessment-report/view/screening/${row?.quizId}`,
                },
              ];
            },
          },
        ]}
        groups={[
          {
            name: 'group1',
            keys: [['seniorityLevel'], ['duration']], // from table
          },
        ]}
        // noDataFound={{
        //   customIcon: 'applicant',
        //   message: 'No assessment created yet',
        // }}
        placeholder={{
          title: 'No reports generated yet',
          subTitle: 'Assign assessments to applicants to generate comprehensive evaluation reports.',
          image: '/UI/src/assets/placeholder/NoReports.svg',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        hideJumbotron
        isScrollableTabsExists
      />

      {/* Need to add to table main buttons */}
      {/* <div className="flex justify-between items-center gap-x-2">
        <div className="calendar flex flex-row justify-end items-center gap-2 text-gray-700 dark:text-gray-300 rounded-lg h-fit">
          <Datepicker
            className="inline-block w-full sm:w-44"
            // onSelectedDateChanged={() => {}}
            showTodayButton={false}
            showClearButton={false}
            // value=""
          />
        </div>

        <div className="flex gap-3">
          <Button label="Template" tertiary customIcon="template" />
          <Button label="List" icon="ix:list" />
        </div>
      </div> */}
    </>
  );
};
