import { Api } from '../';
import axios from 'axios';

// @FIXME: Error handling
export const uploadToS3 = async (videoBlob: Blob, interviewId: string, answerNumber: number, applicantId: string) => {
  if (!videoBlob || !interviewId || !answerNumber || !applicantId) return;

  // Generate a unique filename per answer to avoid overwrite
  const timestamp = Date.now();
  const randomSuffix = Math.floor(Math.random() * 100000);
  const fileName = `interviews/${interviewId}/${applicantId}/answer-${answerNumber}-${timestamp}-${randomSuffix}.webm`;

  const file = new File([videoBlob], fileName, { type: 'video/webm' });

  const MAX_RETRIES = 3;
  let retryCount = 0;

  while (retryCount < MAX_RETRIES) {
    try {
      // 1. Get pre-signed URL from backend
      const { data } = await Api.get(`ai-interview/upload-url?mimeType=${file.type}&fileName=${encodeURIComponent(fileName)}`);

      // 2. Upload to S3
      await axios.put(data.url, file, {
        headers: {
          'Content-Type': file.type,
          'Content-Disposition': 'attachment',
        },
      });

      return data.key;
    } catch (error) {
      retryCount++;
      console.error(`Upload attempt ${retryCount} failed:`, error);

      if (retryCount >= MAX_RETRIES) {
        console.error('Failed to upload interview recording after multiple attempts');
      }

      // Exponential backoff
      await new Promise((resolve) => setTimeout(resolve, 1000 * Math.pow(2, retryCount)));
    }
  }
  throw new Error('Failed to upload to S3 after multiple attempts');
};

export const uploadAvatarToS3 = async (imageBlobOrUrl: Blob | string, orgId: string, name: string) => {
  if (!imageBlobOrUrl || !orgId || !name) return;

  let blob;

  // Check if it's a URL string (starts with blob:) or a real Blob/File
  if (typeof imageBlobOrUrl === 'string' && imageBlobOrUrl.startsWith('blob:')) {
    const response = await fetch(imageBlobOrUrl);
    blob = await response.blob();
  } else {
    blob = imageBlobOrUrl;
  }

  // Generate a unique filename
  const timestamp = Date.now();
  const randomSuffix = Math.floor(Math.random() * 100000);
  const fileExtension = (blob as Blob)?.type?.split('/')?.[1] || 'png';
  const fileName = `organizations/${orgId}/avatars/${name}-${timestamp}-${randomSuffix}.${fileExtension}`;

  const file = new File([blob], fileName, { type: (blob as Blob).type });

  try {
    // 1. Get pre-signed URL from backend
    const { data } = await Api.get(`ai-interview/upload-url?mimeType=${file.type}&fileName=${encodeURIComponent(fileName)}`);

    // 2. Upload to S3
    await axios.put(data.url, file, {
      headers: {
        'Content-Type': file.type,
        'Content-Disposition': 'attachment',
      },
    });

    return data.key; // S3 key to store or reference
  } catch (error) {
    console.error('Avatar upload failed:', error);
    throw new Error('Failed to upload avatar to S3');
  }
};

export const uploadLogoToS3 = async (imageBlobOrUrl: Blob | string, orgId: string, name: string) => {
  if (!imageBlobOrUrl || !orgId || !name) return;

  let blob;

  // Check if it's a URL string (starts with blob:) or a real Blob/File
  if (typeof imageBlobOrUrl === 'string' && imageBlobOrUrl.startsWith('blob:')) {
    const response = await fetch(imageBlobOrUrl);
    blob = await response.blob();
  } else {
    blob = imageBlobOrUrl;
  }

  // Generate a unique filename
  const timestamp = Date.now();
  const randomSuffix = Math.floor(Math.random() * 100000);
  const fileExtension = (blob as Blob)?.type?.split('/')?.[1] || 'png';
  const fileName = `organizations/${orgId}/logo/${name}-${timestamp}-${randomSuffix}.${fileExtension}`;

  const file = new File([blob], fileName, { type: (blob as Blob).type });

  try {
    // 1. Get pre-signed URL from backend
    const { data } = await Api.get(`ai-interview/upload-url?mimeType=${file.type}&fileName=${encodeURIComponent(fileName)}`);

    // 2. Upload to S3
    await axios.put(data.url, file, {
      headers: {
        'Content-Type': file.type,
        'Content-Disposition': 'attachment',
      },
    });

    return data.key; // S3 key to store or reference
  } catch (error) {
    console.error('Avatar upload failed:', error);
    throw new Error('Failed to upload avatar to S3');
  }
};
