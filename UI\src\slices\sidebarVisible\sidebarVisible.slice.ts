import { createSlice } from '@reduxjs/toolkit';
import type { RootState } from '../../store';

interface sidebarVisibleState {
  isVisible: boolean;
}

const initialState: sidebarVisibleState = {
  isVisible: false,
};

const sidebarVisibleSlice = createSlice({
  name: 'sidebarVisible',
  initialState,
  reducers: {
    setSidebarVisiblity: (state, { payload }) => {
      state.isVisible = payload;
    },
  },
});

export const sidebarVisibleState = (state: RootState) => state.sidebarVisible;
export const { setSidebarVisiblity } = sidebarVisibleSlice.actions;
export default sidebarVisibleSlice.reducer;
