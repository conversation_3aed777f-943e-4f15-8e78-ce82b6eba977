import React, { PureComponent } from 'react';
import { PieChart, Pie, Sector, Cell, ResponsiveContainer } from 'recharts';

export interface ResultChartProps {
  test: { score: number; status: number; startedAt?: Date };
  size?: number;
  fontSize?: string;
}

export const ResultChart = ({ test, size = 180, fontSize = '36px' }: ResultChartProps) => {
  // Circle chart methods
  const data = [
    { name: 'Group A', value: test.score },
    { name: 'Group B', value: 100 - (test.score || 0) },
  ];

  const chartColor = () => {
    if (test.score < 50) return '#EF4444';
    else if (test.score <= 80) return '#FBBF24';
    else if (test.score > 80) return '#22C55E';
    else if (test.startedAt) return '#F2F2F2';
    else return '#F2F2F2';
  };

  const COLORS = [chartColor(), '#d1d5db'];

  const RADIAN = Math.PI / 180;

  interface CustomizedLabelProps {
    cx: number;
    cy: number;
    midAngle: number;
    innerRadius: number;
    outerRadius: number;
    percent: number;
    index: number;
  }
  
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: CustomizedLabelProps) => {
    const radius = innerRadius + (outerRadius - innerRadius) * -1.466;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  // Details chart methods
  const handleResult = () => {
    if (test.status === 1) return 'Not Started';
    else if (test.status === 2) return 'In Progress';
    else if (test.status === 3) {
      if (test.score > 80) return 'Excellent';
      else if (test.score >= 50) return 'Good';
      else if (test.score < 50) return 'Poor';
    }
  };

  const handleColors = () => {
    if (test.status === 1) {
      return {
        label: 'bg-[#EDEEF0] text-[#6B7280]',
      };
    } else if (test.status === 2) {
      return {
        label: 'text-statusColorInProgressText bg-statusColorInProgressBackground',
      };
    } else if (test.status === 3) {
      if (test.score > 80) {
        return {
          circle: 'text-chartExcellentCircle',
          label:
            'text-chartExcellentTextColor dark:text-darkChartExcellentTextColor bg-chartExcellentBackground dark:bg-darkChartExcellentBackground border-chartExcellentTextColor dark:border-darkChartExcellentTextColor', // light & dark mode classes
        };
      } else if (test.score >= 50) {
        return {
          circle: 'text-chartGoodCircle',
          label:
            'text-chartGoodTextColor dark:text-darkChartGoodTextColor bg-chartGoodBackground dark:bg-darkChartGoodBackground border-chartGoodTextColor dark:border-darkChartGoodTextColor', // light & dark mode classes
        };
      } else if (test.score < 50) {
        return {
          circle: 'text-chartPoorCircle',
          label:
            'text-chartPoorTextColor dark:text-darkChartPoorTextColor bg-chartPoorBackground dark:bg-darkChartPoorBackground border-chartPoorTextColor dark:border-darkChartPoorTextColor', // light & dark mode classes
        };
      }
    }
  };

  return (
    <div className="relative self-center sm:col-span-2 mx-auto ">
      {/* Circle chart */}
      <ResponsiveContainer width={size} minHeight={size}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            startAngle={450}
            endAngle={90}
            innerRadius={size * 0.35}
            outerRadius={size * 0.45}
            fill="#8884d8"
            dataKey="value"
            stroke="none"
          >
            {data?.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Pie>
        </PieChart>
      </ResponsiveContainer>
      {/* Details chart */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        {/* if we want to handle colors later */}
        {/* <p className={`text-4xl text-black font-medium ${handleColors()?.circle} text-center`}>{test.score || 0}%</p> */}
        <p style={{ fontSize: fontSize }} className={`text-4xl text-black font-medium dark:text-white  text-center`}>
          {test.score || 0}%
        </p>

        <p className="text-center text-[#798296] text-xs font-medium text-nowrap">Total Score</p>
      </div>
      <div
        className={`w-[88%] text-center p-1 text-base font-medium rounded-xl absolute left-1/2 -translate-x-1/2 bottom-0 py-1 border 
        ${handleColors()?.label}`}
      >
        {handleResult()}
      </div>
    </div>
  );
};
