import { FC } from 'react';

// Components
import { Checkbox } from 'src';
import { StaticData } from 'UI/src';
import { type RoleUser } from 'UI';

// Types
type UserListItemProps = {
    user: RoleUser;
    onChangeValue?: (use: RoleUser) => void;
    isView?: boolean;
};

export const UserListItem: FC<UserListItemProps> = ({
    user,
    onChangeValue = () => { },
    isView = false
}) => {

    const formattedUserName = (name: string) => {
        let nameFragmented = name.toLocaleUpperCase().split(' ');
        return nameFragmented.length > 1 ? nameFragmented.map((word) => word[0]) : `${name[0]}${name[1]}`.toLocaleUpperCase()
    }

    return (
        <div className="flex items-center gap-3">
            <div className="flex gab-2">
                {
                    isView && <Checkbox
                        fullWidth={false}
                        value={user.checked}
                        onChange={() => onChangeValue({ ...user, checked: !user.checked })}
                        theme={StaticData?.customThemeCheckbox}
                        className="cursor-pointer"
                        preventSendingMail={false}
                        isCustomLabel={false}
                    />
                }

                <div className="rounded-full p-3 border border-[#E1E4E8] bg-[#EDE9FE] text-[#8D5BF8] content-center w-12 h-12">
                    {formattedUserName(user.name)}
                </div>
            </div>
            <div className="space-y-1">
                <p>{user.name}</p>
                <p className="text-sm text-gray-500">{user.email}</p>
            </div>
        </div>
    );
};
