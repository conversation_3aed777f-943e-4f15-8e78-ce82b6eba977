import { createAsyncThunk } from "@reduxjs/toolkit";
import { Api } from "../../src";

// fetch submission async
export const fetchSubmission = createAsyncThunk(
  'submission/fetchSubmission',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`submissions/progress/${id}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch submission');
    }
  }
);

// log event async
export const logSubmissionEvent = createAsyncThunk(
  'submission/logSubmissionEvent',
  async (payload: any, { rejectWithValue }) => {
    try {
      await Api.post('logs/create', payload);
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || 'Failed to log event');
    }
  }
);