import React from 'react';
import { Breadcrumb } from 'flowbite-react';
import { useLocation } from 'react-router-dom';

import { useBreadcrumb } from 'UI/src/hooks/breadcrumb';

export const AppBreadcrumb = () => {
  const { routes, handleRouteClick } = useBreadcrumb();
  const location = useLocation();
  const currentUrl: string = location.pathname;

  console.log(routes);
  return (
    routes.length > 1 && (
      <Breadcrumb className={`${currentUrl.includes('report') ? 'hidden' : 'block'} print:hidden mb-4`} aria-label="Default breadcrumb example">
        {routes?.map((route, index: number) => (
          <Breadcrumb.Item
            href={routes.length - 1 !== index ? '#' : undefined}
            onClick={(event: React.MouseEvent<HTMLLIElement>) => {
              if (routes.length - 1 !== index) {
                handleRouteClick(event, route.pathname);
              }
            }}
            key={route.pathname}
          >
            <div className="flex items-center justify-center gap-2">
              <p
                className={`text-gray-400 dark:text-white ${
                  index === 0 ? 'hover:text-gray-500 hover:underline dark:hover:text-gray-400' : ''
                } font-normal select-none text-sm`}
              >
                {route.data.label}
              </p>
            </div>
          </Breadcrumb.Item>
        ))}
      </Breadcrumb>
    )
  );
};
