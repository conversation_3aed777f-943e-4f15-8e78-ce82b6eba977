// React
import { memo } from 'react';
import WavesurferPlayer from '@wavesurfer/react';

// Core
import { Button } from 'src';
import { useScreenSize } from 'UI/src';

// Components
import SoundVisualizer from './sound-visualizer';

const RecordingStateView = ({
  stream,
  loading,
  isConverting,
  isRecording,
  isPaused,
  isPlaying,
  deleteRecording,
  pauseRecording,
  resumeRecording,
  onPlayPause,
  audioRef,
  audioURL,
  onReady,
  recordingTime,
  setIsPlaying,
}: any) => {
  const formatTime = (time: any) => {
    const minutes = Math.floor(time / 60);
    const seconds = time % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const screen = useScreenSize();

  return (
    <>
      <div className="rounded-lg md:rounded-2xl flex justify-center items-start gap-2 sm:px-4">
        {/* Delete Button */}
        <Button
          size="sm"
          disabled={loading}
          onClick={deleteRecording}
          icon="octicon:trash-16"
          className="!bg-transparent text-[#C24444] border-0"
          tertiary
        />

        {/* Recording State */}
        {isRecording && (
          <div className="flex justify-center items-center gap-3">
            <div className="px-2 pr-4 flex justify-center items-center gap-3 border border-[#8484E1] rounded-full">
              <SoundVisualizer stream={stream} />
              <div className="text-[15px] font-semibold">{formatTime(recordingTime)}</div>
            </div>

            {/* Pause Button */}
            {isRecording && !isPaused && (
              <Button
                size="sm"
                onClick={pauseRecording}
                icon="solar:pause-bold"
                disabled={loading}
                tertiary
                className="!bg-transparent hover:!bg-transparent text-[#743AF5] border border-[#743AF5] rounded-full"
              />
            )}
          </div>
        )}

        {/* Paused State */}
        {isPaused && (
          <div className="min-w-32 h-10 flex justify-between items-center gap-2 px-2 pr-4 bg-[#CFC8D3] border border-[#8484E1] rounded-full">
            {audioURL && (
              <>
                <Button
                  size="sm"
                  icon={isPlaying ? 'solar:pause-bold' : 'solar:play-bold'}
                  className="!bg-transparent hover:!bg-transparent text-[#8484E1] border-0"
                  onClick={onPlayPause}
                  disabled={loading}
                  tertiary
                />
                <div className="flex gap-2">
                  <audio ref={audioRef} src={audioURL}></audio>
                  <WavesurferPlayer
                    height={40}
                    width={screen.gt.sm() ? 400 : 50}
                    waveColor="#6B7280"
                    url={audioURL}
                    onReady={onReady}
                    onPlay={() => setIsPlaying(true)}
                    onPause={() => setIsPlaying(false)}
                  />
                </div>
              </>
            )}

            <div className="text-[15px] font-semibold">{formatTime(recordingTime)}</div>
          </div>
        )}

        {/* Resume Button */}
        {isPaused && audioURL && (
          <Button
            size="sm"
            onClick={resumeRecording}
            icon="solar:microphone-2-outline"
            className="size-10 border-0 rounded-full"
            disabled={loading}
          />
        )}
      </div>
    </>
  );
};

export default memo(RecordingStateView);
