import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, defaults } from 'chart.js/auto';

defaults.maintainAspectRatio = false;

export default function ApplicantChartPlaceholder() {
  const fakeData = [
    { month: 1, count: 0 },
    { month: 2, count: 0 },
    { month: 3, count: 3 },
    { month: 4, count: 0 },
    { month: 5, count: 0 },
    { month: 6, count: 7 },
    { month: 7, count: 0 },
    { month: 8, count: 0 },
    { month: 9, count: 10 },
    { month: 10, count: 0 },
  ];
  const UserData = [
    {
      month: 'JAN',
    },
    {
      month: 'FEB',
    },
    {
      month: 'MAR',
    },
    {
      month: 'APR',
    },
    {
      month: 'MAY',
    },
    {
      month: 'JUN',
    },
    {
      month: 'JUL',
    },
    {
      month: 'AUG',
    },
    {
      month: 'SEP',
    },
    {
      month: 'OCT',
    },
    {
      month: 'NOV',
    },
    {
      month: 'DEC',
    },
  ];
  const userData = {
    labels: UserData.map((data) => data.month),
    datasets: [
      {
        label: 'Applicants',
        data: fakeData.map((stage) => stage.count),
        backgroundColor: ['rgba(75, 85, 99, 0.50)'],
        borderColor: ['rgba(75, 85, 99, 0.20)'],
        borderWidth: 1,
        borderRadius: 100,
        barThickness: 10,
      },
    ],
  };

  const options = {
    scales: {
      x: {
        grid: {
          display: false, // Hide x-axis grid lines
        },
      },
      y: {
        grid: {
          display: false, // Hide y-axis grid lines
        },
      },
    },
    plugins: {
      legend: {
        display: false, // Hide the legend (labels above the chart)
      },
    },
  };

  return (
    <div>
      <div className='w-20 h-3 my-[10px] bg-gray-300 rounded-full dark:bg-gray-600 '></div>
      <div className='sm:min-h-[250px] max-h-[250px] sm:pl-1 mx-auto animate-pulse'>
        <Bar data={userData} options={options} />
      </div>
    </div>
  );
}
