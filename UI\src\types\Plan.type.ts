export interface PlanPricingFeatures {
  applicants: number;
  users: number;
  avatars: number;
  createCustomQuestions: number;
  createCustomTests: number;
  createCustomInterviews: number;
  aiQuestions: number;
  aiTests: number;
  aiInterviews: number;
  assignTests: number;
  assignCustomInterviews: number;
  assignInteractiveInterviews: number;
  exportReports: number;
  applicantAssessmentReport: number;
  avatarCloning: number;
}

export interface PlanPricing {
  label: string;
  originalAmount: number;
  discountPercentage: number;
  discountedAmount: number;
  currency: string;
  durationInDays: number;
  features: PlanPricingFeatures;
}

// Types
export type PlanDataType = {
  _id: string;
  name: string;
  type: 0;
  mostPopular: boolean;
  description: string;
  pricing: {
    [key: string]: {
      label: string;
      originalAmount: number;
      discountPercentage: number;
      discountedAmount: number;
      currency: string;
      durationInDays: number;
      features: {
        applicants: number;
        users: number;
        avatars: number;
        createCustomQuestions: number;
        createCustomTests: number;
        createCustomInterviews: number;
        aiQuestions: number;
        aiTests: number;
        aiInterviews: number;
        assignTests: number;
        assignCustomInterviews: number;
        assignInteractiveInterviews: number;
        exportReports: number;
        applicantAssessmentReport: number;
        avatarCloning: number;
      };
    };
  };
  isArchived: boolean;
  createdAt: string;
  isSubscribed: boolean;
};

export type VipPlanList = PlanDataType[];

export interface PaymentPlanList {
  items: PlanDataType[];
  count: number;
}
