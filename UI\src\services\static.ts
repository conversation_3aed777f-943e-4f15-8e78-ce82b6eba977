export const StaticData = {
  paginationTheme: {
    layout: {
      table: {
        base: 'text-sm text-gray-700 dark:text-gray-400',
        span: 'font-semibold text-gray-900 dark:text-white',
      },
    },
    pages: {
      previous: {
        base: 'text-sm ml-0 rounded-l-lg border border-gray-300 bg-white px-3 py-2 leading-tight text-gray-500 enabled:hover:bg-purple-100 enabled:hover:text-purple-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-purple-700 enabled:dark:hover:text-white  flex items-center',
      },
      next: {
        base: 'text-sm rounded-r-lg border border-gray-300 bg-white px-3 py-2 leading-tight text-gray-500 enabled:hover:bg-purple-100 enabled:hover:text-purple-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-purple-700 enabled:dark:hover:text-white flex items-center',
      },
      selector: {
        base: 'w-12 border border-gray-300 bg-white py-2 leading-tight text-gray-500 enabled:hover:bg-purple-100 enabled:hover:text-purple-700 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 enabled:dark:hover:bg-purple-700 enabled:dark:hover:text-white',
        active: 'bg-purple-50 text-purple-600 hover:bg-purple-100 hover:text-purple-700 dark:border-gray-700 dark:bg-purple-700 dark:text-white',
      },
    },
  },
  customTooltipTheme: {
    style: {
      auto: 'border  !bg-slate-900  dark:!border-none text-white',
    },
    arrow: {
      style: {
        dark: '!bg-slate-900',
        light: '!bg-slate-900',
        auto: '!bg-slate-900',
      },
    },
  },
  customThemeRadioButton: {
    root: {
      base: 'h-4 w-4 border border-gray-300 text-purple-600 focus:ring-2 focus:ring-purple-500 dark:border-gray-600 dark:bg-gray-700 dark:focus:bg-purple-600 dark:focus:ring-purple-600',
    },
  },
  customThemeCheckbox: {
    root: {
      base: 'h-4 w-4 rounded border border-gray-300 bg-gray-100 focus:ring-2 dark:border-gray-600 dark:bg-gray-700 dark:focus:bg-purple-600 dark:focus:ring-purple-600',
      color: {
        default: 'text-purple-600 rounded border-gray-300 focus:ring-purple-500 focus:ring-offset-2',
      },
    },
  },
};
