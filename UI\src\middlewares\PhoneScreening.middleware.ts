import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';
import type { QuizType } from '../types/Quiz.type';

// Fetch single phone screening
export const fetchPhoneScreening = createAsyncThunk(
  'phoneScreening/fetchPhoneScreening',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get<QuizType>(`quizzes/single/phoneScreening/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch phone screening');
    }
  }
);

// Create phone screening
export const createPhoneScreening = createAsyncThunk(
  'phoneScreening/createPhoneScreening',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('quizzes/single/phoneScreening', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create phone screening');
    }
  }
);

// Update phone screening
export const updatePhoneScreening = createAsyncThunk(
  'phoneScreening/updatePhoneScreening',
  async ({ id, data }: { id: string; data: any }, { rejectWithValue }) => {
    try {
      const response = await Api.put(`quizzes/single/phoneScreening/${id}`, data);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to update phone screening');
    }
  }
);

// Get phone screening list
export const fetchPhoneScreeningList = createAsyncThunk(
  'phoneScreening/fetchList',
  async (params: any, { rejectWithValue }) => {
    try {
      const response = await Api.get('quizzes/list/phoneScreening', params || {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch phone screening list');
    }
  }
);

// Create phone screening submission
export const createPhoneScreeningSubmission = createAsyncThunk(
  'phoneScreening/createSubmission',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('submissions/single', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create phone screening submission');
    }
  }
); 
