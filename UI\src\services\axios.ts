import axios from 'axios';
import { VITE_API_BASE_URL } from '../configs/api';
import globalRouter from './global-router';
import Cookies from 'js-cookie';


const api = axios.create({
  baseURL: VITE_API_BASE_URL,
});

api.interceptors.request.use(
  (config) => {
    // @FIXME: Fix local storage
    const userData = JSON.parse(Cookies.get('userData') || '{}');
    const access_token = userData?.access_token;
    if (access_token) {
      config.headers.Authorization = `Bearer ${access_token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => response,
  (error) => {
    const status = error?.response?.status;
    if (status === 401) {
      // @FIXME: Fix local storage
      localStorage.clear();
      Cookies.remove('userData');
      Cookies.remove('userRoleData');
      globalRouter.navigate('/auth/login' as any);
    } else if(status === 403){
      globalRouter.navigate('/403' as any);
    }
    return Promise.reject(error);
  }
);

export { api };
