// React
import { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';

import { RootState, useAppSelector, UserData } from 'UI/src';

type childrenType = {
  children?: ReactNode;
};

export const UnProtectedRoute = ({ children }: childrenType) => {
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  /* super-admin*/
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role?.some((role: string) => ['super-admin'].includes(role));

  /* admin */
  const isAdmin = Array.isArray(userData?.role) && userData?.role?.some((role: string) => ['admin'].includes(role));

  /* content-creator */
  const canViewQuestions = Array.isArray(userData?.role) && userData?.role?.some((role: string) => ['content-creator'].includes(role));
  const canViewCustomTests = Array.isArray(userData?.role) && userData?.role?.some((role: string) => ['content-creator'].includes(role));

  /* hr */
  // const canViewTests = userData?.role?.some((role) => ['hr'].includes(role)); // Test bank
  const canViewApplicants = Array.isArray(userData?.role) && userData?.role?.some((role) => ['hr'].includes(role));

  if (isSuperAdmin) {
    return <Navigate to="/app/dashboard-super-admin" replace />;
  } else if (isAdmin) {
    return <Navigate to="/app/dashboard" replace />;
  } else if (canViewCustomTests) {
    return <Navigate to="/app/assessment-templates" replace />;
  } else if (canViewQuestions) {
    return <Navigate to="/app/questions" replace />;
    // }
    // else if (canViewTests) {
    //   return <Navigate to="/app/tests/list/prepared" replace />;
  } else if (canViewApplicants) {
    return <Navigate to="/app/applicants" replace />;
  } else {
    return children;
  }
};
