import React from 'react';

import { Dialog } from 'UI';

import { WeiredBehavior } from './weired-behavior';

export const WeirdBehaviorSingleDialog = ({
  onClose,
  onCreate,
  id,
  stage,
}: {
  onClose: () => void;
  onCreate: () => void;
  id: string;
  stage: any;
}) => {
  return (
    <Dialog size="lg" isOpen title={'Weird Behavior Analysis'} onClose={onClose}>
      <div className="dark:text-white">{stage?.weirdBehavior?.length ? <WeiredBehavior stage={stage} /> : <p>No Data</p>}</div>
    </Dialog>
  );
};
