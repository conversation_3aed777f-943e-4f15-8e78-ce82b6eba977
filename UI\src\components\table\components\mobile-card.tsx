import { Copy } from 'lucide-react';
import React, { useState } from 'react';

export type TableCardRow = {
  label: React.ReactNode;
  value: React.ReactNode;
  subValue?: React.ReactNode; // smaller, muted line under the value (e.g., Annually/Monthly)
  copyText?: string; // when provided, a small copy button appears
  valueClassName?: string; // customize value typography per row
  subValueClassName?: string; // customize sub-value typography per row
};

type Props = {
  rows: TableCardRow[];
  className?: string;
};

/**
 * Reusable MobileCard component
 * - Renders a card with two columns (label / value) and dividers between rows
 * - Highly flexible: each row provides its own ReactNode value (you can pass tags, chips, etc.)
 * - Optional copy button per row via `copyText`
 *
 * Example usage:
 * <MobileCard
 *   rows={[
 *     { label: 'Invoice ID', value: '574...', copyText: '574123987' },
 *     { label: 'Purchase', value: <span>Plan Name</span>, subValue: 'Annually/Monthly' },
 *     { label: 'Amount', value: '4608 $' },
 *   ]}
 * />
 */
const MobileCard: React.FC<Props> = ({ rows, className }) => {
  const [copiedIndex, setCopiedIndex] = useState<number | null>(null);

  const handleCopy = async (text: string, idx: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedIndex(idx);
      setTimeout(() => setCopiedIndex(null), 1200);
    } catch {
      // ignore
    }
  };

  return (
    <div className={`bg-white shadow-bg-behind-dialog border rounded-[12px] w-full overflow-hidden border-[#DEE2E4] ${className || ''}`}>
      <div className="divide-y divide-[#DEE2E4]">
        {rows.map((row, idx) => (
          <div key={idx} className="grid grid-cols-[1fr,2fr] md:grid-cols-[240px,1fr] gap-6 min-w-[380px]  bg-white">
            {/* Label */}

            {/* Value + optional copy */}
            <div className="flex items-center justify-start h-[44px]">
              <div className="text-[#4E5E82] bg-[#F9F8FA] h-full text-[14px] min-w-[140px] w-fit flex items-center justify-center  font-[400]">
                {row.label}
              </div>
              <div className="flex flex-col gap-1 px-5">
                <div className={`text-[#2A3348] ${row.valueClassName || 'text-[16px]'}`}>{row.value}</div>
                {row.subValue && <div className={`text-[#98A2B3] ${row.subValueClassName || 'text-[14px] font-[400]'}`}>{row.subValue}</div>}
              </div>

              {row.copyText && (
                <button
                  type="button"
                  onClick={() => handleCopy(row.copyText!, idx)}
                  className="ml-3 shrink-0 h-8 w-8 text-[#8D5BF8] flex items-center justify-center"
                  title={copiedIndex === idx ? 'Copied' : 'Copy'}
                >
                  <Copy className="text-3xl" />
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default MobileCard;
