import { sizeMap } from '../constants/modalSizes';
import type { DialogSize } from '../constants/modalSizes';
import Button from './button';
import { createPortal } from 'react-dom';

interface DialogProps {
  isOpen?: boolean; // Keep for backward compatibility
  onClose?: () => void;
  onOutsideClick?: () => void; // Alternative to onClose for outside clicks
  title?: string;
  subtitle?: React.ReactNode;
  children: React.ReactNode;
  size?: DialogSize;
  customSize?: string;
  icon?: React.ReactNode;
  button?: {
    variant?: 'xs' | 'sm' | 'md' | 'lg';
    colorType?: 'primary' | 'secondary' | 'tertiary' | 'destructive';
    label: string;
    state?: 'default' | 'hover' | 'pressed' | 'disabled';
    icon?: React.ReactNode;
    onClick?: () => void;
    disabled?: boolean;
    className?: string;
  };
  zIndex?: number;
}

const getSizeClasses = (size: DialogProps['size'], customSize?: string) => {
  if (customSize) {
    return customSize;
  }

  if (typeof size === 'string' && size in sizeMap) {
    return sizeMap[size];
  }

  if (typeof size === 'string' && size !== 'md') {
    return size;
  }

  return sizeMap.md;
};

export const Dialog: React.FC<DialogProps> = ({
  isOpen,
  onClose,
  onOutsideClick,
  title = '',
  subtitle,
  children,
  size = 'md',
  customSize,
  icon,
  button,
  zIndex,
}) => {
  if (!isOpen) return null;

  const sizeClasses = getSizeClasses(size, customSize);

  // Check if we have title/subtitle or close button
  const hasCloseButton = !!onClose;
  const hasTitle = !!title || !!subtitle || !!icon || hasCloseButton;
  const shouldCenterContent = !hasTitle && !hasCloseButton;

  // Handle outside click
  const handleOutsideClick = () => {
    // Use onOutsideClick if provided, otherwise fall back to onClose
    if (onOutsideClick) {
      onOutsideClick();
    } else if (onClose) {
      onClose();
    }
  };

  const content = (
    <div
      className={`fixed inset-0 flex items-center justify-center bg-black/50 ${shouldCenterContent ? 'p-0' : 'p-4'}`}
      style={{ zIndex: zIndex ?? 9999 }}
      onClick={handleOutsideClick}
    >
      <div
        className={`w-full ${sizeClasses} rounded-lg bg-white shadow-xl transform transition-all duration-200 ease-out ${
          shouldCenterContent ? 'flex items-center justify-center p-0' : ''
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        {hasTitle && (
          <div
            className={`flex items-center justify-between ${shouldCenterContent ? 'p-0' : hasCloseButton ? 'p-4 sm:p-6' : 'p-0'} ${
              hasCloseButton ? 'border-b border-gray-200 !pb-3 !pt-4' : 'pb-0'
            }`}
          >
            <div className="flex items-start gap-3">
              {icon && <div className="w-8 h-8 flex items-center justify-center -mt-1">{icon}</div>}
              <div className="flex flex-col">
                {title && <h2 className="text-gray-900 thepassHtwo">{title}</h2>}
                {subtitle && <p className="text-sm text-gray-500 mt-0.5">{subtitle}</p>}
              </div>
            </div>
            {onClose && (
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 underline"
                aria-label="Close dialog"
              >
                <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
        )}

        <div
          className={`${
            shouldCenterContent
              ? 'flex items-center justify-center w-full h-full p-0'
              : hasTitle && !hasCloseButton
              ? 'p-4 sm:p-6 pt-0'
              : 'p-4 sm:p-6 pt-1'
          }`}
        >
          {children}

          {button && (
            <div className={`flex justify-center ${shouldCenterContent ? 'mt-0' : 'mt-6'}`}>
              <Button
                variant={button.variant || 'md'}
                colorType={button.colorType || 'primary'}
                label={button.label}
                state={button.state || 'default'}
                icon={button.icon}
                onClick={button.onClick}
                disabled={button.disabled}
                className={button.className}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return createPortal(content, document.body);
};
