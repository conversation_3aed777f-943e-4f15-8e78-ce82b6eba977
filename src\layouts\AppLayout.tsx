import React, { useEffect, useState } from 'react';
import { Outlet } from 'react-router-dom';
import { useLocation } from 'react-router-dom';

// Hooks
import { useScreenSize, Button } from 'UI/src';

// Components
import { AppHeader } from '../modules/app/components/header';
import { AppSidebar } from '../modules/app/components/sidebar';
import { AppBreadcrumb } from '../modules/app/components/breadcrumb';
// import { AppJumbotron } from '../components/jumbotron';
import { MainSidebar } from './../modules/app/components/mainSidebar';
import { BackButton } from "../components/back-button"

export const AppLayout = () => {
  // State
  const [isDrawerVisible, setIsDrawerVisible] = useState<boolean>(false);
  const location = useLocation();
  const currentUrl: string = location.pathname;

  // Hooks
  const screen = useScreenSize();
  // Methods
  const isReport: boolean = currentUrl.includes('pdf');

  useEffect(() => {
    if (screen.lt.xl()) {
      setIsDrawerVisible(false);
    } else if (isReport) {
      setIsDrawerVisible(false);
    } else {
      setIsDrawerVisible(true);
    }
  }, [screen.size]);

  return (
    <div className="min-h-screen antialiased">
      {/* Header */}
      <AppHeader isDrawerVisible={isDrawerVisible} setIsDrawerVisible={setIsDrawerVisible} />

      {/* <!-- Sidebar --> */}
      {/* <AppSidebar isDrawerVisible={isDrawerVisible} setIsDrawerVisible={setIsDrawerVisible} /> */}
      <MainSidebar isDrawerVisible={isDrawerVisible} setIsDrawerVisible={setIsDrawerVisible} />

      {/* Content */}
      {/* <main
        className={`transition-all ${isReport ? '' : 'pt-[70px] p-4'}  ${isDrawerVisible && screen.gt.lg() ? 'pl-[270px]' : ''}`}
        onClick={() => (screen.lt.xl() ? setIsDrawerVisible(false) : {})}
      > */}
      <main
        className={`transition-all ${isReport ? '' : 'pt-[70px] p-4'}  ${isDrawerVisible && screen.gt.lg() ? 'pl-[270px]' : ''}`}
        onClick={() => (screen.lt.xl() ? setIsDrawerVisible(false) : {})}
      >
        <div>
          {/* <!-- Breadcrumb --> */}
          {/* <AppBreadcrumb /> */}

          {/* <!-- Jumbotron --> */}
          {/* <AppJumbotron /> */}
          {screen.lt.sm() && <BackButton/>}
          <Outlet />
        </div>

        {/* Overlay */}
        {isDrawerVisible && screen.lt.xl() && <div className="absolute z-50 top-0 left-0 right-0 bottom-0 bg-black/50" />}
      </main>
    </div>
  );
};
