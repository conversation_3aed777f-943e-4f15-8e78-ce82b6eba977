// @ts-nocheck
// React
import { useState, useEffect, useRef } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// UI
import { Api, AiAvatarModelLanguages } from 'UI/src';

// Components
import { StepperAiHeader } from './header';
import { StepperAiQuestion } from './question';
import { StepperAiFooter } from './footer';
import { StepperAiProgress } from './progress';
import { fetchSubmissionAi, RootState, setErrorNotify, useSpeech, setAiLoading, useAppDispatch, useAppSelector } from 'UI/src';

export const SubmissionAiStepper = () => {
  // Hooks
  const { id } = useParams();
  const navigate = useNavigate();

  const dispatch = useAppDispatch();
  const { loading, submissionAi, isSpeaking } = useAppSelector((state: RootState) => state.submissionAi);

  // Ref
  const isLoaded = useRef(false);
  const hasStarted = useRef(false);
  const modelRef = useRef();
  const intervalRef = useRef(null);

  // State
  const [isRecording, setIsRecording] = useState(false);
  const [loadingProgression, setLoadingProgression] = useState(0);
  const [isFinished, setIsFinished] = useState(false);
  const [textAnswer, setTextAnswer] = useState('');
  const [start, setStart] = useState(null);
  const [result, setResult] = useState(null);
  const [recordingMode, setRecordingMode] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isChatVisible, setIsChatVisible] = useState(false);
  const [testRemainingInMilliSeconds, setTestRemainingInMilliSeconds] = useState<number | null>(null);
  const [timingView, setTimingView] = useState<{ textExceedsOneHour: boolean; hours: number; minutes: number; seconds: number }>({
    textExceedsOneHour: false,
    hours: 0,
    minutes: 0,
    seconds: 0,
  });

  // Define the selected lang
  const selectedLanguage = submissionAi?.interview.avatarLang;
  const voiceConfig = AiAvatarModelLanguages.find((v) => v.lang === selectedLanguage);
  const langCode = voiceConfig?.langCode;

  isLoaded.current = loadingProgression === 100 ? true : false;
  const { isListening, transcript, startListening, stopListening, pauseListening, resumeListening, clearTranscript, isConverting } = useSpeech({
    interimResults: true,
    lang: langCode,
    continuous: true,
  });

  // @TODO: To be checked twice and thrice, then delete it when implementing isSpeaking
  // if (isFinished && !isSpeaking) {
  //   handleGetSubmissionAi();
  // }

  const handleStart = async () => {
    try {
      dispatch(setAiLoading(true));

      navigate(`/interview/${submissionAi.interview._id}`, { replace: true });
      const response = await Api.post(`ai-interview/single/start/${submissionAi.interview._id}`);
      setStart(response.data);
    } catch (error) {
      dispatch(setErrorNotify(error.response.data.message));
    } finally {
      dispatch(setAiLoading(false));
    }
  };

  // Speak when result transcript changes
  const getTranscript = (selectedResult) => {
    if (!selectedResult) return '';

    const { currentQuestion, transcript } = selectedResult;

    if (currentQuestion?.type === 'mcq') {
      return `${transcript} ${currentQuestion.options.toString()}`;
    }

    if (currentQuestion?.type === 'true_false') {
      return `${transcript} is that true or false ?`;
    }

    return transcript || '';
  };

  const sendManualReply = async (text) => {
    setTextAnswer(() => text);
    try {
      dispatch(setAiLoading(true));
      const payload = {
        interviewId: id,
        userAnswerText: text,
      };
      const response = await Api.post('ai-interview/single/talk', payload);
      setResult(response.data);
      setTextAnswer('');
    } catch (error) {
      console.error('Error uploading audio:', error);
    } finally {
      dispatch(setAiLoading(false));
      setRecordingMode(false);
      id && (await dispatch(fetchSubmissionAi(id)).unwrap());
    }
  };

  const getAvailableSkips = () => {
    if (start?.availableSkips > 0)
      return {
        showAvailableSkips: true,
        availableSkips: result?.availableSkips >= 0 ? result?.availableSkips : start?.availableSkips,
        isIntroduceYourself: result ? result?.chat.length === 1 : start?.chat?.length === 1,
      };
  };

  useEffect(() => {
    const selectedResult = result || start;
    const transcript = getTranscript(selectedResult);

    if (isLoaded.current && transcript) {
      modelRef?.current?.handleSpeak(transcript);
    }
  }, [result?.transcript, start?.transcript, isLoaded.current, modelRef]);

  useEffect(() => {
    if (!hasStarted.current && isLoaded.current) {
      handleStart();
      hasStarted.current = true;
    }
  }, [isLoaded.current]);

  useEffect(() => {
    if (isRecording && !isPaused) {
      intervalRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } else {
      clearInterval(intervalRef.current);
    }
  }, [isRecording, isPaused]);

  // console.log(submissionAi);

  // const timingStatus = () => {
  //   if ((testRemainingInMilliSeconds ?? 0) < 0 && (submissionAi.interview?.exceededTime ?? 0) > 0) {
  //     // Exceeded Red
  //     return {
  //       type: 3,
  //       text: 'exceeded',
  //       textStyles: 'text-[#798296] font-semibold',
  //       timeStyles: 'text-[#C83333] ml-auto',
  //       styles: 'bg-[#FFF3F3] dark:bg-[#C727164D]',
  //       icon: (
  //         <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
  //           <path
  //             fillRule="evenodd"
  //             clipRule="evenodd"
  //             d="M17.3327 8.66536C17.3327 13.2677 13.6017 16.9987 8.99935 16.9987C4.39697 16.9987 0.666016 13.2677 0.666016 8.66536C0.666016 4.06299 4.39697 0.332031 8.99935 0.332031C13.6017 0.332031 17.3327 4.06299 17.3327 8.66536ZM8.99935 13.457C9.34452 13.457 9.62435 13.1772 9.62435 12.832V7.83203C9.62435 7.48686 9.34452 7.20703 8.99935 7.20703C8.65418 7.20703 8.37435 7.48686 8.37435 7.83203V12.832C8.37435 13.1772 8.65418 13.457 8.99935 13.457ZM8.99935 4.4987C9.4596 4.4987 9.83268 4.8718 9.83268 5.33203C9.83268 5.79226 9.4596 6.16536 8.99935 6.16536C8.5391 6.16536 8.16602 5.79226 8.16602 5.33203C8.16602 4.8718 8.5391 4.4987 8.99935 4.4987Z"
  //             fill="#C72716"
  //           />
  //         </svg>
  //       ),
  //     };
  //   } else {
  //     if ((testRemainingInMilliSeconds ?? 0) - 10 * 60 * 1000 > 0) {
  //       // Safe zoon Gray
  //       return {
  //         type: 1,
  //         text: 'remaining',
  //         textStyles: 'text-[#798296] dark:text-white font-medium',
  //         timeStyles: 'text-[#394240] dark:text-white',
  //         styles: 'bg-[#F5F6F8] dark:bg-darkGrayBackground',
  //         icon: (
  //           <svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
  //             <rect width="30" height="30" rx="15" fill="#F5F1FF" />
  //             <path
  //               d="M15 25.5C12.3478 25.5 9.8043 24.4464 7.92893 22.5711C6.05357 20.6957 5 18.1522 5 15.5C5 12.8478 6.05357 10.3043 7.92893 8.42893C9.8043 6.55357 12.3478 5.5 15 5.5C17.6522 5.5 20.1957 6.55357 22.0711 8.42893C23.9464 10.3043 25 12.8478 25 15.5C25 18.1522 23.9464 20.6957 22.0711 22.5711C20.1957 24.4464 17.6522 25.5 15 25.5ZM15 23.5C17.1217 23.5 19.1566 22.6571 20.6569 21.1569C22.1571 19.6566 23 17.6217 23 15.5C23 13.3783 22.1571 11.3434 20.6569 9.84315C19.1566 8.34285 17.1217 7.5 15 7.5C12.8783 7.5 10.8434 8.34285 9.34315 9.84315C7.84285 11.3434 7 13.3783 7 15.5C7 17.6217 7.84285 19.6566 9.34315 21.1569C10.8434 22.6571 12.8783 23.5 15 23.5ZM14 15.91V9.5H16V15.09L19.95 19.04L18.54 20.45L14 15.91Z"
  //               fill="#AC86FF"
  //             />
  //           </svg>
  //         ),
  //       };
  //     } else {
  //       // Warning Orange
  //       return {
  //         type: 2,
  //         text: 'remaining',
  //         textStyles: 'text-[#667085] font-semibold',
  //         styles: 'bg-[#F6F0E6] dark:bg-[#E88F264D]',
  //         timeStyles: 'text-[#E88F26] ml-auto',
  //         icon: (
  //           <svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg">
  //             <path
  //               fillRule="evenodd"
  //               clipRule="evenodd"
  //               d="M17.3327 8.66536C17.3327 13.2677 13.6017 16.9987 8.99935 16.9987C4.39697 16.9987 0.666016 13.2677 0.666016 8.66536C0.666016 4.06299 4.39697 0.332031 8.99935 0.332031C13.6017 0.332031 17.3327 4.06299 17.3327 8.66536ZM8.99935 13.457C9.34452 13.457 9.62435 13.1772 9.62435 12.832V7.83203C9.62435 7.48686 9.34452 7.20703 8.99935 7.20703C8.65418 7.20703 8.37435 7.48686 8.37435 7.83203V12.832C8.37435 13.1772 8.65418 13.457 8.99935 13.457ZM8.99935 4.4987C9.4596 4.4987 9.83268 4.8718 9.83268 5.33203C9.83268 5.79226 9.4596 6.16536 8.99935 6.16536C8.5391 6.16536 8.16602 5.79226 8.16602 5.33203C8.16602 4.8718 8.5391 4.4987 8.99935 4.4987Z"
  //               fill="#E88F26"
  //             />
  //           </svg>
  //         ),
  //       };
  //     }
  //   }
  // };

  useEffect(() => {
    const startedAtInSeconds = submissionAi.interview.startedAt ? new Date(submissionAi.interview.startedAt).getTime() / 1000 : 0;
    const testDurationInSeconds = submissionAi.interview.duration ? submissionAi.interview.duration * 60 : 0;
    const testEndInMilliseconds = (startedAtInSeconds + testDurationInSeconds) * 1000;

    const updateRemainingTime = () => setTestRemainingInMilliSeconds(testEndInMilliseconds - new Date().getTime());
    updateRemainingTime();

    const intervalId = setInterval(updateRemainingTime, 1000);
    return () => clearInterval(intervalId);
  }, []);

  return (
    <div className="h-full grid grid-rows-[auto_1fr_auto] gap-4">
      {/* Progress */}
      {!isLoaded?.current && <StepperAiProgress isLoaded={isLoaded} loadingProgression={loadingProgression} />}

      {/* Header */}
      {isLoaded?.current && <StepperAiHeader start={start} result={result} showThemeIcon={true} getAvailableSkips={getAvailableSkips} />}
      {/* <div className="w-full lg:w-[60%] flex">
        <div className={`gap-2 rounded-lg p-3 space-y-2 bg-[#F5F6F8] dark:bg-darkGrayBackground`}>
          <div className={`flex justify-between items-center flex-wrap`}>
            <div className={`text-xl font-semibold text-[#394240] dark:text-white`}>
              {timingView?.textExceedsOneHour && `${timingView?.hours < 10 ? `0${timingView?.hours}` : timingView?.hours}h : `}
              {timingView?.minutes < 10 ? `0${timingView?.minutes}` : timingView?.minutes}m :{' '}
              {timingView?.seconds < 10 ? `0${timingView?.seconds}` : timingView?.seconds}s
            </div>
          </div>
        </div>
      </div> */}
      {/* Question */}
      <StepperAiQuestion
        isLoaded={isLoaded}
        isFinished={isFinished}
        setIsFinished={setIsFinished}
        result={result}
        setResult={setResult}
        setLoadingProgression={setLoadingProgression}
        modelRef={modelRef}
        isRecording={isRecording}
        isChatVisible={isChatVisible}
        start={start}
        textAnswer={textAnswer}
        setTextAnswer={setTextAnswer}
        recordingMode={recordingMode}
        setRecordingMode={setRecordingMode}
        sendManualReply={sendManualReply}
        transcript={transcript}
      />

      {isLoaded?.current && (
        <StepperAiFooter
          recordingMode={recordingMode}
          setRecordingMode={setRecordingMode}
          textAnswer={textAnswer}
          setTextAnswer={setTextAnswer}
          isLoaded={isLoaded}
          isSpeaking={isSpeaking}
          isFinished={isFinished}
          isChatVisible={isChatVisible}
          setIsChatVisible={setIsChatVisible}
          loading={loading}
          setLoading={setAiLoading}
          result={result}
          setResult={setResult}
          isRecording={isRecording}
          setIsRecording={setIsRecording}
          recordingTime={recordingTime}
          setRecordingTime={setRecordingTime}
          sendManualReply={sendManualReply}
          isPaused={isPaused}
          setIsPaused={setIsPaused}
          start={start}
          getAvailableSkips={getAvailableSkips}
          isListening={isListening}
          transcript={transcript}
          startListening={startListening}
          stopListening={stopListening}
          pauseListening={pauseListening}
          resumeListening={resumeListening}
          clearTranscript={clearTranscript}
          isConverting={isConverting}
          currentQuestion={start?.transcript}
        />
      )}
    </div>
  );
};
