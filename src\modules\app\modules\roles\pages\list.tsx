import React, { type FC, useState } from 'react';

// UI
import { useFetchList } from 'UI/src';
import { CustomIcon, Icon, Table, EnumText } from 'src';

// Components
import { MultiStepRolesCreationDialog } from '../components/multi-step-creation-dialog';
import { view } from 'framer-motion';

// Types
type Role = {
  _id: string;
  name: string;
  [key: string]: any;
};

export const RolesListPage: FC = () => {
  const { ready, loading, count, list, refresh, search, pagination } = useFetchList('roles/list', {
    search: '',
    pagination: {
      page: 1,
      size: 20,
    },
  });

  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [isView, setIsView] = useState(false);
  const [id, setId] = useState<string | null>(null);
  const [showMoreMap, setShowMoreMap] = useState<Record<string, boolean>>({});

  const closeCreateDialogVisibility = () => {
    setId(null);
    setCreateDialogVisibility(false);
  };

  const handleEditRole = (roleId: string) => {
    setIsView(false);
    setId(roleId);
    setCreateDialogVisibility(true);
  };

  const handleViewRole = (roleId: string) => {
    setIsView(true);
    setId(roleId);
    setCreateDialogVisibility(true);
  };

  // Render
  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Roles Management"
        addButtonLabel="Create Role"
        searchPlaceholder="Search by role name..."
        count={count}
        search={search}
        pagination={pagination}
        rows={list}
        rowKey="_id"
        onClickAdd={() => setCreateDialogVisibility(true)}
        slots={{
          name: (_: any, row: any) => (
            <div className="text-sm">
              <div className="bg-[#E0F3FB] text-[#11ABE6] rounded-md px-2 py-1 w-fit">{row.name}</div>
            </div>
          ),
          numOfUsers: (_: any, row: any) => (
            <div className="text-sm flex items-center gap-2">
              <Icon icon="lucide:users" className="text-gray-500" width="16" />
              {row.numOfUsers || 0}
            </div>
          ),
          permissions: (_: any, row: any) => (
            <div>
              {!!row.permissions && Array.isArray(row.permissions) && row.permissions.length > 0 ? (
                <div className="flex flex-wrap gap-3">
                  {row.permissions.splice(0, 2).map((permission: number, index: number) => (
                    <div className="border border-[#DEE2E4] rounded-lg w-fit py-1 px-3 flex items-center gap-x-0.5 lowercase" key={index} >
                      {
                        EnumText({ name: 'UserPermissions', value: permission }).replace('_', ' ')
                      }
                    </div>
                  ))}
                  {
                    row.permissions.length > 3 && <div className="border border-[#DEE2E4] rounded-lg w-fit py-1 px-3 flex items-center gap-x-0.5 lowercase">
                      +{row.permissions.length - 3} more
                    </div>
                  }
                </div>
              ) : (
                <span className="text-gray-400 text-sm">No permissions assigned</span>
              )}
            </div>
          ),
        }}
        columns={[
          {
            key: 'name',
            label: 'Role Name',
            primary: true,
            width: '25%',
          },
          {
            key: 'permissions',
            label: 'Permissions',
            width: '50%',
          },
          {
            key: 'numOfUsers',
            label: 'Users Count',
            width: '10%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '15%',
            buttons(_: unknown, row: { _id: string }) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-[#743AF5] dark:text-white',
                  onClick: () => handleViewRole(row._id),
                },
                {
                  label: 'Update',
                  customIcon: 'edit',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-[#743AF5] dark:text-white',
                  onClick: () => handleEditRole(row._id),
                },
              ];
            },
          },
        ]}
        groups={[
          {
            name: 'Role Information',
            keys: [['name'], ['numOfUsers', 'permissions']],
          },
        ]}
        noDataFound={{
          customIcon: 'users',
          message: 'No roles created yet',
          description: 'Create your first role to get started with role-based access control',
        }}
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
      />

      {/* Creation Dialog */}
      {isCreateDialogVisible && (
        <MultiStepRolesCreationDialog
          onClose={closeCreateDialogVisibility}
          id={id}
          closeCreateDialogVisibility={closeCreateDialogVisibility}
          onCreate={refresh}
          view={isView}
        />
      )}
    </>
  );
};
