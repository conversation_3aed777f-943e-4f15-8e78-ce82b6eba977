import { createSlice } from '@reduxjs/toolkit';
import type { RootState } from '../../store';

// initial State
interface notifyState {
  message: string;
  error: {
    message: string;
    type: string;
  };
  warning: {
    message: string;
    type: string;
  };
}

const initialState: notifyState = {
  message: '',
  error: {
    message: '',
    type: 'error',
  },
  warning: {
    message: '',
    type: 'warning',
  },
};

// notify Slice
const notifySlice = createSlice({
  name: 'notify',
  initialState,
  reducers: {
    setNotifyMessage: (state, { payload }) => {
      state.message = payload;
    },
    setErrorNotify: (state, { payload }) => {
      state.error.message = payload;
    },
    setWarningNotify: (state, { payload }) => {
      state.warning.message = payload;
    },
  },
});

export const notifyState = (state: RootState) => state.notify;
export const { setNotifyMessage, setErrorNotify, setWarningNotify } = notifySlice.actions;
export default notifySlice.reducer;
