// React
import { useEffect, useState } from 'react';
import { Navigate, useNavigate } from 'react-router-dom';

// Core
import {
  Icon,
  CustomIcon,
  ToggleFilter,
  Button,
  TestDifficulty,
  TestSeniorityLevel,
  AvarageScore,
  Table,
  DurationFieldColumn,
  NameFieldColumn,
  CategoryFieldColumn,
  SubcategoryFieldColumn,
} from 'src';

import { StaticData, RootState, useAppSelector, UserData, useFetchList, useScreenSize, Tags, useAppDispatch, setBackupListTest } from 'UI/src';

export const TestList = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Permissions
  const isPermitted = Array.isArray(userData?.role) && userData?.role.some((role) => ['super-admin', 'admin', 'hr'].includes(role));
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');

  // State
  const [selectedIds, setSelectedIds] = useState([]);
  const [showMoreMap, setShowMoreMap] = useState<{ [key: string]: boolean }>({});
  const [backupList, setBackupList] = useState([]);
  const [filterCountNumber, setFilterCountNumber] = useState<number>(0);
  const [isShowDrawerFilter, setShowDrawerFilter] = useState<boolean>(false);

  // Hooks
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const screen = useScreenSize();
  const initialFilters = {
    // ...(userData.trackId
    //   ? {}
    //   : {
    category: {
      label: 'Category',
      lookup: 'category',
    },
    // }),
    seniorityLevel: {
      label: 'Seniority Level',
      enum: 'QuizDifficulty',
    },

    // subCategory: {
    //   label: 'Sub Category',
    //   lookup: 'subcategory',
    //   parentLookup: { key: 'category', fieldName: 'categoryId', fieldValue: null },
    // },
    difficulty: {
      label: 'Difficulty',
      enum: 'QuestionDifficulty',
    },

    averageScore: {
      label: 'Average Score',
      enum: 'AverageScore',
    },
  };
  const filterFeedData = Object.keys(initialFilters).map((key) => (key === 'difficulty' ? initialFilters.difficulty.enum : key));
  const { ready, loading, setLoading, list, count, search, pagination, filters, setFilters, refresh, handleDates } = useFetchList(
    'submissions/generated-tests',
    {
      search: '',
      pagination: {
        page: 1,
        size: 20,
      },
      filters: initialFilters,
    }
  );

  useEffect(() => {
    if (backupList.length === 0) {
      setBackupList(list);
      dispatch(setBackupListTest(list.length));
    }
  }, [list]);

  return (
    <>
      <Table
        ready={ready}
        loading={loading}
        title="Test Reports"
        searchPlaceholder="Search for template name..."
        count={count}
        search={search}
        filters={filters}
        setFilters={setFilters}
        // filterFeedData={filterFeedData}
        // drawerFilter={{
        //   filterCountNumber: filterCountNumber,
        //   isShowDrawerFilter: isShowDrawerFilter,
        //   setShowDrawerFilter: setShowDrawerFilter,
        // }}
        pagination={pagination}
        rows={list}
        backupRows={backupList}
        slots={{
          applicantName: (_: unknown, row: { _id: string; title: string; quizId: string }) => (
            <NameFieldColumn
              id={row?._id}
              name={row?.title}
              showMoreMap={showMoreMap}
              onClick={() => navigate(`/app/assessment-report/view/test/${row?.quizId}/generated links`)}
            />
          ),
          category: (_: unknown, row: { categoryName: string[] }) => <CategoryFieldColumn categoryNameArray={row?.categoryName} />,
          subCategory: (_: unknown, row: { subCategoryName: string[] }) => <SubcategoryFieldColumn subCategoryName={row?.subCategoryName} />,
          // seniorityLevel: (_: unknown, row: { seniorityLevel: number }) => {
          //   return (
          //     <div className="w-fit">
          //       <TestSeniorityLevel seniorityLevel={row?.seniorityLevel} />
          //     </div>
          //   );
          // },
          seniorityLevel: (_: unknown, row: { seniorityLevel: number }) => {
            const getSeniorityLevelText = (level: any): string => {
              // Convert to number if it's a string
              const numLevel = typeof level === 'string' ? parseInt(level) : level;

              switch (numLevel) {
                case 1:
                  return 'intern';
                case 2:
                  return 'fresh';
                case 3:
                  return 'junior';
                case 4:
                  return 'mid-level';
                case 5:
                  return 'senior';
                default:
                  return 'intern';
              }
            };

            return (
              <div className="w-fit">
                <Tags type={getSeniorityLevelText(row?.seniorityLevel)} color="bg-transparent" />
              </div>
            );
          },

          difficulty: (_: unknown, row: { difficulty: number }) => {
            const getDifficultyText = (level: number): string => {
              switch (level) {
                case 1:
                  return 'easy';
                case 2:
                  return 'medium';
                case 3:
                  return 'hard';
                default:
                  return 'easy';
              }
            };

            const getDifficultyColor = (level: number): string => {
              switch (level) {
                case 1:
                  return 'bg-[#EEFFF1] text-[#056816]';
                case 2:
                  return 'bg-[#FFFCDF] text-[#BA8500]';
                case 3:
                  return 'bg-[#FFECE9] text-[#A80000]';
                default:
                  return 'bg-[#EEFFF1] text-[#056816]';
              }
            };

            return (
              <div className="w-fit">
                <Tags type={getDifficultyText(row?.difficulty)} color={getDifficultyColor(row?.difficulty)} />
              </div>
            );
          },
          duration: (_: unknown, row: { duration: number }) => <DurationFieldColumn duration={row?.duration} />,
          usage: (_: unknown, row: { assignedCount: number }) => {
            return <div className="capitalize font-medium text-sm text-[#535862] truncate">{row?.assignedCount}</div>;
          },

          averageScore: (_: unknown, row: { averageScore: number }) => {
            const getScoreColor = (score: number) => {
              if (score >= 0 && score < 50) {
                return 'bg-[#FFECE9] text-[#A80000]';
              } else if (score >= 50 && score < 75) {
                return 'bg-[#FFEDD8] text-[#E9760F]';
              } else if (score >= 75 && score < 100) {
                return 'bg-[#FFFCDF] text-[#BA8500]';
              } else if (score >= 100) {
                return 'bg-[#EEFFF1] text-[#056816]';
              }
              return 'bg-gray-100 text-gray-800';
            };

            const getScoreText = (score: number) => {
              if (score === null || score === undefined) return '—';
              return `${score}%`;
            };

            return (
              <div className="w-fit">
                <Tags type="score" color={getScoreColor(row?.averageScore)}>
                  {getScoreText(row?.averageScore)}
                </Tags>
              </div>
            );
          },
        }}
        columns={[
          {
            key: 'applicantName',
            label: 'Name',
            primary: true,
            width: '22%',
          },
          {
            key: 'category',
            label: 'Category',
            primary: true,
            width: '23%',
            cardFooterProp: true,
          },
          {
            key: 'seniorityLevel',
            label: 'Seniority Level',
            primary: true,
            width: '18%',
            inline: true,
          },
          {
            key: 'difficulty',
            label: 'Difficulty',
            width: '15%',
            inline: true,
          },
          {
            key: 'duration',
            label: 'Duration',
            width: '15%',
          },

          {
            key: 'averageScore',
            label: 'Average Score',
            width: '18%',
          },
          {
            key: 'usage',
            label: 'Usage',
            width: '10%',
          },
          {
            key: 'actions',
            label: 'Actions',
            width: '10%',
            buttons(_: unknown, row: { quizId: string }) {
              return [
                {
                  label: 'View',
                  customIcon: 'eye',
                  iconWidth: '22',
                  iconHeight: '22',
                  color: 'text-black dark:text-white',
                  path: `/app/assessment-report/view/test/${row?.quizId}/generated links`,
                },
              ];
            },
          },
        ]}
        groups={[
          {
            name: 'group1',
            keys: [['seniorityLevel', 'difficulty'], ['duration']], // from table
          },
        ]}
        // noDataFound={{
        //   customIcon: 'applicant',
        //   message: 'No assessment reports found',
        // }}

        placeholder={{
          title: 'No reports generated yet',
          subTitle: 'Assign assessments to applicants to generate comprehensive evaluation reports.',
          image: '/UI/src/assets/placeholder/NoReports.svg',
        }}
        noDataFoundIconWidth="60"
        noDataFoundIconHeight="60"
        showMoreMap={showMoreMap}
        setShowMoreMap={setShowMoreMap}
        hideJumbotron
        isScrollableTabsExists
      />

      {/* Need to add to table main buttons */}
      {/* <div className="flex justify-between items-center gap-x-2">
        <div className="calendar flex flex-row justify-end items-center gap-2 text-gray-700 dark:text-gray-300 rounded-lg h-fit">
          <Datepicker
            className="inline-block w-full sm:w-44"
            // onSelectedDateChanged={() => {}}
            showTodayButton={false}
            showClearButton={false}
            // value=""
          />
        </div>

        <div className="flex gap-3">
          <Button label="Template" tertiary customIcon="template" />
          <Button label="List" icon="ix:list" />
        </div>
      </div> */}
    </>
  );
};
