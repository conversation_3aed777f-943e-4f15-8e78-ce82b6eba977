import React, { useState, useEffect, type FC, useRef } from 'react';

import { Icon, Button, Logo } from 'src';
import { RootState, setThemeColor, useAppDispatch, useAppSelector } from 'UI/src';
// OTHER WAY TO RENDER TIME
// TS props Type
type HeaderProps = {
  showThemeIcon: boolean;
  setShowBookMark: (value: boolean) => void;
  startQuiz: boolean;
  duration: number;
};

// TODO: otherway :
export const Header: FC<HeaderProps> = ({ showThemeIcon, setShowBookMark, startQuiz, duration }) => {
  // export const Header = ({ showThemeIcon, setShowBookMark, startQuiz, duration }:HeaderProps) => {
  //state
  const [timeLeft, setTimeLeft] = useState<string | null>('00:00:00');
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const time = useRef({ hours: 0, minutes: 0, seconds: 0 });

  const dispatch = useAppDispatch();
  const themeColor = useAppSelector((state: RootState) => state.app.themeColor);
  const newMode = (): 'light' | 'dark' => {
    if (themeColor === 'light') {
      return 'dark';
    }
    return 'light';
  };

  // Methods
  const getTimeUnitFormated = (number: number): string => `${number < 10 ? `0${number}` : number}`;

  const getTime = () => {
    time.current.seconds++;
    if (time.current.seconds === 60) {
      time.current.seconds = 0;
      time.current.minutes++;
      if (time.current.minutes === 60) {
        time.current.minutes = 0;
        time.current.hours++;
        if (time.current.hours === 24) {
          time.current.hours = 0;
        }
      }
    }

    return `${getTimeUnitFormated(time.current.hours)}:${getTimeUnitFormated(time.current.minutes)}:${getTimeUnitFormated(time.current.seconds)}`;
  };

  const showBookMarkList = () => {
    setShowBookMark(true);
  };

  // Effects
  useEffect(() => {
    if (startQuiz) {
      timerRef.current = setInterval(() => {
        setTimeLeft(getTime());
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [startQuiz]);

  return (
    <nav className="bg-white dark:bg-darkBackgroundCard mb-1 border-b border-[#f4f4f4] px-4 pt-[14px] pb-[10px] dark:border-[#374151] fixed left-0 right-0 top-0 z-[60]">
      <div className="mx-6">
        <div className="flex justify-between items-center">
          <div className="flex justify-between items-center">
            <div className="flex items-center justify-between mr-4 cursor-pointer">
              <Logo className="h-8" />
            </div>
          </div>
          <div className="flex items-center gap-4 md:gap-3 xl:gap-4">
            {startQuiz && (
              <div className="flex font-medium gap-4">
                <div className="flex items-center text-base font-normal text-secondaryGray">
                  <p>
                    Estimation time {duration - 10}m - {duration + 10}m
                  </p>
                </div>
                <div className="dark:bg-gray-900 dark:text-white flex gap-2 px-3 py-2 bg-[#F9FAFC] text-xl text-secondaryGray rounded-md">
                  <Icon icon="octicon:clock-16"></Icon>
                  <span>{timeLeft}</span>
                </div>
                <Button onClick={showBookMarkList} outline label="Questions" icon="ion:book-outline" />
              </div>
            )}
            {/* <!-- Dark --> */}
            {showThemeIcon && (
              <button
                type="button"
                className="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 flex items-center justify-center"
                onClick={() => dispatch(setThemeColor(newMode()))}
              >
                <Icon icon={themeColor ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'} width="20px" />
              </button>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};
