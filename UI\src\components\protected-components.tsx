import { Navigate } from 'react-router-dom';
import { useUserPermissions } from '../hooks/user-permissions';

type ProtectedComponentProps = {
    permissions: number | number[];
    children: React.ReactNode;
    operator?: "every" | "some";
};

export const PermissionProtectedComponent: React.FC<ProtectedComponentProps> = ({ permissions, children, operator }) => {
    const { hasPermission } = useUserPermissions();
    const hasPermissionValue = hasPermission(permissions, operator);

    return <>{hasPermissionValue && children}</>;
};

export const PermissionProtectedRoute: React.FC<ProtectedComponentProps> = ({ permissions, children, operator }) => {
    // Hooks
    const { hasPermission } = useUserPermissions();
    const hasPermissionValue = hasPermission(permissions, operator);
    if (!hasPermissionValue) {
        return <Navigate to={'/403'} replace />;
    }
    return <>{children}</>;
};