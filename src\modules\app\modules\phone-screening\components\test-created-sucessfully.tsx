import { Dialog } from 'UI';
import { Icon } from 'src';
import { useAppDispatch } from 'UI/src';
import { setNotifyMessage } from 'UI';
import doneMark from 'images/Vector.svg';

export const TestCreatedSucessfully = ({ quizUrl, onClose }: any) => {
  // Hooks
  const dispatch = useAppDispatch();

  // Methods
  const handleCopyLink = () => {
    navigator.clipboard.writeText(quizUrl);
    dispatch(setNotifyMessage('Link copied'));
  };

  return (
    <Dialog isOpen size="lg" onClose={onClose}>
      <div className="py-5 pt-0 text-center">
        <div>
          <div className="flex justify-center items-center mx-auto mb-5 !mt-0  text-gray-400 dark:text-gray-200">
            <img src={doneMark} alt="done mark" />
          </div>
          <div className="text-center">
            <h2 className="text-center dark:text-white font-medium text-xl ">Screening Generated Successfully!</h2>
            <div className="w-full text-center mx-auto mt-2">
              <p className="text-center font-normal text-base   dark:text-white text-[#626262]">
                Send the link below for quick and easy access to the applicant
              </p>
            </div>
          </div>
          <div className="mt-5 py-1">
            <div className="grid w-full max-w-80 mx-auto text-center">
              <div className="relative flex ">
                <input
                  value={quizUrl}
                  type="text"
                  className="col-span-6 block w-full rounded-lg border border-gray-300 bg-gray-50 px-2.5 py-4 text-sm text-[#313437] pr-12 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-400 dark:placeholder:text-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
                />
                <Icon
                  icon={'ooui:copy-ltr'}
                  onClick={() => handleCopyLink()}
                  className="dark:text-white cursor-pointer text-gray-400  text-2xl absolute right-3 top-1/4"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
};
