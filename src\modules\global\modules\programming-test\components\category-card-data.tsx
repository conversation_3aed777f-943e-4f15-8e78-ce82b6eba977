// UI
import { Button, useScreenSize } from 'UI/src';
import { Icon, EnumText, CustomIcon } from 'src';

// Flowbite
import { Tooltip } from 'flowbite-react';

// React icons
import { FaUserGraduate, FaUser, FaStar, FaMedal } from 'react-icons/fa';

// Lucide icons
import { Clock5, HelpCircle, ChartColumnIncreasing, User } from 'lucide-react';

export const CategoryCardData = ({ test, creationSubmissionsDialog, back }: any) => {
  const text = test?.title ?? 'Quiz';
  const screen = useScreenSize();

  // Define icon and color for difficulty levels
  let difficultyIcon = null;
  let difficultyColor = 'font-semibold';

  let iconSize = 'text-sm';
  switch (test.difficulty) {
    case 1:
      difficultyIcon = <FaUserGraduate className={`${iconSize}${difficultyColor}`} />; // Intern
      break;

    // Star Icon fresh level
    case 2:
      difficultyIcon = <FaUser className={`${iconSize} ${difficultyColor}`} />; // Fresh
      break;
    // Medal Star junior
    case 3:
      difficultyIcon = <FaStar className={`${iconSize} ${difficultyColor}`} />; // Junior
      break;
    case 4:
      difficultyIcon = <FaMedal className={`${iconSize} ${difficultyColor}`} />; // Mid-level
      break;

    // Tropy icon for senior with star
    case 5:
      difficultyIcon = <Icon icon="solar:crown-star-bold" width="18" className={`${iconSize}  ${difficultyColor}`} />; // Senior
      // difficultyColor = 'text-red-800';
      break;
    default:
      difficultyIcon = null;
  }

  return (
    <div className="relative flex flex-col p-2.5 bg-white space-y-3 border-b border-gray-200 ">
      <div className="flex justify-between">
        <div>
          <h3 className="text-lg font-medium text-[#1B1F3B] truncate capitalize">{text}</h3>
          {screen.gt.lg() && (
            <Tooltip content={text} placement="bottom" arrow={false} className="bg-gray-700 dark:bg-gray-200 dark:text-gray-900 w-fit text-sm">
              <div className="w-[300px] h-[50px] absolute left-0 top-3"></div>
            </Tooltip>
          )}
        </div>
        {creationSubmissionsDialog && (
          <div>
            <Button onClick={() => creationSubmissionsDialog(test, back)} colorType="secondary" label="Assign" />
          </div>
        )}
      </div>

      {/* TODO: Make this global component to be used in: assessmnet-comp-card , programming-test-list, programmin-test-single */}
      <div className="flex flex-wrap justify-between items-center text-[#798296] dark:text-[#838398] text-base gap-3 mb-4">
        <div className="flex items-center gap-1">
          <User className="size-[18px] text-[#743AF5]" />
          <p className="text-sm text-[#4E5E82]">Seniority:</p>
          <p className="text-[#1B1F3B] thepassBtwo">
            <EnumText name="QuizDifficulty" value={test.seniorityLevel} />
          </p>
        </div>

        <div className="flex items-center gap-1">
          <ChartColumnIncreasing className="size-[18px] text-[#743AF5]" />
          <p className="text-sm text-[#4E5E82]">Difficulty:</p>
          <p className="text-[#1B1F3B] thepassBtwo">
            <EnumText name="QuestionDifficulty" value={test.difficulty} />
          </p>
        </div>

        <div className="flex items-center gap-1">
          <HelpCircle className="size-[18px] text-[#743AF5]" />
          <p className="text-sm text-[#4E5E82]">Questions:</p>
          <p className="text-[#1B1F3B] thepassBtwo">{test.questionIds.length}</p>
        </div>

        <div className="flex items-center gap-1">
          <Clock5 className="size-4 text-[#743AF5]" />
          <p className="text-sm text-[#4E5E82]">Duration:</p>

          <p className="text-[#1B1F3B] thepassBtwo">
            <span> {test.duration} </span> min
          </p>
        </div>
      </div>
      <div className="flex gap-3 items-center">
        {test.subCategory.map((item: { categoryId: string; subCategoryId: string; subCategoryName: string }) => (
          <p className="capitalize border border-[#E7E7E7] text-[#2A3348] font-medium rounded-3xl px-7 py-1 text-sm w-fit h-fit">
            {item.subCategoryName}
          </p>
        ))}
      </div>
    </div>
  );
};
