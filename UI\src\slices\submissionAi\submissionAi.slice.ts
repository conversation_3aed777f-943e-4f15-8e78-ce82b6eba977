import { createSlice, type PayloadAction } from '@reduxjs/toolkit';
import { fetchSubmissionAi } from '../../middlewares';
import type { RootState } from '../../store';

interface SubmissionAiState {
  submissionAi: {
    interview: {
      numOfQuestions: number;
      currentIndex: number;
      templateId: string;
      questionList: any;
      availableSkips: any;
      allowedSkips: any;
      recordInterview: any;
      avatarName: string;
      difficulty: any;
      applicantId: string;
      _id: string;
      expired: any;
      submittedAt: any;
      locked: any;
      startedAt: any;
      randomId: string;
      avatarLang: any;
      category: {
        categoryId: string;
      };
    };
    applicant: {
      _id: string;
      email: string;
      seniorityLevel: any;
      name: string;
      mobileNumber: string;
      gender: any;
    };
  };
  loading: boolean;
  aiIsTyping: boolean;
  isSpeaking: boolean;
}

const initialState: SubmissionAiState = {
  submissionAi: {
    interview: {
      numOfQuestions: 0,
      currentIndex: 0,
      templateId: '',
      questionList: [],
      availableSkips: 0,
      allowedSkips: 0,
      recordInterview: false,
      avatarName: '',
      difficulty: 0,
      applicantId: '',
      _id: '',
      expired: false,
      submittedAt: '',
      locked: false,
      startedAt: '',
      randomId: '',
      avatarLang: '',
      category: {
        categoryId: '',
      },
    },
    applicant: {
      _id: '',
      email: '',
      seniorityLevel: 0,
      name: '',
      mobileNumber: '',
      gender: 0,
    },
  },
  loading: false,
  aiIsTyping: false,
  isSpeaking: false,
};

const submissionAiSlice = createSlice({
  name: 'submissionAi',
  initialState,
  reducers: {
    setAiIsTyping: (state, action: PayloadAction<boolean>) => {
      state.aiIsTyping = action.payload;
    },
    setAiLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setIsSpeaking: (state, action: PayloadAction<boolean>) => {
      state.isSpeaking = action.payload;
    },
    setSubmissionAi: (state, action: PayloadAction<any>) => {
      state.submissionAi = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSubmissionAi.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchSubmissionAi.fulfilled, (state, action: PayloadAction<any>) => {
        state.submissionAi = action.payload;
        state.loading = false;
      })
      .addCase(fetchSubmissionAi.rejected, (state) => {
        state.loading = false;
      });
  },
});

export const submissionAiState = (state: RootState) => state.submissionAi;
export const { setAiIsTyping, setIsSpeaking, setSubmissionAi, setAiLoading } = submissionAiSlice.actions;
export default submissionAiSlice.reducer;
