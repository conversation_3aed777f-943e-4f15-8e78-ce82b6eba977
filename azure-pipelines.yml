trigger:
  branches:
    include:
      - main
      - test

pool:
  vmImage: 'ubuntu-latest'

steps:
  # 1. Install Node
  - task: NodeTool@0
    inputs:
      versionSpec: '18.x'
    displayName: 'Install Node.js'

  # 2. Install Dependencies
  - script: |
      npm install
    displayName: 'Install dependencies'

  # 3. Run ESLint
  - script: |
      npx eslint . --ext .js,.jsx,.ts,.tsx
    displayName: 'Run ESLint'

  # 4. Run Tests
  - script: |
      npm test
    displayName: 'Run Tests'
