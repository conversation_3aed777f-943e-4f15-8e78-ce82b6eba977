import React from 'react';
import { createRoot } from 'react-dom/client';
import { App } from './app';
import './index.css';
import { Provider } from 'react-redux';
import { store, persistor } from 'UI';
import { PersistGate } from 'redux-persist/lib/integration/react';
import { ConfirmationDialog } from 'UI';
import { NotificationBridge } from './components/notification-bridge';

const rootElement = document.getElementById('root');

createRoot(rootElement!).render(
  <React.StrictMode>
    <Provider store={store}>
      <PersistGate persistor={persistor}>
        <App />
        <NotificationBridge />
        <ConfirmationDialog />
      </PersistGate>
    </Provider>
  </React.StrictMode>
);
