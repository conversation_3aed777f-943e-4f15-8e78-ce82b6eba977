// React
import { useState, useEffect, Dispatch, SetStateAction } from 'react';

// UI
import { Select, Drawer, NoDataFound } from 'src';
import { Api, useValidate, Form, initializeForm, RootState, setFieldValue, useAppSelector, Placeholder } from 'UI/src';

// Components
import { TestCreatedSucessfully } from './test-created-sucessfully';
import { AssignScreeningListView } from './assign-screening-list-view';
import { TimeSettingsDialog } from './assign-time-settings';
import { setErrorNotify, setNotifyMessage } from 'UI';
import { useAppDispatch } from 'UI/src';
import { QuizzesList } from 'UI/src/types/Quiz.type';
import { Applicant } from './applicant-data';
import { CustomIconType } from 'UI/src/types/CustomIcon.type';
import { useFormik } from 'formik';

interface QuestionListItem {
  _id: string;
  index: number;
  title: string;
  isEditMode: boolean;
  pendingTitle: string;
}
/*
  questionsListData = [
    {
      _id: "123456789",
      title: "Question",
      isEditMode: Boolean,
    }
  ]
*/

interface AssignScreeningProps {
  isAssignTestVisible: boolean;
  setAssignTestVisibility: (value: boolean) => void;
  applicantDetails: Applicant | null;
  setApplicantDetails: Dispatch<SetStateAction<Applicant | null>>;
  refreshMainTable: () => void;
}

interface ScreeningData {
  _id: string;
  title: string;
  duration: string;
  numOfQuestions: number;
}

export const AssignScreening: React.FC<AssignScreeningProps> = ({
  setAssignTestVisibility,
  applicantDetails,
  setApplicantDetails,
  refreshMainTable = () => {},
}) => {
  // State
  const [quizUrl, setQuizUrl] = useState<string>('');
  const [isTestCreatedSucessfullyVisible, setTestCreatedSucessfullyVisibilty] = useState<boolean>(false);
  const [quizzesListData, setQuizzesListData] = useState<QuizzesList>({ items: [], count: 0 });
  const [screeningData, setScreeningData] = useState<ScreeningData>();
  const [questionsListData, setQuestionsListData] = useState<QuestionListItem[]>([]);
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState(() => {
    const result = new Date(startDate);
    result.setDate(result.getDate() + 1);
    return result;
  });
  const [extraTime, setExtraTime] = useState<number>(0);
  const [isTimeSettingsVisible, setTimeSettingsVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const type: string = 'screening';

  // Hooks
  const dispatch = useAppDispatch();
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: { screening: null },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });
  const { isRequired } = useValidate();

  const handleGetPhoneScreeningTests = async () => {
    try {
      const response = await Api.get<QuizzesList>('quizzes/list/phoneScreening', {});
      console.log('quizzes/list/phoneScreening', response.data);
      const { items }: any = response.data; // @TODO: Markos will fix this
      setQuizzesListData(items);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
    }
  };

  const onClose = () => {
    setAssignTestVisibility(false);
    setApplicantDetails({} as Applicant);
  };

  const handleUpdate = async (id: string) => {
    try {
      setLoading(true);
      let payload = {
        questionsStatus: {
          deleted: [],
          created: [],
          updated: questionsListData
            ?.filter((question) => question?._id === id)
            ?.map((question) => ({ title: question?.pendingTitle, id: question?._id })),
        },
        duration: screeningData?.duration,
      };
      await Api.put(`quizzes/single/phoneScreening/${screeningData?._id}`, payload);
      dispatch(setNotifyMessage('Questions saved successfully!'));
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!startDate) {
      dispatch(setErrorNotify('Please select start date'));
    } else if (!endDate) {
      dispatch(setErrorNotify('Please select end date'));
    } else {
      try {
        setLoading(true);
        let payload = {
          quizId: screeningData?._id,
          dueDate: endDate,
          startDate: startDate,
        };
        // if (extraTime >= 1) payload.exceededTime = extraTime; // @FIXME: payload.exceededTime is not defined in the payload

        // @TODO:Handle multi applicant assignment
        // if (applicantDetails?._id) {
        //   payload.applicantId = [applicantDetails?._id]; // @FIXME: payload.applicantId is not defined in the payload
        // }

        const response = await Api.post('submissions/single', payload);
        setQuizUrl(response?.data.quizUrl);
        setTestCreatedSucessfullyVisibilty(true);
      } catch (error: any) {
        dispatch(setErrorNotify(error?.response?.data?.message));
      } finally {
        setLoading(false);
      }
    }
  };

  const noDataFound = {
    customIcon: {
      definedIcon: 'screening',
    },
    message: 'No screening selected yet',
  };

  useEffect(() => {
    handleGetPhoneScreeningTests();
  }, []);

  // /* Initial value of select component when open drawer with applicantDetails */
  // useEffect(() => {
  //   if (applicantDetails) {
  //     dispatch(
  //       setFieldValue({ path: 'screening', value: quizzesListData?.find((test: any) => test?.difficulty === applicantDetails?.seniorityLevel)?._id })
  //     );
  //   }
  // }, [quizzesListData]);

  return (
    <Drawer onClose={onClose}>
      <Drawer.SingleView>
        <Drawer.Header
          headerLabel={applicantDetails?.email ? 'Assign Screening' : 'Generate Screening Link'}
          headerSubLabel={applicantDetails?.email}
          onClose={onClose}
          className="border-b border-[#E5E7EB] pb-2"
        />

        <Drawer.Body.DatePicker
          startDate={startDate}
          dueDate={endDate}
          extraTime={extraTime}
          setExtraTime={setExtraTime as () => void}
          setTimeSettingsVisible={setTimeSettingsVisible as () => void}
          type={type}
        />

        <Form className="flex flex-col xsmd:flex-row gap-4 xsmd:items-center w-full" onSubmit={() => {}}>
          <p className="font-medium text-[#3C3D3E] dark:text-white">
            Select a Screening <span className="text-red-600 dark:text-red-800"> *</span>
          </p>
          <div className="sm:min-w-[250px]">
            <Select
              name="screening"
              lookup={quizzesListData.items?.map((test: { _id: string; title: string }) => ({ value: test?._id, label: test?.title }))}
              value={form.screening}
              onChange={(value: any) => dispatch(setFieldValue({ path: 'screening', value }))}
              dropIcon={true}
              validators={[isRequired()]}
            />
          </div>
        </Form>

        {form?.screening ? (
          <AssignScreeningListView
            screeningData={screeningData ?? { _id: '', title: '', duration: '', numOfQuestions: 0 }}
            setScreeningData={setScreeningData}
            questionsListData={questionsListData}
            setQuestionsListData={setQuestionsListData as any}
            applicantDetails={applicantDetails}
            setApplicantDetails={setApplicantDetails}
            handleUpdate={handleUpdate}
            handleSubmit={handleSubmit}
            difficulty={quizzesListData.items?.find((test: { _id: string }) => test?._id === form?.screening)?.difficulty}
            onClose={onClose}
            handleLoading={loading}
          />
        ) : (
          // <NoDataFound noDataFound={noDataFound} width="70" height="70" textMessageSize="text-[20px]" margin="mx-auto" />
          <Placeholder image="/UI/src/assets/placeholder/TestImagePlaceholder.svg" title="No screening selected yet" subTitle="" />
        )}
      </Drawer.SingleView>

      {/* Test Ceated Sucessfully Visible */}
      {isTestCreatedSucessfullyVisible && (
        <TestCreatedSucessfully
          assignment={applicantDetails?.email ? true : false}
          defaultType="Screening"
          quizUrl={quizUrl}
          onClose={() => {
            applicantDetails?.email && refreshMainTable();
            onClose();
          }}
        />
      )}

      {/* Time Setting */}
      {isTimeSettingsVisible && (
        <TimeSettingsDialog
          onClose={() => setTimeSettingsVisible(false)}
          startDate={startDate}
          setStartDate={setStartDate}
          dueDate={endDate}
          setDueDate={setEndDate}
          type={type}
        />
      )}
    </Drawer>
  );
};
