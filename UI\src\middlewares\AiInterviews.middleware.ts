import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';

// Fetch single AI interview
export const fetchAiInterview = createAsyncThunk(
  'aiInterviews/fetchAiInterview',
  async ({ id, weirdBehavior, typicalBehavior }: { id: string; weirdBehavior: boolean; typicalBehavior: boolean }, { rejectWithValue }) => {
    try {
      const response = await Api.get(`ai-interview/single/${id}?weirdBehavior=${weirdBehavior}&typicalBehavior=${typicalBehavior}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch AI interview');
    }
  }
);

// Create AI interview
export const createAiInterview = createAsyncThunk(
  'aiInterviews/createAiInterview',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('ai-interview/single', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create AI interview');
    }
  }
);

// Get AI interview stages report
export const fetchAiInterviewStagesReport = createAsyncThunk(
  'aiInterviews/fetchStagesReport',
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`ai-interview/stages/report/${id}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch stages report');
    }
  }
);

// Get AI interview uploads
export const fetchAiInterviewUploads = createAsyncThunk(
  'aiInterviews/fetchUploads',
  async ({ assessmentId, applicantId }: { assessmentId: string; applicantId: string }, { rejectWithValue }) => {
    try {
      const response = await Api.get(`ai-interview/list-uploads/${assessmentId}/${applicantId}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch uploads');
    }
  }
);

// Get AI interview upload URL
export const getAiInterviewUploadUrl = createAsyncThunk(
  'aiInterviews/getUploadUrl',
  async ({ mimeType, fileName }: { mimeType: string; fileName: string }, { rejectWithValue }) => {
    try {
      const response = await Api.get(`ai-interview/upload-url?mimeType=${mimeType}&fileName=${encodeURIComponent(fileName)}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to get upload URL');
    }
  }
);

// AI interview talk
export const aiInterviewTalk = createAsyncThunk(
  'aiInterviews/talk',
  async (payload: { interviewId: string; userAnswerText: string }, { rejectWithValue }) => {
    try {
      const response = await Api.post('ai-interview/single/talk', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to process AI interview talk');
    }
  }
);

// Generate AI interview questions
export const generateAiInterviewQuestions = createAsyncThunk(
  'aiInterviews/generateQuestions',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('ai-interview/generate/questions', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to generate AI interview questions');
    }
  }
); 
