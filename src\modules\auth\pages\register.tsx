import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { TextInput, Button, Icon, Logo, Checkbox } from 'src';
import { Regex, Form, initializeForm, useValidate, setFieldValue, useAppSelector, Api } from 'UI/src';
import { useAppDispatch, updateUser, setErrorNotify, useUserPermissions, CookieStorage } from 'UI';
import { GoogleOAuthProvider, GoogleLogin, CredentialResponse } from '@react-oauth/google';
import { VITE_GOOGLE_CLIENT_ID } from 'UI/src/configs/api';
import { useFormik } from 'formik';

export const Register = () => {
  const { isRequired, minLength, maxLength, validateRegex } = useValidate();
  const dispatch = useAppDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const { handleGetUserRole } = useUserPermissions();
  const redirectAfter = location.state?.redirectAfter || '/app/dashboard';

  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState('');
  const [termsAccepted, setTermsAccepted] = useState(false);
  const formik = useFormik({
    initialValues: {
      name: '',
      email: '',
      password: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });
  const form = useAppSelector((state) => state.form.data);

  const clientId = `${VITE_GOOGLE_CLIENT_ID}.apps.googleusercontent.com`;

  const handleGoogleAccountLoginSuccess = async (credentialResponse: CredentialResponse) => {
    try {
      const { credential } = credentialResponse;
      const { data } = await Api.post('auth/google-login', { idToken: credential });

      if (data?.access_token) {
        // @FIXME: Fix local storage
        CookieStorage.setItem('userData', JSON.stringify(data));
        dispatch(updateUser(data));
        dispatch(handleGetUserRole);
        navigate('/app');
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message || 'Google login failed'));
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (!termsAccepted) {
      dispatch(setErrorNotify('You must accept the terms of service to register'));
      return;
    }

    if (form.password !== confirmPassword) {
      dispatch(setErrorNotify('Passwords do not match'));
      return;
    }

    try {
      setLoading(true);
      const { data } = await Api.post('organizations/single', form);
      if (data?.access_token) {
        // @FIXME: Fix local storage
        CookieStorage.setItem('userData', JSON.stringify(data));
        dispatch(updateUser(data));
        dispatch(handleGetUserRole);

        if (redirectAfter === '/app/dashboard') {
          navigate('/app/dashboard', { state: { subscriptionSuccess: true }, replace: true });
        } else {
          navigate(redirectAfter);
        }
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message || 'Registration failed'));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen grid place-items-center bg-gray-50 dark:bg-gray-900 px-4">
      <div className="w-full max-w-lg bg-white dark:bg-gray-800 rounded-xl shadow p-6 space-y-8">
        <div className="flex flex-col gap-5">
          <a onClick={() => navigate('/')} className="cursor-pointer">
            <Logo className="h-8" />
          </a>

          <h1 className="text-base font-semibold leading-tight tracking-tight text-[#4E5E82]">Create an Account</h1>
        </div>

        <Form onSubmit={handleSubmit} className="space-y-5">
          <TextInput
            name="name"
            // TODO: Markos
            label="Full Name"
            placeholder="Enter"
            disabled={loading}
            value={form.name}
            onChange={(value: string) => dispatch(setFieldValue({ path: 'name', value }))}
            validators={[isRequired(), minLength(3), maxLength(100), validateRegex(Regex.name)]}
          />

          <TextInput
            name="email"
            // TODO: Markos
            label="Email Address"
            customPlaceholder={
              <span className="text-gray-400 dark:text-gray-500 flex gap-2">
                <Icon icon="mdi:email-outline" width="20" /> Enter
              </span>
            }
            disabled={loading}
            value={form.email}
            onChange={(value: string) => dispatch(setFieldValue({ path: 'email', value }))}
            validators={[isRequired(), validateRegex(Regex.email)]}
          />

          <div className="relative">
            <TextInput
              // TODO: Markos
              label="Password"
              name="password"
              placeholder="Enter"
              type={showPassword ? 'text' : 'password'}
              autoComplete="new-password"
              disabled={loading}
              value={form.password}
              onChange={(value: string) => dispatch(setFieldValue({ path: 'password', value }))}
              validators={[isRequired(), minLength(6)]}
              rightIcon={() => {}}
            />
            <span onClick={() => setShowPassword(!showPassword)} className="absolute top-9 right-3 cursor-pointer">
              <Icon icon={showPassword ? 'mdi:eye-outline' : 'mdi:eye-off-outline'} width="24" />
            </span>
          </div>

          <div className="relative">
            <TextInput
              // TODO: Markos
              label="Confirm Password"
              name="confirmPassword"
              placeholder="Enter"
              type={showConfirmPassword ? 'text' : 'password'}
              disabled={loading}
              value={confirmPassword}
              onChange={setConfirmPassword}
              validators={[isRequired()]}
              rightIcon={() => {}}
            />
            <span onClick={() => setShowConfirmPassword(!showConfirmPassword)} className="absolute top-9 right-3 cursor-pointer">
              <Icon icon={showConfirmPassword ? 'mdi:eye-outline' : 'mdi:eye-off-outline'} width="24" />
            </span>
            {form.password !== confirmPassword && confirmPassword && <p className="text-sm text-red-500 mt-1">Passwords do not match</p>}
          </div>

          <Checkbox
            name="terms"
            value={termsAccepted}
            onChange={setTermsAccepted}
            // TODO: Markos
            label={
              <span className="text-sm text-[#4E5E82]">
                I Agree To The{' '}
                <span
                  className="text-[#743AF5] font-medium cursor-pointer hover:underline"
                  onClick={(e) => {
                    e.stopPropagation();
                    navigate('/terms');
                  }}
                >
                  Terms Of Service
                </span>
              </span>
            }
          />

          <Button
            type="submit"
            label="Register"
            icon="mdi:send"
            disabled={!termsAccepted || form.password !== confirmPassword || !form.password || !form.email || !form.name}
            loading={loading}
            className="w-full"
            gradientMonochrome="purple"
          />

          <div className="relative flex items-center px-12">
            <hr className="flex-grow border-gray-300 dark:border-gray-600" />
            <span className="mx-4 text-sm text-gray-500 dark:text-gray-400">Or sign up With</span>
            <hr className="flex-grow border-gray-300 dark:border-gray-600" />
          </div>

          <GoogleOAuthProvider clientId={clientId}>
            <div className="flex justify-center [&_.nsm7Bb-HzV7m-LgbsSe-BPrWId]:hidden [&_.nsm7Bb-HzV7m-LgbsSe-Bz112c]:!mr-0">
              <GoogleLogin
                onSuccess={handleGoogleAccountLoginSuccess}
                onError={() => dispatch(setErrorNotify('Google login failed'))}
                shape="circle"
                size="large"
              />
            </div>
          </GoogleOAuthProvider>
          <div className="flex gap-2 justify-center items-center">
            <p className="text-[#4E5E82]">Do You Have An Account? </p>
            <span onClick={() => navigate('/auth/login')} className="text-purple-600 cursor-pointer underline">
              Login
            </span>
          </div>
        </Form>
      </div>
    </div>
  );
};
