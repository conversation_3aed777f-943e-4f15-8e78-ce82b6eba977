// React
import { StrictMode } from 'react';
import { BrowserRouter, Routes, Route, Navigate, Outlet } from 'react-router-dom';
import { APP_ROUTE_PATH, AUTH_ROUTE_PATH } from 'UI/src/configs/router';
import { UserPermissions } from 'UI';

// Deps
import 'iconify-icon';

// Components
import UserSSEListener from 'src/components/UserSSEListener';

// Protected and Unprotected Routes
import { ProtectedRoute } from 'src/modules/auth/components/protected-route';
import { UnProtectedRoute } from 'src/modules/auth/components/un-protected-route';
import { PermissionProtectedRoute } from 'src/components';

// Layouts
import { AppLayout, AuthLayout, GlobalLayout, PaymentLayout } from 'src/layouts';
import AppWrapper from 'src/routes/RouterProvider';
import { QuizLayout } from 'src/layouts/quiz';
import { QuizAiLayout } from 'src/layouts/quiz-ai';
import { QuickAssignMainLayout } from 'src/layouts/QuickAssignLayout';

// Auth Pages
import { LoginPage } from 'src/modules/auth/pages/login';
import { Register } from 'src/modules/auth/pages/register';

// App Module Pages
import { StatisticsPage } from 'src/modules/app/modules/dashboard/pages/statistics';
import { DashboardSuperAdmin as DashboardSuperAdminPage } from 'src/modules/app/modules/dashboard-super-admin/pages';
import { OrganizationsListPage } from 'src/modules/app/modules/organizations/pages/list';
import { PlansListPage } from 'src/modules/app/modules/plans/pages/list';
import { RolesListPage } from 'src/modules/app/modules/roles/pages/list';
import { QuestionsListPage } from 'src/modules/app/modules/questions/pages/list';
import { QuestionsSinglePage } from 'src/modules/app/modules/questions/pages/single';
import { QuizzesListPage } from 'src/modules/app/modules/quizzes/pages/list';
import { QuizzesSinglePage } from 'src/modules/app/modules/quizzes/pages/single';
import { SubmissionsListPage } from 'src/modules/app/modules/submissions/pages/list';
import { SubmissionsSinglePage } from 'src/modules/app/modules/submissions/pages/single';
import { PDFViewerComponent } from 'src/modules/app/modules/submissions/components/pdf-viewer';
import { ApplicantsListPage } from 'src/modules/app/modules/applicants/pages/list';
import { ApplicantProgressSingle } from '@/modules/app/modules/applicants/pages/single';
import { UsersListPage } from 'src/modules/app/modules/users/pages/list';
import { AiInterviewsListPage as InterviewsListPage } from 'src/modules/app/modules/interviews/pages/list';
import { AiInterviewSinglePage as InterviewsSinglePage } from 'src/modules/app/modules/interviews/pages/single';
import { PhoneScreeningListPage } from 'src/modules/app/modules/phone-screening/pages/list';
import { SinglePhoneScreeningPage as PhoneScreeningSinglePage } from 'src/modules/app/modules/phone-screening/pages/single';
import { AssessmentsListPage } from 'src/modules/app/modules/assessments/pages/list';
import { AssessmentSingle, AssessmentSingle as AssessmentsSinglePage } from 'src/modules/app/modules/assessments/pages/single';
import { AssessmentReportPage as AssessmentReportListPage } from 'src/modules/app/modules/assessment-report/pages/list';
import { AssessmentReport as AssessmentReportSinglePage } from 'src/modules/app/modules/assessment-report/pages/single';
import { CategoryManagementComponent } from 'src/modules/app/modules/category-management/pages/list';
import { StudioPage } from 'src/modules/app/modules/studio';
import { AssessmentsCreatePage } from './modules/app/modules/assessments/pages/create';
import { AssessmentsAssignPage } from './modules/app/modules/assessments/pages/assign';
import { AddCreditsComponent } from './modules/app/modules/add-credits/pages/list';
import { OrganizationsProfile } from '@/modules/app/modules/organizations/pages/profile';
import { TransactionsList } from './modules/app/modules/transactions/pages/list';

// Global Module Pages
import { PricingGlobal } from 'src/modules/global/modules/pricing/pages';
import { ContactUsGlobal } from 'src/modules/global/modules/contact-us/pages';
import { TermsOfServicePage } from 'src/modules/global/modules/terms/pages';
import { NewLandingPage } from 'src/modules/global/modules/new-landing/pages';
import { ProgrammingTestsPage as ProgrammingTestListPage } from 'src/modules/global/modules/programming-test/pages';

// Payment Pages
import { PaymentVisaInfoData } from 'src/modules/payment/pages/visa-info';
import { PaymentStatusPage } from 'src/modules/payment/pages/payment-status';

// Test Pages
import { QuizPage } from 'src/pages/quiz/QuizPage';
import { QuizAiPage } from 'src/pages/quiz-ai/QuizAiPage';

// Global Pages
import { Error404Page } from 'src/pages/404';
import { Error403Page } from 'src/pages/403';
import { DemoRequest } from 'src/pages/demo-request';
import { DemoMainLayout } from 'src/pages/mainDemo-request';
import { AvatarComponent } from './modules/app/modules/avatars/pages/list';
import { TestDetails } from './modules/global/modules/programming-test/pages/single';
import { QuickAssignPage } from './modules/global/modules/quick-assign/pages';
import { AvatarManagement } from '@/modules/app/modules/avatar-managment/pages/list';
// import { PDFViewerComponent } from './modules/app/modules/submissions/components/pdf-viewer';

export const App = () => {
  return (
    <StrictMode>
      <UserSSEListener />

      <BrowserRouter>
        <Routes>
          <Route path="/" element={<AppWrapper />}>
            {/* Global Routes */}
            <Route path="/" element={<GlobalLayout />}>
              <Route index element={<NewLandingPage />} />

              {/* Programming Test */}
              <Route path="programming-test" element={<Outlet />}>
                <Route index element={<Navigate to="/programming-test/list" />} />
                <Route path="list" element={<ProgrammingTestListPage />} />
                <Route path="single/:id" element={<TestDetails />} />
              </Route>

              {/* Global Pages */}
              <Route path="pricing" element={<PricingGlobal />} />
              <Route path="contact-us" element={<ContactUsGlobal />} />
              <Route path="terms" element={<TermsOfServicePage />} />
              <Route path="quick-assign" element={<QuickAssignPage />} />

              {/* Demo request */}
              <Route path="request-demo" element={<DemoMainLayout />}>
                <Route index element={<DemoRequest />} />
              </Route>
            </Route>

            {/* Auth Routes */}
            <Route
              path={AUTH_ROUTE_PATH}
              element={
                <UnProtectedRoute>
                  <AuthLayout />
                </UnProtectedRoute>
              }
            >
              <Route path="login" element={<LoginPage />} />
              <Route path="register" element={<Register />} />
            </Route>

            {/* App Routes */}
            <Route
              path={APP_ROUTE_PATH}
              element={
                <ProtectedRoute>
                  <AppLayout />
                </ProtectedRoute>
              }
            >
              {/* Default */}
              <Route index element={<UnProtectedRoute />} />

              {/* Dashboard */}
              <Route path="dashboard" element={<StatisticsPage />} />

              {/* Dashboard Super Admin */}
              <Route path="dashboard-super-admin" element={<DashboardSuperAdminPage />} />

              {/* Organizations */}
              <Route path="organizations" element={<Outlet />}>
                <Route index element={<Navigate to="/app/organizations/list" />} />
                <Route path="list" element={<OrganizationsListPage />} />
                <Route path="profile/:id/:tab" element={<OrganizationsProfile />} />
              </Route>

              {/* Transactions */}
              <Route path="transactions" element={<Outlet />}>
                <Route index element={<Navigate to="/app/transactions/list" />} />
                <Route path="list" element={<TransactionsList />} />
              </Route>

              {/* Plans */}
              <Route path="plans" element={<Outlet />}>
                <Route index element={<Navigate to="/app/plans/list" />} />
                <Route path="list" element={<PlansListPage />} />
              </Route>

              {/* Roles */}
              <Route path="roles" element={<Outlet />}>
                <Route index element={<Navigate to="/app/roles/list" />} />
                <Route path="list" element={<RolesListPage />} />
              </Route>

              {/* Questions */}
              <Route
                path="questions"
                element={
                  <PermissionProtectedRoute permissions={UserPermissions.VIEW_QUESTION}>
                    <Outlet />
                  </PermissionProtectedRoute>
                }
              >
                <Route index element={<Navigate to="/app/questions/list" />} />
                <Route path="list" element={<QuestionsListPage />} />
                <Route
                  path="create"
                  element={
                    <PermissionProtectedRoute permissions={UserPermissions.CREATE_QUESTION}>
                      <QuestionsSinglePage />
                    </PermissionProtectedRoute>
                  }
                />
                <Route path="view/:id" element={<QuestionsSinglePage />} />
                <Route
                  path="edit/:id"
                  element={
                    <PermissionProtectedRoute permissions={UserPermissions.UPDATE_QUESTION}>
                      <QuestionsSinglePage />
                    </PermissionProtectedRoute>
                  }
                />
              </Route>

              {/* Tests/Quizzes */}
              <Route path="tests" element={<Outlet />}>
                <Route index element={<Navigate to="/app/tests/list/setup" />} />
                <Route path="list/setup" element={<QuizzesListPage />} />
                <Route path="create" element={<QuizzesSinglePage />} />
                <Route path="edit/:id" element={<QuizzesSinglePage />} />
                <Route path="view/:id" element={<QuizzesSinglePage />} />
                <Route path="pdf/:id" element={<PDFViewerComponent />} />
              </Route>

              {/* Submissions */}
              <Route path="submissions" element={<Outlet />}>
                <Route index element={<Navigate to="/app/submissions" />} />
                <Route path="list" element={<SubmissionsListPage />} />
                <Route path="single/:id" element={<SubmissionsSinglePage />} />
              </Route>

              {/* Applicants */}
              <Route
                path="applicants"
                element={
                  <PermissionProtectedRoute permissions={UserPermissions.VIEW_APPLICANT}>
                    <Outlet />
                  </PermissionProtectedRoute>
                }
              >
                <Route index element={<Navigate to="/app/applicants/list" />} />
                <Route path="list" element={<ApplicantsListPage />} />
                <Route path="progress/:id/:tab" element={<ApplicantProgressSingle />} />
              </Route>

              {/* Users */}
              <Route
                path="users"
                element={
                  <PermissionProtectedRoute permissions={UserPermissions.VIEW_USER}>
                    <Outlet />
                  </PermissionProtectedRoute>
                }
              >
                <Route index element={<Navigate to="/app/users/list" />} />
                <Route path="list" element={<UsersListPage />} />
              </Route>

              {/* Interviews */}
              <Route path="interviews" element={<Outlet />}>
                <Route index element={<Navigate to="/app/interviews/list" />} />
                <Route path="list" element={<InterviewsListPage />} />
                <Route path="single/:id" element={<InterviewsSinglePage />} />
              </Route>

              {/* Phone Screening */}
              <Route path="phone-screening" element={<Outlet />}>
                <Route index element={<Navigate to="/app/phone-screening" />} />
                <Route path="list" element={<PhoneScreeningListPage />} />
                <Route path="single/:id" element={<PhoneScreeningSinglePage />} />
              </Route>

              {/* Assessments Templates */}
              <Route
                path="assessment-templates"
                element={
                  <PermissionProtectedRoute permissions={UserPermissions.VIEW_ASSESSMENT}>
                    <Outlet />
                  </PermissionProtectedRoute>
                }
              >
                <Route index element={<Navigate to="/app/assessment-templates/test/list" />} />
                <Route path="screening" element={<Navigate to="/app/assessment-templates/screening/list" />} />
                <Route path="test" element={<Navigate to="/app/assessment-templates/test/list" />} />
                <Route path="interview" element={<Navigate to="/app/assessment-templates/interview/list" />} />

                <Route path=":type/list" element={<AssessmentsListPage />} />
                <Route
                  path=":type/create"
                  element={
                    <PermissionProtectedRoute permissions={UserPermissions.CREATE_ASSESSMENT}>
                      <AssessmentsCreatePage />
                    </PermissionProtectedRoute>
                  }
                />
                <Route
                  path=":type/assign/:id?"
                  element={
                    <PermissionProtectedRoute permissions={UserPermissions.ASSIGN_ASSESSMENT}>
                      <AssessmentsAssignPage />
                    </PermissionProtectedRoute>
                  }
                />
                <Route path=":type/view/:id" element={<AssessmentSingle />} />
                <Route
                  path=":type/edit/:id"
                  element={
                    <PermissionProtectedRoute permissions={UserPermissions.UPDATE_ASSESSMENT}>
                      <AssessmentsCreatePage />
                    </PermissionProtectedRoute>
                  }
                />
              </Route>

              {/* Assessment Report */}
              <Route
                path="assessment-report"
                element={
                  <PermissionProtectedRoute permissions={UserPermissions.VIEW_ASSESSMENTS_REPORT}>
                    <Outlet />
                  </PermissionProtectedRoute>
                }
              >
                <Route index element={<Navigate to="/app/assessment-report/list/tests" />} />
                <Route path="list/:tab" element={<AssessmentReportListPage />} />
                <Route path="view/:type/:quizId/:tab" element={<AssessmentReportSinglePage />} />
              </Route>

              {/* Add Credits */}
              <Route path="add-credits" element={<AddCreditsComponent />} />

              {/* Category Management */}
              <Route
                path="category-management"
                element={
                  <PermissionProtectedRoute permissions={UserPermissions.VIEW_CATEGORY}>
                    <CategoryManagementComponent />
                  </PermissionProtectedRoute>
                }
              />

              {/* Avatars */}
              <Route path="avatars" element={<AvatarComponent />} />

              {/* Avatars  management*/}
              <Route path="avatars-management" element={<Outlet />}>
                <Route index element={<Navigate to="/app/avatars-management/system" />} />
                <Route path="/app/avatars-management/:tab" element={<AvatarManagement />} />
              </Route>

              {/* Studio */}
              <Route path="studio" element={<StudioPage />} />
            </Route>

            {/* Payment Routes */}
            <Route path="payment" element={<PaymentLayout />}>
              <Route index element={<Navigate to="/pricing" />} />
              <Route path=":planId/:planKey?" element={<PaymentVisaInfoData />} />
              <Route path="status/:paymentStatus/:subscriptionId" element={<PaymentStatusPage />} />
            </Route>

            {/* Quick Assign */}
            {/* <Route path="quick-assign" element={<QuickAssignMainLayout />}>
              <Route path="" element={<QuickAssignPage />} />
            </Route> */}

            {/* Test Routes */}
            <Route path="test" element={<QuizLayout />}>
              <Route index element={<Navigate to="/404" replace />} />
              <Route path=":id" element={<QuizPage />} />
            </Route>

            {/* Ai-Test Routes */}
            <Route path="interview" element={<QuizAiLayout />}>
              <Route index element={<Navigate to="/404" replace />} />
              <Route path=":id" element={<QuizAiPage />} />
            </Route>

            {/* <Route path="/test-ai-api" element={<TestAiApi />} /> */}
          </Route>

          <Route path="/403" element={<Error403Page />} />
          <Route path="*" element={<Error404Page />} />
        </Routes>
      </BrowserRouter>
    </StrictMode>
  );
};
