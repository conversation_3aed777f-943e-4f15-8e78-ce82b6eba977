import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/20/solid';
import React from 'react';

interface Props {
  onNextClick: () => void;
  onPreviousClick: () => void;
  pages: number;
  currentPage: number;
  onPageClick: (page: number) => void;
}

const Pagination: React.FC<Props> = ({ onNextClick, onPreviousClick, pages, currentPage, onPageClick }) => {
  if (pages === 1) {
    return;
  }
  const renderPageNumbers = () => {
    const pageNumbers = [];

    // Always show the first 3 pages
    for (let i = 1; i <= Math.min(3, pages); i++) {
      pageNumbers.push(i);
    }

    // Add ellipsis if currentPage is far from the start
    if (currentPage > 4) {
      pageNumbers.push('...');
    }

    // Add the current page and its neighbors
    if (currentPage > 3 && currentPage < pages - 2) {
      pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
    }

    // Add ellipsis if currentPage is far from the end
    if (currentPage < pages - 3) {
      pageNumbers.push('...');
    }

    // Always show the last 3 pages
    for (let i = Math.max(pages - 2, 4); i <= pages; i++) {
      pageNumbers.push(i);
    }

    return pageNumbers.map((page, index) =>
      typeof page === 'number' ? (
        <button
          key={index}
          onClick={() => onPageClick(page)}
          className={`${
            currentPage === page
              ? 'relative z-10 inline-flex items-center bg-[#8A43F90A] px-4 py-2 text-sm font-semibold focus:z-20 text-[#8A43F9] focus-visible:outline-2 focus-visible:outline-offset-0  focus-visible:outline-[#DDDEE0] inset-ring inset-ring-gray-300'
              : 'relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 inset-ring inset-ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0 cursor-pointer'
          }`}
        >
          {page}
        </button>
      ) : (
        <span
          key={index}
          className="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 inset-ring inset-ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0"
        >
          ...
        </span>
      )
    );
  };

  return (
    <div className="flex items-center justify-center lg:border-t border-gray-200 lg:bg-[#F9F8FA] px-4 py-3 sm:px-6 w-full">
      <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-center">
        <div>
          <nav aria-label="Pagination" className="isolate inline-flex -space-x-px rounded-md shadow-xs">
            <button
              onClick={onPreviousClick}
              className="relative inline-flex items-center rounded-l-md px-2 py-2 text-gray-400 inset-ring inset-ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-2 cursor-pointer"
            >
              <ChevronLeftIcon aria-hidden="true" className="size-5 text-[#8A43F9]" />
              <span className="text-[#343537]">Previous</span>
            </button>
            <div className="hidden md:-mt-px md:flex">{renderPageNumbers()}</div>
            <button
              onClick={onNextClick}
              className="relative inline-flex items-center rounded-r-md px-2 py-2 text-gray-400 inset-ring inset-ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-2 cursor-pointer"
            >
              <span className="text-[#343537]">Next</span>
              <ChevronRightIcon aria-hidden="true" className="size-5 text-[#8A43F9]" />
            </button>
          </nav>
        </div>
      </div>
    </div>
  );
};

export default Pagination;
