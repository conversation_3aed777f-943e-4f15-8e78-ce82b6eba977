// React
import React, { useEffect, useRef, useState } from 'react';
// Components
import { Dialog, MultiSelect, TextInput, Select, Button } from 'src';
import { Form, initializeForm, RootState, setFieldValue, useAppDispatch, useAppSelector, useValidate, Api } from 'UI/src';
import { setErrorNotify } from 'UI';
import { useFormik } from 'formik';

// Types

type SubmissionsGenerateDialogProps = {
  onClose: () => void;
  category: number;
  questionIds: string[];
  setQuestions: (ids: string[]) => void;
  setUpdateVal: (val: boolean) => void;
  handleGetQuestionData: (id: string) => void;
};

export const SubmissionsGenerateDialog = ({
  onClose,
  category,
  questionIds,
  setQuestions,
  setUpdateVal,
  handleGetQuestionData,
}: SubmissionsGenerateDialogProps) => {
  //State
  const subCategoryRef = useRef<HTMLInputElement | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const dispatch = useAppDispatch();
  const { isRequired, isSelected } = useValidate();

  // Form

  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      numOfQuestions: 10,
      subCategory: [],
      questionsDifficulty: 0,
      // topic: null,
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  // Methods
  const handleSubmit = async () => {
    try {
      if (category === 0) {
        dispatch(setErrorNotify('Please select a category.'));
      } else if ((form.subCategory as string[]).length < 1) {
        dispatch(setErrorNotify('There is no question subcategory chosen'));
      } else if (form.questionsDifficulty < 1) {
        dispatch(setErrorNotify('There is no question difficulty chosen'));
      } else {
        setLoading(true);
        const response = await Api.post('questions/generate', {
          numOfQuestions: form.numOfQuestions,
          category: category,
          subCategory: form.subCategory,
          questionsDifficulty: form.questionsDifficulty,
          exclude: questionIds,
        });
        (response.data as any[]).map((question) => handleGetQuestionData(question._id));
        const result = (response.data as any[]).map((item) => item._id);
        setQuestions(result);
        if (result.length === 0 && questionIds.length > 0) {
          dispatch(setErrorNotify('There is no more questions for this category'));
        } else if (result.length === 0) {
          dispatch(setErrorNotify('There is no question for this category'));
        } else {
          setUpdateVal(true);
          onClose();
        }
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error.response));
    } finally {
      setLoading(false);
    }
  };

  // On Mount
  useEffect(() => {
    setUpdateVal(false);
  }, []);

  return (
    <Dialog show popup size="lg" modalHeader="Generate Questions " onClose={onClose} overflowVisible={true}>
      <Form onSubmit={handleSubmit}>
        {/* Generate Random Questions */}
        <MultiSelect
          key={category}
          ref={subCategoryRef}
          label="Question Subcategory"
          name="SubCategory"
          value={form.subCategory}
          onChange={(newSubCategory: any) => {
            dispatch(setFieldValue({ path: 'subCategory', value: newSubCategory }));
          }}
          disabled={!category}
          lookup="subcategory"
          params={{ categoryId: category }}
          optionValueKey="_id"
          optionLabelKey="name"
          validators={[isSelected()]}
        />
        <div className="grid grid-cols-2 gap-2 space-y-2 mb-6">
          {/* <div className="col-span-2">
            <Select
              name="topic"
              label="Topic"
              lookup=""
              value={!form.subCategory}
              onChange={setFieldValue('topic')}
              dropIcon={true}
              validators={[isRequired()]}
              disabled={!form.subCategory.length}
            />
          </div> */}
          <Select
            name="questionsDifficulty"
            label="Difficulty"
            lookup="$QuestionDifficulty"
            value={form.questionsDifficulty}
            onChange={(value: any) => dispatch(setFieldValue({ path: 'questionsDifficulty', type: Number, value }))}
            dropIcon={true}
            validators={[isRequired()]}
          />
          <TextInput
            name="numOfQuestions"
            label="Question Count"
            placeholder="Enter number"
            type="number"
            value={form.numOfQuestions}
            onChange={(value: any) => dispatch(setFieldValue({ path: 'numOfQuestions', type: Number, value }))}
            min={1}
            max={100}
            validators={[]}
          />
        </div>
        {/* Actions */}
        <Button type="submit" label="Generate" className="w-full" loading={loading} disabled={loading} />
      </Form>
    </Dialog>
  );
};
