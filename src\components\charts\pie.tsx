import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Toolt<PERSON>, ResponsiveContainer } from 'recharts';
import { ReactNode } from 'react';

interface PieChartsProps {
  data: {
    name: string;
    value: number;
  }[];
  COLORS: string[];
  customLegend: (payload: any) => ReactNode;
  className?: string;
}
export const PieCharts = ({ data, COLORS, customLegend, className }: PieChartsProps) => {
  return (
    <ResponsiveContainer width="100%" height={300}>
      <PieChart>
        <Pie data={data} cx="50%" cy="50%" outerRadius={80} dataKey="value">
          {data?.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index]} />
          ))}
        </Pie>
        <Tooltip />

        {customLegend ? <Legend content={customLegend} /> : <Legend verticalAlign="bottom" align="center" layout="vertical" />}
      </PieChart>
    </ResponsiveContainer>
  );
};
