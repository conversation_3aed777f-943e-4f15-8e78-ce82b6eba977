// React
import { useEffect, useRef, useState } from 'react';

// Flowbite

// Core
import { CustomIcon, Button, TestDifficulty, Textarea, Radio, Checkbox, Icon, CategoryFieldColumn, SubcategoryFieldColumn } from 'src';
import {
  hideConfirm,
  QuestionType,
  initializeForm,
  RootState,
  setFieldValue,
  showConfirm,
  useAppDispatch,
  useAppSelector,
  Form,
  useScreenSize,
  useValidate,
  Api,
  StaticData,
  QuestionTypeEnum,
  Tags,
  setErrorNotify,
  setNotifyMessage,
  UpdateQuestionByIdRes,
} from 'UI';

interface QuestionProps {
  questionId: string;
  selectedQuestionsID?: Record<string, boolean>;
  setSelectedQuestionsID?: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  anyQuestionHasEditMode: Record<string, boolean>;
  setAnyQuestionHasEditMode: React.Dispatch<React.SetStateAction<Record<string, boolean>>>;
  canEditQuestion: boolean;
  canRemoveQuestion?: boolean;
}
export const QuestionItem = ({
  questionId,
  selectedQuestionsID,
  setSelectedQuestionsID,
  anyQuestionHasEditMode,
  setAnyQuestionHasEditMode,
  canEditQuestion,
  canRemoveQuestion,
}: QuestionProps) => {
  // Reference
  const textRef = useRef(null);

  const isViewOnly = useAppSelector((state: RootState) => state.viewOnly.isVisible);

  // Hooks
  const screen = useScreenSize();
  const { isRequired, isNotSpaces } = useValidate();

  let row = {};
  let selectedQuestionIds = [];
  let mainQuestionsListForm = [];

  //
  // useEffect(() => {
  //   dispatch(initializeForm({}));
  // }, []);

  const dispatch = useAppDispatch();
  const form = useAppSelector((state: RootState) => state.form.data);

  // State
  const [isExpanded, setIsExpanded] = useState(false);
  const [isOverflow, setIsOverflow] = useState(false);
  const [isEditMode, setEditMode] = useState(false);
  const [isShowFullAnswers, setShowFullAnswers] = useState(false);
  const [question, setQuestion] = useState<QuestionType>();
  const [loading, setLoading] = useState(true);

  const fetchQuestionData = async () => {
    try {
      setLoading(true);
      const response = await Api.get<QuestionType>(`questions/single/${questionId}`, {});
      console.log('questions/single/${questionId}', response.data);
      setQuestion(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error.response.data.message));
    } finally {
      setLoading(false);
    }
  };

  //
  // const handleCheckboxChange = () => {};

  // const handleGetQuestionData = async () => {
  //   try {
  //     const response = await Api.get<QuestionType>(`questions/single/${questionId}`, {});
  //     console.log('questions/single/', response.data);
  //     dispatch(initializeForm(response?.data));
  //   } catch (error: any) {
  //     dispatch(setErrorNotify(error.response.data.message));
  //   }
  // };

  const toggleExpand = () => {
    //     handleGetQuestionData();

    if (!question) {
      fetchQuestionData();
    }
    setShowFullAnswers((prev) => !prev);
  };

  const handleAddOption = () => {
    if (form.options.length >= 4) return;
    const newOption = {
      id: form.options.length + 1,
      label: '',
    };
    dispatch(setFieldValue({ path: 'options', value: [...form.options, newOption] }));
  };

  const handleSelectCheckbox = (value: boolean, id: number) => {
    const numberOfSelectedAnswers = Object.entries(form.multiChoiceAnswer).filter(([key, value]) => value).length;
    if (numberOfSelectedAnswers >= 2 && value) {
      return dispatch(setErrorNotify('Only two answers are allowed'));
    }
    const newObj = { ...form.multiChoiceAnswer, [id]: value };
    dispatch(setFieldValue({ path: 'multiChoiceAnswer', value: newObj }));
  };

  const handleRemoveOption = (id: number) => {
    // Remove unwanted form options
    const updatedOptions = form.options.filter((option: { id: number; label: string }) => option.id !== id);
    dispatch(setFieldValue({ path: 'options', value: updatedOptions }));

    // Set removed form multiChoiceAnswer to false
    const updateMultiChoiceAnswer = { ...form.multiChoiceAnswer, [id]: false };
    dispatch(setFieldValue({ path: 'multiChoiceAnswer', value: updateMultiChoiceAnswer }));
  };

  const getDifficultyText = (level: number): string => {
    switch (level) {
      case 1:
        return 'easy';
      case 2:
        return 'medium';
      case 3:
        return 'hard';
      default:
        return 'easy';
    }
  };

  const getDifficultyColor = (level: number): string => {
    switch (level) {
      case 1:
        return 'bg-[#EEFFF1] text-[#056816]';
      case 2:
        return 'bg-[#FFFCDF] text-[#BA8500]';
      case 3:
        return 'bg-[#FFECE9] text-[#A80000]';
      default:
        return 'bg-[#EEFFF1] text-[#056816]';
    }
  };

  const ConfirmText = () => (
    <div className="flex flex-col w-full items-center gap-2">
      <svg width="66" height="66" viewBox="0 0 66 66" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="5" y="5" width="56" height="56" rx="28" fill="#F0E7FF" />
        <rect x="5" y="5" width="56" height="56" rx="28" stroke="#F8F4FF" strokeWidth="10" />
        <g clipPath="url(#clip0_8621_5254)">
          <path
            d="M38.8085 26.3846C38.2939 25.4066 37.4884 24.5917 36.4904 24.0006C35.4929 23.4121 34.2888 23.0528 32.982 23.0528C31.3711 23.0488 30.0328 23.4685 29.0227 24.0497C28.0085 24.6288 27.5713 25.3031 27.5713 25.3031C27.4008 25.451 27.3047 25.6662 27.3087 25.8912C27.3133 26.1166 27.4171 26.3282 27.5928 26.4687L28.9964 27.593C29.2825 27.822 29.692 27.8134 29.9681 27.5727C29.9681 27.5727 30.1406 27.2611 30.6809 26.9524C31.2243 26.6458 31.9288 26.3988 32.982 26.3955C33.9006 26.3935 34.7016 26.7363 35.2482 27.2047C35.5198 27.4368 35.7227 27.6967 35.847 27.9344C35.9723 28.1741 36.0181 28.3836 36.0174 28.5428C36.0148 29.0805 35.9103 29.4322 35.7595 29.7319C35.6445 29.9557 35.4943 30.1543 35.301 30.3469C35.0125 30.6353 34.6214 30.9019 34.1825 31.1469C33.7432 31.3948 33.2698 31.613 32.7914 31.8766C32.2454 32.1789 31.6675 32.6132 31.2406 33.265C31.0277 33.5872 30.8613 33.9558 30.7556 34.3447C30.6484 34.7339 30.6001 35.1421 30.6001 35.5584C30.6001 36.0026 30.6001 36.3673 30.6001 36.3673C30.6001 36.786 30.9395 37.1255 31.3583 37.1255H33.1849C33.6036 37.1255 33.9431 36.786 33.9431 36.3673C33.9431 36.3673 33.9431 36.0026 33.9431 35.5584C33.9431 35.398 33.9614 35.2946 33.979 35.2289C34.0091 35.1308 34.0261 35.1062 34.0754 35.0466C34.1258 34.9898 34.2276 34.903 34.4152 34.7992C34.6894 34.6451 35.13 34.4369 35.6289 34.1674C36.3758 33.7589 37.2838 33.2046 38.0497 32.29C38.4306 31.8335 38.7674 31.2838 38.9998 30.6506C39.2342 30.0174 39.3608 29.3056 39.3602 28.5428C39.3595 27.7699 39.1499 27.036 38.8085 26.3846Z"
            fill="#7E3AF2"
          />
          <path
            d="M32.273 38.9297C31.1342 38.9297 30.2109 39.8533 30.2109 40.9917C30.2109 42.1298 31.1343 43.0531 32.273 43.0531C33.4111 43.0531 34.334 42.1298 34.334 40.9917C34.334 39.8533 33.4111 38.9297 32.273 38.9297Z"
            fill="#7E3AF2"
          />
        </g>
        <defs>
          <clipPath id="clip0_8621_5254">
            <rect width="20" height="20" fill="white" transform="translate(23.334 23.0527)" />
          </clipPath>
        </defs>
      </svg>

      <p className="text-xl dark:text-white">Are you sure?</p>
      <p className="items-center text-[#626262] font-light justify-center dark:text-white">
        Once confirmed, these changes will be applied to the main question as well.{' '}
      </p>
    </div>
  );

  const handleMinTwoAnswers = () => {
    let counter = 0;
    Object.entries(form.multiChoiceAnswer).forEach(([key, value]) => {
      value && counter++;
    });
    if (form.type === 2) {
      if (counter === 2) {
        return true;
      }
      return dispatch(setErrorNotify('Choose two answers'));
    }
    return true;
  };

  const handleUpdate = async (): Promise<void> => {
    if (!!handleMinTwoAnswers()) {
      if (form.title !== '') {
        try {
          const response = await Api.put(`questions/single/${questionId}`, form);
          dispatch(setNotifyMessage('Question updated successfully!'));
          fetchQuestionData();
          setShowFullAnswers(false);
          setEditMode(false);
          setAnyQuestionHasEditMode((prev) => ({ ...prev, [questionId]: false }));
        } catch (error: any) {
          dispatch(setErrorNotify(error?.response?.data?.message));
        } finally {
          dispatch(hideConfirm());
        }
      } else {
        dispatch(setErrorNotify(`Content shouldn't be empty`));
      }
    }
  };

  useEffect(() => {
    if (textRef && textRef.current) {
      setIsOverflow((textRef.current as HTMLElement).scrollHeight > (textRef.current as HTMLElement).offsetHeight);
    }
  }, []);

  // Fetch question data on mount
  useEffect(() => {
    if (questionId) {
      fetchQuestionData();
    }
  }, [questionId]);

  if (loading) {
    return (
      <div className="p-4 border border-gray-200 rounded-lg animate-pulse  dark:bg-gray-700 overflow-hidden">
        <div className="flex flex-col space-y-3 w-full">
          <div className="w-full h-5 border border-gray-200 rounded-lg animate-pulse bg-gray-100 dark:bg-gray-700"></div>
          <div className="flex gap-4 ">
            <div className="w-24 h-6 border border-gray-200 rounded-lg animate-pulse bg-gray-100  dark:bg-gray-700"></div>
            <div className="w-24 border border-gray-200 rounded-lg animate-pulse bg-gray-100  dark:bg-gray-700"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!question) {
    return (
      <div className="p-4 border rounded-lg bg-red-50 dark:bg-red-900/20">
        <p className="text-red-500 dark:text-red-400">Failed to load question data</p>
      </div>
    );
  }

  return (
    <Form
      onSubmit={(e) => e.preventDefault()}
      className={`p-4 space-y-3 border transition-all border-[#E8E8E8] dark:border-gray-700 rounded-2xl ${
        selectedQuestionsID && selectedQuestionsID[question?._id] && canRemoveQuestion && 'border-purple-600 bg-[#F9F6FE] dark:bg-gray-900'
      }`}
    >
      <div className="flex flex-col sm:flex-row justify-between items-start gap-2">
        <div className="flex gap-2 w-full">
          {/* Checkbox */}
          {/* {!canRemoveQuestion && !isEditMode && (
              <FlowbiteCheckbox
                value={row._id}
                checked={selectedQuestionIds?.includes(row._id) || mainQuestionsListForm?.questionIds?.includes(row._id)}
                className={`mt-0.5 ${mainQuestionsListForm?.questionIds?.includes(row._id) ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                // onChange={handleCheckboxChange}
                theme={StaticData?.customThemeCheckbox}
              />
            )} */}

          {/* Question text || Edit Question text */}
          {isEditMode ? (
            <div className="w-full">
              <Textarea
                name="title"
                value={form?.title}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'title', value: value }))}
                validators={[isRequired()]}
              />
            </div>
          ) : (
            <>
              {/* {!isViewOnly && canRemoveQuestion && (
                  <Checkbox
                    checked={isChecked}
                    theme={StaticData?.customThemeCheckbox}
                    />
                    )} */}

              {selectedQuestionsID && canRemoveQuestion && (
                <div className="flex items-start justify-center pt-0.5">
                  <Checkbox
                    name="select-question"
                    fullWidth={false}
                    value={selectedQuestionsID && selectedQuestionsID[question._id]}
                    onChange={() => {
                      setSelectedQuestionsID && setSelectedQuestionsID((prev) => ({ ...prev, [question._id]: !prev[question._id] }));
                    }}
                    theme={StaticData?.customThemeCheckbox}
                    className="cursor-pointer"
                    preventSendingMail={false}
                    isCustomLabel={false}
                  />
                </div>
              )}

              <div className={`flex gap-1 items-start thepassHfour ${canRemoveQuestion ? '' : ''}`}>
                <p className={`text-gray-900 native-break-all-words thepassHfour ${!isExpanded && 'line-clamp-2'}`} ref={textRef}>
                  {question?.title}
                </p>

                {/* <div>
                    <p className={`dark:text-white text-[15px] native-break-all-words ${!isExpanded && 'line-clamp-2'}`} ref={textRef}>
                      {question?.title}
                    </p>
                    {isOverflow && (
                      <span className="text-sm text-[#333333C2] font-medium cursor-pointer" onClick={() => setIsExpanded((prev) => !prev)}>
                        {isExpanded ? 'Read less' : 'Read more'}
                      </span>
                    )}
                  </div> */}
              </div>
            </>
          )}
        </div>

        {/* Buttons handle view */}
        {!isEditMode && (
          <div className="flex justify-between self-end sm:self-start -order-1 sm:order-1 gap-2">
            {/* {!isViewOnly && isShowFullAnswers && canEditQuestion && (
              <Button
                size="sm"
                label={!screen.lt.xs() ? 'Edit' : ''}
                tertiary
                customIcon={{ definedIcon: 'edit' }}
                iconWidth="18"
                onClick={() => {
                  setEditMode(true);
                  setAnyQuestionHasEditMode((prev) => ({ ...prev, [questionId]: true }));
                }}
              />
            )} */}

            {question.type !== QuestionTypeEnum.Essay && (
              <div className="cursor-pointer">
                <CustomIcon definedIcon={isShowFullAnswers ? 'arrowUp' : 'arrowDown'} className="text-[#8D5BF8]" onClick={toggleExpand} />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Question details - Difficulty, subCategory */}
      {!isEditMode && (
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center">
          <div className="flex flex-wrap gap-2">
            <CategoryFieldColumn categoryNameArray={question?.categoryName} />
            <SubcategoryFieldColumn subCategoryName={question?.subCategoryName} />
          </div>

          <div className="w-fit">
            {question?.difficulty && <Tags type={getDifficultyText(question?.difficulty)} color={getDifficultyColor(question?.difficulty)} />}
          </div>
        </div>
      )}

      {/* Answers */}
      {isShowFullAnswers && (
        <div className="p-3 space-y-2 border border-[#DEE2E4] rounded-2xl">
          <p className={`text-[#3A4458] thepassBtwo ${canRemoveQuestion ? '' : ''}`}>Answer Options</p>
          <div className="space-y-3">
            {(isEditMode ? form?.options : question?.options)?.map((option: { id: number; label: string }, index: number) => {
              const letters = ['a', 'b', 'c', 'd'];
              return (
                <div
                  key={option.id}
                  className={`w-full px-3 py-1.5 space-y-2 flex items-center justify-center rounded-lg ${
                    +(isEditMode ? form.singleChoiceAnswer : question?.singleChoiceAnswer) === index + 1
                      ? 'bg-[#EEFFF1] border border-[#009217]'
                      : 'bg-[#F8FAFC] border border-[#E2E8F0]'
                  }`}
                >
                  <div className="flex items-center gap-5 pt-2">
                    {/* Radio Button for Single Choice */}
                    {(isEditMode ? form.type : question?.type) === QuestionTypeEnum.Singlechoice && (
                      <div className="flex items-center">
                        {(isEditMode ? form.singleChoiceAnswer : question?.singleChoiceAnswer) === index + 1 ? (
                          <div className="mr-3 ">
                            <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M0 10.5C0 4.97715 4.47715 0.5 10 0.5C15.5228 0.5 20 4.97715 20 10.5C20 16.0228 15.5228 20.5 10 20.5C4.47715 20.5 0 16.0228 0 10.5ZM13.7071 7.79289C14.0976 8.18342 14.0976 8.8166 13.7071 9.2071L10.0243 12.8899C9.4586 13.4556 8.5414 13.4556 7.97568 12.8899L6.29289 11.2071C5.90237 10.8166 5.90237 10.1834 6.29289 9.7929C6.68342 9.4024 7.31658 9.4024 7.70711 9.7929L9 11.0858L12.2929 7.79289C12.6834 7.40237 13.3166 7.40237 13.7071 7.79289Z"
                                fill="#009217"
                              />
                            </svg>
                          </div>
                        ) : (
                          <Radio
                            name={`singleChoiceAnswer${option.id}`}
                            label=""
                            labelTooltip=""
                            labelTooltipStyles=""
                            isCustomLabel={false}
                            selectionValue={option.id}
                            value={isEditMode ? form.singleChoiceAnswer : question?.singleChoiceAnswer}
                            onChange={(value: any) => {
                              dispatch(setFieldValue({ path: 'singleChoiceAnswer', type: Number, value }));
                            }}
                            disabled={!isEditMode}
                            className={`${isEditMode ? 'cursor-pointer' : 'cursor-not-allowed'}`}
                            theme={StaticData?.customThemeRadioButton}
                            fullWidth={false}
                            pointer={false}
                            applicantTestView={false}
                          />
                        )}
                      </div>
                    )}

                    {/* Checkboxes for Multi Choice */}
                    {(isEditMode ? form.type : question?.type) === QuestionTypeEnum.Multichoice && (
                      <div className="flex items-center">
                        <Checkbox
                          name={`multichoiceAnswer${option.id}`}
                          label=""
                          value={
                            isEditMode ? form.multiChoiceAnswer[option.id] : (question?.multiChoiceAnswer as Record<number, boolean>)?.[option.id]
                          }
                          onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleSelectCheckbox(e.target.checked, option.id)}
                          disabled={!isEditMode}
                          className={`${isEditMode ? 'cursor-pointer' : 'cursor-not-allowed'}`}
                          theme={StaticData?.customThemeCheckbox}
                          fullWidth={false}
                          preventSendingMail={false}
                          isCustomLabel={false}
                        />
                      </div>
                    )}

                    {/* {isEditMode && (
                      <span className="text-nowrap text-xs font-medium text-inputLabel dark:text-inputDarkLabel">
                        {(form.type === 2 ? index < 3 : index < 2) && <span className="text-red-600 dark:text-red-800"> *</span>}
                      </span>
                    )} */}
                  </div>

                  {/* Answer text */}
                  {isEditMode ? (
                    <div className="flex items-center gap-2 flex-grow">
                      <div className="w-full">
                        {/* <p className="capitalize">{letters[index]}</p> */}
                        <Textarea
                          name={`answer${option.id}`}
                          placeholder="Write your answer"
                          className="w-full bg-white"
                          value={option.label}
                          onChange={(value: any) => {
                            dispatch(setFieldValue({ path: `options.${index}.label`, value }));
                          }}
                          validators={
                            (isEditMode ? form.type : question?.type) === 2
                              ? index < 3
                                ? [isRequired(), isNotSpaces()]
                                : [isNotSpaces()]
                              : index < 2
                              ? [isRequired(), isNotSpaces()]
                              : [isNotSpaces()]
                          }
                          requiredLabel={index < 2}
                          maxHeight="150"
                          rows={1}
                        />
                      </div>

                      {/* Show delete button only if there are more than two options */}
                      {isEditMode &&
                        ((isEditMode ? form.type : question?.type) === 2 ? form.options.length > 3 : form.options.length > 2) &&
                        index === form.options.length - 1 && (
                          <Button
                            tertiary
                            size="sm"
                            icon="hugeicons:delete-02"
                            width={'22'}
                            className="text-[#B83434] cursor-pointer"
                            onClick={() => handleRemoveOption(option.id)}
                          />
                        )}
                    </div>
                  ) : (
                    <div className="w-full text-sm rounded-lg thepassBtwo">
                      <span className="capitalize">{letters[index]}.</span> {option.label}
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Add button to add more options */}
          {isEditMode && (isEditMode ? form?.options?.length : question?.options?.length) < 4 && (
            <div
              className="bg-newQuAnsBg w-full my-2 py-2 rounded-lg dark:bg-newQuAnsDarkBg dark:bg-opacity-80 hover:bg-newQuAnsHoverBg mx-auto cursor-pointer"
              onClick={handleAddOption}
            >
              <button type="button" className="flex items-center justify-center mx-auto">
                <Icon icon="icon-park-solid:add-one" className="text-primaryPurple text-opacity-60" width="20" />
                <span className="ml-2 text-newQuAnsText font-medium dark:text-newQuAnsDarkText">New Answer</span>
              </button>
            </div>
          )}
        </div>
      )}

      {/* Buttons handle question answers */}
      {isEditMode && (
        <div className="flex gap-2">
          <Button
            label="Cancel"
            tertiary
            size="sm"
            onClick={() => {
              setEditMode(false);
              setAnyQuestionHasEditMode((prev) => ({ ...prev, [questionId]: false }));
              setShowFullAnswers(false);
              //   dispatch(initializeForm({}));
            }}
          />

          <Button size="sm" label="Save Changes" className="opacity-90" onClick={handleUpdate} />
        </div>
      )}
    </Form>
  );
};
