// React
import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// Core
import { Jumbotron, Icon, CustomIcon, SubscribeDialog } from 'src';
import { AlertNote, Button, UserPermissions, RootState, useAppSelector, UserData, useBreadcrumb, PlanFeatures } from 'UI/src';

// Flowbite
import { Dropdown, DropdownItem } from 'flowbite-react';

// Components
import { ScreeningTab } from '../components/list/screening-tab';
import { TestTab } from '../components/list/test-tab';
import { InterviewTab } from '../components/list/interview-tab';
import { PermissionProtectedComponent } from 'src/components';
import CheckFeatureManagement from '@/composables/feature-management';

export const AssessmentsListPage = () => {
  const navigate = useNavigate();
  const { type } = useParams();

  const typeTitle: { [key: string]: any } = {
    interview: 'Interview',
    test: 'Test',
    screening: 'Screening',
  };

  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');
  const isAdmin = Array.isArray(userData?.role) && userData?.role.includes('admin');
  const isContentCreator = Array.isArray(userData?.role) && userData?.role.includes('content-creator');
  const isHr = Array.isArray(userData?.role) && userData?.role.includes('hr');
  const { checkFeature } = CheckFeatureManagement();

  const [needSubscription, setNeedSubscription] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateTemplateVisible, setCreateTemplateVisibilty] = useState<Boolean | string>(false);

  const actions = {
    label: 'Create Template',
    // label: `Create ${typeTitle[type ?? '']}`,
    dropdownlist: [
      {
        label: 'Manual Creation',
        customIcon: 'magicStick',
        onClick: () => navigate(`/app/assessment-templates/${type}/create`),
      },
      {
        label: 'AI Creation',
        customIcon: 'mind',
        onClick: () => setCreateTemplateVisibilty('AI'),
      },
    ],
  };

  const renderContent = () => {
    if (type === 'screening')
      return <ScreeningTab onLoadingChange={setIsLoading} createTemplateDialog={{ isCreateTemplateVisible, setCreateTemplateVisibilty }} />;
    if (type === 'test')
      return <TestTab onLoadingChange={setIsLoading} createTemplateDialog={{ isCreateTemplateVisible, setCreateTemplateVisibilty }} />;
    if (type === 'interview')
      return <InterviewTab onLoadingChange={setIsLoading} createTemplateDialog={{ isCreateTemplateVisible, setCreateTemplateVisibilty }} />;
    return <div className="text-center text-gray-500">Invalid assessment type.</div>;
  };

  const checkPermissionCreate = () => {
    if (type === 'test') {
      return PlanFeatures.CREATE_CUSTOM_TESTS;
    } else {
      return PlanFeatures.CREATE_CUSTOM_INTERVIEWS;
    }
  };

  const checkPermissionAssign = () => {
    if (type === 'test') {
      return PlanFeatures.ASSIGN_TESTS;
    } else {
      return PlanFeatures.ASSIGN_CUSTOM_INTERVIEWS;
    }
  };

  return (
    <div className="space-y-2">
      <div className="flex flex-wrap gap-2 justify-between items-center relative z-40">
        {/* {!isLoading && <Jumbotron />} */}
        <div className="">
          <p className="thepassHone">Ready Assessment Templates</p>
          <p className="thepassSubHone text-[#868D9C]">Oversee, customize, and deploy pre-built assessments across subscribers</p>
        </div>

        {!isLoading && (
          <div className="flex flex-wrap items-center gap-2">
            <PermissionProtectedComponent permissions={UserPermissions.ASSIGN_ASSESSMENT}>
              {(isSuperAdmin || isAdmin || isHr) && (
                <Button
                  label="Assign"
                  // label={`Assign ${(type && typeTitle[type]) || 'Assessment'}`}
                  colorType="secondary"
                  // permission={checkPermissionAssign()}
                  onClick={() => navigate(`/app/assessment-templates/${type}/assign`)}
                  icon={<Icon icon="mdi-add" />}
                  permission={PlanFeatures.ASSIGN_CUSTOM_INTERVIEWS}
                />
              )}
            </PermissionProtectedComponent>

            <PermissionProtectedComponent permissions={UserPermissions.CREATE_ASSESSMENT}>
              {(isSuperAdmin || isAdmin || isContentCreator) && (
                <div className="relative z-40">
                  <Dropdown
                    label=""
                    className="w-full sm:ml-9"
                    dismissOnClick={false}
                    renderTrigger={() => (
                      <div className="relative z-50">
                        <Button
                          icon={<Icon icon="mdi-add" />}
                          iconRight={<Icon icon="material-symbols:keyboard-arrow-down-rounded" />}
                          label={actions.label}
                          permission={PlanFeatures.ASSIGN_CUSTOM_INTERVIEWS}
                          // iconRight="ic:twotone-keyboard-arrow-down"
                        />
                      </div>
                    )}
                  >
                    {actions.dropdownlist.map(({ label, customIcon, onClick }, index) => (
                      <div key={label}>
                        <DropdownItem onClick={onClick}>
                          <div className="flex gap-2 cursor-pointer w-fit">
                            {customIcon && (
                              <div className="w-5 flex justify-center items-center">
                                <CustomIcon definedIcon={customIcon} className="cursor-pointer" width="20" height="20" onClick={onClick} />
                              </div>
                            )}
                            <p className="text-sm font-medium text-black text-nowrap">{label}</p>
                          </div>
                        </DropdownItem>
                        {actions.dropdownlist.length - 1 > index && <hr className="border-[#1A1A1A14]" />}
                      </div>
                    ))}
                  </Dropdown>
                </div>
              )}
            </PermissionProtectedComponent>
          </div>
        )}
      </div>

      <PermissionProtectedComponent permissions={UserPermissions.ASSIGN_ASSESSMENT}>
        {!checkFeature(type === 'test' ? (PlanFeatures.ASSIGN_TESTS as any) : (PlanFeatures.ASSIGN_TESTS as any)) && (
          <AlertNote message="Oops! You’ve hit your limit , Add extra credits to keep enjoying all the features." nav={{}} />
        )}
      </PermissionProtectedComponent>

      {renderContent()}
    </div>
  );
};
