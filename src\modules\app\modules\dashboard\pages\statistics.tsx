// Core
import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'src';

// Components
import { CurrentPlanSummary } from '../../organizations/components/profile/overview/current-plan-summary';
import { UsersCard } from '../../organizations/components/profile/overview/users-card';
import { TicketsCard } from '../../organizations/components/profile/overview/tickets-card';
import { FeatureEngagement } from '../../organizations/components/profile/overview/feature-engagement';
import { OrganizationGrowthIndividual } from '../../organizations/components/profile/overview/organization-growth-individual';
import { useParams } from 'react-router-dom';
import { OrganizationsOverview, useAppDispatch, Api, UserData, useAppSelector, RootState } from 'UI/src';
import { setErrorNotify } from 'UI';

export const StatisticsPage = () => {
  const [organizationData, setOrganizationData] = useState<OrganizationsOverview>();
  const dispatch = useAppDispatch();
  const { id } = useParams();
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const orgId = id ?? userData.organizationId;

  const getOrganizationOverview = async () => {
    try {
      const response = await Api.get<OrganizationsOverview>(`organizations/overview/${orgId}`, {});
      console.log(`organizations/overview/${orgId}`, response.data);
      setOrganizationData(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  // Styles
  const cardClassNames = 'p-3 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg';

  const CardTitle = ({ icon, title, onClick }: { icon?: any; title: string; onClick?: { title: string; onClick: () => void } }) => {
    return (
      <div className="min-h-12 flex flex-wrap justify-between gap-2 border-b dark:border-gray-700 px-2 pb-2">
        <div className="flex items-center gap-2">
          {icon && icon}
          <div className="flex items-center gap-3 font-semibold text-[#313D4F] dark:text-white">{title}</div>
        </div>
        {onClick && <Button onClick={onClick.onClick} label={onClick.title} tertiary size="sm" />}
      </div>
    );
  };

  const blockCards = () => {
    const data = [
      {
        id: 1,
        title: 'Applicants',
        icon: (
          <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              opacity="0.21"
              d="M0.5 16C0.5 7.16344 7.66344 0 16.5 0C25.3366 0 32.5 7.16344 32.5 16C32.5 24.8366 25.3366 32 16.5 32C7.66344 32 0.5 24.8366 0.5 16Z"
              fill="#F1E9FE"
            />
            <path
              opacity="0.587821"
              d="M20.7676 13.1572C21.9456 13.1575 22.9004 14.113 22.9004 15.291C22.9001 16.4688 21.9454 17.4236 20.7676 17.4238C19.5895 17.4238 18.6341 16.469 18.6338 15.291C18.6338 14.1128 19.5894 13.1572 20.7676 13.1572ZM14.3682 9.60156C15.939 9.60172 17.2119 10.8754 17.2119 12.4463C17.2118 14.017 15.9389 15.2899 14.3682 15.29C12.7973 15.29 11.5236 14.0171 11.5234 12.4463C11.5234 10.8753 12.7972 9.60156 14.3682 9.60156Z"
              fill="#743AF5"
            />
            <path
              d="M20.4848 18.1357C22.9067 18.1626 24.8838 19.3857 25.0327 21.9736C25.0387 22.0781 25.0325 22.4004 24.6469 22.4004H21.9057C21.9057 20.8004 21.3767 19.3239 20.4848 18.1357ZM14.3569 16.7109C17.7615 16.711 20.5587 18.3419 20.7671 21.8311C20.7754 21.9702 20.7669 22.3994 20.2329 22.3994H8.48582C8.30747 22.3994 7.9542 22.0149 7.96921 21.8301C8.24499 18.4364 10.9996 16.7109 14.3569 16.7109Z"
              fill="#743AF5"
            />
          </svg>
        ),
        count: organizationData?.applicantCount,
      },
      {
        id: 2,
        title: 'Engagement',
        icon: (
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              opacity="0.21"
              d="M16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16C4.5101e-07 7.16344 7.16344 4.5098e-07 16 0Z"
              fill="#EEFFF1"
            />
            <path
              d="M10.1905 21.8053H22.635C23.0931 21.8053 23.4646 22.1768 23.4646 22.635C23.4646 23.0931 23.0931 23.4646 22.635 23.4646H9.36088C8.90269 23.4646 8.53125 23.0931 8.53125 22.635V9.36088C8.53125 8.90269 8.90269 8.53125 9.36088 8.53125C9.81907 8.53125 10.1905 8.90269 10.1905 9.36088V21.8053Z"
              fill="#009217"
            />
            <path
              opacity="0.5"
              d="M13.2864 18.2272C12.9731 18.5614 12.448 18.5784 12.1138 18.265C11.7795 17.9516 11.7626 17.4266 12.0759 17.0923L15.1871 13.7738C15.4901 13.4505 15.9938 13.4225 16.3308 13.7101L18.7863 15.8055L21.9856 11.7531C22.2695 11.3935 22.7912 11.3321 23.1508 11.616C23.5105 11.8999 23.5718 12.4216 23.2879 12.7812L19.5546 17.5101C19.263 17.8795 18.7229 17.9326 18.3649 17.6271L15.856 15.4863L13.2864 18.2272Z"
              fill="#009217"
            />
          </svg>
        ),
        percentage: organizationData?.engagementScore ?? 0,
      },
      {
        id: 3,
        title: 'Tickets',
        icon: (
          <svg width="33" height="32" viewBox="0 0 33 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              opacity="0.21"
              d="M0.5 16C0.5 7.16344 7.66344 0 16.5 0C25.3366 0 32.5 7.16344 32.5 16C32.5 24.8366 25.3366 32 16.5 32C7.66344 32 0.5 24.8366 0.5 16Z"
              fill="#E0F3FB"
            />
            <path
              d="M22.3761 18.4729L18.9786 21.8704C17.5836 23.2654 15.3186 23.2654 13.9161 21.8704L10.6236 18.5779C9.22862 17.1829 9.22862 14.9179 10.6236 13.5154L14.0286 10.1254C14.7411 9.4129 15.7236 9.0304 16.7286 9.0829L20.4786 9.2629C21.9786 9.3304 23.1711 10.5229 23.2461 12.0154L23.4261 15.7654C23.4711 16.7779 23.0886 17.7604 22.3761 18.4729Z"
              stroke="#11ABE6"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M18.375 16C17.3395 16 16.5 15.1605 16.5 14.125C16.5 13.0895 17.3395 12.25 18.375 12.25C19.4105 12.25 20.25 13.0895 20.25 14.125C20.25 15.1605 19.4105 16 18.375 16Z"
              stroke="#11ABE6"
              stroke-width="1.5"
              stroke-linecap="round"
            />
          </svg>
        ),
        count: 0,
      },
      {
        id: 4,
        title: 'Users',
        icon: (
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              opacity="0.3"
              d="M16 0C24.8366 0 32 7.16344 32 16C32 24.8366 24.8366 32 16 32C7.16344 32 0 24.8366 0 16C4.5101e-07 7.16344 7.16344 4.5098e-07 16 0Z"
              fill="#FFFCDF"
            />
            <path
              d="M19 21.75V20.25C19 19.4544 18.6839 18.6913 18.1213 18.1287C17.5587 17.5661 16.7956 17.25 16 17.25H11.5C10.7044 17.25 9.94129 17.5661 9.37868 18.1287C8.81607 18.6913 8.5 19.4544 8.5 20.25V21.75"
              stroke="#BA8500"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M19 8.34375C19.6433 8.51053 20.213 8.8862 20.6198 9.4118C21.0265 9.9374 21.2471 10.5832 21.2471 11.2478C21.2471 11.9123 21.0265 12.5581 20.6198 13.0837C20.213 13.6093 19.6433 13.985 19 14.1517"
              stroke="#BA8500"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M23.5 21.7462V20.2462C23.4995 19.5815 23.2783 18.9358 22.871 18.4105C22.4638 17.8851 21.8936 17.5099 21.25 17.3438"
              stroke="#BA8500"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M13.75 14.25C15.4069 14.25 16.75 12.9069 16.75 11.25C16.75 9.59315 15.4069 8.25 13.75 8.25C12.0931 8.25 10.75 9.59315 10.75 11.25C10.75 12.9069 12.0931 14.25 13.75 14.25Z"
              stroke="#BA8500"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        ),
        percentage: 0,
      },
    ];
    return data.map((singleData) => (
      <div
        key={singleData?.id}
        className="flex items-start justify-between px-4 py-3 shadow-[0px_7px_10px_0px_#743AF51A] dark:border-gray-700 rounded-2xl"
      >
        <div className="space-y-1">
          <p className="text-[#5F5F5F] dark:text-white text-sm font-medium">{singleData.title}</p>
          <div className="flex items-center gap-2 text-sm">
            <p className="font-semibold dark:text-white">
              {singleData.count && singleData.count} {singleData.percentage && singleData.percentage + '%'}
            </p>
          </div>
        </div>
        <div className={`flex justify-center items-center  rounded-full size-8`}>{singleData.icon}</div>
      </div>
    ));
  };

  useEffect(() => {
    getOrganizationOverview();
  }, []);

  return (
    <div className="space-y-3">
      <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-3">{blockCards()}</div>

      <main className="grid grid-cols-1 lg:grid-cols-10 gap-4">
        <div className={`h-full lg:col-span-7 ${cardClassNames}`}>
          <CardTitle title="Organization Growth" />
          <OrganizationGrowthIndividual />
        </div>

        <div className={`lg:col-span-3 h-full`}>
          <CurrentPlanSummary />
        </div>
      </main>

      <main className="grid grid-cols-1 gap-4">
        <div className={`${cardClassNames}`}>
          <CardTitle title="Feature Engagement" />
          <FeatureEngagement />
        </div>
      </main>
    </div>
  );
};
