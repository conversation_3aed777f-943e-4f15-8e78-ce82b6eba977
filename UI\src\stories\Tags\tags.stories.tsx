import type { Meta, StoryObj } from '@storybook/react';
import { Tags } from '../../components/tags';

// Example React component icon
const CustomIcon = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <circle cx="8" cy="8" r="6" fill="#FF6B6B" stroke="#FF6B6B" strokeWidth="2" />
  </svg>
);

const meta: Meta<typeof Tags> = {
  title: 'Components/Tags',
  component: Tags,
  argTypes: {
    type: {
      control: 'text',
      defaultValue: 'senior',
    },
    color: {
      control: 'text',
      defaultValue: 'bg-blue-100 text-blue-800',
    },
    icon: {
      control: 'text',
      defaultValue: '',
    },
    children: {
      control: 'text',
      defaultValue: '',
    },
  },
};

export default meta;

type Story = StoryObj<typeof Tags>;

export const Default: Story = {
  args: {
    type: 'senior',
    color: 'bg-blue-100 text-blue-800',
  },
};

export const WithCustomLabel: Story = {
  args: {
    type: 'junior',
    color: 'bg-green-100 text-green-800',
    children: 'Custom Junior Tag',
  },
};

export const WithCustomIcon: Story = {
  args: {
    type: 'intern',
    icon: 'FreshIcon', // make sure this icon exists in tagsList
    color: 'bg-yellow-100 text-yellow-800',
  },
};

export const WithReactComponentIcon: Story = {
  args: {
    type: 'custom',
    icon: <CustomIcon />,
    color: 'bg-red-100 text-red-800',
    children: 'Custom React Icon',
  },
};
