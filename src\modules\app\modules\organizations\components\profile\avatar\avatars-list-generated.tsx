// React
import { useEffect, useState } from 'react';
import { useAppDispatch, Api } from 'UI/src';
import { Link, useParams } from 'react-router-dom';

// Core
import { Card, Icon } from 'src';
import { setErrorNotify } from 'UI';

type AvatarsGeneratedProps = {
  showEmpty?: boolean;
};

export const AvatarsGenerated = ({ showEmpty = true }: AvatarsGeneratedProps) => {
  // Hooks
  const dispatch = useAppDispatch();
  const { id } = useParams();

  // State
  const [uploadedAvatars, setUploadedAvatars] = useState([]);
  const [loading, setLoading] = useState(false);

  // Helpers
  const handleGet = async () => {
    setLoading(true);
    try {
      const { data } = await Api.get(`organizations/list-uploads/${id}`, {});
      console.log(`organizations/list-uploads/${id}`, data);
      setUploadedAvatars(data);
    } catch (error: any) {
      dispatch(setErrorNotify(`Failed to fetch uploaded avatars: ${error}`));
    } finally {
      setLoading(false);
    }
  };

  const extractImageName = (url: string) => {
    const fileName = url.split('/').pop();
    const namePart = fileName?.split('-')[0];
    return namePart;
  };

  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, [id]);

  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="w-full h-40 p-4 space-y-4 border rounded-lg animate-pulse">
          <p className="w-full h-24 max-w-[70%] bg-gray-200 rounded-md"></p>
          <div className="flex justify-between items-center gap-6">
            <p className="w-full h-4 max-w-[70%] bg-gray-200 rounded-md"></p>
            <p className="w-full h-4 max-w-[70%] bg-gray-200 rounded-md"></p>
          </div>
        </div>
        <div className="w-full h-40 p-4 space-y-4 border rounded-lg animate-pulse">
          <p className="w-full h-24 max-w-[70%] bg-gray-200 rounded-md"></p>
          <div className="flex justify-between items-center gap-6">
            <p className="w-full h-4 max-w-[70%] bg-gray-200 rounded-md"></p>
            <p className="w-full h-4 max-w-[70%] bg-gray-200 rounded-md"></p>
          </div>
        </div>
        <div className="w-full h-40 p-4 space-y-4 border rounded-lg animate-pulse">
          <p className="w-full h-24 max-w-[70%] bg-gray-200 rounded-md"></p>
          <div className="flex justify-between items-center gap-6">
            <p className="w-full h-4 max-w-[70%] bg-gray-200 rounded-md"></p>
            <p className="w-full h-4 max-w-[70%] bg-gray-200 rounded-md"></p>
          </div>
        </div>
        <div className="w-full h-40 p-4 space-y-4 border rounded-lg animate-pulse">
          <p className="w-full h-24 max-w-[70%] bg-gray-200 rounded-md"></p>
          <div className="flex justify-between items-center gap-6">
            <p className="w-full h-4 max-w-[70%] bg-gray-200 rounded-md"></p>
            <p className="w-full h-4 max-w-[70%] bg-gray-200 rounded-md"></p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
      {uploadedAvatars?.length > 0 ? (
        uploadedAvatars.map((image, index) => (
          <Card key={image} className="p-4 shadow-md rounded-xl hover:shadow-lg transition-shadow duration-300">
            <div className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-800">{extractImageName(image)}</h3>

              <Link to={image} target="_blank" rel="noopener noreferrer">
                <img
                  src={image}
                  alt={extractImageName(image)}
                  className="w-full h-48 object-cover rounded-lg hover:opacity-90 transition-opacity duration-200"
                />
              </Link>

              <div className="flex justify-between items-center pt-2">
                <a href={image} download className="text-sm text-blue-600 font-medium hover:underline">
                  Download
                </a>
              </div>
            </div>
          </Card>
        ))
      ) : showEmpty ? (
        <div className="text-center py-12 col-span-full">
          <Icon icon="lsicon:picture-off-outline" className="text-5xl mx-auto mb-4 text-gray-400" />
          <p className="text-gray-500 text-lg"> No Avatars Requested for this Organization</p>
        </div>
      ) : null}
    </div>
  );
};
