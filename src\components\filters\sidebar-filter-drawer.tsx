// Core
import { Drawer, SidebarFilterPage } from '../';

export interface DrawerFilter {
  count?: number;
  drawerClearAll: () => void;
  isShowDrawerFilter?: boolean;
  setShowDrawerFilter: React.Dispatch<React.SetStateAction<boolean>>;
  isAnyFilterApplied?: () => boolean;
  filterCountNumber?: number;
  element?: string;
}

interface FilterData {
  filterFeedData: string[];
  setFilters: ({}) => void;
}

interface SidebarFilterDrawerProps {
  drawerFilter: DrawerFilter;
  filterData: FilterData;
}

export const SidebarFilterDrawer = ({ drawerFilter, filterData }: SidebarFilterDrawerProps) => {
  // Methods
  const onCloseDrawerFilter = (): void => drawerFilter.setShowDrawerFilter((prev: boolean) => !prev);

  return (
    <Drawer className="w-screen max-w-[340px]" onClose={onCloseDrawerFilter}>
      <Drawer.SingleView className="!px-0 !py-1 !space-y-0">
        <Drawer.Body className="p-2 overflow-y-auto">
          <SidebarFilterPage filterData={filterData} searchInputField={{ value: '', update: () => {} }} />
        </Drawer.Body>
      </Drawer.SingleView>
    </Drawer>
  );
};
