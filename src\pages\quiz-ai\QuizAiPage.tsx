// React
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
// UI
import { Logs } from 'UI';

// Components
import {
  SubmissionAiLoading,
  SubmissionAiOnboarding,
  SubmissionAiStepper,
  SubmissionAiFinish,
  SubmissionAiLocked,
  SubmissionAiExpired,
  UnsupportedBrowser,
} from '../..';
import { useEventListener } from 'UI/src';
import { useAppDispatch, useAppSelector } from 'UI/src/store';
import { submissionAiState, setErrorNotify } from 'UI/src/slices';
import { fetchSubmissionAi, logInterviewEvent } from 'UI/src/middlewares';

export const QuizAiPage = () => {
  // Hooks
  const { id } = useParams();
  const dispatch = useAppDispatch();

  const { submissionAi, loading, aiIsTyping, isSpeaking } = useAppSelector(submissionAiState);

  // Computed
  const isSubmissionLoggable =
    submissionAi && submissionAi.interview?.startedAt && !submissionAi?.interview?.submittedAt && !submissionAi?.interview?.locked;

  // Detect Device User Agent
  const [isMobile, setIsMobile] = useState<boolean>(false);
  const [isFirefox, setIsFirefox] = useState(false);
  const userAgent = navigator.userAgent.toLowerCase();
  const detectDevice = () => {
    setIsMobile(userAgent.match(/mobile|android|iphone|ipad|ipod|tablet/i) ? true : false);
    setIsFirefox(userAgent.indexOf('firefox') > -1);
  };

  // Fetch Data
  useEffect(() => {
    if (id) {
      dispatch(fetchSubmissionAi(id))
        .unwrap()
        .catch((error) => {
          dispatch(setErrorNotify(error));
        });
    }
    detectDevice();
  }, [id]);

  // Handle listeners
  useEventListener('visibilitychange', async () => {
    if (isSubmissionLoggable && document.visibilityState !== 'visible') {
      const payload = {
        applicantId: submissionAi?.interview?.applicantId,
        interviewId: submissionAi?.interview?._id,
        type: Logs.WindowSwitched,
        date: {},
        logType: 'interview',
      };

      dispatch(logInterviewEvent(payload))
        .unwrap()
        .catch((error) => {
          console.error('Failed to log event:', error);
        });
    }
  });
  useEventListener('keydown', async ({ keyCode }: any) => {
    if (isSubmissionLoggable) {
      const payload = {
        applicantId: submissionAi?.interview?.applicantId,
        interviewId: submissionAi?.interview?._id,
        type: Logs.KeyboardKeyDown,
        data: { keyCode },
        logType: 'interview',
      };

      dispatch(logInterviewEvent(payload))
        .unwrap()
        .catch((error) => {
          console.error('Failed to log event:', error);
        });
    }
  });

  // listen for PrtSc key, as it only fires keyup event
  // This code works only in Windows!
  useEventListener('keyup', async ({ keyCode }: any) => {
    if (isSubmissionLoggable && keyCode === 44) {
      const payload = {
        applicantId: submissionAi?.interview?.applicantId,
        interviewId: submissionAi?.interview?._id,
        type: Logs.KeyboardKeyDown,
        data: { keyCode },
        logType: 'interview',
      };

      dispatch(logInterviewEvent(payload))
        .unwrap()
        .catch((error) => {
          console.error('Failed to log event:', error);
        });
    }
  });
  useEventListener('contextmenu', async () => {
    if (isSubmissionLoggable) {
      const payload = {
        applicantId: submissionAi?.interview?.applicantId,
        interviewId: submissionAi?.interview?._id,
        type: Logs.ContextMenu,
        logType: 'interview',
        data: {},
      };

      dispatch(logInterviewEvent(payload))
        .unwrap()
        .catch((error) => {
          console.error('Failed to log event:', error);
        });
    }
  });
  // Listen for refresh
  useEventListener('beforeunload', async () => {
    if (isSubmissionLoggable) {
      alert('beforeunload');
      const payload = {
        applicantId: submissionAi?.interview?.applicantId,
        interviewId: submissionAi?.interview?._id,
        type: Logs.WindowRefresh,
        data: {},
        logType: 'interview',
      };

      dispatch(logInterviewEvent(payload))
        .unwrap()
        .catch((error) => {
          console.error('Failed to log event:', error);
        });
    }
  });

  const aiHasFinished = () => !aiIsTyping && !isSpeaking;

  const render = () => {
    if (isFirefox) return <UnsupportedBrowser />;
    if (submissionAi?.interview?.expired) return <SubmissionAiExpired />;
    if (submissionAi?.interview?.submittedAt && aiHasFinished()) return <SubmissionAiFinish />;
    if (submissionAi?.interview?.locked) return <SubmissionAiLocked />;
    if (submissionAi?.interview?.startedAt) return <SubmissionAiStepper />;
    if (submissionAi?.applicant?.email || submissionAi?.interview?.randomId) return <SubmissionAiOnboarding />;
    return <SubmissionAiLoading />;
  };

  return <>{render()}</>;
};
