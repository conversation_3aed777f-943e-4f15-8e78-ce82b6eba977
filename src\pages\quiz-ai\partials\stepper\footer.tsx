// @ts-nocheck
// React
import { useRef, useState, useEffect, useCallback, memo } from 'react';
import { useParams } from 'react-router-dom';

// Core
import { Button } from 'src';
import { setAiLoading, useScreenSize } from 'UI/src';

// Components
import RecordingStateView from '../../components/recording-state';

import { uploadToS3 } from 'UI/src/services/uploadS3';
import Message from '../../components/message';
import { Api, AiAvatarModels, fetchSubmissionAi, RootState, setErrorNotify, useAppDispatch, useAppSelector } from 'UI/src';
import thePass1 from 'images/Thepass-1.svg';
// 1. Memoize the component to prevent unnecessary re-renders
export const StepperAiFooter = memo(
  ({
    recordingMode,
    setRecordingMode,
    textAnswer,
    setTextAnswer,
    isLoaded,
    isSpeaking,
    isFinished,
    isChatVisible,
    setIsChatVisible,
    loading,
    setLoading,
    result,
    start,
    setResult,
    isRecording,
    setIsRecording,
    recordingTime,
    setRecordingTime,
    isPaused,
    setIsPaused,
    sendManualReply,
    getAvailableSkips,
    currentQuestion,
    isListening,
    transcript,
    startListening,
    stopListening,
    pauseListening,
    resumeListening,
    clearTranscript,
    isConverting,
    onAnswerChanged,
  }: any) => {
    const submissionAi = useAppSelector((state: RootState) => state.submissionAi.submissionAi);
    const notify = useAppSelector((state: RootState) => state.notify);
    const { id } = useParams();
    const audioRef = useRef(null);
    const videoRef = useRef(null);
    const mediaRecorderRef = useRef(null);
    const animationFrameRef = useRef(null);
    const canvasRef = useRef(null);
    const dispatch = useAppDispatch();
    const screen = useScreenSize();

    // State
    const [isWriteAnswerVisible, setWriteAnswerVisibility] = useState(false);
    const [mediaRecorder, setMediaRecorder] = useState(null);
    const [wavesurfer, setWavesurfer] = useState(null);
    const [stream, setStream] = useState(null);
    const [audioURL, setAudioURL] = useState(null);
    const [isUploading, setIsUploading] = useState(false);
    const [recordedChunks, setRecordedChunks] = useState([]);
    const [isPlaying, setIsPlaying] = useState(false);
    const [audioChunks, setAudioChunks] = useState([]);
    const answerIndexRef = useRef(1);

    // Create a single stable logo image reference
    const logoImageRef = useRef(null);

    // Load the logo image once on component mount
    useEffect(() => {
      // Only load if not already loaded
      if (!logoImageRef.current) {
        const img = new Image();
        img.src = thePass1; // Use fixed path to logo
        img.onload = () => {
          logoImageRef.current = img;
        };
        img.onerror = () => {
          console.error('Failed to load logo image');
          logoImageRef.current = null;
        };
      }
    }, []); // Empty dependency array - only run once on mount

    useEffect(() => {
      const handleBeforeUnload = (event) => {
        // The most critical condition for preventing close: isUploading
        const hasUnsavedChanges = isUploading;

        if (hasUnsavedChanges) {
          event.preventDefault(); // Prevent default behavior (e.g., navigating away immediately)
          event.returnValue =
            "Your video is still uploading. Please don't close this tab until the upload is complete to avoid losing your recording."; // For Chrome and Firefox
          return "Your video is still uploading. Please don't close this tab until the upload is complete to avoid losing your recording."; // For older browsers
        }
      };

      window.addEventListener('beforeunload', handleBeforeUnload);

      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
      // Dependency: Only re-run this effect when isUploading changes.
    }, [isUploading]);

    // 2. Use useCallback for functions to prevent them from being recreated on each render
    const handleInputChange = useCallback(
      (e) => {
        setTextAnswer(e.target.value);
        if (onAnswerChanged) {
          onAnswerChanged(e.target.value);
        }
      },
      [setTextAnswer, onAnswerChanged]
    );

    const sendFunction = useCallback(async () => {
      try {
        dispatch(setAiLoading(true));
        const payload = {
          interviewId: id,
          userAnswerText: textAnswer,
        };
        const response = await Api.post('ai-interview/single/talk', payload);
        setResult(response.data);
        setTextAnswer('');
        id && (await dispatch(fetchSubmissionAi(id)).unwrap());
      } catch (error) {
        console.error('Error uploading audio:', error);
      } finally {
        dispatch(setAiLoading(false));
        setRecordingMode(false);
      }
    }, [id, textAnswer, setLoading, setResult, setTextAnswer, setRecordingMode]);

    const stopVideoRecording = () => {
      // Cancel the animation frame to stop drawing
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }

      // Stop the media recorder if it's recording
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
        setIsRecording(false);
        stopListening();
      }

      // Stop all tracks in the stream
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
        setStream(null);
      }
    };

    // 5. Use useRef for the canvas drawing function to prevent it from causing re-renders
    const drawCanvasRef = useRef((ctx) => {
      if (!ctx) return;

      // Clear the canvas
      const canvas = canvasRef.current;
      if (!canvas) return;

      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // First, draw the webcam feed as the background
      if (videoRef.current && stream) {
        try {
          // Draw the webcam video onto the canvas
          ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
        } catch (e) {
          console.error('Error drawing video to canvas:', e);
        }
      }

      // Draw the logo in the bottom left (changed from top left)
      const logoSize = 100;
      const logoX = 40;
      const logoY = canvas.height - logoSize - 40; // Changed to position at bottom left

      // Use the ref for the logo image
      if (logoImageRef.current) {
        ctx.drawImage(logoImageRef.current, logoX, logoY, logoSize, logoSize);
      } else {
        // Fall back to the Logo component
        renderLogoToCanvas(ctx, logoX, logoY, logoSize);
      }

      // Draw the question at the top middle (already positioned at top)
      const question = result?.transcript || currentQuestion;
      if (question) {
        // Set font properties
        ctx.font = 'bold 22px Arial';

        // Calculate text dimensions and positioning
        const maxWidth = canvas.width * 0.8; // 80% of canvas width
        const lineHeight = 30;
        const padding = 20;

        // Function to wrap text and return array of lines
        const wrapText = (text, maxWidth) => {
          const words = text.split(' ');
          const lines = [];
          let currentLine = words[0];

          for (let i = 1; i < words.length; i++) {
            const word = words[i];
            const width = ctx.measureText(currentLine + ' ' + word).width;

            if (width < maxWidth) {
              currentLine += ' ' + word;
            } else {
              lines.push(currentLine);
              currentLine = word;
            }
          }

          lines.push(currentLine);
          return lines;
        };

        // Wrap the text
        const lines = wrapText(question, maxWidth);

        // Calculate background dimensions
        const totalTextHeight = lines.length * lineHeight;
        const backgroundWidth = maxWidth + padding * 2;
        const backgroundHeight = totalTextHeight + padding * 2;
        const backgroundX = (canvas.width - backgroundWidth) / 2;
        const backgroundY = 20; // 20px from top

        // Draw background with rounded corners
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.beginPath();

        // Use roundRect if available, otherwise use a fallback
        if (ctx.roundRect) {
          ctx.roundRect(backgroundX, backgroundY, backgroundWidth, backgroundHeight, 10);
        } else {
          // Fallback for browsers that don't support roundRect
          const radius = 10;
          ctx.moveTo(backgroundX + radius, backgroundY);
          ctx.lineTo(backgroundX + backgroundWidth - radius, backgroundY);
          ctx.quadraticCurveTo(backgroundX + backgroundWidth, backgroundY, backgroundX + backgroundWidth, backgroundY + radius);
          ctx.lineTo(backgroundX + backgroundWidth, backgroundY + backgroundHeight - radius);
          ctx.quadraticCurveTo(
            backgroundX + backgroundWidth,
            backgroundY + backgroundHeight,
            backgroundX + backgroundWidth - radius,
            backgroundY + backgroundHeight
          );
          ctx.lineTo(backgroundX + radius, backgroundY + backgroundHeight);
          ctx.quadraticCurveTo(backgroundX, backgroundY + backgroundHeight, backgroundX, backgroundY + backgroundHeight - radius);
          ctx.lineTo(backgroundX, backgroundY + radius);
          ctx.quadraticCurveTo(backgroundX, backgroundY, backgroundX + radius, backgroundY);
        }

        ctx.fill();

        // Draw text
        ctx.fillStyle = 'white';
        ctx.textAlign = 'center';

        lines.forEach((line, index) => {
          const y = backgroundY + padding + index * lineHeight;
          ctx.fillText(line, canvas.width / 2, y + 20);
        });
      }
    });

    // 6. Update the startRecording function to use the drawCanvasRef
    const startRecording = useCallback(async () => {
      setRecordingMode(true);
      setIsRecording(true);

      try {
        // Request camera and microphone access
        const mediaStream = await navigator.mediaDevices.getUserMedia({
          audio: true,
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: 'user',
          },
        });

        setStream(mediaStream);

        // Create a new video element and set it up
        if (videoRef.current) {
          videoRef.current.srcObject = mediaStream;

          // Wait for video to be ready before proceeding
          await new Promise((resolve) => {
            videoRef.current.onloadedmetadata = () => {
              videoRef.current
                .play()
                .then(resolve)
                .catch((e) => {
                  console.error('Error playing video:', e);
                  resolve(); // Resolve anyway to continue
                });
            };
          });
        }

        // Set up canvas with correct dimensions
        const canvas = canvasRef.current;
        if (!canvas) {
          console.error('Canvas ref is null');
          return;
        }

        // Set canvas size to match video dimensions for better quality
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          console.error('Could not get canvas context');
          return;
        }

        // Create a stream from the canvas
        const canvasStream = canvas.captureStream(30); // 30 FPS

        // Add audio track from original stream to canvas stream
        mediaStream.getAudioTracks().forEach((track) => {
          canvasStream.addTrack(track);
        });

        // Create recorder from canvas stream
        const recorder = new MediaRecorder(canvasStream, {
          mimeType: 'video/webm;codecs=vp9,opus',
        });

        setMediaRecorder(recorder);
        mediaRecorderRef.current = recorder;
        setRecordedChunks([]);

        // Set up animation frame for drawing
        const animate = () => {
          if (videoRef.current && videoRef.current.readyState >= 2) {
            try {
              // Clear the canvas first
              ctx.clearRect(0, 0, canvas.width, canvas.height);

              // Draw video frame to canvas - this is the critical part
              ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);

              // Draw logo and other elements on top
              if (logoImageRef.current) {
                const logoSize = 100;
                ctx.drawImage(logoImageRef.current, 20, canvas.height - logoSize, logoSize, logoSize);
              }

              // Draw question text if available
              if (result?.transcript || currentQuestion) {
                drawQuestionText(ctx, result?.transcript || currentQuestion, canvas);
              }
            } catch (e) {
              console.error('Error drawing to canvas:', e);
            }
          } else {
            // If video isn't ready, draw a subtle waiting indicator
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Add text explaining the issue
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Initializing camera...', canvas.width / 2, canvas.height / 2);
          }

          // Continue animation loop
          animationFrameRef.current = requestAnimationFrame(animate);
        };

        // Start animation loop
        animationFrameRef.current = requestAnimationFrame(animate);

        // Set up recorder events
        recorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            setRecordedChunks((prev) => [...prev, event.data]);
          }
        };

        // Start recording
        recorder.start(100);
        setIsPaused(false);
        setRecordingTime(0);
        setAudioChunks([]);
        startListening();
      } catch (error) {
        console.error('Error starting video recording:', error);
        setIsRecording(false);
        setRecordingMode(false);
        dispatch(setErrorNotify('Failed to access camera. Please check your permissions.'));
      }
    }, [
      setRecordingMode,
      setIsRecording,
      setStream,
      setMediaRecorder,
      setRecordedChunks,
      setIsPaused,
      setRecordingTime,
      setAudioChunks,
      startListening,
      notify,
    ]);

    const toggleRecording = () => {
      startRecording();
      startStop();
      if (isRecording) {
        setIsRecording(false);
      } else {
        setIsRecording(true);
      }
    };

    const startStop = () => {
      if (isListening) {
        stopListening();
      } else {
        startListening();
      }
    };

    const deleteRecording = () => {
      setIsRecording(false);
      deletTranscript();
      setAudioChunks([]);
      setAudioURL(null);
      setRecordedChunks([]);
      setRecordingTime(0);
      stopListening();
      setRecordingMode(false);
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
        setStream(null);
      }
    };

    const handleRecordButtonEvent = () => {
      if (recordingMode) {
        sendRecording();
      } else if (textAnswer) {
        sendFunction();
      } else {
        startRecording();
      }
    };

    const pauseRecording = () => {
      if (mediaRecorderRef.current) {
        mediaRecorderRef.current.stop();
        setIsPaused(true);
        setIsRecording(false);
        pauseListening();

        // Create audio URL from recorded chunks for playback during pause
        if (recordedChunks.length > 0) {
          const blob = new Blob(recordedChunks, { type: 'audio/webm' });
          const url = URL.createObjectURL(blob);
          setAudioURL(url);
        }
      }
    };

    const resumeRecording = async () => {
      resumeListening();
      if (stream) {
        const newMediaRecorder = new MediaRecorder(stream);
        setMediaRecorder(newMediaRecorder);
        mediaRecorderRef.current = newMediaRecorder;

        newMediaRecorder.ondataavailable = (e) => {
          if (e.data.size > 0) {
            setAudioChunks((prev) => [...prev, e.data]);
            const blob = new Blob([...audioChunks, e.data], { type: 'audio/wav' });
            const url = URL.createObjectURL(blob);
            setAudioURL(url);
          }
        };

        newMediaRecorder.start();
        setIsRecording(true);
        setIsPaused(false);
      } else {
        await startRecording();
      }
    };

    const sendRecording = async () => {
      try {
        dispatch(setAiLoading(true));
        setIsUploading(true); // Keep this to indicate a background process is starting
        stopVideoRecording();

        // Make sure we have recorded chunks before creating a blob
        if (recordedChunks.length === 0) {
          throw new Error('No video data recorded');
        }

        // --- IMMEDIATE API CALL (No s3Key needed here initially) ---
        const initialPayload = {
          interviewId: id,
          userAnswerText: transcript,
        };
        // Send the text answer immediately
        const response = await Api.post('ai-interview/single/talk', initialPayload);
        setResult(response.data); // User sees the result much faster

        deleteRecording(); // Clean up local recording data
        id && (await dispatch(fetchSubmissionAi(id)).unwrap());

        if (onAnswerChanged) onAnswerChanged(transcript);
        answerIndexRef.current += 1; // Increment for the next answer

        setLoading(false); // User is no longer "loading" or blocked
        setRecordingMode(false); // User can proceed with the interview

        // --- BACKGROUND VIDEO UPLOAD (Fire-and-Forget) ---
        // Only upload video if recordInterview is true
        if (submissionAi?.interview?.recordInterview) {
          const videoBlob = new Blob(recordedChunks, { type: 'video/webm' });

          // Use an IIFE for async background upload.
          // We don't await this, so it runs in parallel.
          (async () => {
            try {
              // Pass the answerIndexRef.current *before* it was incremented for this answer.
              // Or better, capture it in a variable if you need it to correlate logs.
              // For a fire-and-forget, the exact index might not even matter much if the backend doesn't use the S3 key.
              const currentAnswerIndex = answerIndexRef.current - 1;
              const s3Key = await uploadToS3(videoBlob, id, currentAnswerIndex, submissionAi?.interview?.applicantId);
            } catch (uploadError) {
              console.error('Error uploading video in background:', uploadError);
              // Log this error to a monitoring service.
              // No need to notify the user if the video isn't critical for immediate backend processing.
            } finally {
              // This setIsUploading(false) will happen when the background upload finishes.
              // It could be used to clear a *subtle* background indicator.
              setIsUploading(false);
            }
          })();
        } else {
          // If recordInterview is false, no upload was initiated, so set to false immediately.
          setIsUploading(false);
        }
      } catch (error) {
        console.error('Error sending recording:', error);
        dispatch(setErrorNotify(`Error sending recording: ${error.message || 'Unknown error'}`));
        dispatch(setAiLoading(false));
        setRecordingMode(false); // Ensure recording mode is off on error
        setIsUploading(false); // Ensure uploading state is reset
      }
    };

    const deletTranscript = () => {
      clearTranscript();
      setRecordingMode(false);
      if (onAnswerChanged) {
        onAnswerChanged('');
      }
    };

    const renderLogoToCanvas = (ctx, x, y, size) => {
      // Create a temporary div to render the Logo component
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.top = '-9999px';
      document.body.appendChild(tempDiv);

      // Render the Logo component to the div
      const logoElement = document.createElement('img');
      logoElement.src = thePass1; // Use the same path as in the Logo component
      logoElement.className = 'h-16 w-16';
      tempDiv.appendChild(logoElement);

      // Wait for the image to load
      logoElement.onload = () => {
        // Draw the logo on the canvas
        ctx.drawImage(logoElement, x, y, size, size);

        // Clean up
        document.body.removeChild(tempDiv);
      };
    };

    // 7. Clean up animation frame on component unmount
    useEffect(() => {
      return () => {
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }
      };
    }, []);

    // Define drawFunction using the drawCanvasRef
    const drawFunction = useCallback((ctx) => {
      if (drawCanvasRef.current) {
        drawCanvasRef.current(ctx);
      }
    }, []);

    // Helper function to draw question text
    const drawQuestionText = (ctx, question, canvas) => {
      // Set font properties
      ctx.font = 'bold 22px Arial';

      // Calculate text dimensions and positioning
      const maxWidth = canvas.width * 0.8; // 80% of canvas width
      const lineHeight = 30;
      const padding = 20;

      // Function to wrap text and return array of lines
      const wrapText = (text, maxWidth) => {
        const words = text.split(' ');
        const lines = [];
        let currentLine = words[0] || '';

        for (let i = 1; i < words.length; i++) {
          const word = words[i];
          const width = ctx.measureText(currentLine + ' ' + word).width;

          if (width < maxWidth) {
            currentLine += ' ' + word;
          } else {
            lines.push(currentLine);
            currentLine = word;
          }
        }

        lines.push(currentLine);
        return lines;
      };

      // Wrap the text
      const lines = wrapText(question, maxWidth);

      // Calculate background dimensions
      const totalTextHeight = lines.length * lineHeight;
      const backgroundWidth = maxWidth + padding * 2;
      const backgroundHeight = totalTextHeight + padding * 2;
      const backgroundX = (canvas.width - backgroundWidth) / 2;
      const backgroundY = 20; // Changed from bottom to top (20px from top)

      // Draw background with rounded corners
      ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
      ctx.beginPath();

      // Use roundRect if available, otherwise use a fallback
      if (ctx.roundRect) {
        ctx.roundRect(backgroundX, backgroundY, backgroundWidth, backgroundHeight, 10);
      } else {
        // Fallback for browsers that don't support roundRect
        const radius = 10;
        ctx.moveTo(backgroundX + radius, backgroundY);
        ctx.lineTo(backgroundX + backgroundWidth - radius, backgroundY);
        ctx.quadraticCurveTo(backgroundX + backgroundWidth, backgroundY, backgroundX + backgroundWidth, backgroundY + radius);
        ctx.lineTo(backgroundX + backgroundWidth, backgroundY + backgroundHeight - radius);
        ctx.quadraticCurveTo(
          backgroundX + backgroundWidth,
          backgroundY + backgroundHeight,
          backgroundX + backgroundWidth - radius,
          backgroundY + backgroundHeight
        );
        ctx.lineTo(backgroundX + radius, backgroundY + backgroundHeight);
        ctx.quadraticCurveTo(backgroundX, backgroundY + backgroundHeight, backgroundX, backgroundY + backgroundHeight - radius);
        ctx.lineTo(backgroundX, backgroundY + radius);
        ctx.quadraticCurveTo(backgroundX, backgroundY, backgroundX + radius, backgroundY);
      }

      ctx.fill();

      // Draw text
      ctx.fillStyle = 'white';
      ctx.textAlign = 'center';

      lines.forEach((line, index) => {
        const y = backgroundY + padding + index * lineHeight;
        ctx.fillText(line, canvas.width / 2, y + 20);
      });
    };

    const chat = result?.chat || start?.chat || [];
    const selectedAiModel = AiAvatarModels?.find((value) => value?.value === submissionAi?.interview?.avatarName);

    return (
      <>
        <div className="flex flex-col gap-4 justify-between w-full lg:w-[60%] h-[calc((100vh-100px)/2)] p-4 px-4 mx-auto mb-4 border border-[#DEE2E4] rounded-xl">
          <div className={`${isWriteAnswerVisible ? 'h-[50%]' : 'h-[80%]'} flex flex-col gap-2`}>
            <div className="flex justify-between items-center">
              <p className="text-base font-semibold capitalize">{selectedAiModel?.value}:</p>
              {/* {submissionAi?.interview?.templateId && ( */}
              <div>
                <div className="flex gap-2">
                  {Array.from({ length: submissionAi?.interview?.numOfQuestions }).map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-1.5 rounded-2xl ${submissionAi?.interview?.currentIndex >= index ? 'bg-[#7E3AF2]' : 'bg-[#9B89F585]'}`}
                    />
                  ))}
                </div>
              </div>
              {/* )} */}
              <p className="font-semibold text-[#1B1F3B]">
                {submissionAi?.interview?.currentIndex + 1}/{submissionAi?.interview?.numOfQuestions}
              </p>
            </div>

            <div className="overflow-auto">
              {chat.length > 0 && (
                <Message
                  key={`footer-message-${chat.length}-${chat[chat.length - 1]?.text?.substring(0, 10)}`}
                  message={chat[chat.length - 1]}
                  isLastMessage={true}
                  loading={loading}
                  hideIcon
                />
              )}
            </div>
          </div>

          <div className={`${isWriteAnswerVisible ? 'h-[50%]' : 'h-[20%]'} flex flex-col justify-end gap-4`}>
            <hr className="border-[#DEE2E4]" />

            {/* Add Recording State View in LARGE screens */}
            {screen.gt.sm() && (isRecording || audioURL) && (
              <RecordingStateView
                stream={stream}
                loading={loading}
                isConverting={isConverting}
                isRecording={isRecording}
                isPaused={isPaused}
                isPlaying={isPlaying}
                deleteRecording={deleteRecording}
                pauseRecording={pauseRecording}
                resumeRecording={resumeRecording}
                audioRef={audioRef}
                audioURL={audioURL}
                recordingTime={recordingTime}
                setIsPlaying={setIsPlaying}
                onPlayPause={() => {
                  if (wavesurfer) {
                    wavesurfer.playPause();
                  }
                }}
                onReady={(ws) => {
                  setWavesurfer(ws);
                  setIsPlaying(false);
                }}
              />
            )}

            {isWriteAnswerVisible && (
              <div className="space-y-3">
                <p className="font-medium text-sm text-nowrap mt-1.5">{submissionAi?.applicant?.name}:</p>
                <textarea
                  value={textAnswer}
                  onChange={handleInputChange}
                  className="w-full border rounded-lg border-[#DEE2E4] text-[#86869D] bg-transparent text-sm focus:outline-none focus:ring-0 p-2"
                  placeholder="Type..."
                  rows={4}
                />
              </div>
            )}

            <div className="flex justify-between items-center gap-2">
              {/* Chat History Button */}
              <Button
                label={screen.gt.sm() && 'Chat History'}
                icon="material-symbols:chat-bubble-outline-rounded"
                size="sm"
                tertiary
                onClick={() => setIsChatVisible((prev) => !prev)}
              />

              {/* Add Recording State View in SMALL screens */}
              {!screen.gt.sm() && (isRecording || audioURL) && (
                <RecordingStateView
                  stream={stream}
                  loading={loading}
                  isConverting={isConverting}
                  isRecording={isRecording}
                  isPaused={isPaused}
                  isPlaying={isPlaying}
                  deleteRecording={deleteRecording}
                  pauseRecording={pauseRecording}
                  resumeRecording={resumeRecording}
                  audioRef={audioRef}
                  audioURL={audioURL}
                  recordingTime={recordingTime}
                  setIsPlaying={setIsPlaying}
                  onPlayPause={() => {
                    if (wavesurfer) {
                      wavesurfer.playPause();
                    }
                  }}
                  onReady={(ws) => {
                    setWavesurfer(ws);
                    setIsPlaying(false);
                  }}
                />
              )}

              {/* Skip Button */}
              <div className="flex justify-center items-center gap-2">
                {!(isWriteAnswerVisible || isRecording || audioURL) && (
                  <Button
                    size="sm"
                    label={
                      <span>
                        Skip {submissionAi?.interview?.availableSkips}/{submissionAi?.interview?.allowedSkips}
                      </span>
                    }
                    outline
                    onClick={() => sendManualReply('skip')}
                    disabled={
                      isSpeaking ||
                      isFinished ||
                      !isLoaded.current ||
                      loading ||
                      recordingMode ||
                      isConverting ||
                      getAvailableSkips()?.availableSkips === 0 ||
                      getAvailableSkips()?.isIntroduceYourself ||
                      submissionAi?.interview?.availableSkips === submissionAi?.interview?.allowedSkips
                    }
                  />
                )}

                {/* Microphone Button */}
                {!isWriteAnswerVisible && (
                  <Button
                    size="sm"
                    icon={!isRecording && !audioURL && 'lucide:mic'}
                    label={(isRecording || audioURL) && 'Submit'}
                    disabled={isSpeaking || isFinished || !isLoaded?.current || loading || isConverting || result?.processStatus === 'Finished'}
                    onClick={handleRecordButtonEvent}
                  />
                )}

                {/* Write Answer Button */}
                {!(isRecording || audioURL) && (
                  <Button
                    disabled={loading || isSpeaking}
                    size="sm"
                    icon={!textAnswer && 'solar:keyboard-outline'}
                    label={
                      textAnswer ? (
                        screen.gt.sm() ? (
                          'Submit'
                        ) : (
                          <Icon icon="material-symbols:send-rounded" width={22} />
                        )
                      ) : isWriteAnswerVisible ? (
                        'Hide Answer'
                      ) : (
                        'Write Answer'
                      )
                    }
                    onClick={() => {
                      if (!!textAnswer) {
                        handleRecordButtonEvent();
                      } else {
                        setWriteAnswerVisibility((prev) => !prev);
                      }
                    }}
                  />
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Video element for camera feed - hidden but accessible */}
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className="hidden"
          style={{
            position: 'absolute',
            left: '-9999px',
            top: '-9999px',
            width: '640px',
            height: '480px',
          }}
        />

        {/* Canvas for recording with overlays */}
        <canvas
          ref={canvasRef}
          className="absolute top-0 left-0 z-50 w-full h-full"
          style={{
            opacity: 0, // Make it invisible
            pointerEvents: 'none',
            display: isRecording ? 'block' : 'none', // Only create when recording
          }}
        />
      </>
    );
  }
);

// Create a separate component for the canvas
const RecordingCanvas = memo(({ isRecording, canvasRef, drawFunction }) => {
  // Set up the animation frame
  useEffect(() => {
    if (isRecording && canvasRef.current) {
      const ctx = canvasRef.current.getContext('2d');
      if (!ctx) return;

      const animate = () => {
        drawFunction(ctx);
        return requestAnimationFrame(animate);
      };

      const animationId = requestAnimationFrame(animate);

      return () => {
        cancelAnimationFrame(animationId);
      };
    }
  }, [isRecording, canvasRef, drawFunction]);

  return (
    <>
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0 z-50 w-full h-full"
        style={{
          opacity: 0, // Make it invisible
          pointerEvents: 'none',
          display: isRecording ? 'block' : 'none', // Only create when recording
        }}
      />

      {isUploading && (
        <div
          style={{
            marginTop: '20px',
            padding: '10px',
            backgroundColor: '#fff3cd', // Light yellow background
            color: '#856404', // Dark yellow text
            border: '1px solid #ffeeba',
            borderRadius: '5px',
            display: 'flex',
            alignItems: 'center',
            gap: '10px',
            width: 'fit-content', // Adjust width to content
            margin: '20px auto 0 auto', // Center horizontally below other content
          }}
        >
          <span style={{ fontSize: '1.5em' }} role="img" aria-label="uploading-icon">
            ⬆️
          </span>
          <p style={{ margin: 0, fontWeight: 'bold' }}>
            **Uploading your video in the background...**
            <br />
            Please **don't close this tab** until the upload is complete!
          </p>
        </div>
      )}
    </>
  );
});
