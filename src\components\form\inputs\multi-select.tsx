import { forwardRef, useEffect, useRef, useState } from 'react';

import { Label, TextInput as Input, ListGroup, Badge, Tooltip, HelperText, ListGroupItem } from 'flowbite-react';
import ClickAwayListener from 'react-click-away-listener';
import { asField } from '../hocs/field';

import { Icon, Button, TextInput } from 'src';
import { useAppDispatch, useScreenSize, useLookups, Api } from 'UI/src';
import { setNotifyMessage, setErrorNotify } from 'UI';

type MultiSelectProps = {
  name: string;
  label?: string;
  subLabel?: string;
  placeholder?: string;
  options?: any[];
  value?: any[];
  disabled?: boolean;
  disabledMessage?: string;
  onChange?: (value: any[]) => void;
  onSearch?: (keyword: string) => void;
  filterOnly?: boolean;
  optionLabelKey?: string;
  optionValueKey?: string;
  minSelection?: number;
  maxSelection?: number;
  breakDown?: any;
  lookup?: string;
  params?: any;
  cached?: boolean;
  validators: any[];
  validatorsScroll?: boolean;
  errorMessage?: string;
  requiredLabel?: boolean;
  showSingleClear?: boolean;
  handleSingleClear?: () => void;
  readOnly?: boolean;
  customSize?: string;
  customWeight?: string;
  creationOptions?: any;
  dropIcon?: boolean;
};

// TODO: Markos
export const MultiSelect = forwardRef<HTMLDivElement, MultiSelectProps>(
  (
    {
      name,
      label = '',
      subLabel = '',
      placeholder = '',
      options,
      value,
      disabled,
      disabledMessage,
      onChange,
      onSearch,
      filterOnly,
      optionLabelKey,
      optionValueKey,
      minSelection,
      maxSelection,
      breakDown,
      lookup,
      params,
      cached = false,
      validatorsScroll,
      errorMessage,
      requiredLabel,
      showSingleClear,
      handleSingleClear,
      readOnly,
      customSize = 'sm',
      customWeight = 'medium',
      creationOptions,
      // @TODO: Confirmation warning message
      // confirmOptions = false,
    },
    ref
  ) => {
    // State
    const [keyword, setKeyword] = useState('');
    const [listVisibility, setListVisibility] = useState(false);

    const [isCreating, setIsCreating] = useState(false);
    const [customValue, setCustomValue] = useState('');
    const [errorCustomMessage, setErrorCustomMessage] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    // Hooks
    const { lookups, loading, refresh } = useLookups(lookup, { cached, params });
    const screen = useScreenSize();

    // Reference
    const inputRef = useRef(null);

    // dispatch
    const dispatch = useAppDispatch();

    const customTheme = {
      field: {
        input: {
          colors: {
            gray: `${
              readOnly ? 'cursor-not-allowed dark:bg-gray-800 text-gray-500' : 'dark:bg-gray-700'
            } block w-full border disabled:cursor-not-allowed   disabled:opacity-50 bg-gray-50 border-gray-300 text-gray-900 dark:border-gray-600 dark:text-white dark:placeholder-gray-400 p-2.5 text-sm rounded-lg focus:ring-0 focus:border-gray-300`,
            failure:
              'border-red-500 bg-white-500 text-gray-900 dark:text-white placeholder-gray-400 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-[#374151] dark:focus:border-red-500 dark:focus:ring-red-500',
          },
        },
      },
    };

    const tooltipCustomTheme = {
      target: 'w-auto',
    };

    const filteredLookup = () => {
      if (onSearch) {
        return lookups;
      }
      return (
        lookups
          // Remove selected values
          .filter((option: any) => !(Array.isArray(value) ? value : []).includes(option[optionValueKey as string]))
          // Search the results
          .filter((option: any) => option[optionLabelKey as string]?.toLowerCase().includes(keyword?.toLowerCase()))
      );
    };

    const getOptionLabel = (id: string) => {
      const foundOption = lookups.find((option: any) => option[optionValueKey as string] == id);
      return foundOption?.name || (foundOption as any)?.[optionLabelKey as string] || '';
    };

    // Actions
    const updateInput = (updatedValue = value) => {
      setKeyword('');
    };
    const handleSelect = (selectedValue: any) => () => {
      const selectValue = Array.isArray(value) ? value : [];
      const nextValue = [...selectValue, selectedValue];
      if (nextValue.length >= (minSelection || 0) && nextValue.length <= (maxSelection || Infinity)) {
        onChange?.(nextValue);
        setListVisibility(false);

        if (!filterOnly) {
          updateInput(nextValue);
        } else {
          updateInput([]);
        }
      }
    };

    const handleSubmit = async (e?: any) => {
      e?.stopPropagation();
      try {
        setIsLoading(true);
        if (!customValue) {
          setErrorCustomMessage('This field is required');
          return;
        }
        const response = await Api.post(creationOptions.url, { ...params, [creationOptions?.fieldName]: customValue });
        if (response) {
          await refresh();
          dispatch(setNotifyMessage(`${label || 'Option'} created successfully`));
          setCustomValue('');
          setKeyword('');
          setIsCreating(false);
        }
      } catch (error: any) {
        dispatch(setErrorNotify(error.response?.data?.message || 'Failed to create option'));
      } finally {
        setIsLoading(false);
      }
    };

    const handleReset = () => {
      // TODO: Markos
      onChange?.([]);
      setKeyword('');
      setListVisibility(true);

      if (onSearch) {
        onSearch('');
      }
    };
    const handleClickAway = () => {
      setListVisibility(false);
      updateInput();
    };

    const handleRemoveItem = (id: string) => {
      // // @TODO: Confirmation warning message
      // const element = () => {
      // TODO: Markos
      const nextUpdate = (value || []).filter((item) => item !== id);
      onChange?.(nextUpdate);
      // };

      // // @TODO: Confirmation warning message
      // if (confirmOptions?.isConfirmationDialog) {
      //   showConfirm(confirmOptions.confirmTextNotChangeInputs(), {
      //     onConfirm() {
      //       element();
      //       hideConfirm();
      //       confirmOptions.doAfterConfirmation();
      //     },
      //   });
      // } else {
      //   element();
      // }
    };

    const onFocus = () => {
      // // @TODO: Confirmation warning message
      // const element = () => {
      setListVisibility(true);
      // };

      // // @TODO: Confirmation warning message
      // if (confirmOptions?.isConfirmationDialog) {
      //   inputRef.current.blur();
      //   showConfirm(confirmOptions.confirmTextNotChangeInputs(), {
      //     onConfirm() {
      //       element();
      //       hideConfirm();
      //       confirmOptions.doAfterConfirmation();
      //     },
      //   });
      // } else {
      //   element();
      // }
    };

    // Lifecycle
    useEffect(() => {
      if (value) {
        updateInput();
      }
    }, [value]);

    const element = (
      <>
        <Input
          ref={inputRef}
          theme={customTheme}
          className="overflow-x-auto w-full"
          id={name}
          placeholder={placeholder}
          value={keyword}
          disabled={disabled}
          autoComplete="off"
          onInput={(e: React.ChangeEvent<HTMLInputElement>) => !readOnly && setKeyword(e.target.value)}
          onFocus={onFocus}
          color={errorMessage ? 'failure' : 'gray'}
          readOnly={readOnly}
        />
        {/* TODO: Add when apply the validation */}
        {/* <HelperText>{errorMessage || undefined}</HelperText> */}
      </>
    );

    return (
      <div className="relative flex flex-col gap-2">
        <div className="block">
          <Label htmlFor={name}>
            <div className="flex w-full justify-between">
              <div>
                <span className={`text-${customSize} font-${customWeight} thepassBtwo`}>{label}</span>{' '}
                <span className="text-inputSubLabel dark:text-inputLabel">{subLabel}</span>
                {requiredLabel && <span className="text-red-600 dark:text-red-800"> *</span>}
              </div>
              {showSingleClear && (
                <p className="text-[#9061F9] cursor-pointer text-sm select-none" onClick={handleSingleClear}>
                  Clear
                </p>
              )}
            </div>
          </Label>
        </div>
        <ClickAwayListener onClickAway={handleClickAway}>
          {disabled && disabledMessage ? (
            <Tooltip content={disabledMessage} arrow={false} placement="bottom" theme={tooltipCustomTheme}>
              {element}
            </Tooltip>
          ) : (
            <div>
              <div className="relative">{element}</div>

              {!readOnly && listVisibility && !loading && (
                <ListGroup className="absolute left-0 right-0 z-10 max-h-[222px] overflow-x-hidden overflow-y-scroll">
                  {filteredLookup()?.length > 0 ? (
                    filteredLookup().map((option: any) => (
                      <ListGroupItem
                        key={(option as any)[optionValueKey || 'value']}
                        onClick={handleSelect((option as any)[optionValueKey || 'value'])}
                      >
                        <p className="truncate">{(option as any)[optionLabelKey || 'label']}</p>
                      </ListGroupItem>
                    ))
                  ) : (
                    <div className="p-2 px-4 border-b border-b-white/10">
                      <p className="py-1 cursor-default">No results found...</p>
                    </div>
                  )}
                  {/* Add the new UI components here */}
                  {creationOptions && !isCreating && filteredLookup().length < 4 && (
                    <div
                      className="text-white py-2 m-2 rounded-md text-center cursor-pointer bg-[#702ede] hover:opacity-90"
                      onClick={(e) => {
                        e.preventDefault(); // Prevent default behavior
                        e.stopPropagation(); // Stop the event from bubbling up
                        setIsCreating(true);
                      }}
                    >
                      Create new +
                    </div>
                  )}

                  {creationOptions && isCreating && (
                    <ListGroupItem className="my-1 [&_div]:w-full ">
                      <div className="text-start">
                        <div className="flex gap-2">
                          <TextInput
                            className="text-white"
                            name="name"
                            placeholder={`Enter new ${label.toLowerCase() || 'option'}`}
                            value={customValue}
                            onChange={(event: string) => {
                              setCustomValue(event);
                              if (event === '' || creationOptions?.validation?.test(event)) {
                                setErrorCustomMessage('');
                              } else {
                                setErrorCustomMessage('Please enter a valid text');
                              }
                            }}
                          />

                          <Button
                            label="Create"
                            disabled={!customValue.trim() || !!errorCustomMessage}
                            loading={isLoading}
                            gradientMonochrome="purple"
                            onClick={handleSubmit}
                          />
                        </div>
                        {errorCustomMessage && <p className="mt-2 ms-1 text-sm text-red-600 dark:text-red-500">{errorCustomMessage}</p>}
                      </div>
                    </ListGroupItem>
                  )}
                </ListGroup>
              )}
            </div>
          )}
        </ClickAwayListener>
        {value && value?.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {Array.isArray(value) &&
              value?.map((id, index) => (
                <div
                  key={index}
                  className={`flex rounded-xl justify-between border border-gray-200 dark:border-gray-600 min-w-fit px-4 py-2 text-sm ${
                    readOnly ? 'text-gray-500 cursor-not-allowed' : 'text-black  dark:text-white'
                  } `}
                >
                  <p className="line-clamp-1">{getOptionLabel(id)}</p>

                  {!readOnly && !disabled && (
                    <div className="flex" onClick={() => handleRemoveItem(id)}>
                      <Icon icon="material-symbols-light:close" width="20" className="text-[#667085] dark:text-gray-200 cursor-pointer pl-2" />
                    </div>
                  )}
                </div>
              ))}
          </div>
        )}
      </div>
    );
  }
);
