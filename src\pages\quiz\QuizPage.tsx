import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { fetchSubmission, Logs, logSubmissionEvent, setIsMobile, useAppDispatch, useAppSelector, useEventListener } from 'UI/src';
import { setErrorNotify } from 'UI';

// Components
import { SubmissionLoading } from './partials/loading';
import { SubmissionOnboarding } from './partials/onboarding';
import { SubmissionStepper } from './partials/stepper';
import { SubmissionFinish } from './partials/finish';
import { SubmissionLocked } from './partials/locked';
import { SubmissionExpired } from './partials/expired';
import { MobileDevice } from './partials/mobileDevice';

export const QuizPage = () => {
  const { id } = useParams();
  const dispatch = useAppDispatch();

  const { submission, isMobile } = useAppSelector((state) => state.submission);

  const isSubmissionLoggable = submission && submission.startedAt && !submission.submittedAt && !submission.locked;

  // 🟢 Detect Device
  useEffect(() => {
    const userAgent = navigator.userAgent.toLowerCase();
    const mobile = /mobile|android|iphone|ipad|ipod|tablet/i.test(userAgent);
    dispatch(setIsMobile(mobile));
  }, []);

  // 🟢 Fetch submission
  useEffect(() => {
    if (id) {
      dispatch(fetchSubmission(id))
        .unwrap()
        .catch((error) => {
          if (error.includes('submission-locked')) {
            // Lock fallback
            dispatch(setErrorNotify('This submission is locked'));
          } else {
            dispatch(setErrorNotify(error));
          }
        });
    }
  }, [id, dispatch]);

  // 🟢 Handle listeners
  useEventListener('visibilitychange', () => {
    if (isSubmissionLoggable && document.visibilityState !== 'visible') {
      const payload = {
        applicantId: submission.applicantId,
        submissionId: submission._id,
        type: Logs.WindowSwitched,
        date: {},
        stageId: submission.stage?._id,
      };
      dispatch(logSubmissionEvent(payload));
    }
  });

  useEventListener('keydown', ({ keyCode }: any) => {
    if (isSubmissionLoggable) {
      const payload = {
        applicantId: submission.applicantId,
        submissionId: submission._id,
        type: Logs.KeyboardKeyDown,
        date: { keyCode },
        stageId: submission.stage?._id,
      };
      dispatch(logSubmissionEvent(payload));
    }
  });
  // listen for PrtSc key, as it only fires keyup event
  // This code works only in Windows!
  useEventListener('keyup', async ({ keyCode }: any) => {
    if (isSubmissionLoggable && keyCode === 44) {
      const payload = {
        applicantId: submission.applicantId,
        submissionId: submission._id,
        type: Logs.KeyboardKeyDown,
        date: { keyCode },
        stageId: submission.stage?._id,
      };
      dispatch(logSubmissionEvent(payload));
    }
  });
  useEventListener('contextmenu', async () => {
    if (isSubmissionLoggable) {
      const payload = {
        applicantId: submission.applicantId,
        submissionId: submission._id,
        type: Logs.ContextMenu,
        date: {},
        stageId: submission.stage?._id,
      };
      dispatch(logSubmissionEvent(payload));
    }
  });

  useEventListener('beforeunload', () => {
    if (isSubmissionLoggable) {
      const payload = {
        applicantId: submission.applicantId,
        submissionId: submission._id,
        type: Logs.WindowRefresh,
        date: {},
        stageId: submission.stage?._id,
      };
      dispatch(logSubmissionEvent(payload));
    }
  });

  const render = () => {
    if (isMobile) return <MobileDevice />;
    if (submission?.expired) return <SubmissionExpired />;
    if (submission?.submittedAt) return <SubmissionFinish isPhoneScreening={String(submission?.quiz?.phoneScreening)} />;
    if (submission?.locked) return <SubmissionLocked />;
    if (submission?.startedAt) return <SubmissionStepper />;
    if (submission?.randomId || submission?.applicant) return <SubmissionOnboarding />;
    return <SubmissionLoading />;
  };

  return <>{render()}</>;
};
