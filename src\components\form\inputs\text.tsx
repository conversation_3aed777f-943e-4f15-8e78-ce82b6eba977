import React from 'react';

import { Label, TextInput as Input, Tooltip } from 'flowbite-react';
import { Icon } from 'src';
import { StaticData } from 'UI/src/';
import { asField } from '../hocs/field';

type TextInputType = {
  name: string;
  requiredLabel?: boolean;
  label?: string;
  subLabel?: string;
  onChange: any;
  validatorsScroll?: boolean;
  errorMessage?: string;
  autoComplete?: string;
  labelTooltip?: string;
  placeholder?: string | React.ReactNode; // Allow JSX for placeholder
  customPlaceholder?: React.ReactNode; // Alternative JSX placeholder
};

export const TextInput = asField(
  ({
    name,
    requiredLabel,
    label,
    subLabel,
    onChange,
    validatorsScroll,
    errorMessage,
    autoComplete,
    labelTooltip,
    placeholder,
    customPlaceholder,
    ...props
  }: TextInputType | any) => {
    // Methods
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange(e.target.value);
    };
    const customTheme = {
      field: {
        input: {
          colors: {
            gray: `${
              props.readOnly ? 'cursor-not-allowed  dark:bg-gray-800 text-gray-500' : ' dark:bg-gray-700 text-gray-800'
            } block w-full border bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50 border-gray-300  dark:border-gray-600 dark:text-white dark:placeholder-gray-400 p-2.5 text-sm rounded-lg focus:ring-0 focus:border-gray-300`,
            failure:
              'border-red-500 bg-white-500 text-gray-900 dark:text-white placeholder-gray-400 focus:border-red-500 focus:ring-red-500 dark:border-red-400 dark:bg-[#374151] dark:focus:border-red-500 dark:focus:ring-red-500',
          },
        },
      },
    };

    return (
      <div>
        {label && (
          <div className="mb-2 flex items-center gap-2">
            <Label htmlFor={name}>
              <span className="text-[#1B1F3B] font-medium thepassBtwo">{label}</span>{' '}
              <span className="text-inputSubLabel dark:text-inputLabel">{subLabel}</span>
              {requiredLabel && <span className="text-red-600 dark:text-red-800"> *</span>}
            </Label>
            {labelTooltip && (
              <Tooltip theme={StaticData.customTooltipTheme} content={labelTooltip} style="auto" className="border border-white">
                <Icon icon="solar:info-circle-outline" className="text-gray-600 dark:text-gray-200" width="18" />
              </Tooltip>
            )}
          </div>
        )}

        {/* Custom JSX Placeholder Support */}
        {customPlaceholder ? (
          <div className="relative">
            <Input
              theme={customTheme}
              id={name}
              onChange={handleChange}
              {...props}
              placeholder="" // Empty placeholder for custom JSX
              rightIcon={props.rightIcon}
              autoComplete={!!autoComplete ? autoComplete : 'off'}
              color={errorMessage ? 'failure' : 'gray'}
              helpertext={errorMessage ? <>{errorMessage}</> : undefined}
              scrolltoerror={validatorsScroll && errorMessage && document.getElementById(name)?.scrollIntoView({ behavior: 'smooth', block: 'end' })}
            />
            {/* JSX Placeholder Overlay */}
            {!props.value && (
              <div className="absolute inset-0 flex items-center px-3 pointer-events-none text-gray-400 dark:text-gray-500">{customPlaceholder}</div>
            )}
          </div>
        ) : (
          <Input
            theme={customTheme}
            id={name}
            onChange={handleChange}
            {...props}
            placeholder={typeof placeholder === 'string' ? placeholder : props.placeholder}
            rightIcon={props.rightIcon}
            autoComplete={!!autoComplete ? autoComplete : 'off'}
            color={errorMessage ? 'failure' : 'gray'}
            helpertext={errorMessage ? <>{errorMessage}</> : undefined}
            scrolltoerror={validatorsScroll && errorMessage && document.getElementById(name)?.scrollIntoView({ behavior: 'smooth', block: 'end' })}
          />
        )}
      </div>
    );
  }
);
