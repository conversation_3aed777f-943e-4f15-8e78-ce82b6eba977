export const backRouteMap: { pattern: RegExp; to: (match: RegExpMatchArray) => string }[] = [
    // Organizations
    { pattern: /^\/app\/organizations\/profile\/[^/]+\/[^/]+$/, to: () => "/app/organizations/list" },

    // Questions
    { pattern: /^\/app\/questions\/view\/[^/]+$/, to: () => "/app/questions/list" },
    { pattern: /^\/app\/questions\/edit\/[^/]+$/, to: () => "/app/questions/list" },
    { pattern: /^\/app\/questions\/create$/, to: () => "/app/questions/list" },

    // Tests/Quizzes
    { pattern: /^\/app\/tests\/view\/[^/]+$/, to: () => "/app/tests/list/setup" },
    { pattern: /^\/app\/tests\/edit\/[^/]+$/, to: () => "/app/tests/list/setup" },
    { pattern: /^\/app\/tests\/create$/, to: () => "/app/tests/list/setup" },

    // Submissions
    { pattern: /^\/app\/submissions\/single\/[^/]+$/, to: () => "/app/submissions/list" },

    // Applicants
    { pattern: /^\/app\/applicants\/progress\/[^/]+\/[^/]+$/, to: () => "/app/applicants/list" },

    // Interviews
    { pattern: /^\/app\/interviews\/single\/[^/]+$/, to: () => "/app/interviews/list" },

    // Phone Screening
    { pattern: /^\/app\/phone-screening\/single\/[^/]+$/, to: () => "/app/phone-screening/list" },

    // Assessments (view/edit/create → go to list of same type)
    {
        pattern: /^\/app\/assessment-templates\/([^/]+)\/view\/[^/]+$/,
        to: (m) => `/app/assessment-templates/${m[1]}/list`,
    },
    {
        pattern: /^\/app\/assessment-templates\/([^/]+)\/edit\/[^/]+$/,
        to: (m) => `/app/assessment-templates/${m[1]}/list`,
    },
    {
        pattern: /^\/app\/assessment-templates\/([^/]+)\/create$/,
        to: (m) => `/app/assessment-templates/${m[1]}/list`,
    },
    {
        pattern: /^\/app\/assessment-templates\/([^/]+)\/assign\/[^/]+$/,
        to: (m) => `/app/assessment-templates/${m[1]}/list`,
    },{
        pattern: /^\/app\/assessment-templates\/([^/]+)\/assign$/,
        to: (m) => `/app/assessment-templates/${m[1]}/list`,
    },

    // Assessment Report
    {
        pattern: /^\/app\/assessment-report\/view\/[^/]+\/[^/]+\/[^/]+$/,
        to: () => "/app/assessment-report/list/tests",
    },
];