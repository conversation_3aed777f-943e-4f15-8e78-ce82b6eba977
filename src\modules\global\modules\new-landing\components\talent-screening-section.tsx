import React from 'react';

import { Checkbox, Button, CustomIcon, Icon } from 'src';
import { Label } from 'flowbite-react';
import talentGroupSection from 'images/landing/talent-group-section.png';
import gradientBackground from 'images/landing/gradientBackground.png';

export const TalentScreeningSection = () => {
  const features = ['Predefined Assessments', 'Build Customized Assessments', 'Tailored Skill Assessments', 'Applicant Insightful  Reporting'];

  return (
    <div className="py-7 mt-9 mb-10 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row lg:justify-around items-center justify-center gap-y-4 gap-x-8 lg:gap-x-44">
          <div className="hidden md:block lg:hidden">
            <div className="flex flex-row items-start justify-between gap-8">
              <div className="flex flex-col flex-1">
                <div className="w-fit h-8 bg-[#ddd7ff]/30 rounded-lg flex items-center justify-center px-4 py-2 mb-4">
                  <h2 className="text-sm text-[#743AF5] dark:text-white leading-5 font-semibold tracking-wider uppercase">FILTER FAST. HIRE SMART</h2>
                </div>

                <div className="mb-4">
                  <div className="text-[40px] font-semibold">
                    Finding <span className="capitalize font-semibold gradient-text p-1 py-2 text-transparent bg-clip-text">The Right Talent</span>{' '}
                    Isn't About Guesswork.
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-gray-600">
                    We help you screen applicants with custom questions and assign targeted skill tests—so you identify the right talent faster and
                    more reliably.
                  </p>
                </div>
              </div>

              <div className="relative flex-1 flex justify-center">
                <img src={talentGroupSection} alt="Talent Group" className="max-w-[396px] scale-95" />
                <img className="absolute top-0 left-0 opacity-40 z-[-1] w-full h-auto object-contain" src={gradientBackground} />
              </div>
            </div>

            <div className="w-full mt-8">
              <div className="grid grid-cols-2 gap-4 w-full">
                {features.map((item: any) => (
                  <div key={item} className="flex items-center">
                    <Checkbox className="text-[#704EE6]" value={true} onChange={() => {}} label="" name="" />
                    <Label className="flex text-sm text-text-500 ml-2">{item}</Label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* text and left things - original */}
          <div className="flex flex-col justify-between items-center lg:items-start md:hidden lg:flex">
            <div className="w-fit h-8 sm:text-nowrap text-wrap bg-[#ddd7ff]/30 rounded-lg flex items-center justify-center mt-8 px-4 py-2 mb-9">
              <h2 className="text-sm text-[#743AF5] dark:text-white leading-5 font-semibold tracking-wider uppercase">Filter Fast. Hire Smart</h2>
            </div>
            <div className="flex flex-col space-y-4 pb-4">
              <div className="flex flex-col items-center lg:items-start mb-3">
                <span className="text-center lg:text-start flex flex-row text-nowrap items-center gap-1">
                  <span className="text-base sm:text-[55px]   font-semibold ">Finding</span>
                  <span className="text-base sm:text-[55px]  capitalize font-semibold gradient-text p-1 py-2 text-transparent bg-clip-text">
                    the right talent
                  </span>
                </span>
                <span className="text-center lg:text-start capitalize  text-base sm:text-[55px] font-semibold ">isn't about guesswork.</span>
              </div>

              <div className="max-w-xl">
                <p className="thepassBtwo sm:thepassSubHone  text-center sm:text-start text-text-500">
                  We help you screen applicants with custom questions and assign targeted skill tests—so you identify the right talent faster and more
                  reliably.
                </p>
              </div>
              <div className="lg:hidden">
                <div className="relative">
                  <img src={talentGroupSection} className="w-full scale-90" alt="Talent Group" />
                  <img src={gradientBackground} className="absolute top-0 left-[125px] opacity-40 z-[-1] hidden lg:block" />
                </div>
              </div>
            </div>

            <div className="flex items-center md:justify-start justify-start w-full mt-4 sm:mt-0">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 w-fit mb-7  sm:gap-4">
                {features.map((item: any) => (
                  <div key={item} className="flex items-center ">
                    {/* TODO: Markos */}
                    <Checkbox className="text-[#704EE6]" value={true} onChange={() => {}} label="" name="" />
                    <Label htmlFor={`item-${item.id}`} className="flex text-[18px] text-text-500 ml-2">
                      {item}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* image and  right things  */}
          <div className="hidden lg:block ">
            <div className="relative">
              <img src={talentGroupSection} alt="Talent Group" className="w-full max-w-full scale-125 sm:scale-125" />
              <img
                className="absolute hidden lg:block top-0 left-0 sm:left-[60px] md:left-[90px] lg:left-[50px] opacity-40 z-[-1] w-full h-auto object-contain scale-200"
                src={gradientBackground}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
