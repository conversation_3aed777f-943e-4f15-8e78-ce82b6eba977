// React
import { useNavigate } from 'react-router-dom';

// Core
import { Dialog, Button } from 'UI';
import { Icon } from 'src';
import { RootState, useAppSelector, UserData } from 'UI/src';

type SubscribeDialogType = {
  onClose: any;
};

export const SubscribeDialog = ({ onClose }: SubscribeDialogType) => {
  // Hooks
  const navigate = useNavigate();

  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  return (
    <Dialog isOpen size="md" onClose={onClose}>
      <div className="space-y-4 text-center">
        <Icon icon="material-symbols:lock-outline" width={'60'} className="w-fit p-4 mx-auto bg-purple-300 dark:text-white rounded-full" />

        <p className="dark:text-white text-2xl font-bold">
          {userData?.hasOwnProperty('features') ? 'You’ve Reached the Maximum Number of Uses Allowed.' : 'Subscribe to Unlock This Feature'}
        </p>
        <p className="dark:text-white">
          {userData?.hasOwnProperty('features')
            ? 'Please upgrade your plan to unlock more access.'
            : 'Complete your subscription and enjoy full access to all features and services.'}
        </p>

        <Button label="Go to Plans" onClick={() => navigate('/pricing')} colorType="primary" className="mx-auto " />
      </div>
    </Dialog>
  );
};
