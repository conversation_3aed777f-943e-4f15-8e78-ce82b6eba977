import React from 'react';

export default function SubmissionsChartsPlaceholder() {
  return (
    <div className="flex flex-col gap-2">
      <div className="w-20 h-3 my-[10px] bg-gray-300 rounded-full dark:bg-gray-600"></div>
      <div className="flex-grow grid grid-cols-1 sm:grid-cols-5 lg:gap-12 sm:gap-8 px-4 sm:px-8">
        <div className="col-span-2 flex justify-center">
          {/*Pie Charts Placeholder */}
          <div className="flex justify-center">
            <div className="w-[170px] h-[170px] bg-gray-300 dark:bg-gray-600 rounded-full flex justify-center items-center animate-pulse">
              <div className="w-32 h-32 rounded-full bg-white dark:bg-darkBackgroundCard flex justify-center items-center"></div>
            </div>
          </div>
        </div>
        <div className="transition-all ease-in duration-300 sm:col-span-3 text-base sm:text-xl flex items-center animate-pulse">
          <div className="w-full">
            {[...Array(3)].map((ele, index) => (
              <div key={index} className="py-2 text-black dark:text-white gap-6 flex justify-between items-center">
                <div className="flex items-center gap-4">
                  <div className="w-6 h-6 bg-gray-200 rounded-full dark:bg-gray-700"></div>
                  <div className="w-40 h-4 my-[6px] bg-gray-300 rounded-full dark:bg-gray-600"></div>
                </div>
                <div className="w-8 h-3 my-[10px] bg-gray-300 rounded-full dark:bg-gray-600"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
