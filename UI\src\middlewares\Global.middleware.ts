import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api } from '../../src';

// Send contact email
export const sendContactEmail = createAsyncThunk(
  'global/sendContactEmail',
  async (payload: { type: string; [key: string]: any }, { rejectWithValue }) => {
    try {
      const response = await Api.post('/mails/contact', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to send contact email');
    }
  }
);

// Get custom template
export const fetchCustomTemplate = createAsyncThunk(
  'global/fetchCustomTemplate',
  async (quizId: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`templates/single/custom/${quizId}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch custom template');
    }
  }
);

// Get programming test
export const fetchProgrammingTest = createAsyncThunk(
  'global/fetchProgrammingTest',
  async (testId: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`templates/single/custom/${testId}`, {});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch programming test');
    }
  }
);

// Create programming test submission
export const createProgrammingTestSubmission = createAsyncThunk(
  'global/createProgrammingTestSubmission',
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await Api.post('submissions/single', payload);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to create programming test submission');
    }
  }
);

// Get programming test quiz
export const fetchProgrammingTestQuiz = createAsyncThunk(
  'global/fetchProgrammingTestQuiz',
  async (quizId: string, { rejectWithValue }) => {
    try {
      const response = await Api.get(`templates/single/custom/${quizId}`);
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error?.response?.data?.message || 'Failed to fetch programming test quiz');
    }
  }
); 
