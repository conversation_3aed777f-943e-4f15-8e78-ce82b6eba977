import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import { analyzer } from 'vite-bundle-analyzer';
import tailwindcss from '@tailwindcss/vite';

export default {
  plugins: [react(), analyzer(), tailwindcss()],
  optimizeDeps: {
    include: ['date-fns-tz'],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      src: path.resolve(__dirname, './src'),
      UI: path.resolve(__dirname, './UI'),
      images: path.resolve(__dirname, './src/images'),
    },
  },
  preview: {
    allowedHosts: ['techpass-test-frontend'],
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    strictPort: true,
    allowedHosts: ['techpass-test-frontend', 'localhost'],
    proxy: {
      '/api': {
        target: process.env.VITE_API_BASE_URL,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  build: {
    chunkSizeWarningLimit: 4600,
  },
};
