export interface QuizzesStatistic {
  totalQuizzes: number;
  quizzesByCategory: {
    _id: string[];
    subcategories: {
      subcategoryName: string;
      count: number;
      quizzes: {
        title: string;
        duration: number;
        difficulty: number;
        numOfQuestions: number;
      }[];
    }[];
    subcategoryCount: number;
  }[];
  highQuizzes: {
    _id: string;
    quizId: string;
    quiz: {
      _id: string;
      title: string;
      duration: number;
      numOfQuestions: number;
      category: {
        categoryId: string;
        categoryName: string;
      }[];
      subCategory: {
        subCategoryId: string;
        subCategoryName: string;
        categoryId: string;
      }[];
    };
  }[];
  lowQuizzes: {
    _id: string;
    quizId: string;
    quiz: {
      _id: string;
      title: string;
      duration: number;
      numOfQuestions: number;
      category: {
        categoryId: string;
        categoryName: string;
      }[];
      subCategory: {
        subCategoryId: string;
        subCategoryName: string;
        categoryId: string;
      }[];
    };
  }[];
}

export interface QuestionStatistic {
  totalQuestions: number;
  questionsByCategory: {
    _id: string;
    count: number;
  }[];
  questionsBySubCategory: {
    _id: string;
    count: number;
  }[];
}

export interface SubmissionsStatistic {
  submissionsStatistic: {
    inProgressCount: number;
    notStartedCount: number;
    submittedCount: number;
    notStartedPercentage: number;
    inProgressPercentage: number;
    submittedPercentage: number;
    totalSubmissions: number;
    submissionsAbove5number: number;
  };
  interviewStatistic: {
    totalInterviews: number;
    interviewsPassed: number;
    inProgressInterviewCount: number;
    notStartedInterviewCount: number;
    submittedInterviewCount: number;
    inProgressInterviewPercentage: number;
    notStartedInterviewPercentage: number;
    submittedInterviewPercentage: number;
    interviewByCategory: [
      {
        _id: string;
        count: number;
      }
    ];
  };
}

export interface AuthorsStatistic {
  topQuestionsAuthor: {}[];
  topQuizzesAuthor: {}[];
}

export interface UsersStatistic {
  totalUsers: number;
  contentCreatorUsers: number;
  userStatistics: {}[];
  contentCreatorTracks: {}[];
}

export interface AssessmentsStatistics {
  assessmentsSubmittedPercentage: number;
  assessmentsMissedPercentage: number;
  interviewStatistic: any;
  submissionsStatistic: any;
}

export interface AssessmentsCount {
  totalInterview: number;
  totalTest: number;
  totalScreening: number;
}
