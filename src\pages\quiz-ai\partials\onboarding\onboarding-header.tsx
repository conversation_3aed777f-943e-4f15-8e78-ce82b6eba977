import React from 'react';
import { Icon, Logo } from 'src';
import { RootState, setThemeColor, useAppDispatch, useAppSelector } from 'UI/src';

interface Props {
  showThemeIcon: boolean;
}
export const OnboardingHeader: React.FC<Props> = ({ showThemeIcon }) => {
  // Hooks
  const dispatch = useAppDispatch();
  const themeColor = useAppSelector((state: RootState) => state.app.themeColor);
  const newMode = (): 'light' | 'dark' => {
    if (themeColor === 'light') {
      return 'dark';
    }
    return 'light';
  };

  return (
    <nav className="bg-white dark:bg-darkBackgroundCard mb-1 border-b border-[#f4f4f4] px-4 pt-[14px] pb-[10px] dark:border-[#374151] fixed left-0 right-0 top-0 z-[60]">
      <div className="mx-6">
        <div className="flex justify-between items-center">
          <div className="flex justify-between items-center">
            <div className="flex items-center justify-between mr-4 cursor-pointer">
              <Logo className="h-8" />
            </div>
          </div>
          {/* <!-- Dark --> */}
          {showThemeIcon && (
            <button
              type="button"
              className="text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 rounded-lg text-sm p-2.5 flex items-center justify-center"
              onClick={() => dispatch(setThemeColor(newMode()))}
            >
              <Icon icon={themeColor === 'light' ? 'ic:outline-light-mode' : 'ic:outline-dark-mode'} width="20px" />
            </button>
          )}
        </div>
      </div>
    </nav>
  );
};
