// React
import { useEffect, useState } from 'react';
import { Form, initializeForm, RootState, setFieldValue, useAppDispatch, useAppSelector, useValidate, uploadAvatarToS3 } from 'UI/src';

// Core
import { Button } from 'UI';

import { Dialog, ImageUploader, TextInput, Icon } from 'src';
import { setErrorNotify, setNotifyMessage } from 'UI';
import { useFormik } from 'formik';

export const UploadDialog = ({ onClose, orgId }: { onClose: () => void; orgId: string }) => {
  // State
  const [loading, setLoading] = useState(false);

  // Hooks
  const dispatch = useAppDispatch();
  const { isRequired } = useValidate();

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      image: '',
      name: '',
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  const handleSubmit = async () => {
    if (!form.image) {
      dispatch(setErrorNotify('Select image!'));
    } else {
      try {
        setLoading(true);
        const key = await uploadAvatarToS3(form.image, orgId, form.name);
        dispatch(setNotifyMessage('Avatar uploaded successfully!'));
        onClose();
      } catch (error) {
        dispatch(setErrorNotify('Failed to upload avatar'));
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <Dialog size="lg" show popup onClose={onClose} overflowVisible={true} modalHeader="Request new avatar">
      <Form onSubmit={handleSubmit}>
        <div className="space-y-5">
          {/* FIXME: Add form validation */}
          <ImageUploader label="Upload Image" value={form.image} onChange={(value: any) => dispatch(setFieldValue({ path: 'image', value }))} />

          {/* TODO: Markos */}
          <div className="flex gap-4">
            <div className="flex-1">
              <TextInput
                name="name"
                label="Name"
                placeholder="Enter"
                value={form.name}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'name', value }))}
                validators={[isRequired()]}
              />
            </div>

            <div className="flex-1">
              <TextInput
                name="role"
                label="Role"
                placeholder="Enter "
                value={form.role}
                onChange={(value: any) => dispatch(setFieldValue({ path: 'role', value }))}
                validators={[isRequired()]}
              />
            </div>
          </div>
          <div className="rounded-lg bg-blue-50 p-4">
            <div className="flex items-center gap-2 mb-2">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="16" height="16" rx="6" fill="#11ABE6" />
                <path d="M8 10.6641V7.33073" stroke="white" stroke-width="1.5" stroke-linecap="round" />
                <circle cx="0.666667" cy="0.666667" r="0.666667" transform="matrix(1 0 0 -1 7.33594 6)" fill="white" />
              </svg>
              <span className="font-semibold text-sm text-[#1B1F3B]">Avatar Guidelines</span>
            </div>
            <ul className="list-disc  ml-5 text-[#1B1F3B] space-y-1">
              <li>Use high-quality, professional images</li>
              <li>Ensure the face is clearly visible</li>
              <li>Avoid inappropriate or offensive content</li>
              <li>Square images work best (1:1 ratio)</li>
            </ul>
          </div>

          <div className="flex gap-2">
            <div className="flex-1">
              <Button type="button" label="Cancel" colorType="tertiary" className="w-full " loading={loading} disabled={loading} onClick={onClose} />
            </div>
            <div className="flex-1">
              <Button type="submit" colorType="primary" label="Send" className="w-full" loading={loading} disabled={loading} />
            </div>
          </div>
        </div>
      </Form>
    </Dialog>
  );
};
