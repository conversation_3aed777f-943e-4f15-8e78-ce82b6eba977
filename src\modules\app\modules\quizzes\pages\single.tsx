import { Pagination } from 'flowbite-react';
import { useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { QuizType } from 'UI/src';

// UI
import {
  useValidate,
  useScreenSize,
  useLookups,
  hideConfirm,
  initializeForm,
  QuestionType,
  RootState,
  setFieldValue,
  showConfirm,
  useAppSelector,
  UserData,
  Api,
  StaticData,
  Regex,
} from 'UI/src';

// UI
import { Card, TextInput, Select, Button, Jumbotron, Icon, MultiSelect, CustomIcon } from 'src';

// Components
// import { QuestionsListItem } from '../components/questions-list-item';
// import { SubmissionsGenerateDialog } from '../components/generate-dialog';
// import { ManualSelect } from '../components/manual-select';
import { ListPlaceholder } from '../components/list-placeholder';
import { QuizSelectionManual } from '../components/manual-selection';
import { QuizSelectionAutomate } from '../components/automate-generation';
import { QuestionsSelection } from '../components/questions-selection';
import { Form, useAppDispatch } from 'UI/src';
import { setErrorNotify, setNotifyMessage } from 'UI';
import {
  setQuizzesManualSelection,
  setQuizzesAutomateSelection,
  setQuizzesQuestionDatabase,
  setQuizzesQuestionsList,
  setQuizzesSelectedQuestionIds,
  setQuizzesCurrentPage,
  setQuizzesAnyQuestionHasEditMode,
  updateQuizzesQuestionEditMode,
  resetQuizzesSelectionStates,
  clearQuizzesQuestionData,
} from 'UI/src/slices/quizzesSingle/quizzesSingle.slice';
import { useFormik } from 'formik';

// Types

type QuestionDB = QuestionType & { _id: string };
type AnyObject = { [key: string]: any };

type IsAnyQuestionHasEditMode = { [key: string]: boolean };

export const QuizzesSinglePage = () => {
  const isViewOnly = useAppSelector((state: RootState) => state.viewOnly.isVisible);
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  // Hooks
  const { id } = useParams<{ id?: string }>();
  const { isRequired } = useValidate();
  const navigate = useNavigate();
  const screen = useScreenSize();
  const dispatch = useAppDispatch();

  // Reference
  const subCategoryRef = useRef<HTMLInputElement | null>(null);

  // Redux State
  const { isManualSelection, isAutomateSelection, questionDatabase, questionsList, currentPage, selectedQuestionIds, isAnyQuestionHasEditMode } =
    useAppSelector((state: RootState) => state.quizzesSingle);

  const questionsPerPage = 20;
  const totalPages = Math.max(Math.ceil(questionsList.length / questionsPerPage), 1);

  // Calculate the current questions to display based on pagination
  const indexOfLastQuestion = currentPage * questionsPerPage;
  const indexOfFirstQuestion = indexOfLastQuestion - questionsPerPage;
  const currentQuestions = questionsList.slice(indexOfFirstQuestion, indexOfLastQuestion);

  // Showing Text in Pagination as table
  const showingText = `${questionsList.length ? currentPage * questionsPerPage - questionsPerPage + 1 : questionsList.length} - ${
    currentPage * questionsPerPage > questionsList.length ? questionsList.length : currentPage * questionsPerPage
  }`;

  // Form
  const form = useAppSelector((state: RootState) => state.form.data);
  const formik = useFormik({
    initialValues: {
      duration: 50,
      questionIds: [],
      difficulty: 0,
      locked: false,
      numOfQuestions: null,
      category: 0,
      subCategory: [],
      subCategoryFiltration: [],
      questionsDifficulty: [],
      internPhase: 0,
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });

  // Hooks
  const { lookups } = useLookups('subcategory', { cached: false, params: form.category ? { categoryId: form.category } : null } as any);

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`templates/single/${id}`, {});
      console.log(`quizzes/single/${id}`, response.data);
      dispatch(initializeForm({ ...response.data, subCategoryFiltration: [] }));

      // Initialize questionDatabase
      dispatch(setQuizzesQuestionDatabase([]));
      // Fetch all questions concurrently
      // Extract data from responses and remove duplicates
      const questionsData = await Promise.all(
        (response.data.questionIds as string[]).map((questionId: string) => Api.get<QuestionType>(`questions/single/${questionId}`))
      );
      const uniqueQuestions = Array.from(new Map(questionsData.map((q) => [q.data._id, q.data])).values());

      dispatch(setQuizzesQuestionDatabase(uniqueQuestions));
      dispatch(setFieldValue({ path: 'questionsDifficulty', value: [] }));
      dispatch(setFieldValue({ path: 'numOfQuestions', value: null }));
    } catch (error: any) {
      dispatch(setErrorNotify(error.response?.data?.message));
    }
  };

  // Old behavior test creation
  // const handleSearch = async (keyword, questionManualSelectIds) => {
  //   try {
  //     let filter = {};
  //     if (form.subCategory !== 0) {
  //       filter.subCategory = form.subCategory;
  //     }
  //     if (form.questionsDifficulty) {
  //       filter.questionsDifficulty = form.questionsDifficulty;
  //     }
  //     if (form.category) {
  //       filter.category = form.category;
  //     }
  //     const result = await Api.get('questions/search', {
  //       keyword,
  //       exclude: [...form.questionIds, ...questionManualSelectIds],
  //       filter,
  //     });
  //     setQuestions(result?.data);
  //   } catch (error) {
  //     dispatch(setErrorNotify(error.response.data.message));
  //   }
  // };

  const ConfirmText = () => {
    return (
      <div>
        <div className="flex mx-auto p-4 mb-7 bg-[#ddd1f8] w-24 h-24 rounded-full">
          <div className="flex mx-auto mb-7 bg-[#cab6f5] w-16 h-16 justify-center rounded-full">
            <Icon icon="uil:question" className="text-[#9061F9]" width="40" />
          </div>
        </div>
        <p>Once confirmed, This test will be {id ? 'updated permanently!' : 'created'}</p>
      </div>
    );
  };

  // const handleInsert = async (e: React.FormEvent<HTMLFormElement>) => {
  //   e.preventDefault();
  //   if (form.questionIds.length) {

  const setFormValue = (newForm: any) => {
    dispatch(initializeForm(newForm));
  };
  const setFieldValueLocal = (field: string, value: any) => {
    dispatch(setFieldValue({ path: field, value }));
  };

  const handleInsert = async (formData: Record<string, any>) => {
    if (formData.questionIds.length) {
      dispatch(
        showConfirm({
          message: ConfirmText(),
          options: {
            onConfirm: async () => {
              //   const { numOfQuestions, questionsDifficulty, subCategory, internPhase, ...payload } = form;
              // if (form.difficulty === 1) {
              // (payload as AnyObject).internPhase = form.internPhase;

              const { numOfQuestions, questionsDifficulty, subCategory, internPhase, ...payload } = formData;
              if (formData.difficulty === 1) {
                (payload as AnyObject).internPhase = formData.internPhase;
              }
              try {
                await Api.post('quizzes/single', payload);
                dispatch(hideConfirm());
                dispatch(setNotifyMessage('Test added successfully!'));
                navigate('/app/tests');
              } catch (error: any) {
                dispatch(setErrorNotify(error?.response?.data?.message));
              }
            },
          },
        })
      );
    } else {
      dispatch(setErrorNotify('Please add a question'));
    }
  };
  //   const handleUpdate = async (e: React.FormEvent<HTMLFormElement>) => {
  // e.preventDefault();
  const handleUpdate = async (formData: Record<string, any>) => {
    dispatch(
      showConfirm({
        message: ConfirmText(),
        options: {
          onConfirm: async () => {
            try {
              //  const { numOfQuestions, questionsDifficulty, subCategory, internPhase, ...payload } = form;
              // if (form.difficulty === 1) {
              // (payload as AnyObject).internPhase = form.internPhase;
              const { numOfQuestions, questionsDifficulty, subCategory, internPhase, ...payload } = formData;
              if (formData.difficulty === 1) {
                (payload as AnyObject).internPhase = formData.internPhase;
              }
              await Api.put(`templates/single/${id}`, payload);
              dispatch(setNotifyMessage('Test updated successfully!'));
              navigate('/app/tests');
            } catch (error: any) {
              dispatch(setErrorNotify(error?.response?.data?.message));
            } finally {
              dispatch(hideConfirm());
            }
          },
        },
      })
    );
  };

  // Old behavior test creation
  // const handleGenerateDialog = () => {
  //   // if (!form.category) dispatch(setErrorNotify('You Should Choose Category First');
  //   // else setGenerateDialogVisibility(true);
  //   setGenerateDialogVisibility(true);
  // };

  // Old behavior test creation
  // const onRemove = (id) => {
  //   setFormValue({
  //     ...form,
  //     questionIds: form.questionIds.filter((target) => id !== target),
  //   });
  //   setQuestionDatabase((prev) => prev.filter((target) => id !== target._id));
  //   setSelectedQuestionIds((prev) => prev.filter((target) => id !== target));
  // };

  // On Mount
  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, [window.location.href]);

  useEffect(() => {
    dispatch(setQuizzesQuestionsList([...questionDatabase]));
  }, [questionDatabase]);

  const handleGetQuestionData = async (id: string) => {
    try {
      const response = await Api.get<QuestionType>(`questions/single/${id}`, {});
      // setQuestionDatabase((prev: any) => [...prev, response.data]);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  // Old behavior test creation
  // // Filtration
  // const handleSelectedQuestionsSearch = (keyword) => {
  //   const results = questionDatabase.filter(
  //     (item) =>
  //       item.title.toLowerCase().includes(keyword.toLowerCase()) ||
  //       item.subCategoryName.toLowerCase().includes(keyword.toLowerCase()) ||
  //       item.topicName.toLowerCase().includes(keyword.toLowerCase())
  //   );
  //   setQuestionsList(results);
  // };
  // const handleFiltration = (fieldName, value) => {
  //   const results = questionDatabase.filter((item) => item[fieldName].toLowerCase().includes(value.toLowerCase()));
  //   setQuestionsList(results);
  // };
  // const getAllFilterValues = (fieldName) => {
  //   let result = [];
  //   questionDatabase.forEach((item) => {
  //     if (!result.includes(item[fieldName])) {
  //       result.push(item[fieldName]);
  //     }
  //   });
  //   return result;
  // };

  const renderNoSelection = () => {
    return (
      <div className="mt-5  px-4">
        <div className="py-3 px-3  gap-3 flex  items-center bg-[#F2F4F788] dark:bg-[#374151] rounded-md">
          <Icon icon="material-symbols:info-rounded" className="text-[#D1D5DB]" width="25" />
          <p className="dark:text-white text-[#6B7280C7] text-base">
            You haven't selected any questions yet, use the button above to add question that will be retrieved from the question bank{' '}
          </p>
        </div>

        <div className="flex mt-4 gap-2 ">
          <div className="bg-[#f5f7fa]  dark:bg-gray-700  dark:text-gray-400 mt-auto mb-auto h-fit p-2 rounded-full">
            <svg width="25" height="25" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M16.6486 20.5562L24.1915 28.099C24.6999 28.6075 25.5244 28.6075 26.0329 28.099L26.7956 27.3363C27.3041 26.8278 27.3041 26.0033 26.7956 25.4949L19.2528 17.952M16.6486 20.5562L13.0121 16.9196C12.5036 16.4111 12.5036 15.5867 13.0121 15.0782L13.7748 14.3154C14.2833 13.807 15.1077 13.807 15.6162 14.3154L19.2528 17.952M16.6486 20.5562L19.2528 17.952"
                stroke="currentColor"
                strokeWidth="1.25"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M22.5082 2.97852C23.5427 5.77427 24.8573 7.12883 27.7165 8.18685C24.9208 9.22137 23.5663 10.5359 22.5082 13.3952C21.5135 10.7073 20.05 9.20448 17.2999 8.18685C20.0956 7.15233 21.4501 5.83776 22.5082 2.97852Z"
                stroke="currentColor"
                strokeWidth="1.25"
                strokeLinejoin="round"
              />
              <path
                d="M8.18652 5.58789C8.96241 7.6847 9.94835 8.70063 12.0928 9.49414C9.99596 10.27 8.98004 11.256 8.18652 13.4004C7.41063 11.3036 6.4247 10.2877 4.28027 9.49414C6.37708 8.71826 7.39301 7.73232 8.18652 5.58789Z"
                stroke="currentColor"
                strokeWidth="1.25"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <div className="p-1">
            <div className="flex">
              <p className="text-md align-middle mb-1 font-medium dark:text-white">Auto Selection</p>
              <div className="flex gap-1 p-1 px-3 ml-3 align-middle rounded-full  bg-[#FBC02D] bg-opacity-10 dark:bg-[#FBC02D] dark:bg-opacity-20">
                <Icon icon="iconamoon:lightning-2-fill" className=" dark:text-[#ba9331]  text-[#DCA92B]" width="13" />
                <p className="dark:text-[#b58f31] text-[12px] flex items-center text-[#FBC02D]">Speed</p>
              </div>
            </div>
            <p className="text-[#667085] dark:text-[#9AA0AC] text-base font-normal  ">
              {' '}
              Specify the number of questions you want, and we’ll automatically generate them based on your need
            </p>
          </div>
        </div>

        <div className="flex mt-4 gap-2">
          <div className=" bg-[#f5f7fa] dark:bg-gray-700  dark:text-gray-400 mt-auto mb-auto  h-fit p-2 rounded-full ">
            <svg width="25" height="25" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M21.375 9.375C23.1862 9.375 24.7012 8.08463 25.0492 6.375H28.125V4.875H25.0492C24.7012 3.16537 23.1862 1.875 21.375 1.875C19.5638 1.875 18.0487 3.16537 17.7007 4.875H1.875V6.375H17.7007C18.0491 8.08463 19.5641 9.375 21.375 9.375ZM21.375 3.375C22.6159 3.375 23.625 4.38412 23.625 5.625C23.625 6.86588 22.6159 7.875 21.375 7.875C20.1341 7.875 19.125 6.86588 19.125 5.625C19.125 4.38412 20.1341 3.375 21.375 3.375ZM15.375 18.75C17.1862 18.75 18.7013 17.4596 19.0493 15.75H28.125V14.25H19.0493C18.7009 12.5404 17.1859 11.25 15.375 11.25C13.5641 11.25 12.0487 12.5404 11.7007 14.25H1.875V15.75H11.7007C12.0491 17.4596 13.5641 18.75 15.375 18.75ZM15.375 12.75C16.6159 12.75 17.625 13.7591 17.625 15C17.625 16.2409 16.6159 17.25 15.375 17.25C14.1341 17.25 13.125 16.2409 13.125 15C13.125 13.7591 14.1341 12.75 15.375 12.75ZM9.375 28.125C11.1862 28.125 12.7013 26.8346 13.0493 25.125H28.125V23.625H13.0493C12.7009 21.9154 11.1859 20.625 9.375 20.625C7.56413 20.625 6.04875 21.9154 5.70075 23.625H1.875V25.125H5.70075C6.04912 26.8346 7.56413 28.125 9.375 28.125ZM9.375 22.125C10.6159 22.125 11.625 23.1341 11.625 24.375C11.625 25.6159 10.6159 26.625 9.375 26.625C8.13412 26.625 7.125 25.6159 7.125 24.375C7.125 23.1341 8.13412 22.125 9.375 22.125Z"
                fill="currentColor"
              />
            </svg>
          </div>
          <div className="p-1">
            <p className="text-md font-medium mb-1 dark:text-white">Manual Selection</p>
            <p className="text-[#667085] dark:text-gray-400 "> Handpick specific questions for more control.</p>
          </div>
        </div>
      </div>
    );
  };

  const onCheckSelectionInputs = (drawerSelection: (v: boolean) => void) => {
    if (!form.category) {
      return dispatch(setErrorNotify('Please select a category.'));
    } else if (!form.difficulty) {
      return dispatch(setErrorNotify('Please select a difficulty.'));
    } else if (form.difficulty === 1 && !form.internPhase) {
      return dispatch(setErrorNotify('Please select an intern phase.'));
    } else if (!form.subCategory.length) {
      return dispatch(setErrorNotify('Please select subcategory.'));
    } else {
      drawerSelection(true);
    }
  };

  // Subtract one from pagination when delete the last element in page
  useEffect(() => {
    if (currentPage > 0 && questionsList.length > 0 && questionsList.length <= (currentPage - 1) * questionsPerPage) {
      dispatch(setQuizzesCurrentPage(currentPage - 1));
    }
  }, [questionsList]);

  const JumbotronModuleInfo = {
    moduleName: 'test',
    routeName: 'tests',
  };

  const setFieldValueAutomate = (field: string) => (value: any) => {
    dispatch(setFieldValue({ path: field, value }));
  };

  return (
    <>
      <Form className="space-y-4" onSubmit={id ? handleUpdate : handleInsert}>
        <Jumbotron type={id ? 'update' : 'create'} header isShowViewButtons={JumbotronModuleInfo} />
        <div className="grid grid-cols-1 gap-4 relative">
          {/* <!-- Meta Data --> */}
          <Card className="space-y-4 !pb-4 !px-0 !py-0 mt-2">
            <div className="flex flex-col gap-3 sm:gap-0 sm:flex-row justify-between border-b px-4 pb-3 py-2 bg-[#F8FAFC] dark:bg-gray-700 dark:border-none rounded-t-md">
              <p className="text-[17px] font-medium text-[#111827] py-2 dark:text-white">Test Details</p>
            </div>
            <div className="px-4">
              <div
                className={`grid grid-cols-1 ${
                  form.difficulty === 1 ? 'sm:grid-cols-2 lg:grid-cols-4' : 'sm:grid-cols-3 lg:grid-cols-3'
                } items-start gap-4 mb-4`}
              >
                {/* {!userData.trackId && ( */}
                  <Select
                    label="Category"
                    requiredLabel
                    name="category"
                    required
                    placeholder="Search for category"
                    value={form.category}
                    onChange={(newCategory: any) => {
                      subCategoryRef.current?.blur();
                      dispatch(setFieldValue({ path: 'category', value: newCategory }));
                      dispatch(setFieldValue({ path: 'subCategory', value: [] }));
                    }}
                    lookup="category"
                    creationOptions={{
                      url: 'lookups/category/single',
                      fieldName: 'name',
                      validation: Regex.categorySubcategoryTopic,
                    }}
                    optionValueKey="_id"
                    optionLabelKey="name"
                    dropIcon={true}
                    validators={[isRequired()]}
                    // @TODO: Confirmation warning message
                    // confirmOptions={{
                    //   isConfirmationDialog: isConfirmationDialog,
                    //   doAfterConfirmation: doAfterConfirmation,
                    //   confirmTextNotChangeInputs: () => confirmTextNotChangeInputs('category'),
                    // }}
                    readOnly={isViewOnly}
                  />
                {/* )} */}
                <Select
                  label="Difficulty"
                  name="difficulty"
                  requiredLabel
                  required
                  value={form.difficulty}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'difficulty', type: Number, value }))}
                  lookup="$QuizDifficulty"
                  dropIcon={true}
                  validators={[isRequired()]}
                  placeholder="Search for difficulty level"
                  readOnly={isViewOnly}
                />
                {form.difficulty === 1 && (
                  <Select
                    name="internPhase"
                    label="Intern Phase"
                    value={form.internPhase}
                    onChange={(value: any) => dispatch(setFieldValue({ path: 'internPhase', type: Number, value }))}
                    lookup="$InternPhase"
                    dropIcon={true}
                    validators={form.difficulty === 1 ? [isRequired()] : []}
                    readOnly={isViewOnly}
                  />
                )}
                <TextInput
                  name="duration"
                  label="Duration"
                  labelTooltip="Duration time in minutes."
                  requiredLabel={true}
                  placeholder="Enter duration..."
                  type="number"
                  value={form.duration <= 1 ? 1 : form.duration}
                  onChange={(value: any) => dispatch(setFieldValue({ path: 'duration', type: Number, value }))}
                  min={20}
                  max={120}
                  readOnly={isViewOnly}
                  validators={[]}
                />
              </div>
              <MultiSelect
                key={form.category}
                label="Subcategory"
                subLabel="(Test Name)"
                requiredLabel
                name="SubCategory"
                placeholder="Search for subcategory"
                value={form.subCategory}
                onChange={(newSubCategory: any) => {
                  dispatch(setFieldValue({ path: 'subCategory', value: newSubCategory }));
                  dispatch(setFieldValue({ path: 'topic', value: null }));
                }}
                disabled={!form.category}
                disabledMessage="Please select category first"
                lookup="subcategory"
                params={{ categoryId: form.category } as AnyObject}
                creationOptions={{
                  url: 'lookups/subCategory/single',
                  fieldName: 'name',
                  validation: Regex.categorySubcategoryTopic,
                }}
                optionValueKey="_id"
                optionLabelKey="name"
                dropIcon={true}
                validators={form.category ? [isRequired()] : []}
                // @TODO: Confirmation warning message
                // confirmOptions={{
                //   isConfirmationDialog: isConfirmationDialog,
                //   doAfterConfirmation: doAfterConfirmation,
                //   confirmTextNotChangeInputs: () => confirmTextNotChangeInputs('subcategory'),
                // }}
                readOnly={isViewOnly}
              />
            </div>
          </Card>
          <Card className="!pb-4 !px-0 !py-0 mt-2">
            {/* Action buttons */}
            <div className="gap-4">
              {/* Filters Button */}
              {/* <div className="flex flex-col">
                  {getAllFilterValues('subCategoryName').map((item, index) => (
                    <div className="bg-white rounded-md w-fit px-4 py-1 mt-2" onClick={() => handleFiltration('subCategoryName', item)} key={index}>
                      {item}
                    </div>
                  ))}
                </div> */}

              <div className="flex flex-wrap gap-4 rounded-t-md justify-between  border-b py-3 bg-[#F8FAFC] dark:bg-gray-700 dark:border-none">
                <div className="flex items-center gap-3 px-4 dark:text-white mb-2 sm:mb-0">
                  <p className="text-[17px] font-medium">Questions</p>
                  {form.questionIds.length > 0 && (
                    <p className="text-sm border rounded-full px-2 py-1 text-[#8A43F9] dark:text-white bg-[#ece2fb] font-normal dark:bg-[#6F3ED8] dark:border-none">
                      {form.questionIds.length}
                    </p>
                  )}
                </div>
                <div className="flex flex-col md:flex-row gap-4">
                  {/* <div className="relative gap-4">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Icon icon="mdi:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search questions ..."
                      className="block w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg pl-10 p-2 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white focus:ring-0"
                      onChange={(e) => handleSelectedQuestionsSearch(e.currentTarget.value)}
                    />
                  </div> */}
                  {!isViewOnly && (
                    <div className="flex w-full flex-wrap gap-4 px-4">
                      <Button
                        label="Manual Selection"
                        onClick={() => onCheckSelectionInputs((value: boolean) => dispatch(setQuizzesManualSelection(value)))}
                        outline
                        disabled={!form.category || !form.difficulty || (form.difficulty === 1 && !form.internPhase) || !form.subCategory?.length}
                        disabledMessage="Please fill data above"
                      />
                      <Button
                        label="Auto Selection"
                        onClick={() => onCheckSelectionInputs((value: boolean) => dispatch(setQuizzesAutomateSelection(value)))}
                        disabled={!form.category || !form.difficulty || (form.difficulty === 1 && !form.internPhase) || !form.subCategory?.length}
                        disabledMessage="Please fill data above"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>
            {currentQuestions.length ? (
              <QuestionsSelection
                currentQuestions={currentQuestions}
                form={form}
                currentPage={currentPage}
                questionsPerPage={questionsPerPage}
                setFieldValue={setFieldValue}
                questionDatabase={questionDatabase}
                setQuestionDatabase={() => {}}
                setAnyQuestionHasEditMode={(editModeState: IsAnyQuestionHasEditMode) => dispatch(setQuizzesAnyQuestionHasEditMode(editModeState))}
              />
            ) : (
              renderNoSelection()
            )}
            {questionDatabase.length > 0 && (
              <nav className="flex justify-center items-center px-4 my-1">
                {/* <span className="text-sm font-normal text-gray-500 dark:text-gray-400">
                  Showing <span className="font-semibold text-gray-900 dark:text-white">{showingText}</span> of{' '}
                  <span className="font-semibold text-gray-900 dark:text-white">{questionsList.length}</span>
                </span> */}

                {questionsList.length > questionsPerPage && (
                  <Pagination
                    theme={StaticData.paginationTheme}
                    currentPage={currentPage}
                    onPageChange={(page: number) => dispatch(setQuizzesCurrentPage(page))}
                    showIcons
                    totalPages={totalPages}
                    layout={screen.gt.sm() ? 'pagination' : 'navigation'}
                    previousLabel={screen.gt.sm() ? 'Previous' : undefined}
                    nextLabel={screen.gt.sm() ? 'Next' : undefined}
                  />
                )}
              </nav>
            )}
          </Card>
        </div>
        <div className="flex justify-end">
          <Jumbotron
            buttons
            type={id ? 'update' : 'create'}
            isShowViewButtons={{
              ...JumbotronModuleInfo,
              disabled:
                !!Object.keys(isAnyQuestionHasEditMode)?.find((question) => isAnyQuestionHasEditMode[question]) || currentQuestions.length === 0,
              disabledMessage: 'Fill the data above',
            }}
          />
        </div>
      </Form>

      {/* {isGenerateDialogVisible && (
        <SubmissionsGenerateDialog
          onClose={() => setGenerateDialogVisibility(false)}
          category={form.category}
          questionIds={form.questionIds}
          setQuestions={setQuestions}
          setUpdateVal={setUpdateVal}
          handleGetQuestionData={handleGetQuestionData}
        />
      )} */}

      {/* {isManualSelect && (
        <ManualSelect
          form={form}
          questions={questions}
          handleSearch={handleSearch}
          onClose={() => setIsManualSelect(false)}
          questionDatabase={questionDatabase}
          currentQuestions={currentQuestions}
          setFormValue={setFormValue}
          handleGetQuestionData={handleGetQuestionData}
        />
      )} */}

      {isManualSelection && (
        <QuizSelectionManual
          setManualSelection={
            ((value: boolean) => {
              dispatch(setQuizzesManualSelection(value));
            }) as any
          }
          selectedQuestionIds={selectedQuestionIds}
          setSelectedQuestionIds={
            ((ids: string[]) => {
              dispatch(setQuizzesSelectedQuestionIds(ids));
            }) as any
          }
          form={form as any}
          setFormValue={setFormValue}
          //            setFieldValue={setFieldValue}
          setFieldValue={setFieldValueLocal}
          handleGetQuestionData={handleGetQuestionData}
          questionDatabaseOfMainTest={questionDatabase as any}
        />
      )}
      {isAutomateSelection && (
        <QuizSelectionAutomate
          setAutomateSelection={((value: boolean) => dispatch(setQuizzesAutomateSelection(value))) as any}
          form={form as any}
          //    form={form}
          // setFieldValue={setFieldValue}
          setFieldValue={setFieldValueAutomate}
          handleGetQuestionData={handleGetQuestionData}
          lookups={lookups}
          selectedQuestionIds={selectedQuestionIds}
          setSelectedQuestionIds={((ids: string[]) => dispatch(setQuizzesSelectedQuestionIds(ids))) as any}
          questionDatabaseOfMainTest={questionDatabase as any}
        />
      )}
    </>
  );
};
