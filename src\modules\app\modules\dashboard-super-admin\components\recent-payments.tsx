// Core
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Icon, EnumText, CustomIcon } from 'src';
import '@emran-alhaddad/saudi-riyal-font/index.css';
import { Api, useAppDispatch } from 'UI/src';
import { setErrorNotify } from 'UI';

type recentPayments = {
  recentPayment: [];
};

interface recentPaymentsItemData {
  organizationName: string;
  price: number;
  planName: string;
  billingCycle: number;
}

export const RecentPayments = ({ onDataLoad }: { onDataLoad: Dispatch<SetStateAction<number>> }) => {
  // Hooks
  const dispatch = useAppDispatch();
  // state
  const [recentPaymentsData, setRecentPaymentsData] = useState<recentPayments>();

  //  Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`superAdmin/organizations/subscriptions`, {});
      console.log(`superAdmin/organizations/subscriptions`, response.data);
      setRecentPaymentsData(response.data);
      if (onDataLoad && typeof onDataLoad === 'function') {
        onDataLoad(response.data?.recentPayment?.length || 0);
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
      if (onDataLoad && typeof onDataLoad === 'function') {
        onDataLoad(0);
      }
    }
  };

  // Effects
  useEffect(() => {
    handleGet();
  }, []);

  const hasRecentPayments = recentPaymentsData && recentPaymentsData?.recentPayment?.length > 0;

  return (
    <div className="sm:flex 2xl:block gap-4 space-y-3 sm:space-y-0 2xl:space-y-3">
      {hasRecentPayments ? (
        recentPaymentsData?.recentPayment?.slice(0, 2).map((itemData: recentPaymentsItemData, index: number) => (
          <div key={index} className="py-3 px-4 w-full border border-[#E1E4E8] dark:border-[#4A5462] rounded-xl flex items-center gap-2">
            <div className="border border-[#E1E4E8] rounded-full p-5"></div>
            <div className="space-y-1 w-full">
              <div className="flex justify-between">
                <p className="thepassBone text-primary-text dark:text-white">{itemData.organizationName}</p>
              </div>
              <div className="flex justify-between">
                <p className="dark:text-[#bebec2] text-text-500 text-sm font-semibold">{itemData.planName}</p>
                <p className="dark:text-white font-medium whitespace-nowrap">
                  <span className="icon-saudi_riyal"></span>
                  {itemData.price}
                  <span className="thepassBfive text-secondaryTextDisabled  dark:text-white pl-1">
                    / {itemData.billingCycle === 1 ? 'month' : 'year'}
                  </span>
                </p>
                {/* <p className="text-[#656575] dark:text-[#bebec2] text-sm">
                  <EnumText name="BillingCycle" value={itemData.billingCycle} />
                </p> */}
              </div>
            </div>
          </div>
        ))
      ) : (
        <div className="flex flex-col m-0 items-center text-center justify-center gap-y-4 h-full w-full">
          <CustomIcon definedIcon="NoMoney" width="25" height="60" />
          <p className="text-gray-400 mt-2">No recent payments found</p>
        </div>
      )}
    </div>
  );
};
