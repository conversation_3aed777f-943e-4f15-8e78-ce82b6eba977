import React from 'react';

export default function Author({ name, number }: { name: string; number: number }) {
  return (
    <div className="grid grid-cols-4 gap-8">
      <div className="col-span-3 py-2 px-3.5 rounded-xl bg-[#F2F7FF] dark:bg-[#363844] text-[#1C2434] dark:text-white text-sm relative">
        <p className="truncate">{name}</p>
      </div>
      <p className="text-[#1C2434] dark:text-white text-sm self-center">{number}</p>
    </div>
  );
}
