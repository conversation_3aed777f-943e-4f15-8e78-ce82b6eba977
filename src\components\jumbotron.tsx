// React
import { ReactNode, SetStateAction, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

// Core
import { Icon, Button } from '../';

// Flowbite
import { Tooltip } from 'flowbite-react';
import { RootState, setViewOnly, useAppDispatch, useAppSelector, useBreadcrumb } from 'UI/src';

// Types
interface JumbotronProps {
  isShowViewButtons?: {
    disabled?: boolean;
    disabledMessage?: string | ReactNode;
    moduleName?: string;
    routeName?: string;
    customOnClick?: () => void;
  };
  header?: boolean;
  buttons?: boolean;
  type?: 'create' | 'update';
  createAnotherQuestionButton?: boolean;
  isLoading?: boolean;
  setExitAfterSubmission?: React.Dispatch<React.SetStateAction<boolean>>;
}

export const Jumbotron = ({
  isShowViewButtons,
  header,
  buttons,
  type,
  createAnotherQuestionButton = false,
  isLoading,
  setExitAfterSubmission,
}: JumbotronProps) => {
  const isViewOnly = useAppSelector((state: RootState) => state.viewOnly.isVisible);
  const dispatch = useAppDispatch();

  // Hooks
  const { currentRoute } = useBreadcrumb();
  const navigate = useNavigate();

  // Params
  const { id } = useParams();

  // Fix the isViewOnly detection to properly handle create/edit pages
  useEffect(() => {
    const url = window.location.href;
    const isViewPage = url.includes('/view/');
    const isCreatePage = url.includes('/create/');
    const isEditPage = url.includes('/edit/');

    // Only set to true if explicitly on a view page
    // Set to false for create and edit pages
    dispatch(setViewOnly(isViewPage && !isCreatePage && !isEditPage));
  }, [window.location.href]);

  if (currentRoute?.data) {
    return (
      <div>
        {header ? (
          <div className={`flex flex-col sm:flex-row items-start justify-between gap-2 bg-white dark:bg-darkGrayBackground`}>
            <div>
              {currentRoute?.data?.title && (
                <h3 className="text-gray-800 dark:text-white font-bold text-[24px] sm:text-[28px]">{currentRoute.data.title}</h3>
              )}
              {currentRoute?.data?.subtitle && (
                <h3 className="w-full break-words text-[#8C939F] dark:text-gray-400 text-[15px] sm:text-[17px] font-[400] max-w-fit">
                  {currentRoute.data.subtitle}
                </h3>
              )}
            </div>

            {currentRoute?.data?.infoIcon && (
              <Tooltip content={currentRoute?.data?.infoText} className="z-[100]">
                <Icon icon="solar:info-circle-outline" className="dark:text-white" width={'20'} />
              </Tooltip>
            )}
            {isViewOnly && (
              <Button
                tertiary
                label={`Edit ${isShowViewButtons?.moduleName}`}
                customIcon={{ definedIcon: 'edit' }}
                onClick={() => navigate(`/app/${isShowViewButtons?.routeName}/edit/${id}`)}
                className="text-nowrap capitalize"
              />
            )}
            {id && !isViewOnly && type === 'update' && (
              <div className="flex justify-end gap-4">
                <Button
                  label="Cancel"
                  tertiary
                  onClick={() => navigate(id ? `/app/${isShowViewButtons?.routeName}/view/${id}` : `/app/${isShowViewButtons?.routeName}`)}
                />
                <Button
                  type="submit"
                  label={id ? 'Update' : 'Create'}
                  className="min-w-[100px]"
                  disabled={isShowViewButtons?.disabled}
                  disabledMessage={isShowViewButtons?.disabledMessage}
                  onClick={isShowViewButtons?.customOnClick}
                />
              </div>
            )}
          </div>
        ) : buttons ? (
          !!isShowViewButtons &&
          !isViewOnly && (
            <div className="flex justify-between items-center my-4">
              <div className="flex justify-end gap-4">
                <Button
                  label="Cancel"
                  tertiary
                  onClick={() => navigate(id ? `/app/${isShowViewButtons.routeName}/view/${id}` : `/app/${isShowViewButtons.routeName}`)}
                />
                {/* Create & Add Another Button (only in create mode) */}
                {createAnotherQuestionButton && (
                  <Button
                    outline
                    type="submit"
                    // className="thepassBone"
                    loading={isLoading}
                    disabled={isLoading}
                    onClick={() => setExitAfterSubmission && setExitAfterSubmission(false)}
                    label="Create & Add Another"
                  />
                )}
                <Button
                  type="submit"
                  label={id ? 'Update' : 'Create'}
                  className="min-w-[100px]"
                  disabled={isShowViewButtons?.disabled}
                  disabledMessage={isShowViewButtons.disabledMessage}
                  onClick={isShowViewButtons.customOnClick}
                />
              </div>
            </div>
          )
        ) : (
          <div
            className={`flex flex-col sm:flex-row items-start justify-between gap-2 bg-white dark:bg-darkGrayBackground ${
              !!isShowViewButtons && 'sticky top-[60px] z-10'
            }`}
          >
            <div className="">
              {currentRoute?.data?.title && (
                <h3 className="text-gray-800 dark:text-white font-bold text-[24px] sm:text-[28px]">{currentRoute.data.title}</h3>
              )}
              {currentRoute?.data?.subtitle && (
                <h3 className="w-full break-words text-[#8C939F] dark:text-gray-400 text-[15px] sm:text-[17px] font-[400] max-w-fit">
                  {currentRoute.data.subtitle}
                </h3>
              )}
            </div>

            {currentRoute?.data?.infoIcon && (
              <Tooltip content={currentRoute?.data?.infoText} className="z-[100]">
                <Icon icon="solar:info-circle-outline" className="dark:text-white" width={'20'} />
              </Tooltip>
            )}
            {!!isShowViewButtons && (
              <div className="flex justify-between items-center my-4">
                {isViewOnly && (
                  <Button
                    tertiary
                    label={`Edit ${isShowViewButtons.moduleName}`}
                    customIcon={{ definedIcon: 'edit' }}
                    onClick={() => navigate(`/app/${isShowViewButtons.routeName}/edit/${id}`)}
                    className="text-nowrap capitalize"
                  />
                )}
                {!isViewOnly && (
                  <div className="flex justify-end gap-4">
                    <Button
                      label="Cancel"
                      tertiary
                      onClick={() => navigate(id ? `/app/${isShowViewButtons.routeName}/view/${id}` : `/app/${isShowViewButtons.routeName}`)}
                    />
                    <Button
                      tooltipPlacement="bottom"
                      type="submit"
                      label={id ? 'Update' : 'Create'}
                      className="min-w-[100px]"
                      disabled={isShowViewButtons?.disabled}
                      disabledMessage={isShowViewButtons.disabledMessage}
                      onClick={isShowViewButtons.customOnClick}
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }

  return null;
};
