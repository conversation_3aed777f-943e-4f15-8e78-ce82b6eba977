// React
import { useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { FileCog } from 'lucide-react';

// Core
import { Icon, TopicsFieldColumn } from 'src';
import {
  useAppSelector,
  useAppDispatch,
  RootState,
  UserData,
  updateUser,
  Button,
  useFetchList,
  QuizDifficulty,
  setFieldValue,
  AssessmentCompCard,
} from 'UI/src';

// Components

export const TestDifficultyAssignPage = ({ formData, disableButtons, setAssignDialogVisibility }: any) => {
  // Destructuring
  const { form } = formData || {};

  // State
  const [showMoreMap, setShowMoreMap] = useState<any>({});

  // Hooks
  const navigate = useNavigate();
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);
  const dispatch = useAppDispatch();

  const { ready, loading, setLoading, list, count, filters, search, pagination, refresh, handleDates } = useFetchList(
    'templates/list/filtered-by-seniority',
    {
      search: '',
      pagination: {
        page: 1,
        size: 10,
      },
      props: {
        filters: {
          category: [form?.categoryId],
          subCategory: form?.subCategoryId,
        },
        type: form.type,
      },
    }
  );

  useEffect(() => {
    disableButtons.setDisableNextButton(!form.testId);
  }, [form.testId]);

  if (!ready) {
    return;
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <div className="w-15 h-15 rounded-full bg-[#F9F8FA] flex justify-center items-center">
          <Icon className="text-[#743AF5]" icon="pepicons-pencil:file" width={'30'} />
        </div>
        <div>
          <p className="thepassSubHone text-[#1B1F3B] font-semibold">Skills Test</p>
          <p className="thepassBtwo text-[#4E5E82]">Choose a recommended test tailored to your hiring needs to begin assignments</p>
        </div>
      </div>
      <div className="columns-1 lg:columns-2 space-y-4">
        {list?.map((singleData: any) => {
          return (
            <div key={singleData?._id} className={`p-4 space-y-3 rounded-lg break-inside-avoid`}>
              <AssessmentCompCard
                createdByName={singleData?.authorName || ''}
                name={singleData?.title || ''}
                seniority={singleData?.seniorityLevel || ''}
                difficulty={singleData?.difficulty || ''}
                questionsNumber={singleData?.numOfQuestions || 0}
                duration={singleData?.duration || 0}
                categoryName={singleData?.categoryName?.[0] || ''}
                subCategories={singleData?.subCategoryName || []}
                isSelected={form?.testId === singleData?._id}
                onRadioSelect={() => {
                  console.log('🎯 Radio button selected, setting _id:', singleData?._id);
                  dispatch(setFieldValue({ path: 'testId', value: singleData?._id }));
                }}
              />

              {showMoreMap[singleData?._id] && (
                <div className="p-2 bg-[#F9FAFB] rounded-md space-y-3">
                  <p className="text-sm text-[#667085]">Topics covered:</p>
                  <div className="flex flex-wrap gap-2">
                    {singleData?.topicName.map((topic: any) => (
                      <TopicsFieldColumn topicName={topic} />
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
