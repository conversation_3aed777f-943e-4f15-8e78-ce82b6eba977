// Core
import { useEffect, useMemo, useState } from 'react';
import { setErrorNotify, useAppDispatch, Api } from 'UI/src';
import { Icon } from 'src';
import { Responsive<PERSON>ontaine<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, Tooltip } from 'recharts';

type subscriptionStatistics = {
  plans: [];
  totalCount: number;
};
type plansDistribution = {
  renewalPercentage: number;
  upgradePercentage: number;
  downgradePercentage: number;
  cancellationPercentage: number;
};

interface subscriptionData {
  subscriptionStatistics: subscriptionStatistics[];
  plansDistribution: plansDistribution[];
}

export const OrganizationSubscriptionAndPlanDistribution = () => {
  const [subscriptionData, setSubscriptionData] = useState<subscriptionData>();
  const dispatch = useAppDispatch();

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get('superAdmin/subscription/overview', {});
      console.log('superAdmin/subscription/overview', response.data);
      setSubscriptionData(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  // !*guide to define colors for plans - Fixed colors based on plan names
  const getColorByPlanName = (planName: string) => {
    const colorMap: { [key: string]: string } = {
      Enterprise: '#D4C8FB',
      Basic: '#AC7FF4',
      Pro: '#7F7FF4',
      free: '#4F4FD9',
    };

    // Return mapped color or fallback to a default color
    return colorMap[planName] || '#AC7FF4';
  };

  //!*guide  Process the data for the chart
  const getChartData = () => {
    if (!subscriptionData || !subscriptionData.subscriptionStatistics.length) return [];

    const plans = subscriptionData.subscriptionStatistics[0]?.plans || [];
    return plans.map((plan: { planName: string; percentage: number; count: number }) => ({
      name: plan.planName,
      value: plan.percentage,
      count: plan.count,
      color: getColorByPlanName(plan.planName),
    }));
  };

  // metrics data for the bottom section of the chart
  const getMetricsData = () => {
    if (!subscriptionData || !subscriptionData.plansDistribution.length) return [];

    const planDistribution = subscriptionData.plansDistribution[0] || {};

    const metrics = [
      {
        name: 'Renewals',
        value: planDistribution.renewalPercentage || 0,
        gain: planDistribution.renewalPercentage > 0,
        lose: planDistribution.renewalPercentage < 0,
      },
      {
        name: 'Upgrade',
        value: planDistribution.upgradePercentage || 0,
        gain: planDistribution.upgradePercentage > 0,
        lose: planDistribution.upgradePercentage < 0,
      },
      {
        name: 'Downgrade',
        value: planDistribution.downgradePercentage || 0,
        gain: planDistribution.downgradePercentage > 0,
        lose: planDistribution.downgradePercentage < 0,
      },
      {
        name: 'Cancelation',
        value: planDistribution.cancellationPercentage || 0,
        gain: false, // Cancelation is typically not seen as a positive
        lose: planDistribution.cancellationPercentage > 0,
      },
    ];

    return metrics.filter((metric) => metric.value !== undefined);
  };

  const centeredTextOfChart = () => (
    <>
      {subscriptionData?.subscriptionStatistics && subscriptionData.subscriptionStatistics.length > 0 && (
        <div>
          <div className="text-2xl font-bold dark:text-white">{subscriptionData.subscriptionStatistics[0]?.totalCount || 0}</div>
          <div className="text-sm text-gray-500">Subscriptions</div>
        </div>
      )}
    </>
  );

  const rightData = () => {
    const chartData = getChartData();
    return chartData.map((item) => (
      <div key={item.name} className="flex items-center space-y-0.5 dark:text-white">
        <div className="size-3 mr-3 mt-0.5 rounded-sm" style={{ backgroundColor: item.color }}></div>
        <span className="w-32 thepassBtwo text-text-500 mr-2">{item.name}</span>
        <span className="font-medium">{item.value}%</span>
      </div>
    ));
  };

  const bottomData = () => {
    const metricsData = getMetricsData();
    return (
      <div className="grid grid-cols-1 xssm:grid-cols-2 sm:grid-cols-4 gap-4">
        {metricsData.map((metric) => (
          <div key={metric.name} className="space-y-2">
            <div className="text-sm font-medium text-gray-500 dark:text-white">{metric.name}</div>
            <div className="flex items-center gap-6">
              <div className="font-bold text-gray-700 dark:text-white">{metric.value}%</div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  useEffect(() => {
    handleGet();
  }, []);
  const chartData = getChartData();
  const largestIndex = useMemo(() => {
    if (!chartData?.length) return 0;
    return chartData.reduce((maxIdx: number, item: any, idx: number, arr: any[]) => (item.value > arr[maxIdx].value ? idx : maxIdx), 0);
  }, [chartData]);

  const overlayData = useMemo(() => {
    if (!chartData?.length) return [] as any[];
    const before = chartData.slice(0, largestIndex).reduce((sum: number, d: any) => sum + d.value, 0);
    const current = chartData[largestIndex].value;
    const after = chartData.slice(largestIndex + 1).reduce((sum: number, d: any) => sum + d.value, 0);
    return [
      { name: '__before__', value: before, count: 0, color: 'transparent' },
      { name: chartData[largestIndex].name, value: current, count: chartData[largestIndex].count, color: chartData[largestIndex].color },
      { name: '__after__', value: after, count: 0, color: 'transparent' },
    ];
  }, [chartData, largestIndex]);

  const CustomTooltip = ({ active, payload }: any) => {
    if (!active || !payload || !payload.length) return null;
    const firstValid = payload.find((p: any) => p?.payload?.name && !String(p.payload.name).startsWith('__'));
    if (!firstValid) return null;
    const data = firstValid.payload as { name: string; color: string; count?: number };
    return (
      <div className="relative p-3 rounded-md shadow-md border bg-white text-black dark:bg-gray-800 dark:text-white" style={{ zIndex: 99999 }}>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-sm flex-shrink-0" style={{ backgroundColor: data.color }}></div>
          <span className="font-medium text-gray-900 dark:text-white whitespace-nowrap">{data.name}</span>
        </div>
        {typeof data.count === 'number' && <div className="mt-1 text-sm text-gray-600 dark:text-gray-300">{data.count}</div>}
      </div>
    );
  };

  return (
    <div className="w-full h-full">
      {subscriptionData && subscriptionData.subscriptionStatistics && subscriptionData.subscriptionStatistics.length > 0 ? (
        <>
          <div className="flex flex-col xslg:flex-row items-center">
            <div className="w-full xslg:w-3/5 relative mt-4">
              <ResponsiveContainer width="100%" height={200}>
                <PieChart margin={{ top: 10, right: 0, bottom: 0, left: 0 }}>
                  <Pie data={chartData as any} cx="50%" cy="50%" innerRadius={60} outerRadius={78} dataKey="value" paddingAngle={0}>
                    {(chartData as any)?.map((entry: any, index: number) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Pie
                    data={overlayData as any}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={91}
                    dataKey="value"
                    paddingAngle={0}
                    cornerRadius={80}
                  >
                    {(overlayData as any)?.map((entry: any, index: number) => (
                      <Cell
                        key={`overlay-${index}`}
                        fill={entry.color}
                        stroke="none"
                        {...(String(entry?.name || '').startsWith('__') ? { style: { pointerEvents: 'none' } } : {})}
                      />
                    ))}
                  </Pie>
                  <Tooltip content={<CustomTooltip />} />
                </PieChart>
              </ResponsiveContainer>
              {centeredTextOfChart() && (
                <div
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center pointer-events-none"
                  style={{ zIndex: -1 }}
                >
                  {centeredTextOfChart()}
                </div>
              )}
            </div>
            {rightData() && <div className={`w-fit flex flex-col justify-center space-y-2 xslg:w-2/5`}>{rightData()}</div>}
          </div>
          {bottomData() && <div className="mt-6 pt-6 border-t">{bottomData()}</div>}
        </>
      ) : (
        <div className={`flex flex-col mb-10 items-center text-center justify-center h-full w-full `}>
          <Icon icon="iconoir:warning-circle" className="dark:text-gray-500 text-gray-400" width="50" />
          <p className={`text-gray-400 mt-2 text-base`}>No Organization Subscriptions & Plan Distribution Found</p>
        </div>
      )}
    </div>
  );
};
