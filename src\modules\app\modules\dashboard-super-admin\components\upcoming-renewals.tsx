// Core
import { Dispatch, SetStateAction, useEffect, useState } from 'react';

import { Icon, EnumText, NoDataFound, CustomIcon } from 'src';
import '@emran-alhaddad/saudi-riyal-font/index.css';
import { Api, useAppDispatch } from 'UI/src';
import { setErrorNotify } from 'UI';

interface UpcomingRenewalsProps {
  onDataLoad: Dispatch<SetStateAction<number>>;
}

interface upcomingRenewalsData {
  upcomingRenewal: [];
}

interface upcomingRenewalItem {
  _id: string;
  organizationName: string;
  planName: string;
  daysLeft: number;
  price: number;
  billingCycle: number;
}

export const UpcomingRenewals = ({ onDataLoad }: UpcomingRenewalsProps) => {
  // Hooks
  const dispatch = useAppDispatch();
  // state
  const [upcomingRenewalsData, setUpcomingRenewalsData] = useState<upcomingRenewalsData>();

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get(`superAdmin/organizations/subscriptions`, {});

      console.log(`superAdmin/organizations/subscriptions`, response.data);
      setUpcomingRenewalsData(response.data);

      if (onDataLoad && typeof onDataLoad === 'function') {
        onDataLoad(response.data?.upcomingRenewal?.length || 0);
      }
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
      if (onDataLoad && typeof onDataLoad === 'function') {
        onDataLoad(0);
      }
    }
  };

  // Effects
  useEffect(() => {
    handleGet();
  }, []);
  const hasUpcomingRenewals = upcomingRenewalsData && upcomingRenewalsData?.upcomingRenewal?.length > 0;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3   gap-4 px-1 py-3">
      {' '}
      {/* grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 */}
      {hasUpcomingRenewals ? (
        upcomingRenewalsData?.upcomingRenewal?.slice(0, 3).map((item: upcomingRenewalItem) => (
          <div key={item._id} className="px-4 py-5 border border-gray-200 dark:border-gray-600 rounded-3xl shadow-lg space-y-4">
            <div className="flex gap-3">
              <div className="border border-[#E1E4E8] rounded-full p-5"></div>

              <div>
                <p className="text-[#1B1F3B] dark:text-white thepassBone font-semibold">{item.organizationName}</p>
                <p className="text-text-500 text-sm font-semibold">{item.planName}</p>
              </div>
            </div>
            <div className="flex gap-2  xssm:justify-between justify-evenly dark:text-white">
              <div className="flex items-center gap-1 text-nowrap">
                {/* <span className="text-md font-semibold">{item.currency}</span> */}
                <span className="font-medium">
                  <span className="icon-saudi_riyal"></span>
                  {item.price}
                </span>

                <span className="text-sm text-secondaryTextDisabled dark:text-white pl-1">{item.billingCycle === 1 ? '/ month' : '/ year'}</span>
                {/* <span className="text-sm text-gray-500 dark:text-white pl-1">
                  <EnumText name="BillingCycle" value={item.billingCycle} />
                </span> */}
              </div>

              <div className="flex items-center gap-1  rounded-lg px-3 py-1.5 text-sm">
                <span className="text-md thepassBone font-semibold">{item.daysLeft}</span>
                <span className="text-nowrap thepassBfive text-secondaryTextDisabled">days left</span>
              </div>
            </div>
          </div>
        ))
      ) : (
        <div className="col-span-1 sm:col-span-2 lg:col-span-3 flex flex-col items-center text-center justify-center  ">
          {/* <Icon icon="iconoir:warning-circle" className="dark:text-gray-500 text-gray-400" width="50" /> */}
          <CustomIcon definedIcon="upcomingRenewalsNotYet" width="75" height="90" />
          <p className={`text-gray-400 mt-2`}>No Upcoming Renwals Found</p>
        </div>
      )}
    </div>
  );
};
