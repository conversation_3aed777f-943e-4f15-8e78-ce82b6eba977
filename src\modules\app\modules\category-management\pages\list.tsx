// React
import { useEffect, useState } from 'react';

// Core
import { Jumbotron, Icon, CustomIcon } from 'src';
import { Api, RootState, useAppSelector, useAppDispatch, showConfirm, hideConfirm } from 'UI/src';
import { setTitle, setEmoji, setDescription, Button, UserPermissions } from 'UI';
import {
  setExpanded,
  setCategorySearch,
  setSearch,
  setCurrentPage,
  setCategoryLoading,
  setCreateCategoryCompVisible,
  setOpenSubModal,
  setEditSubModal,
  setEditCategoryModal,
  setCategories,
  setActiveCategoryId,
  setEditingSub,
  setEditingCategory,
} from 'UI/src/slices/categoryManagement/categoryManagement.slice';
import { CreateCategoryDialog } from '../components/create-category-dialog';
import { PermissionProtectedComponent } from 'src/components';

interface EditingCategoryTypes {
  _id: string;
  id: string;
  name: string;
  description: string;
  icon: string;
  subcategories?: Array<{ _id: string; id: string; name: string }>;
}

type EditingSubType = { _id: string; id: string; name: string };

export const CategoryManagementComponent = () => {
  // Local state for context menu
  const [contextMenu, setContextMenu] = useState<{ visible: boolean; x: number; y: number; categoryId: string | null }>({
    visible: false,
    x: 0,
    y: 0,
    categoryId: null,
  });

  // Redux
  const dispatch = useAppDispatch();
  const {
    expanded,
    showMoreMap,
    categorySearch,
    search,
    currentPage,
    createCategoryCompVisible,
    categories,
    openSubModal,
    activeCategoryId,
    editSubModal,
    editingSub,
    editCategoryModal,
    editingCategory,
    loading,
  } = useAppSelector((state: RootState) => state.categoryManagement);

  // Methods
  const ConfirmText = (categoryName: string) => (
    <div className="flex flex-col items-center">
      <div className="flex mx-auto p-4  mb-5 w-24 h-24 rounded-full">
        <div className="flex mx-auto items-center   w-16 h-16 justify-center rounded-full">
          <CustomIcon definedIcon="dangerDelete" width="50" height="40" />
        </div>
      </div>
      <p className="text-center thepassHtwo py-2 text-[#1B1F3B]">Delete Category</p>
      <p className="text-center thepassBthree text-secondaryTextDisabled">
        Are you sure you want to delete <b>{categoryName} category?</b> This will also delete all subcategories within it.
      </p>
    </div>
  );

  // Fetch categories from API
  const fetchCategories = async () => {
    try {
      dispatch(setCategoryLoading(true));
      const response = await Api.get('lookups/category', { name: 'category' });
      const categoriesData = response.data || [];

      // Fetch subcategories
      let subcategoriesData = [];
      try {
        const subcategoriesResponse = await Api.get('lookups/subCategory');
        subcategoriesData = subcategoriesResponse.data || [];
      } catch (subError) {
        console.error('Error fetching subcategories:', subError);
      }

      // Map subcategories to their categories
      const categoriesWithSubcategories = categoriesData.map((category: { _id: string }) => {
        const categorySubcategories = subcategoriesData.filter((sub: { categoryId: string }) => sub.categoryId === category._id);
        return {
          ...category,
          subcategories: categorySubcategories,
        };
      });

      dispatch(setCategories(categoriesWithSubcategories));
    } catch (error: any) {
      console.error('Error fetching categories:', error);
      // notify.error(error?.response?.data?.message || 'Failed to fetch categories');
      dispatch(setCategories([]));
    } finally {
      dispatch(setCategoryLoading(false));
    }
  };

  // Load categories on component mount
  useEffect(() => {
    fetchCategories();
  }, []);

  const handleCreateCategory = async ({ title, description, icon }: { title: string; description: string; icon: string }) => {
    try {
      dispatch(setCategoryLoading(true));

      // Prepare payload for API
      const payload = {
        name: title,
        icon: icon || '',
        description: description || '',
      };

      // Send to API
      const response = await Api.post('lookups/category/single', payload);

      // Add to local state
      const newCategory = {
        id: response.data._id || Date.now(),
        name: title,
        icon: icon || '',
        description: description || '',
        subcategories: [],
      };

      dispatch(setCategories([...categories, newCategory] as any));

      // notify('Category created successfully');
    } catch (error) {
      console.error('Error creating category:', error);
      // notify.error(error?.response?.data?.message || 'Failed to create category');
    } finally {
      dispatch(setCategoryLoading(false));
    }
  };

  const handleEditCategory = async ({ title, description, icon }: { title: string; description: string; icon: string }) => {
    if (!editingCategory) return;

    try {
      dispatch(setCategoryLoading(true));
      const categoryId = editingCategory?._id || editingCategory?.id;

      // Check if any changes were made
      const hasChanges =
        title !== editingCategory?.name || description !== (editingCategory?.description || '') || icon !== (editingCategory?.icon || '');

      if (!hasChanges) {
        // No changes made, just close the modal
        dispatch(setEditCategoryModal(false));
        dispatch(setEditingCategory(null));
        // notify('No changes were made');
        return;
      }

      const payload = {
        name: title,
        icon: icon || '',
        description: description || '',
      };

      await Api.put(`lookups/category/single/${categoryId}`, payload);

      // Update only the edited category in local state
      const updatedCategories = categories.map((cat: { _id: string }) => {
        const isTargetCategory = cat._id === editingCategory._id;

        if (isTargetCategory) {
          const updatedCategory = {
            ...cat,
            name: title,
            description: description || '',
            icon: icon !== undefined ? icon : editingCategory.icon || '',
          };

          return updatedCategory;
        }
        return cat;
      });

      dispatch(setCategories(updatedCategories));

      dispatch(setEditCategoryModal(false));
      dispatch(setEditingCategory(null));
      // notify('Category updated successfully');
    } catch (error) {
      console.error('Error editing category:', error);
      // notify.error(error?.response?.data?.message || 'Failed to edit category');
    } finally {
      dispatch(setCategoryLoading(false));
    }
  };

  const handleDeleteCategory = async (category: any) => {
    try {
      dispatch(setCategoryLoading(true));
      const categoryId = category._id || category.id;

      await Api.delete(`lookups/category/single/${categoryId}`);

      // Refresh categories from API after delete
      await fetchCategories();

      // notify('Category deleted successfully');
    } catch (error) {
      console.error('Error deleting category:', error);
      // notify.error(error?.response?.data?.message || 'Failed to delete category');
    } finally {
      dispatch(setCategoryLoading(false));
    }
  };

  // Fetch subcategories for a specific category
  const fetchSubcategoriesForCategory = async (categoryId: string) => {
    try {
      const response = await Api.get('lookups/subCategory', { categoryId });
      return response.data || [];
    } catch (error) {
      console.error('Error fetching subcategories for category:', error);
      return [];
    }
  };

  const handleCreateSubcategory = async ({ title }: { title: string }) => {
    if (!activeCategoryId) {
      console.error('No activeCategoryId found');
      return;
    }

    // Prepare payload for API
    const payload = {
      name: title,
      categoryId: activeCategoryId,
    };

    try {
      // Send to API
      const response = await Api.post('lookups/subCategory/single', payload);

      // Fetch updated subcategories from API
      const updatedSubcategories = await fetchSubcategoriesForCategory(activeCategoryId);

      // Update local state with fresh data from API
      const updatedCategories = categories.map((cat) =>
        cat._id === activeCategoryId
          ? {
              ...cat,
              subcategories: updatedSubcategories,
            }
          : cat
      );
      dispatch(setCategories(updatedCategories));

      // notify('Subcategory created successfully');
    } catch (error) {
      console.error('Error creating subcategory:', error);
      // notify.error(error?.response?.data?.message || 'Failed to create subcategory');
    }
  };

  const handleEditSubcategory = async ({ title }: { title: string }) => {
    if (!editingSub || !activeCategoryId) {
      console.error('No editingSub or activeCategoryId found');
      return;
    }

    try {
      const subcategoryId = editingSub?._id || editingSub?.id;
      const payload = {
        name: title,
        categoryId: activeCategoryId,
      };

      // Send PUT request to API
      await Api.put(`lookups/subCategory/single/${subcategoryId}`, payload);

      // Fetch updated subcategories from API
      const updatedSubcategories = await fetchSubcategoriesForCategory(activeCategoryId);

      // Update local state with fresh data from API
      const updatedCategories = categories.map((cat: { _id: string }) =>
        cat._id === activeCategoryId
          ? {
              ...cat,
              subcategories: updatedSubcategories,
            }
          : cat
      );
      dispatch(setCategories(updatedCategories));

      // Update editingCategory with new subcategories if it's the same category
      if (editingCategory && editingCategory._id === activeCategoryId) {
        dispatch(
          setEditingCategory({
            ...editingCategory,
            subcategories: updatedSubcategories,
          } as any)
        );
      }

      dispatch(setEditSubModal(false));
      dispatch(setEditingSub(null));
      // notify('Subcategory updated successfully');
    } catch (error) {
      console.error('Error updating subcategory:', error);
      // notify.error(error?.response?.data?.message || 'Failed to update subcategory');
    }
  };
  // refresh sub
  const newSubCategories = async (categoryId: string) => {
    try {
      const response = await Api.get('lookups/subCategory', { categoryId });
      return response.data || [];
    } catch (error: any) {
      console.error('Error fetching subcategories:', error);
      // notify.error(error?.response?.data?.message || 'Failed to fetch subcategories');
      return [];
    }
  };

  const handleDeleteSubcategory = async (subcategory: { _id: string; id: string }) => {
    try {
      const subcategoryId = subcategory._id || subcategory.id;

      // Send DELETE request to API
      await Api.delete(`lookups/subCategory/single/${subcategoryId}`);

      // Fetch updated subcategories from API using categoryId
      const response = await Api.get('lookups/subCategory', { categoryId: activeCategoryId });
      const updatedSubcategories = response.data || [];

      // Update local state with fresh data from API
      const updatedCategories = categories.map((cat: { _id: string }) =>
        cat._id === activeCategoryId
          ? {
              ...cat,
              subcategories: updatedSubcategories,
            }
          : cat
      );
      dispatch(setCategories(updatedCategories));

      // Update editingCategory with new subcategories if it's the same category
      if (editingCategory && editingCategory._id === activeCategoryId) {
        dispatch(
          setEditingCategory({
            ...editingCategory,
            subcategories: updatedSubcategories,
          } as any)
        );
      }

      newSubCategories(subcategoryId);
      // notify('Subcategory deleted successfully');
    } catch (error) {
      console.error('Error deleting subcategory:', error);
      // notify.error(error?.response?.data?.message);
    }
  };

  const handleResetForm = () => {
    dispatch(setTitle(''));
    dispatch(setEmoji(''));
    dispatch(setDescription(''));
  };

  // Context menu handlers
  const handleContextMenu = (e: React.MouseEvent, categoryId: string) => {
    e.preventDefault();
    e.stopPropagation();
    setContextMenu({
      visible: true,
      x: e.clientX,
      y: e.clientY,
      categoryId,
    });
  };

  const closeContextMenu = () => {
    setContextMenu({
      visible: false,
      x: 0,
      y: 0,
      categoryId: null,
    });
  };

  const handleEditCategoryFromMenu = async (category: any) => {
    // Fetch subcategories for the category being edited
    const subcategories = await fetchSubcategoriesForCategory(category._id);
    const categoryWithSubcategories = {
      ...category,
      subcategories: subcategories,
    };

    dispatch(setEditingCategory(categoryWithSubcategories));
    dispatch(setEditCategoryModal(true));
    closeContextMenu();
  };

  const handleDeleteCategoryFromMenu = (category: any) => {
    dispatch(
      showConfirm({
        message: ConfirmText(category.name),
        options: {
          onConfirm: () => {
            handleDeleteCategory(category);
            dispatch(hideConfirm());
            closeContextMenu();
          },
          confirmLabel: 'Delete',
          cancelLabel: 'Cancel',
        },
      })
    );
  };

  // Show loading state
  if (loading) {
    return (
      <div className="pt-3">
        {/* Search bar and filter skeleton */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-5 gap-2">
          <div className="flex items-center gap-2">
            <div className="h-5 bg-gray-300 rounded-full dark:bg-gray-600 w-32 animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-8 animate-pulse"></div>
          </div>
          <div className="h-8 bg-gray-300 rounded-lg dark:bg-gray-600 w-56 animate-pulse" />
        </div>

        {/* Categories skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-white rounded-xl shadow border border-gray-200 p-5 animate-pulse">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-6">
                <div className="flex-1 w-full">
                  <div className="flex flex-row gap-4">
                    {/* Icon skeleton */}
                    <div className="rounded-full items-center justify-center w-12 h-12 bg-gray-300 hidden sm:flex animate-pulse"></div>

                    {/* Content skeleton */}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-24 animate-pulse"></div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="hidden sm:flex items-center gap-2">
                            <div className="w-8 h-8 bg-gray-300 rounded animate-pulse"></div>
                            <div className="w-8 h-8 bg-gray-300 rounded animate-pulse"></div>
                          </div>
                          <div className="w-6 h-6 bg-gray-300 rounded animate-pulse"></div>
                        </div>
                      </div>
                      <div className="h-4 bg-gray-300 rounded-full dark:bg-gray-600 w-3/4 mt-2 animate-pulse"></div>

                      {/* Mobile responsive skeleton */}
                      <div className="flex items-center justify-between mt-2">
                        <div className="h-6 bg-gray-300 rounded-full dark:bg-gray-600 w-24 animate-pulse"></div>
                        <div className="flex flex-row items-center gap-4 sm:hidden">
                          <div className="w-8 h-8 bg-gray-300 rounded animate-pulse"></div>
                          <div className="w-8 h-8 bg-gray-300 rounded animate-pulse"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex sm:gap-y-0 gap-y-3 flex-col sm:flex-row items-start sm:items-center justify-between ">
          <Jumbotron />

          <PermissionProtectedComponent permissions={UserPermissions.CREATE_CATEGORY}>
            <Button type="button" className="w-fit min-h-10" onClick={() => dispatch(setCreateCategoryCompVisible(true))}>
              <div className="w-5 h-5 sm:mr-2">
                <Icon icon="mdi:add" width="22" />
              </div>
              <p className="text-nowrap">Create Category</p>
            </Button>
          </PermissionProtectedComponent>
        </div>

        <div>
          {/* Search bar and filter side by side */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-5 gap-2">
            <div className="flex items-center gap-2">
              <span className="font-medium">Available Categories</span>
              <span className="bg-[#F9F5FF] border border-[#E9EAEB] text-sm px-2 py-0.5 rounded-full text-[#6941C6] font-semibold">
                {categories.length}
              </span>
            </div>
            <div className="flex items-center gap-2 w-full sm:w-auto mt-2 sm:mt-0">
              <div className="relative w-full max-w-xs">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder={'Search...'}
                  className="bg-gray-white border truncate border-gray-200 text-gray-800 text-[13.5px] rounded-lg block w-full pl-10 p-2"
                  value={categorySearch}
                  onInput={(e) => dispatch(setCategorySearch((e.target as HTMLInputElement).value))}
                />
              </div>
              {/* <ToggleFilter /> */}
            </div>
          </div>

          {/* Available Categories */}
          <div className="columns-1 lg:columns-2 space-y-4">
            {categories.length === 0 ? (
              <div className="text-center py-20 col-span-full">
                <div className="text-gray-500 text-lg mb-2">No categories found</div>
                <div className="text-gray-400 text-sm">Create your first category to get started</div>
              </div>
            ) : (
              categories
                .filter((cat: { name: string }) => cat.name.toLowerCase().includes(categorySearch.toLowerCase()))
                .map(
                  (cat: {
                    subcategories: any[];
                    _id: string;
                    id: string;
                    icon: string;
                    name: string;
                    description: string;
                    subCategoriesCount: number;
                  }) => {
                    const filteredSubcategories = cat.subcategories?.filter((sub) => sub.name.toLowerCase().includes(search.toLowerCase())) || [];
                    const getPageSize = (page: number) => (page === 1 ? 15 : 8);
                    const pageSize = getPageSize(currentPage);
                    const totalPages = filteredSubcategories.length > 15 ? 1 + Math.ceil((filteredSubcategories.length - 15) / 8) : 1;
                    const paginatedSubcategories =
                      currentPage === 1
                        ? filteredSubcategories.slice(0, 15)
                        : filteredSubcategories.slice(15 + (currentPage - 2) * 8, 15 + (currentPage - 1) * 8);
                    return (
                      <div key={cat._id} className="bg-white rounded-xl shadow border border-tertiaryBorder p-5 break-inside-avoid">
                        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-6">
                          <div className="flex-1 w-full">
                            <div className="flex flex-row gap-4">
                              {/* Icon */}
                              <div className="rounded-full items-center justify-center w-12 h-12 bg-[#F3F4F6] hidden sm:flex">
                                {cat.icon ? (
                                  <span className="text-2xl flex items-center justify-center">{cat.icon}</span>
                                ) : (
                                  <CustomIcon definedIcon="code" />
                                )}
                              </div>
                              {/* Texts */}
                              <div
                                className="flex-1 cursor-pointer sm:cursor-default"
                                onClick={async () => {
                                  if (window.innerWidth < 640) {
                                    const categoryId = cat._id;
                                    const isExpanding = expanded !== categoryId;

                                    if (isExpanding) {
                                      // Fetch subcategories when expanding
                                      const subcategories = await fetchSubcategoriesForCategory(categoryId);

                                      // Update the category with fetched subcategories
                                      const updatedCategories = categories.map((category) =>
                                        category._id === categoryId
                                          ? {
                                              ...category,
                                              subcategories: subcategories,
                                            }
                                          : category
                                      );
                                      dispatch(setCategories(updatedCategories));
                                    }

                                    dispatch(setExpanded(expanded === categoryId ? null : categoryId));
                                  }
                                }}
                              >
                                <div className="flex items-center justify-between">
                                  <div className="font-bold text-lg text-gray-900 flex items-center gap-2 capitalize">
                                    {cat.icon && <span className="text-xl sm:hidden flex items-center">{cat.icon}</span>}
                                    {cat.name}
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {/* Edit and Delete Button of Categories */}
                                    {/* <div className="hidden sm:inline-flex items-center gap-2">
                                  <button
                                    className="hover:bg-gray-100 p-2 rounded"
                                    onClick={() => {
                                      dispatch(setEditingCategory(cat));
                                      dispatch(setEditCategoryModal(true));
                                    }}
                                  >
                                    <CustomIcon definedIcon="edit" />
                                  </button>
                                  <button
                                    className="hover:bg-gray-100 p-2 rounded"
                                    onClick={() => {
                                      showConfirm(ConfirmText(cat.name), {
                                        onConfirm: () => {
                                          handleDeleteCategory(cat);
                                          hideConfirm();
                                        },
                                        confirmLabel: 'Delete',
                                        cancelLabel: 'Cancel',
                                        danger: true,
                                      });
                                    }}
                                  >
                                    <CustomIcon definedIcon="trash" />
                                  </button>
                                </div> */}

                                    <div className="flex items-center gap-1">
                                      <button
                                        className="p-1 rounded hover:bg-gray-100"
                                        onClick={async () => {
                                          const categoryId = cat._id;
                                          const isExpanding = expanded !== categoryId;

                                          if (isExpanding) {
                                            // Fetch subcategories when expanding
                                            const subcategories = await fetchSubcategoriesForCategory(categoryId);

                                            // Update the category with fetched subcategories
                                            const updatedCategories = categories.map((category: { _id: string }) =>
                                              category._id === categoryId
                                                ? {
                                                    ...category,
                                                    subcategories: subcategories,
                                                  }
                                                : category
                                            );
                                            dispatch(setCategories(updatedCategories));
                                          }

                                          dispatch(setExpanded(expanded === categoryId ? null : categoryId));
                                          dispatch(setSearch(''));
                                        }}
                                        aria-label={expanded === cat._id ? 'Collapse' : 'Expand'}
                                        type="button"
                                      >
                                        <Icon
                                          width="35"
                                          className="text-[#743AF5]"
                                          icon={expanded === cat._id ? 'ic:round-keyboard-arrow-up' : 'ic:round-keyboard-arrow-down'}
                                        />
                                      </button>
                                      <button
                                        className="p-1 rounded hover:bg-gray-100"
                                        onClick={(e) => handleContextMenu(e, cat._id)}
                                        aria-label="More options"
                                        type="button"
                                      >
                                        <Icon width="20" className="text-[#743AF5]" icon="mdi:dots-vertical" />
                                      </button>
                                    </div>
                                  </div>
                                </div>
                                <div className="text-gray-500 text-base mt-0.5">{cat.description}</div>

                                {/* in responsive */}
                                <div className="flex items-center justify-between mt-2">
                                  <div className="flex flex-row items-center gap-4 sm:hidden">
                                    <button
                                      className="hover:bg-gray-100 p-2 rounded"
                                      onClick={() => {
                                        setEditingCategory(cat);
                                        setEditCategoryModal(true);
                                      }}
                                    >
                                      {/* <CustomIcon definedIcon="edit" /> */}
                                    </button>
                                    <button
                                      className="hover:bg-gray-100 p-2 rounded"
                                      onClick={() => {
                                        dispatch(
                                          showConfirm({
                                            message: ConfirmText(cat.name),
                                            options: {
                                              onConfirm: () => {
                                                handleDeleteCategory(cat);
                                                dispatch(hideConfirm());
                                              },
                                              confirmLabel: 'Delete',
                                              cancelLabel: 'Cancel',
                                            },
                                          })
                                        );
                                      }}
                                    >
                                      {/* <CustomIcon definedIcon="trash" /> */}
                                    </button>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        {!(expanded === cat._id) && (
                          <div className="inline-flex items-center gap-1 bg-[#F9F8FA] text-text-500 thepassBfour rounded-full px-3 py-1 mt-3">
                            <svg width="17" className="mr-1" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M8.5 11.3359H8.50667" stroke="#A47BFA" stroke-linecap="round" stroke-linejoin="round" />
                              <path
                                d="M10.5013 1.33594H4.5013C4.14768 1.33594 3.80854 1.47641 3.55849 1.72646C3.30844 1.97651 3.16797 2.31565 3.16797 2.66927V13.3359C3.16797 13.6896 3.30844 14.0287 3.55849 14.2787C3.80854 14.5288 4.14768 14.6693 4.5013 14.6693H12.5013C12.8549 14.6693 13.1941 14.5288 13.4441 14.2787C13.6942 14.0287 13.8346 13.6896 13.8346 13.3359V4.66927L10.5013 1.33594Z"
                                stroke="#A47BFA"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                              <path
                                d="M6.56641 5.99751C6.72639 5.55659 7.03626 5.18594 7.44183 4.95033C7.8474 4.71472 8.32288 4.62915 8.78515 4.70858C9.24741 4.788 9.66706 5.02737 9.97073 5.38485C10.2744 5.74232 10.4428 6.19515 10.4464 6.66418C10.4464 7.99751 8.44641 8.66418 8.44641 8.66418"
                                stroke="#A47BFA"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                              />
                            </svg>
                            {cat?.subCategoriesCount || 0} Subcategories
                          </div>
                        )}

                        {/* Subcategories Grid */}
                        {expanded === cat._id && (
                          <div className="p-1 rounded-lg pt-4">
                            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-3 gap-2 sm:gap-4">
                              <div className="thepassHsix text-[#262E3D] mb-2 sm:mb-0 text-nowrap">
                                Subcategories ({filteredSubcategories.length || 0})
                              </div>
                              <div className="flex flex-row items-center gap-2 w-full max-w-xl sm:justify-end">
                                <div className="relative flex-1 max-w-xs">
                                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <Icon icon="carbon:search" width="20" className="w-5 h-5 text-gray-500 dark:text-gray-400" />
                                  </div>
                                  <input
                                    type="text"
                                    placeholder={'Search...'}
                                    className="bg-gray-white border truncate border-gray-200 text-gray-800 text-[13.5px] rounded-lg block w-full pl-10 p-2"
                                    value={search}
                                    onInput={(e: React.FormEvent<HTMLInputElement>) => dispatch(setSearch(e.currentTarget.value))}
                                  />
                                </div>
                                <PermissionProtectedComponent permissions={UserPermissions.UPDATE_CATEGORY}>
                                  <Button
                                    label="New"
                                    className="text-xs h-10 ml-0  min-w-0 flex-shrink-0"
                                    colorType="secondary"
                                    icon={
                                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="18" viewBox="0 0 24 24">
                                        <path fill="currentColor" d="M11 13H5v-2h6V5h2v6h6v2h-6v6h-2z" />
                                      </svg>
                                    }
                                    variant="sm"
                                    onClick={() => {
                                      dispatch(setActiveCategoryId(cat._id));
                                      dispatch(setOpenSubModal(true));
                                    }}
                                  />
                                </PermissionProtectedComponent>
                              </div>
                            </div>

                            {/* Show subcategories only if they exist */}
                            {filteredSubcategories.length > 0 ? (
                              <div className="flex flex-wrap items-start justify-start gap-4 mb-4 mt-5">
                                {(typeof window !== 'undefined' && window.innerWidth < 640
                                  ? paginatedSubcategories.slice(0, 8)
                                  : paginatedSubcategories
                                ).map((sub) => (
                                  <div
                                    key={sub.id}
                                    className="flex items-center justify-between bg-[#FFFFFF] border border-[#E2E8F0] rounded-lg p-2 max-w-56"
                                  >
                                    <span className="text-base font-medium px-1 truncate text-gray-800">{sub.name}</span>
                                    {/* Edit Button of Subcategories */}
                                    {/* <button
                                  className="hover:bg-gray-200 p-1 rounded"
                                  onClick={() => {
                                    setActiveCategoryId(cat._id);
                                    setEditingSub(sub);
                                    setEditSubModal(true);
                                  }}
                                >
                                  <Icon icon="carbon:edit" width="18" className="text-gray-500" />
                                </button> */}

                                    {/* Delete Button of Subcategories */}
                                    {/* <button
                                  className="hover:bg-gray-200 p-1 rounded"
                                  onClick={() => {
                                    showConfirm(
                                      <div className="flex flex-col items-center">
                                        <div className="flex mx-auto p-4 mb-7 bg-[#ffeaea] w-24 h-24 rounded-full">
                                          <div className="flex mx-auto items-center mb-7 bg-[#ffd6d6] w-16 h-16 justify-center rounded-full">
                                            <CustomIcon definedIcon="trash" width="50" height="40" />
                                          </div>
                                        </div>
                                        <p className="text-center text-[#626262]">
                                          Are you sure you want to delete <b>{sub.name} subcategory?</b>
                                        </p>
                                      </div>,
                                      {
                                        onConfirm: () => {
                                          handleDeleteSubcategory(sub);
                                          hideConfirm();
                                        },
                                        confirmLabel: 'Delete',
                                        cancelLabel: 'Cancel',
                                        danger: true,
                                      }
                                    );
                                  }}
                                >
                                  <Icon icon="carbon:trash-can" width="18" className="text-gray-500" />
                                </button> */}
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="text-center py-8">
                                <div className="text-gray-500 text-lg mb-2">No subcategories generated</div>
                                <div className="text-gray-400 text-sm">Create your first subcategory to get started</div>
                              </div>
                            )}
                          </div>
                        )}

                        {filteredSubcategories.length > 8 && expanded === cat._id && filteredSubcategories.length > 0 && (
                          <div className="flex flex-col items-center w-full border-t border-[#DEE2E4] py-3 mt-1 gap-2">
                            {/* Pagination Row: Previous - Numbers - Next */}
                            <div className="flex flex-row flex-wrap items-center justify-center gap-1">
                              {/* Previous */}
                              <button
                                className="border border-[#DEE2E4] rounded-lg px-3 py-2 flex items-center gap-1 text-gray-600 hover:bg-gray-50 transition text-base"
                                disabled={currentPage === 1}
                                onClick={() => dispatch(setCurrentPage(currentPage - 1))}
                              >
                                <CustomIcon definedIcon="arrowLeftLong" stroke="#743AF5" width="22" height="22" />
                                <span className="sm:inline hidden">Previous</span>
                              </button>
                              {/* Page Numbers */}
                              {Array.from({ length: totalPages }, (_, i) => i + 1).map((n) => (
                                <button
                                  key={n}
                                  className={`px-3 py-2 rounded-lg text-[#1B1F3B] font-medium text-base ${
                                    n === currentPage ? 'bg-[#F1E9FE]' : 'hover:bg-gray-50'
                                  }`}
                                  onClick={() => dispatch(setCurrentPage(n))}
                                >
                                  {n}
                                </button>
                              ))}
                              {/* Next */}
                              <button
                                className="border border-[#DEE2E4] rounded-lg px-3 py-2 flex items-center gap-1 text-gray-600 hover:bg-gray-50 transition text-base"
                                disabled={currentPage === totalPages}
                                onClick={() => dispatch(setCurrentPage(currentPage + 1))}
                              >
                                <span className="sm:inline hidden">Next</span>
                                <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path
                                    d="M4.66797 9.9974H16.3346M16.3346 9.9974L10.5013 4.16406M16.3346 9.9974L10.5013 15.8307"
                                    stroke="#743AF5"
                                    stroke-width="1.66667"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                  />
                                </svg>
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  }
                )
            )}
          </div>
        </div>

        {createCategoryCompVisible && (
          <CreateCategoryDialog
            onClose={() => {
              handleResetForm();
              dispatch(setCreateCategoryCompVisible(false));
            }}
            onCreate={(data) => {
              handleResetForm();
              handleCreateCategory(data);
              dispatch(setCreateCategoryCompVisible(false));
            }}
            showDescription={false}
          />
        )}
      </div>

      {openSubModal && (
        <CreateCategoryDialog
          onClose={() => {
            handleResetForm();
            dispatch(setOpenSubModal(false));
          }}
          onCreate={({ title }) => {
            handleResetForm();
            handleCreateSubcategory({ title });
            dispatch(setOpenSubModal(false));
          }}
          modalHeader="Create Subcategory"
          showDescription={false}
          showIcon={false}
        />
      )}

      {editSubModal && editingSub && (
        <CreateCategoryDialog
          onClose={() => {
            handleResetForm();
            dispatch(setEditSubModal(false));
            dispatch(setEditingSub(null));
          }}
          onCreate={handleEditSubcategory}
          modalHeader="Edit Subcategory"
          showDescription={false}
          showIcon={false}
          defaultTitle={editingSub.name}
          actionText="Save"
        />
      )}

      {editCategoryModal && editingCategory && (
        <CreateCategoryDialog
          onClose={() => {
            handleResetForm();
            dispatch(setEditCategoryModal(false));
            dispatch(setEditingCategory(null));
          }}
          onCreate={handleEditCategory}
          modalHeader="Edit Category"
          showDescription={false}
          showIcon={true}
          defaultTitle={editingCategory.name}
          defaultIcon={editingCategory.icon || ''}
          actionText="Update"
          subcategories={(editingCategory as any).subcategories || []}
          onEditSubcategory={(subcategory) => {
            dispatch(setActiveCategoryId(editingCategory._id));
            dispatch(setEditingSub(subcategory));
            dispatch(setEditSubModal(true));
            dispatch(setEditCategoryModal(false));
          }}
          onDeleteSubcategory={(subcategory) => {
            dispatch(
              showConfirm({
                message: (
                  <div className="flex flex-col items-center">
                    <div className="flex mx-auto p-4 mb-7 bg-[#ffeaea] w-24 h-24 rounded-full">
                      <div className="flex mx-auto items-center mb-7 bg-[#ffd6d6] w-16 h-16 justify-center rounded-full">
                        <CustomIcon definedIcon="trash" width="50" height="40" />
                      </div>
                    </div>
                    <p className="text-center text-[#626262]">
                      Are you sure you want to delete <b>{subcategory.name} subcategory?</b>
                    </p>
                  </div>
                ),
                options: {
                  onConfirm: () => {
                    handleDeleteSubcategory(subcategory);
                    dispatch(hideConfirm());
                  },
                  confirmLabel: 'Delete',
                  cancelLabel: 'Cancel',
                },
              })
            );
          }}
        />
      )}

      {/* Context Menu */}
      {contextMenu.visible && (
        <>
          {/* Backdrop to close menu when clicking outside */}
          <div className="fixed inset-0 z-40" onClick={closeContextMenu} />
          {/* Context Menu */}
          <div
            className="fixed z-50 bg-white rounded-lg shadow-lg border border-gray-200 py-1 min-w-[120px]"
            style={{
              left: contextMenu.x,
              top: contextMenu.y,
              transform: 'translate(-50%, 0)',
            }}
          >
            {contextMenu.categoryId && (
              <>
                <button
                  className="w-full px-4 py-2 text-left text-[#1B1F3B] hover:bg-gray-100 flex items-center gap-2"
                  onClick={() => {
                    const category = categories.find((cat: any) => cat._id === contextMenu.categoryId);
                    if (category) {
                      handleEditCategoryFromMenu(category);
                    }
                  }}
                >
                  <CustomIcon definedIcon="edit" />
                  Edit
                </button>
                <div className="border-t border-gray-200 my-1" />
                <button
                  className="w-full px-4 py-2 text-left text-[#B42318] hover:bg-red-50 flex items-center gap-2"
                  onClick={() => {
                    const category = categories.find((cat: any) => cat._id === contextMenu.categoryId);
                    if (category) {
                      handleDeleteCategoryFromMenu(category);
                    }
                  }}
                >
                  <CustomIcon definedIcon="trash" stroke="#B42318" />
                  Delete
                </button>
              </>
            )}
          </div>
        </>
      )}
    </>
  );
};
