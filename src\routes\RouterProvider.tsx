import React from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import globalRouter from '../../UI/src/services/global-router';

// Core
import { useScrollToTop, useUserSSE } from 'UI/src';

const AppWrapper = () => {
  const { pathname } = useLocation();
  globalRouter.navigate = (path) => {
    if (!pathname.includes(path)) {
      window.location = path;
    }
  };

  useScrollToTop();

  useUserSSE();

  return <Outlet />;
};

export default AppWrapper;
