// React
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

// React Phone Number
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

// UI
import { Button } from 'UI';
import { Icon, EnumText, CustomIcon, SubscribeDialog } from 'src';

// Components
import { AssignScreening } from '../components/assign-screening';
import { AssignTest } from './assign-test';
import { AssignIntreview } from './assign-interview';
import { ApplicantDataSkeleton } from './applicant-data-skeleton';
import { ApplicantsSingleDialog } from './single-dialog';
import { PerformanceSummaryDialog } from './performance-summary';

// Flowbite
import { Tooltip, Dropdown } from 'flowbite-react';
import { ProfileComCard, QuizDifficulty, setErrorNotify } from 'UI';
import { ApplicantAssessment, Api, setNotifyMessage, useAppDispatch } from 'UI/src';

import { RootState, useAppSelector, UserData } from 'UI/src';
import avatarFemale from 'images/avatar-female.svg';
import avatarMale from 'images/avatar-male.svg';

export interface Applicant {
  _id: string;
  name: string;
  email: string;
  mobileNumber: string;
  gender: number;
  seniorityLevel: string;
  trackName: string;
  createdAt: string;
  track?: string;
}
export interface ApplicantDetails {
  applicant: Applicant;
  totalWeirdBeauvoir: number;
  totalMissed: number;
  assessmentId?: NonNullable<NonNullable<string | number | null | undefined>>;
  applicantId?: NonNullable<NonNullable<string | number | null | undefined>>;
}
export const ApplicantData = () => {
  // User Data
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);
  const isSuperAdmin = Array.isArray(userData?.role) && userData?.role.includes('super-admin');

  // Hooks
  const { id } = useParams();
  const dispatch = useAppDispatch();

  // State
  const [applicantDetails, setApplicantDetails] = useState<ApplicantDetails>({
    applicant: {
      _id: '',
      name: '',
      email: '',
      mobileNumber: '',
      gender: 0,
      seniorityLevel: '',
      trackName: '',
      createdAt: '',
      track: '',
    },
    totalWeirdBeauvoir: 0,
    totalMissed: 0,
  });
  const [isAssignTestVisible, setAssignTestVisibility] = useState(false);
  const [isScreeningVisible, setIsScreeningVisible] = useState(false);
  const [isAssignInterviewTestVisible, setAssignInterviewTestVisible] = useState(false);
  const [isCreateDialogVisible, setCreateDialogVisibility] = useState(false);
  const [isPerformanceSummaryDialogVisible, setIsPerformanceSummaryDialogVisible] = useState(false);

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get<ApplicantDetails>(`applicants/assessments/count/${id}`, {});
      console.log('applicants/assessments/count/', response.data);
      setApplicantDetails(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  const handleJoinDate = (date: string) => {
    const [mon, day, year] = new Date(date).toDateString().slice(4).split(' ');
    return `${day} ${mon}, ${year}`;
  };

  const renderPhoneAndEmail = () => {
    const applicantData = [
      {
        startIcon: 'mynaui:envelope',
        endIcon: 'si:copy-line',
        text: applicantDetails?.applicant?.email,
        notify: 'Email',
      },
      {
        endIcon: 'si:copy-line',
        text: applicantDetails?.applicant?.mobileNumber,
        displayComponent: (
          <PhoneInput
            value={applicantDetails?.applicant?.mobileNumber}
            disabled
            inputStyle={{ border: 'none', width: '200px' }}
            buttonStyle={{ border: 'none', background: 'transparent' }}
          />
        ),
        notify: 'Phone',
      },
    ];

    const dispatch = useAppDispatch();
    return (
      <div className="flex pt-3 px-0 gap-4 text-linaHalfGray dark:text-linaDarkHalfGray justify-center sm:justify-start font-semibold flex-wrap sm:flex-nowrap">
        {applicantData?.map((singleData) => (
          <div
            key={singleData?.startIcon}
            className="flex items-center gap-2 sm:border border-[#EAECF0] rounded-lg px-1 py-1 max-w-full overflow-hidden"
          >
            <Icon className="text-[#798296] dark:text-purple-400" width={'22'} icon={singleData?.startIcon} />
            <p className={`text-[#333] dark:text-gray-300 font-normal truncate ${singleData?.notify === 'Phone' && 'text-[17px] font-semibold'}`}>
              {singleData?.displayComponent || singleData?.text}
            </p>
            <Icon
              className={`dark:text-purple-400 text-[#4f5561] cursor-pointer ${singleData?.notify === 'Phone' && 'text-[#798296] '} `}
              width={singleData?.notify === 'Phone' ? '21' : '20'}
              icon={singleData?.endIcon}
              onClick={() => {
                navigator.clipboard.writeText(singleData?.text);
                dispatch(setNotifyMessage(`${singleData?.notify} copied`));
              }}
            />
          </div>
        ))}
      </div>
    );
  };

  useEffect(() => {
    if (id) {
      handleGet();
    }
  }, []);

  if (Object.keys(applicantDetails).length === 0) {
    return <ApplicantDataSkeleton />;
  }

  return (
    <>
      <ProfileComCard
        applicantName={applicantDetails?.applicant?.name}
        applicantRole={`${
          applicantDetails?.applicant?.seniorityLevel && QuizDifficulty[Number(applicantDetails.applicant.seniorityLevel)]
            ? QuizDifficulty[Number(applicantDetails.applicant.seniorityLevel)]
            : ''
        } ${applicantDetails?.applicant?.trackName || ''}`}
        applicantLocation="Cairo, Egypt"
        joinedDate={applicantDetails?.applicant?.createdAt}
        email={applicantDetails?.applicant?.email}
        phoneNumber={applicantDetails?.applicant?.mobileNumber}
        gender={applicantDetails?.applicant?.gender}
        defaultAvatarFemale={avatarFemale}
        defaultAvatarMale={avatarMale}
        // performanceSummary={{
        //   hasIssues: applicantDetails?.totalWeirdBeauvoir + applicantDetails?.totalMissed > 0,
        //   onClick: () => setIsPerformanceSummaryDialogVisible(true),
        // }}
        actions={{
          editProfile: {
            label: 'Edit Profile',
            className: 'hover:shadow-[0_0_15.9px_0_#D8D8D8] active:border border-[#DEE2E4] ',
            onClick: () => setCreateDialogVisibility(true),
            definedIcon: 'edit',
          },
          // assignToApplicant: {
          //   label: 'Assign To Applicant',
          //   className: 'bg-[#743AF5] hover:bg-[#BFA3FB] shadow-[0_0_15.9px_0_#D8D8D8] active:bg-[#6835EE]',
          //   onClick: () => console.log('Assign to applicant clicked'),
          //   definedIcon: 'plus',
          // },
        }}
        metadata={{
          createdBy: 'Admin User',
          createdDate: handleJoinDate(applicantDetails?.applicant?.createdAt),
          updatedDate: '-', // Default value if no update date
        }}
        // buttonsData={[
        //   {
        //     id: 1,
        //     label: 'Edit Profile',
        //     icon: 'mdi:edit',
        //     colorType: 'primary',
        //     onClick: () => {
        //       setCreateDialogVisibility(true);
        //     },
        //   },
        //   {
        //     id: 2,
        //     label: 'Assign to Applicant',
        //     icon: 'mdi:add',
        //     colorType: 'primary',
        //     onClick: () => {
        //       console.log('Assign to applicant clicked');
        //     },
        //   },
        // ]}
      />

      {/* Create new applicant */}
      {isCreateDialogVisible && <ApplicantsSingleDialog onCreate={handleGet} onClose={() => setCreateDialogVisibility(false)} id={id} />}

      {/* Performance Summary Dialog */}
      {isPerformanceSummaryDialogVisible && (
        <PerformanceSummaryDialog
          onClose={() => setIsPerformanceSummaryDialogVisible(false)}
          applicantDetails={{
            totalWeirdBeauvoir: applicantDetails?.totalWeirdBeauvoir || 0,
            totalMissed: applicantDetails?.totalMissed || 0,
          }}
        />
      )}
    </>
  );
};
