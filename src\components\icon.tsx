import { ReactNode } from 'react';

// Declare iconify-icon as a valid JSX element
declare global {
  namespace JSX {
    interface IntrinsicElements {
      'iconify-icon': any;
    }
  }
}

interface IconProps {
  onClick?: () => void;
  className?: string;
  icon?: string | ReactNode;
  width?: string;
  height?: string;
  name?: string;
  style?: object;
}

/* eslint-disable */
export const Icon = ({ className, name, ...props }: IconProps) => (
  <div className={`flex items-center justify-center ${className}`}>
    <iconify-icon name={name} {...props} />
  </div>
);
