import { createAsyncThunk } from '@reduxjs/toolkit';
import { Api, fetchSubmissionAi, setAiLoading, setErrorNotify, type RootState } from '../..';
import { useParams } from 'react-router-dom';

export const sendManualReply = createAsyncThunk('ai-interview/sendManualReply', async (text: string, { rejectWithValue, dispatch }) => {
  const { id } = useParams();
  const payload = {
    interviewId: id,
    userAnswerText: text,
  };

  try {
    dispatch(setAiLoading(true));
    const response = await Api.post('ai-interview/single/talk', payload);
    return response.data;
  } catch (error: any) {
    rejectWithValue('Error uploading audio:' + error?.response?.data?.message);
  } finally {
    dispatch(setAiLoading(false));
    id && (await dispatch(fetchSubmissionAi(id)).unwrap());
  }
});
