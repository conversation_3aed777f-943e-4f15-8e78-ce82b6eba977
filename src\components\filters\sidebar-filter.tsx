// React
import { useEffect, useState } from 'react';
import { Button } from 'UI';

// Core
import { Icon, MultiSelect, RadioGroup } from 'src';
import { Form, initializeForm, resetForm, RootState, setFieldValue, useAppDispatch, useAppSelector } from 'UI/src';
import { useFormik } from 'formik';

// Flowbite
import { DatePicker } from 'rsuite';

interface FilterData {
  filterFeedData: string[];
  setFilters: ({}) => void;
}

interface SidebarFilterPageProps {
  filterData: FilterData;
  searchInputField: {
    value: string;
    update: (value: string) => void;
  };
}

export const SidebarFilterPage = ({ filterData, searchInputField }: SidebarFilterPageProps) => {
  // const { filterFeedData, setFilters } = sidebarFilter || {};

  // State
  const [search, setSearch] = useState(searchInputField?.value);
  const [filterCountNumber, setFilterCountNumber] = useState(0);

  const dispatch = useAppDispatch();
  // Form
  const formik = useFormik({
    initialValues: {
      createdAt: '',
      track: [],
      seniorityLevel: [],
      phase: [],
      warnings: [],
      recommended: null,
      type: [],
      category: [],
      subCategory: [],
      difficulty: [],
      grade: [],
      startDate: '',
      dueDate: '',
      topic: [],
      scope: [],
    },
    onSubmit(values, formikHelpers) {
      console.log(values, formikHelpers);
    },
  });
  const form = useAppSelector((state: RootState) => state.form.data);

  // Methods drawer filter
  const HandleCollapseFilter = ({ onClick, label, actionLabel }: { onClick: () => void; label: string; actionLabel: string }) => (
    <div className="flex justify-between items-center p-3 bg-[#F9F8FFA3] dark:bg-darkGrayBackground border-y border-[#E8E8E8] dark:border-gray-700">
      <span className="text-[#494C54] dark:text-white text-sm font-semibold">{label}</span>
      <span className="text-[#8A43F9] text-xs font-semibold cursor-pointer" onClick={onClick}>
        {actionLabel}
      </span>
    </div>
  );

  const isAnyFilterApplied = () => {
    return (
      form.createdAt ||
      form.track?.length > 0 ||
      form.seniorityLevel?.length > 0 ||
      form.phase?.length > 0 ||
      form.recommended ||
      form.warnings?.length > 0 ||
      form.type?.length > 0 ||
      form.category?.length > 0 ||
      form.subCategory?.length > 0 ||
      form.difficulty?.length > 0 ||
      form.grade?.length > 0 ||
      form.startDate ||
      form.dueDate
    );
  };

  const clearFilter = () => {
    dispatch(resetForm());
    filterData?.setFilters({});
    setSearch('');
  };

  const applyFilter = () => {
    const cleanedForm = Object.fromEntries(Object.entries(form)?.filter(([_, value]) => value !== undefined && value !== null && value !== ''));
    if (!Array.isArray(cleanedForm?.seniorityLevel) || !cleanedForm.seniorityLevel.includes(1)) {
      delete cleanedForm?.phase;
    }
    filterData?.setFilters(cleanedForm);

    let totalLength = 0;
    for (const key in cleanedForm) {
      if (Array.isArray(cleanedForm[key])) {
        totalLength += cleanedForm[key]?.length;
      } else if (typeof cleanedForm[key] === 'string' || typeof cleanedForm[key] === 'number') {
        totalLength += 1;
      } else if (cleanedForm[key] instanceof Date) {
        totalLength += 1;
      }
    }
    setFilterCountNumber(totalLength);
  };

  // 'joinDate', // Width issues
  // 'track',
  // 'seniortyLevel',
  // 'phase',
  // 'recommended', // Width issues
  // 'warnings',
  // 'type',
  // 'category',
  // 'subCategory',
  // 'difficulty',
  // 'grad',
  // 'startEndDate', // Width issues

  const filterFeedDataReference = {
    joinDate: (
      <div className="space-y-2">
        <p className="text-[13px] text-inputLabel dark:text-inputDarkLabel font-semibold">Join Date</p>
        <DatePicker
          format="dd/MM/yyyy"
          placeholder="Enter a Join Date"
          // className="w-[210px]"
          className="w-full"
          value={form?.createdAt || null}
          onChange={(value: any) => dispatch(setFieldValue({ path: 'createdAt', value }))}
          oneTap
          showMeridiem
        />
      </div>
    ),
    track: (
      <MultiSelect
        // TODO: Markos
        label="Interests"
        name="track"
        placeholder="Search for category"
        value={form.track}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'track', value }))}
        lookup="category"
        optionValueKey="_id"
        optionLabelKey="name"
        showSingleClear={form.track?.length >= 2}
        handleSingleClear={() => dispatch(setFieldValue({ path: 'track', value: '' }))}
        customSize="[13px]"
        customWeight="semibold"
        validators={[]}
      />
    ),
    seniortyLevel: (
      <MultiSelect
        // TODO: Markos
        label="Seniority Level"
        name="seniorityLevel"
        placeholder="Search for level"
        value={form.seniorityLevel}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'seniorityLevel', value }))}
        lookup="$QuizDifficulty"
        showSingleClear={form.seniorityLevel?.length >= 2}
        handleSingleClear={() => dispatch(setFieldValue({ path: 'seniorityLevel', value: '' }))}
        customSize="[13px]"
        customWeight="semibold"
        validators={[]}
      />
    ),
    phase: Array.isArray(form?.seniorityLevel) && form.seniorityLevel.includes(1) && (
      <MultiSelect
        label="Phase"
        name="phase"
        placeholder="Search for phase"
        value={form.phase}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'phase', value }))}
        lookup="$InternPhase"
        showSingleClear={form.phase?.length >= 2}
        handleSingleClear={() => dispatch(setFieldValue({ path: 'phase', value: '' }))}
        validators={[]}
      />
    ),
    recommended: (
      <RadioGroup
        label="Recommendation"
        name="recommended"
        value={form.recommended}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'recommended', type: Number, value }))}
        lookup="$Recommended"
        className="text-sm text-inputLabel dark:text-inputDarkLabel pb-1"
        showSingleClear={form.recommended}
        handleSingleClear={() => dispatch(setFieldValue({ path: 'recommended', value: '' }))}
        customSize="[12px]"
        params={undefined}
        requiredLabel={undefined}
      />
    ),
    warnings: (
      <MultiSelect
        label="Warnings"
        name="warnings"
        placeholder="Search for warnings"
        value={form.warnings}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'warnings', value }))}
        lookup="$SubmissionWarning"
        handleSingleClear={() => dispatch(setFieldValue({ path: 'warnings', value: '' }))}
        showSingleClear={form.warnings?.length >= 2}
        customSize="[13px]"
        customWeight="semibold"
        validators={[]}
      />
    ),
    type: (
      <MultiSelect
        label="Assessment Type"
        name="type"
        placeholder="Search for type"
        value={form.type}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'type', value }))}
        lookup="$AssignmentType"
        handleSingleClear={() => dispatch(setFieldValue({ path: 'type', value: '' }))}
        customSize="[13px]"
        customWeight="semibold"
        showSingleClear={form.type?.length >= 2}
        validators={[]}
      />
    ),
    category: (
      <MultiSelect
        label="Category"
        name="category"
        placeholder="Search for category"
        value={form.category}
        customSize="[13px]"
        customWeight="semibold"
        onChange={(value: any) => {
          dispatch(setFieldValue({ path: 'category', value }));
          dispatch(setFieldValue({ path: 'subCategory', value: [] }));
          dispatch(setFieldValue({ path: 'topic', value: [] }));
        }}
        lookup="category"
        optionValueKey="_id"
        optionLabelKey="name"
        handleSingleClear={() => dispatch(setFieldValue({ path: 'category', value: '' }))}
        showSingleClear={form.category?.length >= 2}
        validators={[]}
      />
    ),
    subCategory: (
      <MultiSelect
        label="Subcategory"
        name="subCategory"
        placeholder="Search for subcategory"
        value={form.subCategory}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'subCategory', value }))}
        lookup="subcategory"
        params={{ categoryId: form.category }}
        optionValueKey="_id"
        optionLabelKey="name"
        disabled={form.category?.length <= 0}
        // disabledMessage="Please select category first"
        handleSingleClear={() => dispatch(setFieldValue({ path: 'subCategory', value: '' }))}
        showSingleClear={form.subCategory?.length >= 2}
        customSize="[13px]"
        customWeight="semibold"
        validators={[]}
      />
    ),
    topic: (
      <MultiSelect
        label="Topic"
        name="topic"
        placeholder="Search for topic"
        value={form.topic}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'topic', value }))}
        lookup="topic"
        params={{ subcategoryId: form.subCategory }}
        optionValueKey="_id"
        optionLabelKey="name"
        disabled={form.subCategory?.length <= 0}
        handleSingleClear={() => dispatch(setFieldValue({ path: 'subCategory', value: '' }))}
        showSingleClear={form.topic?.length >= 2}
        validators={[]}
      />
    ),
    QuizDifficulty: (
      <MultiSelect
        label="Difficulty"
        name="difficulty"
        placeholder="Search for difficulty"
        value={form.difficulty}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'difficulty', value }))}
        lookup="$QuizDifficulty"
        handleSingleClear={() => dispatch(setFieldValue({ path: 'difficulty', value: '' }))}
        showSingleClear={form.difficulty?.length >= 2}
        customSize="[13px]"
        customWeight="semibold"
        validators={[]}
        optionValueKey="value"
        optionLabelKey="label"
      />
    ),
    QuestionDifficulty: (
      <MultiSelect
        label="Difficulty"
        name="difficulty"
        placeholder="Search for difficulty"
        value={form.difficulty}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'difficulty', value }))}
        lookup="$QuestionDifficulty"
        handleSingleClear={() => dispatch(setFieldValue({ path: 'difficulty', value: '' }))}
        showSingleClear={form.difficulty?.length >= 2}
        customSize="[13px]"
        customWeight="semibold"
        validators={[]}
        optionValueKey="value"
        optionLabelKey="label"
      />
    ),
    grad: (
      <MultiSelect
        label="Assessment Score"
        name="grade"
        placeholder="Search for score"
        value={form.grade}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'grade', value }))}
        lookup="$grade"
        handleSingleClear={() => dispatch(setFieldValue({ path: 'grade', value: '' }))}
        showSingleClear={form.grade?.length >= 2}
        customSize="[13px]"
        customWeight="semibold"
        validators={[]}
      />
    ),
    startEndDate: (
      <div className="space-y-2">
        <p className="text-inputLabel dark:text-inputDarkLabel text-[13px] font-semibold">Assessment Due Date</p>
        <div>
          <DatePicker
            format="dd/MM/yyyy hh:mm aa"
            placeholder="Start date"
            // className="w-[210px]"
            className="w-full"
            value={form?.startDate || null}
            onChange={(value: any) => dispatch(setFieldValue({ path: 'startDate', value }))}
            showMeridiem
            placement="autoVerticalStart"
          />
        </div>
        <div>
          <DatePicker
            format="dd/MM/yyyy hh:mm aa"
            placeholder="End date"
            // className="w-[210px]"
            className="w-full"
            value={form?.dueDate || null}
            onChange={(value: any) => dispatch(setFieldValue({ path: 'dueDate', value }))}
            showMeridiem
            placement="autoVerticalEnd"
          />
        </div>
      </div>
    ),
    role: (
      <MultiSelect
        label="Role"
        name="role"
        placeholder="Search for role"
        value={form.role}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'role', value }))}
        lookup="$Role"
        handleSingleClear={() => dispatch(setFieldValue({ path: 'difficulty', value: '' }))}
        showSingleClear={form.difficulty?.length >= 2}
        validators={[]}
      />
    ),
    gender: (
      <MultiSelect
        label="Gender"
        name="gender"
        placeholder="Search for gender"
        value={form.gender}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'gender', value }))}
        lookup="$Gender"
        handleSingleClear={() => dispatch(setFieldValue({ path: 'difficulty', value: '' }))}
        showSingleClear={form.difficulty?.length >= 2}
        validators={[]}
      />
    ),
    scope: (
      <MultiSelect
        label="Scope"
        name="scope"
        placeholder="Search for scope"
        value={form.scope}
        onChange={(value: any) => dispatch(setFieldValue({ path: 'scope', value }))}
        lookup="$Scope"
        handleSingleClear={() => dispatch(setFieldValue({ path: 'difficulty', value: '' }))}
        showSingleClear={form.difficulty?.length >= 2}
        validators={[]}
      />
    ),
  };

  useEffect(() => {
    searchInputField?.update(search);
  }, [search]);

  return (
    <Form className="h-full space-y-4 border border-[#DEE2E4] rounded-xl p-2 overflow-y-auto" onSubmit={() => {}}>
      <div className="flex justify-between px-2 pt-3 pb-1">
        <div className="flex gap-2 items-center">
          <Icon icon="ion:filter" width="22" />
          <span className="hidden sm:block">Filters</span>
        </div>

        <p className=" h-fit pt-1 text-[13px] cursor-pointer text-[#9061F9]" onClick={clearFilter}>
          Clear All
        </p>
      </div>

      {/* TODO: hashed if we used it later */}

      {/* <div className="flex justify-between items-center grow space-y-0 rounded-lg relative">
        <Icon icon="carbon:search" width="20" className="size-5 text-gray-500 dark:text-gray-400 absolute left-3 pointer-events-none" />
        <input
          type="text"
          placeholder="Search..."
          className="w-full p-2 pl-10 dark:bg-gray-700 bg-gray-white text-[13.5px] text-gray-800 border border-gray-200 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white truncate rounded-lg focus:ring-0 focus:border-gray-300 shadow-sm"
          value={search}
          onInput={(e) => setSearch(e.target.value)}
        />
      </div> */}

      {filterData?.filterFeedData?.map((filter) => filterFeedDataReference[filter as keyof typeof filterFeedDataReference])}

      <Button
        label="Show Result"
        onClick={applyFilter}
        className="m-auto !mt-4 bg-[#6835EE] hover:bg-[#BFA3FB] shadow-[0_0_15.9px_0_#D8D8D8] active:bg-[#743AF5]"
      />
    </Form>
  );
};
