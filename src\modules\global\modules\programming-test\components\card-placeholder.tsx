import React from 'react';

export const CardPlaceholder = () => (
  <div className="min-w-[55vw] lg:min-w-[75vw] lg:p-0 xl:min-w-[75vw] md:min-w-[74vw] md:px-5 xl:px-0">
    <div className="grid grid-cols-1 grid-rows-[320px] sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
      {/* <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-6"> */}

      {/* grid grid-cols-1 grid-rows-[320px] sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 */}

      <div className="space-y-6 border text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
        <div className="text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
        {/* <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div> */}
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
      </div>

      <div className="space-y-6 border text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
        <div className="text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
        {/* <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div> */}
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
      </div>

      <div className="space-y-6 border text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
        <div className="text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
        {/* <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div> */}
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
      </div>
      <div className="space-y-6 border text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
        <div className="text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
        {/* <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div> */}
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
      </div>
      <div className="space-y-6 border text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
        <div className="text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
        {/* <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div> */}
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
      </div>
      <div className="space-y-6 border text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
        <div className="text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
        {/* <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div> */}
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
      </div>
      <div className="space-y-6 border text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
        <div className="text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
        {/* <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div> */}
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
      </div>
      <div className="space-y-6 border text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
        <div className="text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
        {/* <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div> */}
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
      </div>
      <div className="space-y-6 border text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
        <div className="text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
        {/* <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div> */}
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
      </div>
      <div className="space-y-6 border text-center border-gray-200 rounded-xl shadow animate-pulse p-6 dark:border-gray-700 h-80">
        <div className="text-center h-4 bg-gray-300 rounded-full dark:bg-gray-600"></div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse" />
        {/* <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-16 mb-2"></div> */}
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
        <hr className="border-gray-200 dark:border-gray-700 animate-pulse my-0" />
        <div>
          <div className="h-3 bg-gray-300 rounded-full dark:bg-gray-600 w-5/6 mb-2"></div>
          <div className="w-full h-2 bg-gray-200 rounded-full dark:bg-gray-700"></div>
        </div>
      </div>
    </div>
  </div>
);
