import React from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { GoArrowUpRight } from 'react-icons/go';
import { FiArrowDownLeft } from 'react-icons/fi';

interface ApplicantGrowthChartProps {
  data: any[];
  chartTitle: string;
}

export const ApplicantGrowthChart = ({ data, chartTitle }: ApplicantGrowthChartProps) => {
  // Map data to have month names
  const applicantData = data?.map((item) => ({
    month: new Date(2024, item.month - 1).toLocaleString('default', { month: 'short' }),
    count: item.count || 0,
  }));

  // Find the most active and least active months

  const maxData = data && data.reduce((max, item) => (item.count > max.count ? item : max), data[0]);
  const minData = data && data.reduce((min, item) => (item.count < min.count ? item : min), data[0]);

  const maxMonth = data && new Date(2024, maxData.month - 1).toLocaleString('default', { month: 'long' });
  const minMonth = data && new Date(2024, minData.month - 1).toLocaleString('default', { month: 'long' });

  // Find previous month data for growth calculation
  const previousMonth = data?.find((item) => item.month === maxData.month - 1);
  // const growthRate = previousMonth ? ((maxData.count - previousMonth.count) / previousMonth.count) * 100 : maxData.count > 0 ? 100 : 0; // Adjust for case when previous count is 0

  // Determine arrow direction and color
  const isIncrease = data && (!previousMonth || maxData.count > previousMonth.count);
  const arrowIcon = isIncrease ? <GoArrowUpRight /> : <FiArrowDownLeft />;
  const arrowColor = isIncrease ? 'text-green-500' : 'text-red-500';

  const CustomTooltip = ({ active, payload }: { active?: boolean; payload?: any[] }) => {
    if (!active || !payload || payload.length === 0) return null;

    return (
      <div className="p-3 rounded-md shadow-md border bg-white text-black dark:bg-gray-800 dark:text-white">
        <p className="font-semibold mb-1">{payload[0].payload.month}</p>
        {payload.map((entry, index) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {`${entry.value} Applicant${entry.value === 1 ? '' : 's'}`}
          </p>
        ))}
      </div>
    );
  };

  return (
    <div className="flex flex-col bg-white rounded-lg shadow-sm border border-gray-200 h-full dark:bg-[#3E3D4B] dark:border-none">
      {/* <div className="px-4 pt-4 flex flex-col"> */}
      <h2 className="text-lg font-semibold dark:text-white text-black pt-4 px-4">{chartTitle}</h2>
      {/* <div className="flex items-center mt-2">
          <div className={`flex items-center ${arrowColor}`}>
            {arrowIcon}
            <span className="ml-1 text-sm">{`${growthRate.toFixed(2)}%`}</span>
          </div>
          <span className={`ml-4 text-sm font-medium ${arrowColor}`}> {maxMonth}</span>
        </div> */}
      {/* </div> */}
      <div className="flex-grow p-4">
        <ResponsiveContainer width="100%" height={250}>
          <LineChart data={applicantData} margin={{ top: 20, bottom: 20, left: -28 }}>
            <CartesianGrid vertical={false} strokeDasharray="3 3" stroke="#E0E0E0" />
            <XAxis dataKey="month" axisLine={false} tickLine={false} tick={{ fontSize: 14 }} />
            <YAxis domain={[0, 100]} axisLine={false} tickLine={false} tick={{ fontSize: 14 }} />
            <Tooltip content={<CustomTooltip />} formatter={(value: any) => `${value} Applicants`} />
            <Line type="monotone" dataKey="count" stroke="#e3aaed" strokeWidth={4} dot={false} />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};
