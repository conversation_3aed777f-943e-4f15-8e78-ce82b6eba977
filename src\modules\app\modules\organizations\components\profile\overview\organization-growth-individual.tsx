// Core
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

// cleaned: removed unused Icon and Rating imports
import { OrganizationsGrowth, useAppDispatch, Api, setErrorNotify, UserData, useAppSelector, RootState } from 'UI/src';

// Custom chart component for this specific use case
const CustomOrganizationChart = ({ data, dataKeys }: { data: { a: number; b: number }[]; dataKeys?: { a: string; b: string } }) => {
  const keys = dataKeys || { a: 'A', b: 'B' };
  const colorConfig = {
    a: { stroke: '#3b82f6', gradient: 'colorA' },
    b: { stroke: '#10b981', gradient: 'colorB' },
  };

  // Calculate max value to set proper Y-axis domain
  const maxValue = Math.max(...data.map((item) => Math.max(item.a || 0, item.b || 0)));

  // Set better domain based on max value
  let yAxisMax;
  if (maxValue === 0) {
    yAxisMax = 5; // Default when no data
  } else if (maxValue <= 3) {
    yAxisMax = 3; // For small values, show 0,1,2,3
  } else if (maxValue <= 5) {
    yAxisMax = 5; // For medium values, show 0,1,2,3,4,5
  } else {
    yAxisMax = Math.ceil(maxValue); // For larger values, use exact max
  }

  // Generate ticks based on the domain
  const ticks = Array.from({ length: yAxisMax + 1 }, (_, i) => i);

  return (
    <ResponsiveContainer width="100%" height={160}>
      <AreaChart data={data}>
        <defs>
          <linearGradient id="colorA" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={colorConfig.a.stroke} stopOpacity={0.5} />
            <stop offset="95%" stopColor={colorConfig.a.stroke} stopOpacity={0} />
          </linearGradient>
          <linearGradient id="colorB" x1="0" y1="0" x2="0" y2="1">
            <stop offset="5%" stopColor={colorConfig.b.stroke} stopOpacity={0.5} />
            <stop offset="95%" stopColor={colorConfig.b.stroke} stopOpacity={0} />
          </linearGradient>
        </defs>
        <XAxis dataKey="x" />
        <YAxis tickFormatter={(value: any) => Math.round(value).toString()} domain={[0, yAxisMax]} ticks={ticks} />
        <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
        <Tooltip
          formatter={(value, name) => {
            const displayName = keys[name as keyof typeof keys] || name;
            return [Math.round(Number(value)), displayName];
          }}
        />
        <Area type="monotone" dataKey="a" stroke={colorConfig.a.stroke} fill="url(#colorA)" strokeWidth={2} name="a" />
        <Area type="monotone" dataKey="b" stroke={colorConfig.b.stroke} fill="url(#colorB)" strokeWidth={2} name="b" />
      </AreaChart>
    </ResponsiveContainer>
  );
};

export const OrganizationGrowthIndividual = () => {
  // Hooks
  const { id } = useParams();
  const userData: UserData = useAppSelector((state: RootState) => state.auth.user);

  const orgId = id ?? userData.organizationId;

  const [organizationGrowthData, setOrganizationGrowthData] = useState<OrganizationsGrowth>({
    usersCount: 0,
    applicantCount: 0,
    usersGrowth: Array(12)
      .fill(0)
      .map((_, i) => ({ month: i + 1, count: 0 })) as any,

    applicantGrowth: Array(12)
      .fill(0)
      .map((_, i) => ({ month: i + 1, count: 0 })) as any,
  });
  const dispatch = useAppDispatch();

  // Methods
  const handleGet = async () => {
    try {
      const response = await Api.get<OrganizationsGrowth>(`organizations/growth/${orgId}`, {});
      console.log(`organizations/growth/${orgId}`, response.data);
      setOrganizationGrowthData(response.data);
    } catch (error: any) {
      dispatch(setErrorNotify(error?.response?.data?.message));
    }
  };

  // cleaned: removed bottom stats logic entirely

  // Transform data for the chart - ensure all values are whole numbers
  const chartsdata = Array.from({ length: 12 }, (_, i) => {
    const monthIndex = i + 1; // 1-12 for data lookup
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    // Get the actual data values and ensure they are whole numbers
    const applicantCount = Math.round((organizationGrowthData?.applicantGrowth as any)?.find?.((item: any) => item.month === monthIndex)?.count || 0);
    const userCount = Math.round((organizationGrowthData?.usersGrowth as any)?.find?.((item: any) => item.month === monthIndex)?.count || 0);

    return {
      x: monthNames[i], // 0-11 for month names
      a: applicantCount,
      b: userCount,
    };
  });

  useEffect(() => {
    handleGet();
  }, []);

  const chartLabels = {
    a: 'Applicants Count',
    b: 'Users Count',
  };

  return (
    <div className="p-2">
      <div className="-ml-8">
        <CustomOrganizationChart data={chartsdata} dataKeys={chartLabels} />
      </div>

      {/* bottom stats removed */}
    </div>
  );
};
