// Validate Phone Number
import { isValidPhoneNumber } from 'libphonenumber-js';

export const useValidate = () => {
  const isRequired = () => {
    return (value: any) => {
      return !!value ? null : 'This field is required';
    };
  };

  const isSelected = () => {
    return (value: any) => {
      return value?.length !== 0 ? null : 'This field is required';
    };
  };

  const isNumber = () => {
    return (value: any) => {
      return !isNaN(value) ? null : 'This field should contain only numbers';
    };
  };

  const phoneNumberValid = () => {
    return (value: any) => {
      const digitsOnly = value.replace(/\D/g, ''); // Remove all non-digit characters
      return digitsOnly.length >= 10 ? null : 'Phone number must contain at least 10 digits';
    };
  };

  const isNotSpaces = () => {
    return (value: any) => {
      return value && value.trim().length === 0 ? `This field shouldn't be empty` : null;
    };
  };

  // const startAndEndWith = (condition) => {
  //   return (value: any) => {
  //     return value.startsWith(condition) || value.endsWith(condition)
  //       ? `This field can't start or end with ${(condition === ' ' ? 'white spaces' : `'${condition}'`)}`
  //       : null;
  //   };
  // };

  // const isNotEmptyString = () => {
  //   return (value: any) => {
  //     return value !== '' ? null : 'This field cannot be an empty string';
  //   };
  // };

  const minLength = (length: number) => {
    return (value: any) => {
      return value && value.length >= length ? null : `This field minimum length is ${length}`;
    };
  };

  const maxLength = (length: number) => {
    return (value: any) => {
      return value && value.length <= length ? null : `This field maximum length is ${length}`;
    };
  };

  const validateRegex = (regex: RegExp) => {
    return (value: any) => {
      return value && regex.test(value) ? null : `This field pattern is invalid`;
    };
  };

  const validatePasswordRegex = (regex: RegExp) => {
    return (value: any) => {
      return value && regex.test(value)
        ? null
        : `Password must contain at least one uppercase letter, one lowercase letter, one digit and one special character`;
    };
  };
  // this function to handle max and min validate number in aiDialog interview
  const isValidateMaxAndMinNumber = (type: string, limit: number) => {
    return (value: any) => {
      if (type === 'min') {
        return value >= limit ? null : `Value must be at least ${limit}`;
      } else if (type === 'max') {
        return value <= limit ? null : `Value must not exceed ${limit}`;
      } else {
        return 'Invalid validation type';
      }
    };
  };

  // Validate Country Code Phone Number
  const countryCodeNumberValid = () => {
    return (value: any) => {
      return isValidPhoneNumber(`+${value}`) ? null : 'Invalid phone number';
    };
  };

  return {
    isRequired,
    minLength,
    maxLength,
    validateRegex,
    isNumber,
    // isNotEmptyString,
    validatePasswordRegex,
    // startAndEndWith,
    isNotSpaces,
    isSelected,
    phoneNumberValid,
    isValidateMaxAndMinNumber,
    countryCodeNumberValid,

    // Error message
    validate(value: any, validators: ((value: any) => string | null)[] = []) {
      // Validation Logic
      const results = validators.map((validator: (value: any) => string | null) => validator(value));

      //   Messages
      const errorMessage = results.every((item) => item === null) ? null : results.find((item) => item !== null);

      return errorMessage;
    },
  };
};
