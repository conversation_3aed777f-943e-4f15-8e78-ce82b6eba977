import React, { FC, PureComponent } from 'react';
import { <PERSON><PERSON>hart, Pie, Sector, Cell, ResponsiveContainer } from 'recharts';

export type ResultProps = {
  testResult: number;
};

export const Result: FC<ResultProps> = ({ testResult }) => {
  const data = [
    { name: 'Group A', value: testResult },
    { name: 'Group B', value: 100 - testResult },
  ];

  // Can't get colors from tailwind
  const chartColor = (chartPercentageScore: number): string => {
    if (chartPercentageScore < 50) return '#EF4444';
    else if (chartPercentageScore <= 80) return '#FBBF24';
    else return '#22C55E';
  };

  const COLORS: string[] = [chartColor(testResult), '#F2F2F2'];

  const RADIAN = Math.PI / 180;
  type LabelProps = {
    cx: number;
    cy: number;
    midAngle: number;
    innerRadius: number;
    outerRadius: number;
    percent: number;
    index: number;
  };
  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: LabelProps) => {
    const radius = innerRadius + (outerRadius - innerRadius) * -1.466;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    return (
      <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
        {`${(percent * 100).toFixed(0)}%`}
      </text>
    );
  };

  return (
    <ResponsiveContainer width={180} minHeight={180}>
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          startAngle={450}
          endAngle={90}
          innerRadius={75}
          outerRadius={90}
          fill="#8884d8"
          dataKey="value"
          stroke="none"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
      </PieChart>
    </ResponsiveContainer>
  );
};
